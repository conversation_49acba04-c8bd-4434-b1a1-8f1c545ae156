/**
 * ✅ CONTEXTO PARA AVISO DE REFRESH MANUAL
 * 
 * Gerencia o estado global do aviso que aparece após gerar cronograma
 */
import React, { createContext, useContext, useState, useCallback } from 'react';

interface RefreshWarningContextType {
  showWarning: boolean;
  showRefreshWarning: () => void;
  hideWarning: () => void;
}

const RefreshWarningContext = createContext<RefreshWarningContextType | undefined>(undefined);

export const RefreshWarningProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showWarning, setShowWarning] = useState(false);

  const showRefreshWarning = useCallback(() => {
    setShowWarning(true);

    // ✅ Esconder após 3 segundos
    setTimeout(() => {
      setShowWarning(false);
    }, 3000);
  }, []);

  // ✅ Expor função globalmente para ser chamada de qualquer lugar
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).showRefreshWarning = showRefreshWarning;
    }

    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).showRefreshWarning;
      }
    };
  }, [showRefreshWarning]);

  const hideWarning = useCallback(() => {
    setShowWarning(false);
  }, []);

  return (
    <RefreshWarningContext.Provider value={{
      showWarning,
      showRefreshWarning,
      hideWarning
    }}>
      {children}
    </RefreshWarningContext.Provider>
  );
};

export const useRefreshWarning = () => {
  const context = useContext(RefreshWarningContext);
  if (context === undefined) {
    throw new Error('useRefreshWarning must be used within a RefreshWarningProvider');
  }
  return context;
};
