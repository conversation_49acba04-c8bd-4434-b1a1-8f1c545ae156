import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface QuestionTime {
  questionId: string;
  startTime: number;
  elapsedTime: number;
  isActive: boolean;
  lastUpdate: number;
}

interface SessionTimerState {
  sessionId: string;
  totalElapsedTime: number;
  questionTimes: Record<string, QuestionTime>;
  currentQuestionId: string | null;
  sessionStartTime: number;
  lastSaveTime: number;
}

const STORAGE_KEY_PREFIX = 'session_timer_';
const SAVE_INTERVAL = 60000; // ✅ OTIMIZADO: Salvar no banco a cada 60 segundos (era 10s)
const INACTIVITY_THRESHOLD = 30000; // 30 segundos de inatividade

export const useSessionTimer = (sessionId: string, userId: string) => {
  const [state, setState] = useState<SessionTimerState>(() => {
    // Tentar carregar estado do localStorage
    const storageKey = `${STORAGE_KEY_PREFIX}${sessionId}`;
    const saved = localStorage.getItem(storageKey);

    if (saved) {
      try {
        const parsed = JSON.parse(saved);

        return parsed;
      } catch (error) {
        // Erro silencioso
      }
    }

    // Estado inicial
    const initialState: SessionTimerState = {
      sessionId,
      totalElapsedTime: 0,
      questionTimes: {},
      currentQuestionId: null,
      sessionStartTime: Date.now(),
      lastSaveTime: Date.now()
    };


    return initialState;
  });

  const intervalRef = useRef<number>();
  const saveTimeoutRef = useRef<number>();
  const lastActivityRef = useRef<number>(Date.now());
  const isActiveRef = useRef<boolean>(true);
  const isManuallyPausedRef = useRef<boolean>(false); // 🔧 Flag para pausa manual

  // Salvar estado no localStorage
  const saveToLocalStorage = useCallback((newState: SessionTimerState) => {
    const storageKey = `${STORAGE_KEY_PREFIX}${sessionId}`;
    localStorage.setItem(storageKey, JSON.stringify(newState));
  }, [sessionId]);

  // Salvar tempos no banco de dados (estrutura JSON)
  const saveToDatabase = useCallback(async (currentState: SessionTimerState) => {
    try {
      // Converter questionTimes para formato JSON simples
      const questionTimesJson: Record<string, number> = {};
      Object.values(currentState.questionTimes).forEach(qt => {
        questionTimesJson[qt.questionId] = Math.floor(qt.elapsedTime);
      });

      // Atualizar sessão com tempos em JSON
      const { error } = await supabase
        .from('study_sessions')
        .update({
          total_time_spent: Math.floor(currentState.totalElapsedTime),
          question_times: questionTimesJson,
          last_activity: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) {
        // Error saving times to database
      }

    } catch (error) {
      // Error saving to database
    }
  }, [sessionId]);

  // Carregar tempos salvos do banco (estrutura JSON)
  const loadFromDatabase = useCallback(async () => {
    try {
      const { data: session, error } = await supabase
        .from('study_sessions')
        .select('question_times, total_time_spent, session_start_time')
        .eq('id', sessionId)
        .single();

      if (error) {
        return;
      }

      if (session && session.question_times) {
        const loadedTimes: Record<string, QuestionTime> = {};
        let totalTime = 0;

        // Converter JSON para estrutura interna
        Object.entries(session.question_times as Record<string, number>).forEach(([questionId, timeSpent]) => {
          loadedTimes[questionId] = {
            questionId,
            startTime: Date.now(),
            elapsedTime: timeSpent || 0,
            isActive: false,
            lastUpdate: Date.now()
          };
          totalTime += timeSpent || 0;
        });

        setState(prev => ({
          ...prev,
          questionTimes: { ...prev.questionTimes, ...loadedTimes },
          totalElapsedTime: Math.max(prev.totalElapsedTime, session.total_time_spent || totalTime),
          sessionStartTime: session.session_start_time ? new Date(session.session_start_time).getTime() : prev.sessionStartTime
        }));


      }
    } catch (error) {
      console.error('❌ [useSessionTimer] Erro ao carregar do banco:', error);
    }
  }, [sessionId]);

  // Detectar mudanças de visibilidade da página
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;

      // 🔧 Só alterar isActiveRef se não estivermos pausados manualmente
      if (!isManuallyPausedRef.current) {
        isActiveRef.current = isVisible;
        console.log(`👁️ [Timer] Visibilidade mudou - isVisible: ${isVisible}, timer ativo: ${isActiveRef.current}`);
      } else {
        console.log(`👁️ [Timer] Visibilidade mudou mas timer está pausado manualmente`);
      }

      lastActivityRef.current = Date.now();

      if (isVisible) {

        // Recalcular tempos baseado no tempo real decorrido
        setState(prev => {
          const now = Date.now();
          const updatedQuestionTimes = { ...prev.questionTimes };

          Object.keys(updatedQuestionTimes).forEach(questionId => {
            const qt = updatedQuestionTimes[questionId];
            if (qt.isActive) {
              const realElapsed = Math.floor((now - qt.lastUpdate) / 1000);
              qt.elapsedTime += realElapsed;
              qt.lastUpdate = now;
            }
          });

          const newState = {
            ...prev,
            questionTimes: updatedQuestionTimes
          };

          saveToLocalStorage(newState);
          return newState;
        });
      } else {

        // Salvar estado atual
        setState(prev => {
          saveToLocalStorage(prev);
          return prev;
        });
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [saveToLocalStorage]);

  // Carregar dados do banco na inicialização
  useEffect(() => {
    loadFromDatabase();
  }, [loadFromDatabase]);

  // Timer principal
  useEffect(() => {
    intervalRef.current = window.setInterval(() => {
      if (!isActiveRef.current) {
        return;
      }

      const now = Date.now();
      const timeSinceLastActivity = now - lastActivityRef.current;

      // Pausar se usuário inativo por muito tempo
      if (timeSinceLastActivity > INACTIVITY_THRESHOLD) {

        return;
      }

      setState(prev => {
        const updatedQuestionTimes = { ...prev.questionTimes };
        let totalElapsed = 0;

        // Atualizar tempo da questão ativa
        Object.keys(updatedQuestionTimes).forEach(questionId => {
          const qt = updatedQuestionTimes[questionId];
          if (qt.isActive) {
            qt.elapsedTime += 1;
            qt.lastUpdate = now;
            // Log apenas a cada 30 segundos para não poluir
            if (qt.elapsedTime % 30 === 0) {
              console.log(`⏱️ [Timer] Tempo da questão ${questionId}: ${qt.elapsedTime}s`);
            }
          }
          totalElapsed += qt.elapsedTime;
        });

        const newState = {
          ...prev,
          questionTimes: updatedQuestionTimes,
          totalElapsedTime: totalElapsed
        };

        // Salvar no localStorage a cada update
        saveToLocalStorage(newState);

        // Salvar no banco periodicamente
        if (now - prev.lastSaveTime > SAVE_INTERVAL) {
          saveToDatabase(newState);
          newState.lastSaveTime = now;
        }

        return newState;
      });

      lastActivityRef.current = now;
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [saveToLocalStorage, saveToDatabase]);

  // Iniciar timer para uma questão
  const startQuestionTimer = useCallback((questionId: string) => {
    console.log(`⏱️ [Timer] Iniciando timer para questão: ${questionId}`);

    // ▶️ Ativar timer global e remover pausa manual
    isActiveRef.current = true;
    isManuallyPausedRef.current = false;

    setState(prev => {
      const updatedQuestionTimes = { ...prev.questionTimes };

      // Pausar questão anterior
      Object.keys(updatedQuestionTimes).forEach(id => {
        updatedQuestionTimes[id].isActive = false;
      });

      // Iniciar nova questão ou retomar existente
      if (!updatedQuestionTimes[questionId]) {
        updatedQuestionTimes[questionId] = {
          questionId,
          startTime: Date.now(),
          elapsedTime: 0,
          isActive: true,
          lastUpdate: Date.now()
        };
      } else {
        updatedQuestionTimes[questionId].isActive = true;
        updatedQuestionTimes[questionId].lastUpdate = Date.now();
      }

      const newState = {
        ...prev,
        questionTimes: updatedQuestionTimes,
        currentQuestionId: questionId
      };

      saveToLocalStorage(newState);
      return newState;
    });

    lastActivityRef.current = Date.now();
    console.log(`✅ [Timer] Timer ativo: ${isActiveRef.current}`);
  }, [saveToLocalStorage]);

  // Pausar timer da questão atual
  const pauseCurrentTimer = useCallback(() => {
    console.log(`⏸️ [Timer] Pausando timer`);

    // ⏸️ Pausar timer global e marcar como pausa manual
    isActiveRef.current = false;
    isManuallyPausedRef.current = true;

    setState(prev => {
      const updatedQuestionTimes = { ...prev.questionTimes };

      Object.keys(updatedQuestionTimes).forEach(id => {
        updatedQuestionTimes[id].isActive = false;
      });

      const newState = {
        ...prev,
        questionTimes: updatedQuestionTimes,
        currentQuestionId: null
      };

      saveToLocalStorage(newState);
      return newState;
    });

    console.log(`⏸️ [Timer] Timer pausado: ${isActiveRef.current}`);
  }, [saveToLocalStorage]);

  // Obter tempo de uma questão específica
  const getQuestionTime = useCallback((questionId: string): number => {
    return state.questionTimes[questionId]?.elapsedTime || 0;
  }, [state.questionTimes]);

  // Finalizar sessão e salvar tudo
  const finishSession = useCallback(async () => {


    // Pausar todos os timers
    pauseCurrentTimer();

    // Salvar final no banco
    await saveToDatabase(state);

    // Limpar localStorage
    const storageKey = `${STORAGE_KEY_PREFIX}${sessionId}`;
    localStorage.removeItem(storageKey);


  }, [sessionId, pauseCurrentTimer, saveToDatabase, state]);

  return {
    totalElapsedTime: state.totalElapsedTime,
    currentQuestionTime: state.currentQuestionId ? getQuestionTime(state.currentQuestionId) : 0,
    startQuestionTimer,
    pauseCurrentTimer,
    getQuestionTime,
    finishSession,
    isActive: isActiveRef.current
  };
};
