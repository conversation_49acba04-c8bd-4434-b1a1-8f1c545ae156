
import React from "react";
import { Toolt<PERSON><PERSON>rovider } from "@/components/ui/tooltip";
import { FeedbackDialogProvider } from "@/components/ui/feedback-dialog";
import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import ProtectedRoute from "@/components/ProtectedRoute";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { initSentry, setUserContext, clearUserContext } from "@/utils/sentry";
import { analytics } from "@/utils/analytics";
import ScrollToTop from "@/components/ScrollToTop";
// import { monitoring } from "@/utils/monitoring";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Study from "@/pages/study";
import Results from "@/pages/Results";
import Progress from "@/pages/Progress";
import Questions from "@/pages/Questions";
import Flashcards from "@/pages/Flashcards";
import { useA<PERSON>, AuthProvider } from "@/contexts/AuthContext";
import { DarkModeProvider, useDarkMode } from "@/contexts/DarkModeContext";
import { RefreshWarningProvider } from "@/contexts/RefreshWarningContext";
import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { SessionContextProvider } from "@supabase/auth-helpers-react";
import QuestionList from "@/components/QuestionList";
import FocusConsolidation from "@/components/admin/FocusConsolidation";
import FocusNameImprovement from "@/pages/admin/FocusNameImprovement";
import FocusConsolidationByYear from "@/pages/admin/FocusConsolidationByYear";
import FocusOptimization from "@/pages/admin/FocusOptimization";
import QuestionCategorization from "@/pages/admin/QuestionCategorization";
import CategoryManagement from "@/pages/admin/CategoryManagement";
import PrevalenceAnalysisPage from "@/pages/admin/prevalence";
import StudyPreferences from "@/pages/StudyPreferences";
import HowItWorks from './pages/HowItWorks';
import Terms from './pages/Terms';
import Privacy from './pages/Privacy';
import Login from './pages/Login';
import Onboarding from "./pages/Onboarding";
import Settings from "./pages/Settings";
import RestrictedAccessPage from "./pages/RestrictedAccess";
import CollaborativeFlashcards from "@/pages/CollaborativeFlashcards";
import CollaborateFlashcards from "@/pages/CollaborateFlashcards";
import Ranking from "@/pages/Ranking";
import QuestionRanking from "@/pages/QuestionRanking";
import StreakRanking from "@/pages/StreakRanking";
import FlashcardsRanking from "@/pages/FlashcardsRanking";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import { useUserData } from "@/hooks/useUserData";
import UsersManagement from "@/pages/admin/UsersManagement";
import QuestionHierarchyImprovement from "@/pages/admin/QuestionHierarchyImprovement";
import Schedule from "@/pages/Schedule";
import StudyFlow from "@/pages/StudyFlow";
import FeedbackSupport from "@/pages/FeedbackSupport";
import ErrorNotebook from "@/pages/ErrorNotebook";
import DrWill from "@/pages/DrWill";

import OAuthCallback from "@/pages/auth/OAuthCallback";
import { NavigationLockProvider } from "@/contexts/NavigationLockContext";
import { NavigationLockIndicator } from "@/components/NavigationLockIndicator";
import { AppStabilizer } from "@/components/AppStabilizer";
import { CurrentQuestionProvider } from "@/contexts/CurrentQuestionContext";
import { SessionFlashcardsProvider } from "@/contexts/SessionFlashcardsContext";
import { logDomainConfig, validateCurrentDomain } from "@/utils/domainConfig";
import { applyConsoleFilters } from "@/utils/consoleFilters";
import { initializeSupabaseQueryTracker, generateQueryReport, clearQueryLogs } from "@/utils/supabaseQueryTracker";
import WelcomeScreen from "@/components/WelcomeScreen";
import { useWelcomeFlow } from "@/hooks/useWelcomeFlow";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import LoadingErrorRecovery from "@/components/common/LoadingErrorRecovery";
import EmergencyFallback from "@/components/common/EmergencyFallback";
import { ReferralDetector } from "@/components/ReferralDetector";
import { FloatingChatButton } from "@/components/FloatingChatButton";
import { DrWillContextTutorial } from "@/components/tutorials/DrWillContextTutorial";
import { useDrWillContextTutorial } from "@/hooks/useDrWillContextTutorial";
import { DarkModeSync } from "@/components/DarkModeSync";

// Componente simplificado para otimizações essenciais
function OptimizedAppProvider() {
  // Aplicar filtros de console e validações apenas uma vez
  React.useEffect(() => {
    // Evitar reinicialização desnecessária
    if (window.__optimizationsInitialized) {
      return;
    }



    // Aplicar filtros para suprimir warnings desnecessários
    applyConsoleFilters();

    // Validar domínio apenas em desenvolvimento
    if (import.meta.env.DEV) {
      logDomainConfig();
      if (!validateCurrentDomain()) {
        // Domain validation warning disabled
      }

      // Inicializar rastreamento de queries do Supabase
      initializeSupabaseQueryTracker();

      // Adicionar funções globais para debug
      (window as any).generateQueryReport = generateQueryReport;
      (window as any).clearQueryLogs = clearQueryLogs;


    }

    window.__optimizationsInitialized = true;
  }, []);

  return null; // Componente invisível apenas para executar otimizações
}

// ✅ INICIALIZAR SISTEMAS DE MONITORAMENTO
initSentry();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache moderado para balance entre performance e dados frescos
      staleTime: 5 * 60 * 1000, // 5 minutos - mais conservador
      cacheTime: 15 * 60 * 1000, // 15 minutos - cache moderado
      refetchOnWindowFocus: false,
      refetchOnMount: true, // ✅ CORRIGIDO: Permitir refetch na montagem
      refetchOnReconnect: true, // ✅ CORRIGIDO: Permitir refetch na reconexão
      retry: 2, // Aumentado para 2 tentativas
      retryDelay: 1000,
      keepPreviousData: false, // ✅ CORRIGIDO: Não manter dados antigos por padrão
      networkMode: 'online',
    },
    mutations: {
      retry: 1, // Simplificado
      retryDelay: 1000, // Delay fixo
      networkMode: 'online',
    },
  },
  // Configuração de garbage collection mais agressiva
  queryCache: new QueryCache({
    onError: (error, query) => {
      // Error logging disabled for production
    },
  }),
  mutationCache: new MutationCache({
    onError: (error, variables, context, mutation) => {
      // Error logging disabled for production
    },
  }),
});

// Disponibilizar queryClient globalmente para otimizações de memória
(window as any).__queryClient = queryClient;

// 🔍 Inicializar sistema de logs para diagnóstico
import('@/utils/requestLogger').then(({ setupSupabaseLogger }) => {
  setupSupabaseLogger();
  // ✅ LIMPEZA: Log removido - sistema de logs ativado
});

// 🔧 Configurar handler para erros de chunk loading
import('@/utils/dynamicImportHelper').then(({ setupChunkErrorHandler }) => {
  setupChunkErrorHandler();
});

// Componente para monitorar rotas e desativar modo de estudo
function RouteMonitor() {
  const location = useLocation();

  useEffect(() => {
    const isInStudySession = location.pathname.startsWith('/questions/') ||
                            location.pathname.startsWith('/plataformadeestudos/questions/');

    // Se não estiver em sessão de estudo, forçar desativação
    if (!isInStudySession) {
      window.dispatchEvent(new CustomEvent('forceDeactivateStudyMode'));
    }
  }, [location.pathname]);

  return null;
}

function AppContent() {
  const { user, loading } = useAuth();
  const { isDarkMode } = useDarkMode();

  if (loading) {
    return <div className={`flex items-center justify-center min-h-screen transition-colors duration-200 ${
      isDarkMode ? 'bg-gray-900' : 'bg-[#FEF7CD]'
    }`}>
      <div className="text-center">
        <div className={`animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4 transition-colors duration-200 ${
          isDarkMode ? 'border-blue-400' : 'border-primary'
        }`}></div>
        <p className={`transition-colors duration-200 ${
          isDarkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>Carregando...</p>
      </div>
    </div>;
  }

  return (
    <SessionContextProvider supabaseClient={supabase}>
      <QueryClientProvider client={queryClient}>
        <AppRoutes />
      </QueryClientProvider>
    </SessionContextProvider>
  );
}

// Componente para controlar o botão flutuante
function FloatingChatController() {
  const location = useLocation();
  const { user } = useAuth();
  const { shouldShowTutorial, markTutorialAsCompleted, skipTutorial } = useDrWillContextTutorial();

  // Páginas onde o botão deve aparecer (todas as páginas protegidas)
  const showOnPages = [
    '/plataformadeestudos',
    '/progress',
    '/flashcards',
    '/collaborative',
    '/collaborate',
    '/schedule',
    '/settings',
    '/ranking',
    '/questions',
    '/results',
    '/feedback-suporte',
    '/caderno-erros'
  ];

  // Não mostrar na página do Dr. Will e páginas públicas
  const hideOnPages = [
    '/dr-will',
    '/',
    '/login',
    '/como-funciona',
    '/terms',
    '/privacy',
    '/onboarding',
    '/acesso-restrito',
    '/admin'
  ];

  // Mostrar se:
  // 1. Usuário está logado
  // 2. Está em uma página permitida OU não está em uma página proibida
  // 3. Não está em página de admin
  const shouldShow = user &&
    (showOnPages.some(page => location.pathname.startsWith(page)) ||
     (!hideOnPages.some(page => location.pathname.startsWith(page)) &&
      !location.pathname.startsWith('/admin')));

  // Verificar se está em uma sessão de questões para mostrar o tutorial
  const isInQuestionSession = location.pathname.startsWith('/questions/');
  const showTutorial = shouldShow && isInQuestionSession && shouldShowTutorial;

  if (!shouldShow) return null;

  return (
    <>
      <FloatingChatButton />
      <DrWillContextTutorial
        isVisible={showTutorial}
        onComplete={markTutorialAsCompleted}
        onSkip={skipTutorial}
      />
    </>
  );
}

// Novo componente que usa useUserData dentro do QueryClientProvider
const AppRoutes = React.memo(() => {
  const { user } = useAuth();



  const { hasCompletedOnboarding, isLoading: userDataLoading, userData, isPremium, profile } = useUserData();

  // Memoizar parâmetros do useWelcomeFlow para evitar re-criações
  const welcomeFlowParams = React.useMemo(() => ({
    profile,
    isPremium,
    isProfileLoading: userDataLoading
  }), [profile?.id, isPremium, userDataLoading]);

  const {
    showWelcome,
    isLoading: welcomeLoading,
    hasError: welcomeError,
    error: welcomeErrorMessage,
    retry: retryWelcome,
    retryCount,
    canRetry,
    grantPremiumAccess,
    userName
  } = useWelcomeFlow(welcomeFlowParams);

  // Memoizar cálculos derivados para evitar re-renderizações
  const hasReachedMaxRetries = React.useMemo(() =>
    retryCount >= 2 && welcomeError,
    [retryCount, welcomeError]
  );

  // ✅ CONFIGURAR CONTEXTO DO USUÁRIO PARA MONITORAMENTO (otimizado)
  React.useEffect(() => {
    // Evitar reconfiguração desnecessária
    if (!user?.id) {
      // Limpar contexto apenas se estava configurado
      if (window.__userContextConfigured) {
        clearUserContext();
        analytics.clearUser();
        window.__userContextConfigured = false;
      }
      return;
    }

    // Configurar apenas se temos dados completos e ainda não foi configurado para este usuário
    if (user && userData && window.__currentUserId !== user.id) {
      // Configurar Sentry
      setUserContext({
        id: user.id,
        email: user.email,
        formation_area: userData.formation_area,
        is_student: userData.is_student,
      });

      // Configurar Analytics
      analytics.setUser(user.id, {
        formation_area: userData.formation_area,
        is_student: userData.is_student,
        registration_date: userData.created_at,
      });

      // Marcar como configurado para este usuário específico
      window.__currentUserId = user.id;
      window.__userContextConfigured = true;
    }
  }, [user?.id, userData?.formation_area, userData?.is_student]);

  // Memoizar dados derivados
  const needsOnboarding = React.useMemo(() =>
    !hasCompletedOnboarding,
    [hasCompletedOnboarding]
  );

  // Mostrar tela de emergência se esgotou todas as tentativas
  if (hasReachedMaxRetries) {
    return (
      <EmergencyFallback
        title="Dados do Usuário Indisponíveis"
        message="Não foi possível carregar seus dados após várias tentativas. Você pode continuar com funcionalidades limitadas ou tentar recarregar completamente."
        onForceReload={() => {
          localStorage.clear();
          sessionStorage.clear();
          window.location.reload();
        }}
        onGoHome={() => window.location.href = '/'}
        onContactSupport={() => {
          const message = encodeURIComponent(
            "Olá! Não consigo carregar meus dados de usuário na plataforma MedEvo mesmo após várias tentativas."
          );
          window.open(`https://api.whatsapp.com/send?phone=5564993198433&text=${message}`, '_blank');
        }}
        showSupportOption={true}
      />
    );
  }

  // Mostrar tela de recovery normal se houver erro no welcome flow
  if (welcomeError) {
    return (
      <LoadingErrorRecovery
        title="Erro ao Carregar Dados"
        message={welcomeErrorMessage || "Não foi possível carregar seus dados de usuário. Verifique sua conexão."}
        onRetry={canRetry ? retryWelcome : undefined}
        onGoHome={() => window.location.href = '/'}
        onContinueOffline={() => {
          // Continuar com dados limitados
          window.location.href = '/plataformadeestudos';
        }}
        showOfflineOption={true}
        retryCount={retryCount}
        maxRetries={2}
      />
    );
  }

  if (userDataLoading || welcomeLoading) {
    return <div className="flex items-center justify-center min-h-screen bg-[#FEF7CD]">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600">Carregando dados do usuário...</p>
      </div>
    </div>;
  }

  // ✅ Mostrar tela de boas-vindas para novos usuários
  if (user && showWelcome) {
    const handleGetPremiumAccess = async () => {
      const success = await grantPremiumAccess();
      if (success) {
        // Redirecionar para plataforma de estudos após ativar premium
        window.location.href = '/plataformadeestudos';
      }
    };

    return (
      <WelcomeScreen
        onGetPremiumAccess={handleGetPremiumAccess}
        userName={userName}
      />
    );
  }

  return (
        <TooltipProvider>
          <FeedbackDialogProvider>
            <RefreshWarningProvider>
              <NavigationLockProvider>
                <CurrentQuestionProvider>
                  <SessionFlashcardsProvider>
                    <NavigationLockIndicator />
                <Router
                  future={{
                    v7_startTransition: true,
                    v7_relativeSplatPath: true
                  }}
                >
                  <AppStabilizer />
                  <OptimizedAppProvider />
                  <ReferralDetector />
                  <RouteMonitor />
                  <ScrollToTop />
                  <DarkModeSync />
                  <FloatingChatController />
            <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/auth/callback" element={<OAuthCallback />} />
            <Route path="/como-funciona" element={<HowItWorks />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/onboarding" element={<Onboarding />} />
            <Route path="/study-preferences" element={<StudyPreferences />} />
            <Route path="/acesso-restrito" element={<RestrictedAccessPage />} />
            <Route
              path="/plataformadeestudos"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Study />
                </ProtectedRoute>
              }
            />
            <Route
              path="/questions"
              element={
                <ProtectedRoute requireAccess={true}>
                  <QuestionList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/progress"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Progress />
                </ProtectedRoute>
              }
            />
            <Route
              path="/flashcards/*"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Flashcards />
                </ProtectedRoute>
              }
            />
             <Route
              path="/collaborative/flashcards"
              element={
                <ProtectedRoute requireAccess={true}>
                  <CollaborativeFlashcards />
                </ProtectedRoute>
              }
            />
             <Route
              path="/collaborate/flashcards"
              element={
                <ProtectedRoute requireAccess={true}>
                  <CollaborateFlashcards />
                </ProtectedRoute>
              }
            />
            <Route
              path="/settings"
              element={
                <ProtectedRoute>
                  <Settings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/schedule"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Schedule />
                </ProtectedRoute>
              }
            />
            <Route
              path="/studyflow"
              element={
                <ProtectedRoute requireAccess={true}>
                  <StudyFlow />
                </ProtectedRoute>
              }
            />
            <Route
              path="/feedback-suporte"
              element={
                <ProtectedRoute requireAccess={true}>
                  <FeedbackSupport />
                </ProtectedRoute>
              }
            />
            <Route
              path="/caderno-erros"
              element={
                <ProtectedRoute requireAccess={true}>
                  <ErrorNotebook />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dr-will"
              element={
                <ProtectedRoute requireAccess={true}>
                  <DrWill />
                </ProtectedRoute>
              }
            />

            {/* Admin Routes */}
            <Route
              path="/admin"
              element={
                <ProtectedRoute>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/focus-name-improvement"
              element={
                <ProtectedRoute>
                  <FocusNameImprovement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/focus-consolidation-by-year"
              element={
                <ProtectedRoute>
                  <FocusConsolidationByYear />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/focus-optimization"
              element={
                <ProtectedRoute>
                  <FocusOptimization />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/question-categorization"
              element={
                <ProtectedRoute>
                  <QuestionCategorization />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/category-management"
              element={
                <ProtectedRoute>
                  <CategoryManagement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/focus-consolidation"
              element={
                <ProtectedRoute>
                  <FocusConsolidation />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/users"
              element={
                <ProtectedRoute>
                  <UsersManagement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/question-hierarchy"
              element={
                <ProtectedRoute>
                  <QuestionHierarchyImprovement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/prevalence"
              element={
                <ProtectedRoute>
                  <PrevalenceAnalysisPage />
                </ProtectedRoute>
              }
            />

            <Route
              path="/focus-consolidation"
              element={
                <ProtectedRoute>
                  <FocusConsolidation />
                </ProtectedRoute>
              }
            />
            <Route
              path="/questions/:sessionId"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Questions />
                </ProtectedRoute>
              }
            />
            <Route
              path="/results/:sessionId"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Results />
                </ProtectedRoute>
              }
            />
            <Route
              path="/results"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Study />
                </ProtectedRoute>
              }
            />
            <Route
              path="/ranking"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Ranking />
                </ProtectedRoute>
              }
            />
            <Route
              path="/ranking/questions"
              element={
                <ProtectedRoute requireAccess={true}>
                  <QuestionRanking />
                </ProtectedRoute>
              }
            />
            <Route
              path="/ranking/streak"
              element={
                <ProtectedRoute requireAccess={true}>
                  <StreakRanking />
                </ProtectedRoute>
              }
            />
            <Route
              path="/ranking/flashcards"
              element={
                <ProtectedRoute requireAccess={true}>
                  <FlashcardsRanking />
                </ProtectedRoute>
              }
            />

            {/* Wildcard route para qualquer caminho que não corresponda aos definidos acima */}
            <Route path="*" element={<NotFound />} />
                </Routes>
                </Router>
                  </SessionFlashcardsProvider>
                </CurrentQuestionProvider>
              </NavigationLockProvider>
          </RefreshWarningProvider>
          </FeedbackDialogProvider>
        </TooltipProvider>
  );
});

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <DarkModeProvider>
          <AppContent />
        </DarkModeProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
