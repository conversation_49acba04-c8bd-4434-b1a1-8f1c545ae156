
import { Button } from "@/components/ui/button";
import { KeyRound, ExternalLink, Info } from "lucide-react";

interface ResetPasswordFormProps {
  onBackToLogin: () => void;
}

const ResetPasswordForm = ({ onBackToLogin }: ResetPasswordFormProps) => {

  return (
    <div className="flex flex-col items-center justify-center space-y-6 py-6 px-4">
      {/* Aviso sobre PedBook - Centralizado */}
      <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-6 w-full max-w-md text-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Info className="h-6 w-6 text-blue-600" />
          </div>

          <div className="space-y-3">
            <h3 className="font-bold text-blue-800 text-lg">Recuperação de Senha</h3>
            <p className="text-blue-700 text-sm leading-relaxed">
              Para recuperar sua senha, você deve fazer isso no site oficial do <strong>PedBook</strong>.
              Após redefinir sua senha lá, você poderá usar a nova senha aqui na <strong>medEVO</strong>.
            </p>
          </div>

          <Button
            type="button"
            onClick={() => window.open('https://pedb.com.br', '_blank')}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white border-2 border-blue-800 rounded-md font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all flex items-center justify-center gap-2 py-3"
          >
            <ExternalLink className="h-4 w-4" />
            Ir para PedBook
          </Button>
        </div>
      </div>

      {/* Botão de voltar - Centralizado */}
      <div className="w-full max-w-md">
        <Button
          type="button"
          variant="outline"
          className="w-full flex items-center justify-center gap-2 bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black rounded-md font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all py-3"
          onClick={onBackToLogin}
        >
          <KeyRound className="h-4 w-4" />
          Voltar para o login
        </Button>
      </div>
    </div>
  );
};

export default ResetPasswordForm;
