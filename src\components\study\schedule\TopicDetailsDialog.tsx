
import React, { useState, lazy, Suspense } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogAction } from '@/components/ui/alert-dialog';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, BookOpen, Check, Calendar, Trash2, CheckCircle, X, BookOpenCheck } from 'lucide-react';
import type { StudyTopic } from '@/types/study-schedule';
import { useStudyTopics } from '@/hooks/study-schedule/useStudyTopics';
import { useFeedbackDialog } from '@/components/ui/feedback-dialog';
import { ConfirmStudyDialog } from '@/components/progress/ConfirmStudyDialog';
import { useStudySession } from '@/hooks/useStudySession';
import { useUser } from '@supabase/auth-helpers-react';
import { useDomain } from '@/hooks/useDomain';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
// Cache local para questões (igual aos outros componentes)
const TOPIC_CACHE = new Map<string, { count: number; data: any[]; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

const getCachedTopicData = (cacheKey: string) => {
  const cached = TOPIC_CACHE.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached;
  }
  return null;
};

const setCachedTopicData = (cacheKey: string, data: { count: number; data: any[] }) => {
  TOPIC_CACHE.set(cacheKey, { ...data, timestamp: Date.now() });
};

// Lazy load do StudyOptionsDialog para otimização
const StudyOptionsDialog = lazy(() => import('@/components/study/StudyOptionsDialog').then(module => ({ default: module.StudyOptionsDialog })));

interface TopicDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  topic: StudyTopic;
  onMarkStudied?: (topicId: string) => void; // ✅ Manter para compatibilidade, mas não usar
  onDelete?: (topic: StudyTopic) => void;
  onRefresh?: () => void; // ✅ NOVO: Callback para atualizar lista após marcar como estudado
}

export const TopicDetailsDialog: React.FC<TopicDetailsDialogProps> = ({
  open,
  onOpenChange,
  topic,
  onMarkStudied, // ✅ Não usar mais
  onDelete,
  onRefresh
}) => {
  // ✅ HOOKS
  const { markTopicAsStudied, createWeeksAndMarkStudied } = useStudyTopics();
  const { showFeedback } = useFeedbackDialog();
  const { createSession } = useStudySession();
  const user = useUser();
  const { domain } = useDomain();
  const navigate = useNavigate();
  const { toast } = useToast();

  // ✅ ESTADOS PARA MARCAR COMO ESTUDADO
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedTopicName, setSelectedTopicName] = useState<string>("");

  // ✅ NOVOS ESTADOS PARA PRATICAR QUESTÕES
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [maxQuestions, setMaxQuestions] = useState(0);
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [availableInstitutions, setAvailableInstitutions] = useState<{ id: string; name: string }[]>([]);
  const [allTopicsData, setAllTopicsData] = useState<Array<{
    topic: any;
    questionCount: number;
    questions: any[];
  }>>([]);

  // ✅ Estados para dialog de sucesso
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [studySuccessMessage, setStudySuccessMessage] = useState("");
  const [nextRevisionInfo, setNextRevisionInfo] = useState<{
    date: string;
    dayOfWeek: string;
    revisionNumber: number;
    daysUntil: number;
  } | null>(null);
  const [isLastRevision, setIsLastRevision] = useState(false);

  // ✅ NOVOS ESTADOS para diálogo de criação de semanas (igual TodayStudies)
  const [createWeeksDialogOpen, setCreateWeeksDialogOpen] = useState(false);
  const [weeksToCreate, setWeeksToCreate] = useState(0);
  const [revisionDateFormatted, setRevisionDateFormatted] = useState("");
  const [pendingTopicId, setPendingTopicId] = useState<string | null>(null);
  const [pendingRevisionNumber, setPendingRevisionNumber] = useState(0);
  // Format next revision date
  const formatNextRevisionDate = (date?: string) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('pt-BR');
  };
  
  // Format time
  const formatTime = (time: string) => {
    if (!time) return 'Horário não definido';
    
    if (time.includes('AM') || time.includes('PM') || time.includes(':')) {
      return time;
    }
    
    try {
      const timeDate = new Date(`2000-01-01T${time}`);
      return timeDate.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } catch (error) {
      return time;
    }
  };
  
  // Get title with improved handling
  const getTitle = () => {
    // If focus exists and is not "Geral", "N/A", or empty, use that
    if (topic.focus && topic.focus !== "Geral" && topic.focus !== "N/A" && topic.focus !== "") {
      return topic.focus;
    }
    // Otherwise, if theme exists and is not empty, use that
    else if (topic.theme && topic.theme !== "") {
      return topic.theme;
    }
    // Finally, if specialty exists, use that
    else if (topic.specialty && topic.specialty !== "") {
      return topic.specialty;
    }
    // Default fallback
    else {
      return "Estudo";
    }
  };
  
  // Get difficulty badge color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Fácil': return 'bg-green-100 text-green-700';
      case 'Médio': return 'bg-yellow-100 text-yellow-700';
      case 'Difícil': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  // ✅ FUNÇÃO IGUAL TODAYSTUDIES: Apenas abrir diálogo de confirmação
  const handleMarkAsStudied = (topicId: string, topicName?: string) => {
    // ✅ Fechar dialog principal primeiro para evitar sobreposição
    onOpenChange(false);

    setSelectedTopicId(topicId);
    setSelectedTopicName(topicName || "este tópico");
    setConfirmDialogOpen(true);
  };

  // ✅ FUNÇÃO IGUAL TODAYSTUDIES: Confirmar estudo após diálogo
  const handleConfirmStudied = async () => {
    if (selectedTopicId) {
      setConfirmDialogOpen(false);
      const result = await markTopicAsStudied(selectedTopicId);

      // ✅ NOVO: Verificar se precisa criar semanas (igual TodayStudies)
      if (result && result.needsWeeks) {
        // Configurar diálogo de criação de semanas
        setWeeksToCreate(result.weeksNeeded || 1);
        setRevisionDateFormatted(result.revisionDate || "");
        setPendingTopicId(result.topicId || selectedTopicId);
        setPendingRevisionNumber(result.revisionNumber || 0);
        setCreateWeeksDialogOpen(true);
        return;
      }

      if (result && result.success) {
        // ✅ Atualizar lista se callback disponível
        if (onRefresh) {
          onRefresh();
        }

        // ✅ Configurar dialog de sucesso
        if (result.isLastRevision) {
          setIsLastRevision(true);
          setStudySuccessMessage(result.message);
        } else if (result.nextRevision) {
          setIsLastRevision(false);
          setNextRevisionInfo(result.nextRevision);
          setStudySuccessMessage(`O tópico foi marcado como estudado com sucesso!`);
        } else {
          setIsLastRevision(false);
          setNextRevisionInfo(null);
          setStudySuccessMessage(result.message);
        }
        setSuccessDialogOpen(true);
      } else {
        showFeedback("error", "Erro", result?.message || "Ocorreu um erro ao marcar o tópico como estudado.");
      }
    }
  };

  // ✅ NOVAS FUNÇÕES para diálogo de criação de semanas (igual TodayStudies)
  const handleConfirmCreateWeeks = async () => {
    if (pendingTopicId) {
      setCreateWeeksDialogOpen(false);

      try {
        const result = await createWeeksAndMarkStudied(
          pendingTopicId,
          weeksToCreate,
          pendingRevisionNumber
        );

        if (result && result.success) {
          // ✅ Atualizar lista se callback disponível
          if (onRefresh) {
            onRefresh();
          }

          // ✅ Configurar dialog de sucesso
          if (result.isLastRevision) {
            setIsLastRevision(true);
            setStudySuccessMessage(result.message);
          } else if (result.nextRevision) {
            setIsLastRevision(false);
            setNextRevisionInfo(result.nextRevision);
            setStudySuccessMessage(`O tópico foi marcado como estudado com sucesso!`);
          } else {
            setIsLastRevision(false);
            setNextRevisionInfo(null);
            setStudySuccessMessage(result.message);
          }
          setSuccessDialogOpen(true);
        } else {
          showFeedback("error", "Erro", result?.message || "Erro ao criar semanas e marcar tópico como estudado.");
        }
      } catch (error) {
        showFeedback("error", "Erro", "Erro inesperado ao criar semanas.");
      }
    }
  };

  const handleCancelCreateWeeks = () => {
    setCreateWeeksDialogOpen(false);
    // Limpar estados
    setPendingTopicId(null);
    setWeeksToCreate(0);
    setRevisionDateFormatted("");
    setPendingRevisionNumber(0);
  };

  // ✅ NOVA FUNÇÃO: Praticar questões (baseada no TodayStudies)
  const handlePracticeQuestions = async () => {
    if (!topic.specialtyId && !topic.themeId && !topic.focusId) {
      showFeedback("error", "Erro", "Este tópico não possui informações suficientes para buscar questões.");
      return;
    }

    try {
      setLoadingQuestions(true);

      // Cache key baseado nos IDs do tópico
      const cacheKey = `${topic.specialtyId}-${topic.themeId}-${topic.focusId}-${domain}-${user?.id}-initial`;
      const cachedData = getCachedTopicData(cacheKey);

      let result;
      if (cachedData) {
        result = cachedData;
      } else {
        const { getQuestionsOptimized } = await import('@/utils/questionUtils');
        result = await getQuestionsOptimized(
          topic.specialtyId,
          topic.themeId,
          topic.focusId,
          1000, // Limite alto para contar todas
          undefined,
          undefined, // Sem filtro de instituições aqui
          false,
          domain,
          false, // hideAnswered será aplicado depois no dialog
          user?.id,
          true // Retornar apenas IDs
        );
        setCachedTopicData(cacheKey, result);
      }

      if (result.count === 0) {
        showFeedback("info", "Sem questões", "Não foram encontradas questões para este tópico.");
        return;
      }

      // Configurar dados para o StudyOptionsDialog
      setMaxQuestions(result.count);
      setAllTopicsData([{
        topic: {
          ...topic,
          specialtyId: topic.specialtyId,
          themeId: topic.themeId,
          focusId: topic.focusId
        },
        questionCount: result.count,
        questions: result.data
      }]);

      // Configurar anos e instituições disponíveis (valores padrão)
      setAvailableYears([]);
      setAvailableInstitutions([]);

      // Fechar dialog principal e abrir dialog de opções
      onOpenChange(false);
      setDialogOpen(true);

    } catch (error) {
      console.error('Erro ao buscar questões:', error);
      showFeedback("error", "Erro", "Ocorreu um erro ao buscar questões para este tópico.");
    } finally {
      setLoadingQuestions(false);
    }
  };

  // ✅ FUNÇÃO: Iniciar sessão de estudo (baseada no TodayStudies)
  const handleStartStudy = async (quantity: number, hideAnswered?: boolean, institutionIds?: string[]) => {
    console.log('🎯 [TopicDetailsDialog] Iniciando sessão de estudo:', {
      quantity,
      hideAnswered,
      institutionIds,
      allTopicsData: allTopicsData.length,
      userId: user?.id
    });

    if (!user?.id || allTopicsData.length === 0) {
      showFeedback("error", "Erro", "Usuário não autenticado ou dados não disponíveis.");
      return;
    }

    try {
      // Para um único tópico, criar sessão diretamente
      if (allTopicsData.length === 1) {
        const { topic } = allTopicsData[0];

        console.log('🎯 [TopicDetailsDialog] Criando sessão para tópico único:', topic);

        const { getQuestionsOptimized } = await import('@/utils/questionUtils');
        const result = await getQuestionsOptimized(
          topic.specialtyId,
          topic.themeId,
          topic.focusId,
          quantity, // Usar quantity diretamente
          undefined,
          institutionIds,
          false,
          domain,
          hideAnswered,
          user.id,
          false // Retornar dados completos, não apenas IDs
        );

        if (!result.data || result.data.length === 0) {
          throw new Error('Nenhuma questão encontrada com os filtros aplicados');
        }

        const title = getTitle();
        const questionIds = result.data.map((q: any) => typeof q === 'string' ? q : q.id);

        console.log('🎯 [TopicDetailsDialog] Criando sessão:', {
          title,
          questionCount: questionIds.length,
          questionIds: questionIds.slice(0, 3) // Log apenas os primeiros 3 IDs
        });

        const session = await createSession(user.id, questionIds, title);
        if (session) {
          setDialogOpen(false);
          navigate(`/questions/${session.id}`);

          toast({
            title: "Sessão iniciada!",
            description: `Estudando ${questionIds.length} questões sobre ${title}`,
          });
        } else {
          throw new Error('Erro ao criar sessão de estudo');
        }
        return;
      }

      // Lógica para múltiplos tópicos (se necessário no futuro)
      console.log('🎯 [TopicDetailsDialog] Múltiplos tópicos não implementado ainda');
      showFeedback("error", "Erro", "Múltiplos tópicos não suportado ainda.");

    } catch (error) {
      console.error('❌ [TopicDetailsDialog] Erro ao iniciar sessão de estudo:', error);
      showFeedback("error", "Erro", "Ocorreu um erro ao iniciar a sessão de estudo.");
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(topic);
      onOpenChange(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-lg sm:max-w-xl p-0 overflow-hidden border-0"
        aria-describedby="topic-details-description"
        hideCloseButton={true}
      >
        {/* Background with design elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-blue-50 rounded-xl"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-300 rounded-full blur-3xl opacity-10 -mr-10 -mt-10"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-green-400 rounded-full blur-3xl opacity-10 -ml-20 -mb-20"></div>

        <div className="relative z-10 p-6">
          <DialogHeader className="pb-2 pr-0">
            <div className="flex items-start gap-3 mb-1">
              <div className="p-2 bg-blue-100 rounded-full flex-shrink-0">
                <BookOpen className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <DialogTitle className="text-lg sm:text-xl font-bold text-slate-800 leading-tight pr-2">
                  {getTitle()}
                </DialogTitle>
              </div>
              {/* Botão de fechar estilizado e funcional */}
              <button
                onClick={() => onOpenChange(false)}
                className="flex-shrink-0 p-1.5 rounded-full hover:bg-red-50 hover:text-red-600 transition-all duration-200 text-gray-400 hover:scale-110"
                aria-label="Fechar dialog"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Descrição oculta para acessibilidade */}
            <DialogDescription id="topic-details-description" className="sr-only">
              Detalhes do tópico de estudo: {getTitle()}. Especialidade: {topic.specialty}, Tema: {topic.theme}.
            </DialogDescription>
            
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge className={`${getDifficultyColor(topic.difficulty)}`}>
                {topic.difficulty}
              </Badge>
              
              {topic.study_status === 'completed' && (
                <Badge className="bg-green-100 text-green-700">
                  <Check className="mr-1 h-3 w-3" />
                  Estudado
                </Badge>
              )}
              
              {topic.next_revision_date && (
                <Badge className="bg-purple-100 text-purple-700">
                  <Calendar className="mr-1 h-3 w-3" />
                  Próxima revisão: {formatNextRevisionDate(topic.next_revision_date)}
                </Badge>
              )}
            </div>
            
            <div className="text-slate-600 mt-3 space-y-1">
              {topic.specialty && <div className="font-medium">Especialidade: {topic.specialty}</div>}
              {topic.theme && topic.theme !== "Geral" && (
                <div className="font-medium">Tema: {topic.theme}</div>
              )}
              {topic.focus && topic.focus !== "Geral" && topic.focus !== "N/A" && (
                <div className="font-medium">Foco: {topic.focus}</div>
              )}
            </div>
          </DialogHeader>
          
          <div className="mt-6 space-y-4">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 space-y-3">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-600" />
                <div className="text-sm font-medium text-gray-700">
                  Horário: {formatTime(topic.startTime)}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-600" />
                <div className="text-sm font-medium text-gray-700">
                  Duração: {topic.duration}
                </div>
              </div>
              
              <div className="pt-3 border-t border-blue-200">
                <div className="text-sm font-semibold text-blue-700 mb-2 flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Atividade:
                </div>
                <div className="text-sm text-slate-700 bg-white/60 p-3 rounded-lg border border-blue-100">
                  {topic.activity}
                </div>
              </div>
            </div>
            
            <DialogFooter className="mt-6 pt-4 border-t border-blue-200">
              {/* Layout responsivo: coluna no mobile, linha no desktop */}
              <div className="flex flex-col gap-3 w-full">

                {/* Linha principal de ações */}
                <div className="flex flex-col sm:flex-row gap-2 w-full">

                  {/* Botão Praticar - apenas para tópicos da plataforma */}
                  {!topic.is_manual && (topic.specialtyId || topic.themeId || topic.focusId) && (
                    <Button
                      onClick={handlePracticeQuestions}
                      disabled={loadingQuestions}
                      className="flex-1 bg-hackathon-yellow hover:bg-hackathon-yellow/90 text-black border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all duration-200 font-bold h-11"
                    >
                      {loadingQuestions ? (
                        <span className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                          Carregando...
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <BookOpenCheck className="h-4 w-4" />
                          Praticar
                        </span>
                      )}
                    </Button>
                  )}

                  {/* Botão Marcar como estudado */}
                  {topic.study_status !== 'completed' && (
                    <Button
                      onClick={() => handleMarkAsStudied(topic.id, `${topic.specialty} > ${topic.theme} > ${topic.focus}`)}
                      className="flex-1 bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white border-2 border-emerald-600 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all duration-200 font-bold h-11"
                    >
                      <span className="flex items-center gap-2">
                        <Check className="h-4 w-4" />
                        Marcar como estudado
                      </span>
                    </Button>
                  )}
                </div>

                {/* Linha secundária de ações */}
                <div className="flex flex-col sm:flex-row gap-2 w-full">

                  {/* Botão Remover - apenas se onDelete existir */}
                  {onDelete && (
                    <Button
                      variant="outline"
                      onClick={handleDelete}
                      className="flex-1 text-rose-600 border-rose-200 hover:bg-rose-50 hover:border-rose-300 transition-all duration-200 font-medium h-10"
                    >
                      <span className="flex items-center gap-2">
                        <Trash2 className="h-4 w-4" />
                        Remover
                      </span>
                    </Button>
                  )}

                  {/* Botão Fechar - removido pois já temos o X no header */}

                </div>
              </div>
            </DialogFooter>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Dialog de sucesso com informações da revisão */}
    <AlertDialog open={successDialogOpen} onOpenChange={setSuccessDialogOpen}>
      <AlertDialogContent className="w-[80dvw] border-2 border-black rounded-xl p-0 overflow-hidden max-w-md">
        <AlertDialogHeader className="p-4 sm:p-6 border-b-2 border-black bg-green-100">
          <div className="flex items-center gap-3">
            <div className="bg-white p-2 rounded-full border-2 border-black">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <AlertDialogTitle className="text-xl font-bold text-black">
              Tópico estudado
            </AlertDialogTitle>
          </div>
        </AlertDialogHeader>

        <div className="p-4 sm:p-6">
          <AlertDialogDescription className="text-base text-gray-700">
            {studySuccessMessage}
          </AlertDialogDescription>

          {!isLastRevision && nextRevisionInfo && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">
                Próxima Revisão Agendada
              </h4>

              <div className="space-y-1 text-sm">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                  <span>
                    <strong>Data:</strong> {nextRevisionInfo.date} ({nextRevisionInfo.dayOfWeek})
                  </span>
                </div>

                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-blue-600" />
                  <span>
                    <strong>Em:</strong> {nextRevisionInfo.daysUntil} dias
                  </span>
                </div>

                <div className="flex items-center">
                  <BookOpen className="h-4 w-4 mr-2 text-blue-600" />
                  <span>
                    <strong>Revisão:</strong> {
                      nextRevisionInfo.revisionNumber === 1 ? 'primeira' :
                      nextRevisionInfo.revisionNumber === 2 ? 'segunda' :
                      nextRevisionInfo.revisionNumber === 3 ? 'terceira' :
                      `${nextRevisionInfo.revisionNumber}ª`
                    }
                  </span>
                </div>
              </div>
            </div>
          )}

          <AlertDialogFooter className="flex justify-end mt-6">
            <AlertDialogAction className="bg-black hover:bg-black/90 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all">
              OK
            </AlertDialogAction>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>

    {/* ✅ DIÁLOGO DE CONFIRMAÇÃO (igual TodayStudies) */}
    <ConfirmStudyDialog
      open={confirmDialogOpen}
      onOpenChange={setConfirmDialogOpen}
      onConfirm={handleConfirmStudied}
      topicName={selectedTopicName}
    />

    {/* ✅ NOVO: Diálogo de criação de semanas (igual TodayStudies) */}
    <AlertDialog open={createWeeksDialogOpen} onOpenChange={setCreateWeeksDialogOpen}>
      <AlertDialogContent className="w-[80dvw] border-2 border-black rounded-xl p-0 overflow-hidden max-w-md">
        <AlertDialogHeader className="p-4 sm:p-6 border-b-2 border-black bg-orange-100">
          <div className="flex items-center gap-3">
            <div className="bg-white p-2 rounded-full border-2 border-black">
              <Calendar className="h-5 w-5 text-orange-600" />
            </div>
            <AlertDialogTitle className="text-xl font-bold text-black">
              Criar Semanas de Estudo
            </AlertDialogTitle>
          </div>
        </AlertDialogHeader>

        <div className="p-4 sm:p-6">
          <AlertDialogDescription className="text-base text-gray-700 mb-4">
            Você não tem semanas de estudo suficientes para agendar a revisão deste tópico.
          </AlertDialogDescription>

          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
            <h4 className="font-semibold text-blue-800 mb-2">Detalhes da Revisão</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                <span><strong>Data prevista:</strong> {revisionDateFormatted}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-blue-600" />
                <span><strong>Semanas necessárias:</strong> {weeksToCreate}</span>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-6">
            <p className="text-sm text-yellow-800">
              <strong>Deseja criar {weeksToCreate} {weeksToCreate === 1 ? 'semana' : 'semanas'} de estudo para revisar este tema no dia {revisionDateFormatted}?</strong>
            </p>
          </div>

          <AlertDialogFooter className="flex gap-2 justify-end">
            <AlertDialogAction
              onClick={handleCancelCreateWeeks}
              className="bg-gray-500 hover:bg-gray-600 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
            >
              Não, cancelar
            </AlertDialogAction>
            <AlertDialogAction
              onClick={handleConfirmCreateWeeks}
              className="bg-green-600 hover:bg-green-700 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
            >
              Sim, criar semanas
            </AlertDialogAction>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>

    {/* ✅ NOVO: StudyOptionsDialog para praticar questões */}
    <Suspense fallback={null}>
      <StudyOptionsDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        maxQuestions={maxQuestions}
        minQuestions={1}
        availableYears={availableYears}
        availableInstitutions={availableInstitutions}
        onStartStudy={handleStartStudy}
        totalTopics={allTopicsData.length}
        specialtyId={allTopicsData.length === 1 ? allTopicsData[0]?.topic?.specialtyId : undefined}
        themeId={allTopicsData.length === 1 ? allTopicsData[0]?.topic?.themeId : undefined}
        focusId={allTopicsData.length === 1 ? allTopicsData[0]?.topic?.focusId : undefined}
        allTopicsData={allTopicsData}
      />
    </Suspense>
    </>
  );
};
