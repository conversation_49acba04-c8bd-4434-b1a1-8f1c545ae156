
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
  DialogClose,
  DialogDescription
} from "@/components/ui/dialog";
import type { FlashcardWithHierarchy } from "@/types/flashcardCollaborate";
import { cn } from "@/lib/utils";
import { Check, Trash2, Share, ThumbsUp, ThumbsDown, UserPlus, Clock } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ImageUpload } from "../form/ImageUpload";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { ensureUserId } from "@/utils/ensureUserId";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

const editButtonClass = "flex items-center gap-2 bg-[#58CC02] hover:bg-[#46a302] text-white font-bold border-b-2 border-[#46a302] px-6 py-2 rounded-lg shadow-md hover:shadow-lg active:border-b-0 active:mt-0.5 active:translate-y-px transition-all";
const removeButtonClass = "flex items-center gap-2 border-2 border-red-300 text-red-600 font-bold px-6 py-2 rounded-lg bg-white hover:bg-red-50 transition-all";
const shareButtonClass = "flex items-center gap-2 border-2 border-blue-300 text-blue-600 font-bold px-6 py-2 rounded-lg bg-white hover:bg-blue-50 transition-all";

function ShareStatusBadge({ isShared }: { isShared: boolean }) {
  if (isShared) {
    return (
      <Badge className="bg-hackathon-yellow text-black px-1.5 py-0.5 text-xs font-semibold border border-yellow-500 whitespace-nowrap">
        Compartilhado
      </Badge>
    );
  }
  return null;
}

function FlashcardTypeBadge({ type }: { type?: string }) {
  if (!type) return null;

  const typeConfig = {
    qa: { label: "Q&A", color: "bg-blue-100 text-blue-800 border-blue-300" },
    cloze: { label: "Lacuna", color: "bg-purple-100 text-purple-800 border-purple-300" },
    multipla: { label: "Múltipla", color: "bg-green-100 text-green-800 border-green-300" },
    vf: { label: "V/F", color: "bg-orange-100 text-orange-800 border-orange-300" }
  };

  const config = typeConfig[type as keyof typeof typeConfig];
  if (!config) return null;

  return (
    <Badge className={`${config.color} px-1.5 py-0.5 text-xs font-semibold border whitespace-nowrap`}>
      {config.label}
    </Badge>
  );
}

// Removido ReviewingBadge - informação irrelevante para área colaborativa



interface FlashcardPreviewCardProps {
  card: FlashcardWithHierarchy & { is_shared?: boolean, isImported?: boolean; import_count?: number; likes?: number; dislikes?: number; };
  onDelete?: (id: string) => Promise<void>;
  onUpdate?: (updatedCard: FlashcardWithHierarchy) => Promise<void>;
  className?: string;
  onLike?: (cardId: string) => void;
  onDislike?: (cardId: string) => void;
  onImportCard?: (cardId: string) => void;
  disableInteractions?: boolean;
  hideImportedBadge?: boolean;
}

export const FlashcardPreviewCard: React.FC<FlashcardPreviewCardProps> = ({
  card,
  onDelete,
  onUpdate,
  className,
  onLike,
  onDislike,
  onImportCard,
  disableInteractions = false,
  hideImportedBadge = false,
}) => {
  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [isCardOwner, setIsCardOwner] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [originalCreatorName, setOriginalCreatorName] = useState<string | null>(null);
  const [creatorName, setCreatorName] = useState<string>("Não disponível");



  const [front, setFront] = useState(card.front);
  const [back, setBack] = useState(card.back);
  const [frontImage, setFrontImage] = useState(card.front_image || "");
  const [backImage, setBackImage] = useState(card.back_image || "");
  const [updating, setUpdating] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [shared, setShared] = useState(!!card.is_shared);

  const [likes, setLikes] = useState(card.likes || 0);
  const [dislikes, setDislikes] = useState(card.dislikes || 0);
  const [importCount, setImportCount] = useState(card.import_count ?? 0);
  const [likedBy, setLikedBy] = useState<string[]>(card.liked_by || []);
  const [dislikedBy, setDislikedBy] = useState<string[]>(card.disliked_by || []);
  const [voting, setVoting] = useState(false);

  useEffect(() => { setLikes(card.likes || 0); }, [card.likes]);
  useEffect(() => { setDislikes(card.dislikes || 0); }, [card.dislikes]);
  useEffect(() => { setImportCount(card.import_count ?? 0); }, [card.import_count]);
  useEffect(() => { setLikedBy(card.liked_by || []); }, [card.liked_by]);
  useEffect(() => { setDislikedBy(card.disliked_by || []); }, [card.disliked_by]);

  useEffect(() => {
    const checkCardOwnership = async () => {
      try {
        const userId = await ensureUserId();
        setCurrentUserId(userId);
        setIsCardOwner(userId === card.user_id);
        
        if (card.user_id) {
          fetchCreatorName(card.user_id);
        }
      } catch (error) {
        setIsCardOwner(false);
      }
    };
    checkCardOwnership();
  }, [card.user_id]);

  useEffect(() => {
    async function fetchOriginalCreator() {
      if (card.origin_id) {
        const { data: cardOrig } = await supabase
          .from('flashcards_cards')
          .select('user_id')
          .eq('id', card.origin_id)
          .maybeSingle();

        if (cardOrig && cardOrig.user_id) {
          const { data: profOrig } = await supabase
            .from('profiles')
            .select('full_name, username')
            .eq('id', cardOrig.user_id)
            .maybeSingle();

          if (profOrig) {
            setOriginalCreatorName(profOrig.full_name || profOrig.username || "Usuário original");
          }
        }
      }
    }
    fetchOriginalCreator();
  }, [card.origin_id]);

  const fetchCreatorName = async (uid: string) => {
    try {
      // Usar função RPC segura que não expõe dados sensíveis
      const { data: creatorName, error } = await supabase
        .rpc('get_flashcard_creator_name', { p_user_id: uid });

      if (error) {
        console.error('❌ [FlashcardPreviewCard] Error fetching creator name:', error);
        setCreatorName("Usuário não encontrado");
        return;
      }

      setCreatorName(creatorName || "Usuário não encontrado");
    } catch (error) {
      console.error('❌ [FlashcardPreviewCard] Error fetching creator name:', error);
      setCreatorName("Usuário não encontrado");
    }
  };

  const frontPreview = card.front.length > 100
    ? card.front.substring(0, 97) + "..."
    : card.front;

  const userHasLiked = currentUserId && likedBy.includes(currentUserId);
  const userHasDisliked = currentUserId && dislikedBy.includes(currentUserId);

  async function handleLike() {
    if (disableInteractions || card.isImported || !currentUserId || voting) return;

    if (userHasLiked) return;

    setVoting(true);
    try {
      let newLikedBy = [...likedBy, currentUserId];
      let newDislikedBy = dislikedBy.filter(id => id !== currentUserId);

      const { error } = await supabase
        .from('flashcards_cards')
        .update({
          liked_by: newLikedBy,
          disliked_by: newDislikedBy,
          likes: (likes + 1),
          dislikes: userHasDisliked ? (dislikes - 1) : dislikes
        })
        .eq('id', card.id);

      if (error) {
        toast.error("Erro ao curtir: " + error.message);
        return;
      }
      setLikedBy(newLikedBy);
      setLikes(likes + 1);
      if (userHasDisliked) {
        setDislikedBy(newDislikedBy);
        setDislikes(dislikes - 1);
      }
    } finally {
      setVoting(false);
    }
  }
  
  async function handleDislike() {
    if (disableInteractions || card.isImported || !currentUserId || voting) return;

    if (userHasDisliked) return;

    setVoting(true);
    try {
      let newDislikedBy = [...dislikedBy, currentUserId];
      let newLikedBy = likedBy.filter(id => id !== currentUserId);

      const { error } = await supabase
        .from('flashcards_cards')
        .update({
          liked_by: newLikedBy,
          disliked_by: newDislikedBy,
          likes: userHasLiked ? (likes - 1) : likes,
          dislikes: (dislikes + 1)
        })
        .eq('id', card.id);

      if (error) {
        toast.error("Erro ao dar dislike: " + error.message);
        return;
      }
      setDislikedBy(newDislikedBy);
      setDislikes(dislikes + 1);
      if (userHasLiked) {
        setLikedBy(newLikedBy);
        setLikes(likes - 1);
      }
    } finally {
      setVoting(false);
    }
  }

  async function handleDelete() {
    if (!onDelete || !isCardOwner) {
      if (!isCardOwner) {
        toast.error("Você só pode excluir seus próprios flashcards");
      }
      return;
    }
    try {
      setUpdating(true);
      await onDelete(card.id);
      toast.success("Flashcard excluído com sucesso");
      setOpen(false);
    } catch (error: any) {
      toast.error("Erro ao excluir: " + (error.message || "Não foi possível excluir o flashcard"));
    } finally {
      setUpdating(false);
    }
  }

  async function handleSave() {
    if (!onUpdate || !isCardOwner) {
      if (!isCardOwner) {
        toast.error("Você só pode editar seus próprios flashcards");
      }
      return;
    }
    setUpdating(true);
    await onUpdate({
      ...card,
      front,
      back,
      front_image: frontImage,
      back_image: backImage
    });
    setEditMode(false);
    setUpdating(false);
  }

  async function handleToggleShare() {
    if (!isCardOwner) {
      toast.error("Você só pode alterar o compartilhamento dos seus próprios flashcards");
      return;
    }

    setIsSharing(true);
    try {
      const newValue = !shared;
      const { error } = await supabase
        .from('flashcards_cards')
        .update({ is_shared: newValue })
        .eq('id', card.id);

      if (error) throw error;

      setShared(newValue);

      toast.success(newValue 
        ? "Flashcard compartilhado!" 
        : "Compartilhamento removido.", 
        { 
          description: newValue
            ? "Seu flashcard agora está visível para toda a comunidade."
            : "Seu flashcard não está mais disponível na área de compartilhados.",
          duration: 4000
        }
      );
    } catch (error: any) {
      toast.error("Erro ao alterar compartilhamento: " + (error?.message || "Não foi possível alterar o status de compartilhamento deste flashcard"));
    } finally {
      setIsSharing(false);
    }
  }

  function handleDialogClose() {
    setEditMode(false);
    setConfirmDelete(false);
    setOpen(false);
  }

  const likeBtnClass = cn(
    "rounded-full flex items-center justify-center transition-all",
    userHasLiked ? "bg-[#F97316] text-white shadow-md ring-2 ring-[#ea384c]/30" : "bg-gray-50 hover:bg-orange-100",
    voting && "opacity-70 pointer-events-none"
  );
  const dislikeBtnClass = cn(
    "rounded-full flex items-center justify-center transition-all",
    userHasDisliked ? "bg-[#ea384c] text-white shadow-md ring-2 ring-[#ea384c]/60" : "bg-gray-50 hover:bg-red-100",
    voting && "opacity-70 pointer-events-none"
  );

  const isCopy = card.origin_id && card.origin_id !== card.id;

  return (
    <>
      <div
        className={cn(
          "group flex flex-col justify-between min-h-[140px] p-5",
          "bg-gradient-to-br from-white to-hackathon-lightBg border-2 border-black rounded-xl",
          "hover:shadow-card transition-all duration-200 ease-in-out",
          "cursor-pointer relative overflow-hidden",
          card.isImported && !hideImportedBadge && "opacity-60 pointer-events-none",
          className
        )}
        onClick={() => setOpen(true)}
      >

        
        <div className="flex flex-col h-full">
          {/* ✅ Conteúdo maximizado horizontalmente */}
          <div className="flex-1 flex items-center justify-center mt-6 mb-2">
            <div className="w-full px-2 pr-2">
              <span className="text-sm font-medium text-gray-500 block mb-2 text-left">
                Frente:
              </span>
              <div className="font-medium text-base text-gray-800 group-hover:text-black transition-colors text-left leading-relaxed">
                {frontPreview}
              </div>
            </div>
          </div>

          {/* ✅ Footer otimizado para mobile - tudo na mesma linha */}
          <div className="mt-auto pt-3 border-t border-black/10 flex justify-between items-center gap-2 min-h-[32px]">
            <div className="flex items-center gap-1 overflow-hidden">
              <ShareStatusBadge isShared={shared} />
            </div>
            <div className="flex items-center gap-2">
              {!card.isImported && onImportCard && (
                <Button
                  className="bg-green-500 hover:bg-green-600 border-2 border-black text-white shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all rounded-md text-xs font-bold px-3 py-1 h-7 flex-shrink-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    onImportCard(card.id);
                  }}
                >
                  Importar
                </Button>
              )}
              <Button
                variant="hackYellow"
                size="sm"
                className="text-xs font-bold px-3 py-1 h-7 flex-shrink-0"
                onClick={(e) => {
                  e.stopPropagation();
                  setOpen(true);
                }}
              >
                Ver
              </Button>
            </div>
          </div>
        </div>
        
        {/* ✅ Ícones e badge na mesma linha */}
        <div className="absolute top-2 right-2 flex items-center gap-1 z-10">
          {/* Badge Importado ao lado esquerdo */}
          {card.isImported && !hideImportedBadge && (
            <span className="bg-green-200 text-green-800 px-2 py-0.5 text-xs rounded font-bold border border-green-400">
              Importado
            </span>
          )}
          {/* Like - Compacto */}
          <Button
            onClick={(e) => {
              e.stopPropagation();
              if (onLike) {
                onLike(card.id);
              } else {
                handleLike();
              }
            }}
            disabled={disableInteractions || userHasLiked || voting}
            variant="ghost"
            className={cn(
              "flex items-center gap-0.5 bg-white/90 backdrop-blur-sm rounded-full px-1.5 py-0.5 border border-black/10 h-auto hover:bg-white transition-all",
              userHasLiked ? "text-[#F97316] border-orange-200" : "text-gray-600 hover:text-[#F97316] hover:border-orange-200"
            )}
            aria-label="Curtir"
          >
            <ThumbsUp className="w-2.5 h-2.5" />
            <span className="text-xs font-medium">{likes}</span>
          </Button>

          {/* Dislike - Compacto */}
          <Button
            onClick={(e) => {
              e.stopPropagation();
              if (onDislike) {
                onDislike(card.id);
              } else {
                handleDislike();
              }
            }}
            disabled={disableInteractions || userHasDisliked || voting}
            variant="ghost"
            className={cn(
              "flex items-center gap-0.5 bg-white/90 backdrop-blur-sm rounded-full px-1.5 py-0.5 border border-black/10 h-auto hover:bg-white transition-all",
              userHasDisliked ? "text-[#ea384c] border-red-200" : "text-gray-600 hover:text-[#ea384c] hover:border-red-200"
            )}
            aria-label="Dislike"
          >
            <ThumbsDown className="w-2.5 h-2.5" />
            <span className="text-xs font-medium">{dislikes}</span>
          </Button>

          {/* Import Count - Compacto */}
          <div className="flex items-center gap-0.5 bg-white/90 backdrop-blur-sm rounded-full px-1.5 py-0.5 border border-black/10">
            <div className="p-0.5 rounded-full bg-hackathon-green/20">
              <UserPlus className="w-2.5 h-2.5 text-hackathon-green" />
            </div>
            <span className="text-xs font-medium text-gray-700">{importCount}</span>
          </div>
        </div>
      </div>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="mb-2 flex items-center gap-2">
              Detalhes do Flashcard
              <ShareStatusBadge isShared={shared} />
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-500">
              Visualize os detalhes deste flashcard e interaja com ele
            </DialogDescription>
          </DialogHeader>
          
          {!editMode && (
            <div className="space-y-2 py-1">
              <div>
                <span className="font-semibold">Frente:</span>
                <div className="text-gray-800 border rounded px-2 py-1 mt-1 bg-gray-50 break-words whitespace-pre-line">
                  {card.front}
                  {card.front_image && <img src={card.front_image} alt="Frente" className="max-w-full mt-2 rounded border" />}
                </div>
              </div>
              <div>
                <span className="font-semibold">Verso:</span>
                <div className="text-gray-700 border rounded px-2 py-1 mt-1 bg-gray-50 break-words whitespace-pre-line">
                  {card.back}
                  {card.back_image && <img src={card.back_image} alt="Verso" className="max-w-full mt-2 rounded border" />}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-1 pt-2">
                <div>
                  <span className="text-xs font-bold">Especialidade:</span>
                  <div className="text-xs">{card.hierarchy.specialty?.name}</div>
                </div>
                {card.hierarchy.theme && (
                  <div>
                    <span className="text-xs font-bold">Tema:</span>
                    <div className="text-xs">{card.hierarchy.theme.name}</div>
                  </div>
                )}
                {card.hierarchy.focus && (
                  <div>
                    <span className="text-xs font-bold">Foco:</span>
                    <div className="text-xs">{card.hierarchy.focus.name}</div>
                  </div>
                )}
                {card.hierarchy.extraFocus && (
                  <div>
                    <span className="text-xs font-bold">Extra Foco:</span>
                    <div className="text-xs">{card.hierarchy.extraFocus.name}</div>
                  </div>
                )}
              </div>

              {/* ✅ Tipo de flashcard */}
              <div>
                <span className="text-xs font-bold">Tipo:</span>
                <div className="text-xs flex items-center gap-2 mt-1">
                  <FlashcardTypeBadge type={card.flashcard_type} />
                  <span className="text-gray-600">
                    {card.flashcard_type === 'qa' && 'Pergunta e Resposta'}
                    {card.flashcard_type === 'cloze' && 'Completar Lacunas'}
                    {card.flashcard_type === 'multipla' && 'Múltipla Escolha'}
                    {card.flashcard_type === 'vf' && 'Verdadeiro ou Falso'}
                    {!card.flashcard_type && 'Não especificado'}
                  </span>
                </div>
              </div>

              {/* Status de revisão removido - irrelevante para área colaborativa */}
              <div>
                <span className="text-xs font-bold">Criado em:</span> <span className="text-xs">{new Date(card.created_at).toLocaleDateString()}</span>
              </div>
              <div className="pt-1 pb-1">
                {!isCopy ? (
                  <div>
                    <span className="text-xs font-semibold text-gray-700">Original:</span>
                    <span className="ml-2 text-xs">{isCardOwner ? "Você" : creatorName}</span>
                  </div>
                ) : (
                  <div className="space-y-1">
                    <div>
                      <span className="text-xs font-semibold text-gray-700">Original:</span>
                      <span className="ml-2 text-xs">{originalCreatorName ? originalCreatorName : "--"}</span>
                    </div>
                    <div>
                      <span className="text-xs font-semibold text-gray-700">Copiado por:</span>
                      <span className="ml-2 text-xs">{creatorName}</span>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex items-center gap-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (onLike) onLike(card.id);
                    }}
                    className="flex items-center gap-1 hover:bg-orange-50 rounded p-1 transition-colors"
                    disabled={!onLike}
                  >
                    <ThumbsUp className={userHasLiked ? "text-[#F97316]" : "text-gray-400 hover:text-[#F97316]"} size={18} />
                    <span className="text-xs">{likes}</span>
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (onDislike) onDislike(card.id);
                    }}
                    className="flex items-center gap-1 hover:bg-red-50 rounded p-1 transition-colors"
                    disabled={!onDislike}
                  >
                    <ThumbsDown className={userHasDisliked ? "text-[#ea384c]" : "text-gray-400 hover:text-[#ea384c]"} size={18} />
                    <span className="text-xs">{dislikes}</span>
                  </button>
                </div>
                <div className="flex items-center gap-1 ml-3">
                  <UserPlus className="w-4 h-4 text-green-700" />
                  <span className="text-xs">{importCount ?? 0}</span>
                </div>
              </div>
              {!isCardOwner && currentUserId && (
                <div className="text-xs text-amber-700 mt-2 bg-amber-50 p-2 rounded-md border border-amber-200">
                  Este flashcard foi criado por outro usuário. Você pode visualizá-lo, mas apenas o proprietário pode editá-lo.
                </div>
              )}
            </div>
          )}
          {editMode && (
            <form className="space-y-4" onSubmit={e => { e.preventDefault(); handleSave(); }}>
              <div>
                <label className="block text-sm font-bold mb-1">Frente</label>
                <Textarea
                  value={front}
                  onChange={e => setFront(e.target.value)}
                  className="w-full"
                  required
                  rows={3}
                  disabled={updating}
                />
                <ImageUpload
                  label="Imagem da Frente"
                  currentImage={frontImage}
                  onImageSelect={setFrontImage}
                  id={`front-image-edit-${card.id}`}
                />
              </div>
              <div>
                <label className="block text-sm font-bold mb-1">Verso</label>
                <Textarea
                  value={back}
                  onChange={e => setBack(e.target.value)}
                  className="w-full"
                  required
                  rows={3}
                  disabled={updating}
                />
                <ImageUpload
                  label="Imagem do Verso"
                  currentImage={backImage}
                  onImageSelect={setBackImage}
                  id={`back-image-edit-${card.id}`}
                />
              </div>
              <DialogFooter className="gap-2 mt-4 flex-col sm:flex-row">
                <Button type="button" variant="outline" onClick={() => setEditMode(false)} disabled={updating}>
                  Cancelar
                </Button>
                <Button type="submit" disabled={updating}>
                  {updating ? "Salvando..." : "Salvar"}
                </Button>
              </DialogFooter>
            </form>
          )}

          {confirmDelete && (
            <div className="pt-3 pb-1 flex flex-col items-center gap-2">
              <div className="text-destructive font-bold text-base">Excluir este flashcard?</div>
              <div className="flex gap-3 mt-2">
                <Button
                  variant="outline"
                  onClick={() => setConfirmDelete(false)}
                  disabled={updating}
                >
                  Cancelar
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={updating}
                >
                  {updating ? "Excluindo..." : "Confirmar"}
                </Button>
              </div>
            </div>
          )}

          {!editMode && !confirmDelete && (
            <DialogFooter className="flex flex-col gap-3 mt-6">
              <div className="flex flex-col w-full gap-2">
                {isCardOwner && (
                  <>
                    <Button
                      type="button"
                      className={editButtonClass + " w-full justify-center"}
                      onClick={() => setEditMode(true)}
                      aria-label="Editar flashcard"
                      disabled={updating}
                    >
                      <Check className="mr-2" size={20} /> Editar
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      className={removeButtonClass + " w-full justify-center"}
                      onClick={() => setConfirmDelete(true)}
                      aria-label="Remover flashcard"
                      disabled={updating}
                    >
                      <Trash2 className="mr-2" size={20} /> Remover
                    </Button>
                    <Button
                      type="button"
                      variant={shared ? "default" : "outline"}
                      className={
                        shareButtonClass +
                        (shared ? " bg-hackathon-yellow text-black border-yellow-400 hover:bg-yellow-300" : "")
                      }
                      onClick={handleToggleShare}
                      aria-label={shared ? "Remover compartilhamento" : "Compartilhar flashcard"}
                      disabled={isSharing || isCopy}
                    >
                      <Share className="mr-2" size={20} />
                      {isSharing
                        ? shared
                          ? "Removendo..."
                          : "Compartilhando..."
                        : shared
                          ? "Remover compartilhamento"
                          : "Compartilhar"}
                    </Button>
                    {isCopy && (
                      <div className="text-xs text-red-600 font-semibold mt-1">
                        Não é possível compartilhar flashcards copiados
                      </div>
                    )}
                  </>
                )}
              </div>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default FlashcardPreviewCard;
