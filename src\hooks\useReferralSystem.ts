import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface ReferralData {
  id: string;
  referral_code: string;
  total_referrals: number;
  created_at: string;
}

interface ReferredUser {
  id: string;
  user_id: string;
  referrer_id: string;
  referral_code: string;
  referred_at: string;
  profiles?: {
    full_name: string;
    formation_area: string;
    preparation_type: string;
    specialty: string;
    is_student: boolean;
    created_at: string;
  };
}

export const useReferralSystem = (options?: { enableQueries?: boolean }) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const enableQueries = options?.enableQueries ?? true;

  // Buscar dados de referência do usuário - só quando necessário
  const { data: referralData, isLoading: isLoadingReferral } = useQuery({
    queryKey: ['user-referral', user?.id],
    queryFn: async (): Promise<ReferralData | null> => {
      if (!user?.id) return null;

      // Fetching referral data for user

      const { data, error } = await supabase
        .from('user_referrals')
        .select('*')
        .eq('referrer_id', user.id)
        .maybeSingle(); // ✅ Usar maybeSingle() em vez de single()

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!user?.id && enableQueries,
    staleTime: 10 * 60 * 1000, // 10 minutos - cache mais longo
  });

  // Buscar usuários referenciados - só quando necessário
  const { data: referredUsers, isLoading: isLoadingReferred } = useQuery({
    queryKey: ['referred-users', user?.id],
    queryFn: async (): Promise<ReferredUser[]> => {
      if (!user?.id) return [];

      // Fetching referred users for user

      const { data, error } = await supabase
        .from('user_referred_by')
        .select(`
          *,
          profiles!user_referred_by_user_profile_fkey (
            full_name,
            formation_area,
            preparation_type,
            specialty,
            is_student,
            created_at
          )
        `)
        .eq('referrer_id', user.id)
        .order('referred_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id && enableQueries,
    staleTime: 10 * 60 * 1000, // 10 minutos - cache mais longo
  });

  // Criar código de referência
  const createReferralCode = useMutation({
    mutationFn: async (): Promise<string> => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .rpc('create_user_referral_code', {
          user_id_param: user.id
        });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-referral', user?.id] });
      // Dialog será mostrado pelo componente que chama esta função
    },
    onError: (error: any) => {
      // Dialog será mostrado pelo componente que chama esta função
    }
  });

  // Atualizar código de referência personalizado
  const updateReferralCode = useMutation({
    mutationFn: async (newCode: string): Promise<boolean> => {
      if (!user?.id) throw new Error('User not authenticated');

      // Verificar se o código já existe
      const { data: existingCode, error: checkError } = await supabase
        .from('user_referrals')
        .select('referrer_id')
        .ilike('referral_code', newCode)
        .neq('referrer_id', user.id)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existingCode) {
        throw new Error('Este código já está em uso');
      }

      // Atualizar o código
      const { error: updateError } = await supabase
        .from('user_referrals')
        .update({
          referral_code: newCode.toUpperCase(),
          updated_at: new Date().toISOString()
        })
        .eq('referrer_id', user.id);

      if (updateError) throw updateError;
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-referral', user?.id] });
    },
    onError: (error: any) => {
      // Error handling será feito pelo componente
    }
  });

  // Processar referência (para novos usuários)
  const processReferral = useMutation({
    mutationFn: async (referralCode: string): Promise<boolean> => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .rpc('process_referral', {
          new_user_id: user.id,
          referral_code_param: referralCode
        });

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (success) => {
      if (success) {
        queryClient.invalidateQueries({ queryKey: ['user-referral'] });
        queryClient.invalidateQueries({ queryKey: ['referred-users'] });
        // Dialog será mostrado pelo componente que chama esta função
      } else {
        // Referral code invalid or already used
        // Dialog será mostrado pelo componente que chama esta função
      }
    },
    onError: (error: any) => {
      // Error processing referral
      // Dialog será mostrado pelo componente que chama esta função
    }
  });

  // Gerar link de compartilhamento
  const generateShareLink = (referralCode?: string) => {
    const code = referralCode || referralData?.referral_code;
    if (!code) return null;
    
    const baseUrl = window.location.origin;
    return `${baseUrl}?ref=${code}`;
  };

  // Copiar link para clipboard
  const copyShareLink = async (referralCode?: string) => {
    const link = generateShareLink(referralCode);
    if (!link) {
      return false;
    }

    try {
      await navigator.clipboard.writeText(link);
      return true;
    } catch (error) {
      return false;
    }
  };

  // Compartilhar via Web Share API (mobile)
  const shareLink = async (referralCode?: string) => {
    const link = generateShareLink(referralCode);
    if (!link) {
      toast.error('Código de referência não encontrado');
      return;
    }

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'MedEvo - Plataforma de Estudos',
          text: 'Venha estudar comigo na melhor plataforma de questões para concursos!',
          url: link,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback para desktop
      await copyShareLink(referralCode);
    }
  };

  return {
    // Data
    referralData,
    referredUsers,
    
    // Loading states
    isLoadingReferral,
    isLoadingReferred,
    
    // Actions
    createReferralCode: createReferralCode.mutate,
    updateReferralCode: updateReferralCode.mutate,
    processReferral: processReferral.mutate,
    generateShareLink,
    copyShareLink,
    shareLink,

    // Loading states for actions
    isCreatingCode: createReferralCode.isPending,
    isUpdatingCode: updateReferralCode.isPending,
    isProcessingReferral: processReferral.isPending,
    
    // Computed values
    hasReferralCode: !!referralData?.referral_code,
    totalReferrals: referredUsers?.length || 0,
    referralCode: referralData?.referral_code,
  };
};
