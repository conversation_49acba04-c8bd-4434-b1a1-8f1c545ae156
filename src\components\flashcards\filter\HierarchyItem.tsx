
import { ChevronRight, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import type { SelectedFilters } from "@/types/flashcard";
import { useRef, useEffect } from "react";

interface HierarchyItemProps {
  id: string;
  name: string;
  level: number;
  count: number;
  isExpanded: boolean;
  isSelected: boolean;
  hasChildren: boolean;
  expandedItems: string[];
  selectedFilters: SelectedFilters;
  item: any;
  onToggleExpand: (id: string) => void;
  onToggleSelect: (id: string, type: keyof SelectedFilters) => void;
}

export const HierarchyItem = ({
  id,
  name,
  level,
  count,
  isExpanded,
  isSelected,
  hasChildren,
  expandedItems,
  selectedFilters,
  item,
  onToggleExpand,
  onToggleSelect,
}: HierarchyItemProps) => {
  const getFilterType = (level: number): keyof SelectedFilters => {
    switch (level) {
      case 0:
        return 'specialties';
      case 1:
        return 'themes';
      case 2:
        return 'focuses';
      case 3:
        return 'extrafocuses';
      default:
        return 'specialties';
    }
  };

  const itemRef = useRef<HTMLDivElement>(null);

  // Get border and background colors based on the hierarchy level
  const getBorderColor = () => {
    switch (level) {
      case 0: return 'border-blue-200'; // Specialties (top level)
      case 1: return 'border-green-200'; // Themes
      case 2: return 'border-amber-200'; // Focuses
      case 3: return 'border-purple-200'; // Extra focuses
      default: return 'border-gray-200';
    }
  };

  const getHoverColor = () => {
    switch (level) {
      case 0: return 'hover:bg-blue-50/60';
      case 1: return 'hover:bg-green-50/60';
      case 2: return 'hover:bg-amber-50/60';
      case 3: return 'hover:bg-purple-50/60';
      default: return 'hover:bg-gray-50/60';
    }
  };

  const getSelectedColor = () => {
    switch (level) {
      case 0: return 'bg-blue-50';
      case 1: return 'bg-green-50';
      case 2: return 'bg-amber-50';
      case 3: return 'bg-purple-50';
      default: return 'bg-[#FEF7CD]';
    }
  };

  const getBadgeColor = () => {
    if (isSelected) return 'bg-[#FF6B00] text-white border-none';
    
    switch (level) {
      case 0: return 'bg-blue-100 text-blue-700 border-blue-200';
      case 1: return 'bg-green-100 text-green-700 border-green-200';
      case 2: return 'bg-amber-100 text-amber-700 border-amber-200';
      case 3: return 'bg-purple-100 text-purple-700 border-purple-200';
      default: return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  return (
    <div className="space-y-1">
      <div
        ref={itemRef}
        className={cn(
          "flex items-center gap-2 p-2 rounded-lg border transition-all duration-200",
          getBorderColor(),
          getHoverColor(),
          isSelected && getSelectedColor(),
          level > 0 && "sm:ml-6 ml-3"
        )}
      >
        {hasChildren && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-transparent"
            onClick={() => onToggleExpand(id)}
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-black" />
            ) : (
              <ChevronRight className="h-4 w-4 text-black" />
            )}
          </Button>
        )}

        <div 
          className="flex items-center gap-3 flex-1 cursor-pointer"
          onClick={() => onToggleSelect(id, getFilterType(level))}
        >
          <div 
            className={cn(
              "w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center",
              isSelected 
                ? "bg-[#FF6B00] border-black text-white" 
                : "border-black hover:border-[#FF6B00]"
            )}
          >
            {isSelected && (
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-3.5 h-3.5"
              >
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            )}
          </div>
          
          <span className={cn(
            "text-sm transition-colors duration-200 overflow-hidden text-ellipsis",
            isSelected && "text-[#FF6B00] font-medium"
          )}>
            {name}
          </span>
        </div>

        <Badge 
          className={cn(
            "min-w-[3rem] justify-center border",
            getBadgeColor()
          )}
        >
          {count}
        </Badge>
      </div>

      {isExpanded && item.children && item.children
        .filter((child: any) => (child.count || 0) > 0) // ✅ Filtrar filhos com 0 cards
        .map((child: any) => (
          <HierarchyItem
            key={child.id}
            id={child.id}
            name={child.name}
            level={level + 1}
            count={child.count || 0}
            isExpanded={expandedItems.includes(child.id)}
            isSelected={selectedFilters[getFilterType(level + 1)]?.includes(child.id)}
            hasChildren={child.children?.filter((grandchild: any) => (grandchild.count || 0) > 0).length > 0}
            expandedItems={expandedItems}
            selectedFilters={selectedFilters}
            item={child}
            onToggleExpand={onToggleExpand}
            onToggleSelect={onToggleSelect}
          />
        ))}
    </div>
  );
};
