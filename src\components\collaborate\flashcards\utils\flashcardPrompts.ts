
export const buildPrompt = (fcType: "cloze"|"vf"|"multipla"|"qa", quantity: number) => {
  if (fcType === "vf") {
    return `
✅ Prompt para Criação de Flashcards Médicos – Verdadeiro ou Falso (V/F)
🎯 Objetivo
Gerar flashcards do tipo "Verdadeiro ou Falso", com foco em conteúdos clínicos, técnicos e baseados em evidências, voltados para estudantes de medicina e médicos em formação.

📌 Formato dos Flashcards
Cada flashcard deve conter dois campos:

Statement: Uma afirmação clínica precisa, direta e com relevância prática.

Answer: "V" se a afirmação for verdadeira, "F" se for falsa.

❌ Não incluir justificativas, explicações, enumerações ou qualquer texto adicional.

📘 Fonte de Conteúdo
Todos os flashcards devem ser criados com base exclusivamente nas questões fornecidas abaixo, representadas pela variável \${questionsText}.

📝 Instruções Específicas

Utilize linguagem técnica e objetiva.

Varie os temas abordados: condutas, diagnósticos diferenciais, critérios clínicos, medicações, doses, semiologia, etc.

Evite afirmações triviais, óbvias ou voltadas a leigos.

O foco é revisar conhecimentos práticos e úteis para provas médicas e atuação clínica.

✅ Exemplo de Flashcard Válido

{
  "statement": "A pressão arterial deve ser aferida em todas as consultas de puericultura a partir dos 3 anos.",
  "answer": "V"
}
📦 Saída Esperada Gere exatamente ${quantity} flashcards no formato JSON com os campos:

statement

answer
`;
  }

  if (fcType === "multipla") {
    return `
✅ Prompt para Criação de Flashcards Médicos – Múltipla Escolha (Alternativas)
🎯 Objetivo
Gerar flashcards no formato de múltipla escolha (tipo questão de prova), com foco em revisão clínica técnica para médicos e estudantes de medicina.

📌 Formato dos Flashcards
Cada flashcard deve conter:

question: Enunciado clínico objetivo e direto.

options: Um array de 4 alternativas, sendo apenas uma correta.

answer: Texto da alternativa correta (exatamente como aparece nas opções).

❌ Não incluir explicações, comentários, temas, fontes, enumerações ou qualquer elemento adicional.

📘 Fonte de Conteúdo
Todas as perguntas devem ser inspiradas exclusivamente nas questões fornecidas abaixo em \${questionsText}.

📝 Instruções Específicas

Use linguagem técnica e voltada a médicos.

Inclua temas relevantes para a prática clínica, diagnósticos, condutas, exames, farmacologia etc.

Crie alternativas plausíveis, evitando pegadinhas óbvias ou questões rasas.

Não repetir conteúdo de forma redundante.

✅ Exemplo de Flashcard Válido

{
  "question": "Qual é o exame de escolha para confirmar o diagnóstico de trombose venosa profunda em um paciente com sintomas sugestivos?",
  "options": [
    "Angiografia cerebral",
    "Radiografia de membros inferiores",
    "Doppler venoso",
    "Tomografia de abdome"
  ],
  "answer": "Doppler venoso"
}
📦 Saída Esperada Gere exatamente ${quantity} flashcards no formato JSON com os campos:

question

options

answer
`;
  }

  if (fcType === "qa") {
    return `
✅ Prompt para Criação de Flashcards Médicos – Pergunta e Resposta (Q&A/Padrão)
🎯 Objetivo
Criar flashcards no formato pergunta e resposta direta, ideais para revisão rápida de conteúdos técnicos e clínicos.

📌 Formato dos Flashcards
Cada flashcard deve conter:

question: Uma pergunta clara, direta e de alta relevância clínica.

answer: Resposta correta, objetiva e sucinta.

❌ Não incluir comentários, enumerações, justificativas ou explicações adicionais.

📘 Fonte de Conteúdo
Use apenas as questões listadas na variável \${questionsText} como base. Não consulte fontes externas.

📝 Instruções Específicas

Mantenha linguagem técnica, sem simplificações leigas.

Explore perguntas sobre condutas, doses, critérios diagnósticos, achados clínicos e terapêutica baseada em evidências.

Evite perguntas genéricas, sem impacto prático ou conteúdo raso.

✅ Exemplo de Flashcard Válido

{
  "question": "Qual é a principal indicação de administração de surfactante em neonatos?",
  "answer": "Síndrome do desconforto respiratório neonatal"
}
📦 Saída Esperada Gere exatamente ${quantity} flashcards no formato JSON com os campos:

question

answer
`;
  }

  return `
✅ Prompt para Criação de Flashcards Médicos CLOZE DELETION
🎯 Objetivo
Gerar flashcards no formato cloze deletion, voltados para estudantes de medicina e médicos em formação, com foco em conteúdos clínicos, técnicos e baseados em evidências.

📌 Formato dos Flashcards
Cada flashcard deve conter exatamente dois campos:

Front: Uma frase com uma lacuna entre colchetes [ ... ], destacando uma informação essencial, objetiva e tecnicamente relevante.

Back: A resposta correta que completa a lacuna de forma direta e precisa.

❌ Não incluir explicações, comentários, temas, fontes, enumerações ou qualquer elemento adicional.

📘 Fonte de Conteúdo
Todos os flashcards devem ser exclusivamente baseados nas questões fornecidas abaixo como exemplo. Elas representam:

O perfil esperado do conteúdo,

O nível técnico exigido,

E os temas frequentemente abordados.

Você não deve utilizar fontes externas.

Utilize as questões abaixo como base de inspiração para formular os flashcards.
As questões estão disponíveis na variável abaixo:

\${questionsText}

📝 Instruções Específicas
Utilize linguagem técnica, objetiva e voltada para médicos.

Crie conteúdo original, sem copiar literalmente as frases das questões.

Varie os tipos de estrutura: use perguntas diretas, lacunas em dados clínicos, condutas, valores numéricos, medicações, doses, critérios diagnósticos, achados semiológicos, etc.

Evite conteúdo genérico, óbvio ou superficial.

Os flashcards devem priorizar revisão de conteúdo prático e de alta relevância clínica.

Não inclua perguntas de conhecimento leigo ou de baixa complexidade.

✅ Exemplo de Flashcard Válido
{
  "front": "A primeira linha de tratamento para anafilaxia é a administração de [ ... ] intramuscular.",
  "back": "adrenalina"
}
📦 Saída esperada
Gere exatamente ${quantity} flashcards no formato JSON, organizados com os seguintes campos:

front

back

A estrutura final da resposta deve ser um array JSON pronto para uso em plataformas de flashcards.
`;
};
