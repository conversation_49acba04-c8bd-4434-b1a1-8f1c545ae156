import React from 'react';
import { Check<PERSON><PERSON><PERSON>, Clock, BookOpen, Target, Calendar, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface GenerationStats {
  totalTopics: number;
  totalWeeks: number;
  specialties: string[];
  totalHours: number;
  generationTime: number;
  domain: string;
}

interface SuccessSectionProps {
  stats?: GenerationStats;
  onClose: () => void;
}

export const SuccessSection: React.FC<SuccessSectionProps> = ({
  stats,
  onClose
}) => {
  const [countdown, setCountdown] = React.useState(3);
  const [showWarning, setShowWarning] = React.useState(true);



  // ✅ NOVO: Refresh automático assim que o componente de sucesso aparece
  React.useEffect(() => {
    // Contador regressivo
    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          window.location.reload();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // ✅ NOVO: Esconder aviso após 3 segundos
    const warningTimer = setTimeout(() => {
      setShowWarning(false);
    }, 3000);

    return () => {
      clearInterval(countdownInterval);
      clearTimeout(warningTimer);
    };
  }, []);

  return (
    <div className="space-y-6 text-center">
      {/* Success Icon */}
      <div className="flex justify-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
      </div>

      {/* Success Message */}
      <div>
        <h2 className="text-2xl font-bold text-green-600 mb-2">
          Cronograma Gerado com Sucesso!
        </h2>
        <p className="text-gray-600 mb-2">
          Seu cronograma de estudos foi criado e está pronto para uso.
        </p>
        <p className="text-sm text-blue-600 font-medium">
          🔄 Atualizando página automaticamente em {countdown} segundos...
        </p>
      </div>

      {/* ✅ NOVO: Aviso sobre refresh manual */}
      {showWarning && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-start gap-3">
          <RefreshCw className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div className="text-amber-800 text-sm">
            <strong>💡 Dica:</strong> Se os tópicos não aparecerem automaticamente,
            pressione <kbd className="px-2 py-1 bg-amber-200 rounded text-xs font-mono">F5</kbd> para atualizar a página.
          </div>
        </div>
      )}

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <BookOpen className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">{stats.totalTopics}</div>
              <div className="text-sm text-gray-600">Tópicos Criados</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Calendar className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-600">{stats.totalWeeks}</div>
              <div className="text-sm text-gray-600">Semanas Geradas</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Clock className="w-6 h-6 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-600">{stats.totalHours}h</div>
              <div className="text-sm text-gray-600">Horas de Estudo</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Target className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600">{stats.specialties?.length || 0}</div>
              <div className="text-sm text-gray-600">Especialidades</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Specialties */}
      {stats?.specialties && stats.specialties.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-3">Especialidades Incluídas</h3>
          <div className="flex flex-wrap gap-2 justify-center">
            {stats.specialties.map((specialty, index) => (
              <Badge key={index} variant="secondary">
                {specialty}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Generation Info */}
      {stats && (
        <div className="text-sm text-gray-500 space-y-1">
          <p>Domínio: <span className="font-medium">{stats.domain}</span></p>
          <p>Tempo de geração: <span className="font-medium">{stats.generationTime.toFixed(1)}s</span></p>
        </div>
      )}

      {/* Action Button */}
      <Button
        onClick={() => window.location.reload()}
        className="w-full"
        size="lg"
      >
        {countdown > 0 ? `Ver Cronograma (${countdown}s...)` : 'Atualizando...'}
      </Button>
    </div>
  );
};
