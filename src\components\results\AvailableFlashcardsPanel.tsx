import React, { useState, useEffect, useMemo } from 'react';
import { Button } from "@/components/ui/button";
import { useParams } from 'react-router-dom';
import { supabase } from "@/integrations/supabase/client";
import { Info } from "lucide-react";
import { FlashcardGenerationPreview } from './FlashcardGenerationPreview';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';

interface Stats {
  totalQuestions: number;
  incorrectQuestions: number;
}

interface AvailableFlashcardsPanelProps {
  stats: Stats;
  onImport: (count: number, cardIds: string[]) => void;
}

// ✅ Constantes para paginação otimizada
const CARDS_PER_PAGE = 8; // Número de cards por página

interface FlashcardInfo {
  id: string;
  front: string;
  back: string;
  front_image?: string | null;
  back_image?: string | null;
  specialty_id: string;
  theme_id?: string;
  focus_id?: string;
  specialty: string;
  theme: string;
  focus: string;
  imported?: boolean;
}

export const AvailableFlashcardsPanel = ({ stats, onImport }: AvailableFlashcardsPanelProps) => {
  const [availableCards, setAvailableCards] = useState<FlashcardInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [importedIndexes, setImportedIndexes] = useState<Set<number>>(new Set());
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [importedCardIds, setImportedCardIds] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCards, setTotalCards] = useState(0);
  const { sessionId } = useParams();
  const isMobile = useIsMobile();

  // ✅ Cálculos de paginação otimizados com useMemo
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(totalCards / CARDS_PER_PAGE);
    const startIndex = (currentPage - 1) * CARDS_PER_PAGE;
    const endIndex = startIndex + CARDS_PER_PAGE;
    const currentPageCards = availableCards.slice(startIndex, endIndex);

    return {
      totalPages,
      currentPageCards,
      hasPrevPage: currentPage > 1,
      hasNextPage: currentPage < totalPages,
      startIndex,
      endIndex
    };
  }, [availableCards, currentPage, totalCards]);

  // Navegação simples
  const goToPrevious = () => {
    setCurrentCardIndex(prev => Math.max(0, prev - 1));
  };

  const goToNext = () => {
    const maxIndex = paginationData.currentPageCards.length - 1;
    setCurrentCardIndex(prev => Math.min(maxIndex, prev + 1));
  };

  // Reset quando mudar de página
  useEffect(() => {
    setCurrentCardIndex(0);
  }, [currentPage]);

  useEffect(() => {
    fetchAvailableFlashcards();
  }, [sessionId]);

  // Update the parent component with the imported card count
  useEffect(() => {
    onImport(importedIndexes.size, importedCardIds);
  }, [importedIndexes.size, importedCardIds, onImport]);

  const fetchAvailableFlashcards = async () => {
    if (!sessionId) return;

    try {
      setLoading(true);

      // Step 1: Get the session questions to extract specialty, theme, focus IDs
      const { data: events, error: eventsError } = await supabase
        .from('session_events')
        .select(`
          question_id,
          specialty_id,
          theme_id,
          focus_id
        `)
        .eq('session_id', sessionId);

      if (eventsError) throw eventsError;

      const sessionQuestions = events.map(event => ({
        id: event.question_id,
        specialty_id: event.specialty_id,
        theme_id: event.theme_id,
        focus_id: event.focus_id
      }));

      // Extract unique specialty, theme and focus IDs from the questions
      const specialtiesSet = new Set(sessionQuestions.map(q => q.specialty_id).filter(Boolean));
      const themesSet = new Set(sessionQuestions.map(q => q.theme_id).filter(Boolean));
      const focusesSet = new Set(sessionQuestions.map(q => q.focus_id).filter(Boolean));

      const specialties = Array.from(specialtiesSet);
      const themes = Array.from(themesSet);
      const focuses = Array.from(focusesSet);

      if (!specialties.length) {
        setLoading(false);
        return;
      }

      // Step 2: Get all flashcards that match these categories
      let query = supabase
        .from('flashcards_cards')
        .select(`
          id,
          front,
          back,
          front_image,
          back_image,
          specialty_id,
          theme_id,
          focus_id,
          specialty:flashcards_specialty(name),
          theme:flashcards_theme(name),
          focus:flashcards_focus(name)
        `)
        .eq('is_shared', true)
        .eq('current_state', 'available');

      if (specialties.length) {
        query = query.in('specialty_id', specialties);
      }

      const { data: cards, error: cardsError } = await query;

      if (cardsError) throw cardsError;



      // Step 3: Format the cards for display
      let formattedCards = cards?.map(card => ({
        id: card.id,
        front: card.front,
        back: card.back,
        front_image: card.front_image,
        back_image: card.back_image,
        specialty_id: card.specialty_id,
        theme_id: card.theme_id,
        focus_id: card.focus_id,
        specialty: card.specialty?.name || 'Desconhecida',
        theme: card.theme?.name || 'Desconhecido',
        focus: card.focus?.name || 'Desconhecido'
      })) || [];

      // Step 4: Filter cards based on session focus
      // Filter by specialty
      const cardsBySpecialty = formattedCards.filter(card =>
        specialties.includes(card.specialty_id)
      );
      
      // Further filter by theme if we have theme filters
      let filteredCards = cardsBySpecialty;
      if (themes.length) {
        filteredCards = cardsBySpecialty.filter(card => {
          return card.theme_id && themes.includes(card.theme_id);
        });
      }

      // Further filter by focus if we have focus filters
      if (focuses.length) {
        filteredCards = filteredCards.filter(card => {
          return card.focus_id && focuses.includes(card.focus_id);
        });
      }

      // Verificar cards já importados pelo usuário
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: userCards } = await supabase
          .from('flashcards_cards')
          .select('origin_id')
          .eq('user_id', user.id)
          .not('origin_id', 'is', null);

        if (userCards && userCards.length > 0) {
          const importedIds = userCards.map(c => c.origin_id).filter(Boolean);
          filteredCards = filteredCards.map(card => ({
            ...card,
            imported: importedIds.includes(card.id)
          }));
        }
      }


      setAvailableCards(filteredCards);
      setTotalCards(filteredCards.length);

    } catch (error) {
      console.error('❌ [AvailableFlashcardsPanel] Error fetching available flashcards:', error);
      toast.error("Erro ao carregar flashcards disponíveis");
    } finally {
      setLoading(false);
    }
  };

  const handleImportCard = (index: number, cardId: string) => {
    setImportedIndexes(prev => {
      const newSet = new Set(prev);
      newSet.add(index);
      return newSet;
    });
    setImportedCardIds(prev => [...prev, cardId]);

    // Notify the parent component about the import
    const newImportedCardIds = [...importedCardIds, cardId];
    onImport(importedIndexes.size + 1, newImportedCardIds);
  };

  return (
    <div className="bg-white/90 border-2 border-gray-200 rounded-lg shadow-sm p-4">
      <div className="mb-4 flex flex-col items-center">
        <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wider text-center mb-2">
          Flashcards disponíveis na plataforma
        </h3>
        <Button variant="ghost" size="icon" className="absolute right-3 top-3">
          <Info className="h-5 w-5 text-gray-500" />
        </Button>
      </div>

      {/* Container simples - mostra apenas o card atual */}
      <div className="flex justify-center w-full">
        {paginationData.currentPageCards.length > 0 && (
          <div className={`${isMobile ? 'w-[280px]' : 'w-[340px]'}`}>
            <FlashcardGenerationPreview
              card={paginationData.currentPageCards[currentCardIndex]}
              isImported={importedIndexes.has((currentPage - 1) * CARDS_PER_PAGE + currentCardIndex) || Boolean(paginationData.currentPageCards[currentCardIndex]?.imported)}
              onImport={(cardId) => handleImportCard((currentPage - 1) * CARDS_PER_PAGE + currentCardIndex, cardId)}
            />
          </div>
        )}

        {availableCards.length === 0 && !loading && (
          <div className="w-full flex items-center justify-center py-10">
            <div className="max-w-md flex flex-col items-center justify-center border border-dashed border-primary/30 rounded-lg p-6 text-center bg-gradient-to-br from-blue-50 to-purple-50">
              <div className="text-gray-500 mb-2">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">
                Nenhum flashcard disponível
              </h3>
              <p className="text-sm text-gray-500">
                Não há flashcards disponíveis para esta sessão no momento.
              </p>
            </div>
          </div>
        )}

        {loading && (
          <div className="w-full flex items-center justify-center py-10">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
              <p className="text-sm text-gray-600">Carregando flashcards disponíveis...</p>
            </div>
          </div>
        )}
      </div>

      {/* ✅ Controles de navegação simplificados */}
      {availableCards.length > 0 && paginationData.currentPageCards.length > 1 && (
        <div className="flex justify-center items-center gap-3 mt-4">
          <Button
            onClick={goToPrevious}
            variant="outline"
            size="sm"
            disabled={currentCardIndex === 0}
            className="px-3"
          >
            ‹
          </Button>

          <span className="text-sm text-gray-600 min-w-[60px] text-center">
            {currentCardIndex + 1} / {paginationData.currentPageCards.length}
          </span>

          <Button
            onClick={goToNext}
            variant="outline"
            size="sm"
            disabled={currentCardIndex === paginationData.currentPageCards.length - 1}
            className="px-3"
          >
            ›
          </Button>
        </div>
      )}
    </div>
  );
};
