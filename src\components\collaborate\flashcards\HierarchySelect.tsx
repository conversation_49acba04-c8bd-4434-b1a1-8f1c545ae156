import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import type { HierarchySelectProps } from "./types";

export const HierarchySelect = ({
  label,
  value,
  onChange,
  options,
  placeholder,
  disabled,
  hasError
}: HierarchySelectProps) => {
  return (
    <div>
      <Label className={cn(hasError && "text-red-500")}>{label}</Label>
      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger className={cn(
          hasError && "border-red-500 ring-red-500 focus:ring-red-500"
        )}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.id} value={option.id}>
              {option.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {hasError && (
        <p className="text-xs text-red-500 mt-1">Este campo é obrigatório</p>
      )}
    </div>
  );
};