
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Flag, AlertTriangle, MessageSquare, Bug, Send } from 'lucide-react';
import { ensureUserId } from '@/utils/ensureUserId';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface QuestionAICommentaryFeedbackProps {
  questionId: string;
  userId: string;
}

export const QuestionAICommentaryFeedback: React.FC<QuestionAICommentaryFeedbackProps> = ({
  questionId,
  userId
}) => {
  const [open, setOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<'error' | 'suggestion' | 'problem'>('error');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { isDarkMode } = useDarkMode();

  const handleSubmit = async () => {
    if (!message.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, escreva um feedback detalhado.",
        variant: "destructive"
      });
      return;
    }

    // Validar questionId
    if (!questionId) {
      toast({
        title: "Erro",
        description: "ID da questão não disponível. Tente novamente mais tarde.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Garantir que temos um userId válido, mesmo que a prop esteja vazia
      const validUserId = await ensureUserId();

      const { error } = await supabase
        .from('medevo_feedbacks')
        .insert({
          question_id: questionId,
          user_id: validUserId,
          feedback_type: feedbackType,
          message: message.trim()
        });

      if (error) throw error;

      toast({
        title: "Feedback enviado",
        description: "Obrigado por nos ajudar a melhorar!",
      });

      setMessage('');
      setOpen(false);
    } catch (error) {
      toast({
        title: "Erro ao enviar feedback",
        description: "Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      // Reset form when dialog is closed
      setMessage('');
      setFeedbackType('error');
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`h-8 w-8 p-0 rounded-full border-2 transition-all duration-200 group shadow-sm ${
            isDarkMode
              ? 'bg-gray-700 border-gray-600 hover:border-red-400 hover:bg-red-900/30 hover:text-red-400'
              : 'bg-white border-gray-200 hover:border-red-300 hover:bg-red-50 hover:text-red-600'
          }`}
          title="Reportar problema na análise"
        >
          <Flag className={`h-3.5 w-3.5 transition-colors ${
            isDarkMode
              ? 'text-gray-400 group-hover:text-red-400'
              : 'text-gray-500 group-hover:text-red-600'
          }`} />
        </Button>
      </DialogTrigger>
      <DialogContent className={`max-w-[95vw] max-h-[90vh] w-full sm:max-w-lg rounded-xl overflow-y-auto transition-colors duration-200 ${
        isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
      }`} style={{ background: isDarkMode ? '#1f2937' : '#ffffff' }}>
        <DialogHeader className="pb-4">
          <DialogTitle className={`flex items-center gap-2 text-lg transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>
            <Flag className="h-5 w-5 text-red-600" />
            Reportar Problema na Análise
          </DialogTitle>
          <p className={`text-sm mt-2 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>
            Ajude-nos a melhorar a qualidade das análises reportando problemas ou sugerindo melhorias.
          </p>
        </DialogHeader>
        <div className="space-y-6">
          <div>
            <h4 className={`font-semibold mb-3 text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-900'
            }`}>Tipo de feedback:</h4>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              <Button
                variant={feedbackType === 'error' ? 'default' : 'outline'}
                onClick={() => setFeedbackType('error')}
                className="flex items-center gap-2 justify-start h-auto p-3 text-left"
              >
                <Bug className="h-4 w-4 flex-shrink-0" />
                <div>
                  <div className="font-medium">Erro</div>
                  <div className="text-xs opacity-70">Informação incorreta</div>
                </div>
              </Button>

              <Button
                variant={feedbackType === 'suggestion' ? 'default' : 'outline'}
                onClick={() => setFeedbackType('suggestion')}
                className="flex items-center gap-2 justify-start h-auto p-3 text-left"
              >
                <MessageSquare className="h-4 w-4 flex-shrink-0" />
                <div>
                  <div className="font-medium">Sugestão</div>
                  <div className="text-xs opacity-70">Melhoria possível</div>
                </div>
              </Button>

              <Button
                variant={feedbackType === 'problem' ? 'default' : 'outline'}
                onClick={() => setFeedbackType('problem')}
                className="flex items-center gap-2 justify-start h-auto p-3 text-left"
              >
                <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                <div>
                  <div className="font-medium">Problema</div>
                  <div className="text-xs opacity-70">Outro problema</div>
                </div>
              </Button>
            </div>
          </div>
          <div>
            <h4 className={`font-semibold mb-3 text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-900'
            }`}>Descreva o problema:</h4>
            <Textarea
              placeholder={
                feedbackType === 'error'
                  ? "Ex: A explicação da alternativa B está incorreta porque..."
                  : feedbackType === 'suggestion'
                  ? "Ex: Seria útil adicionar mais detalhes sobre..."
                  : "Ex: A análise não aborda o ponto principal da questão..."
              }
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className={`min-h-[120px] resize-none transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-gray-200 placeholder:text-gray-400'
                  : 'bg-white border-gray-300 text-gray-900 placeholder:text-gray-500'
              }`}
            />
            <p className={`text-xs mt-2 transition-colors duration-200 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-500'
            }`}>
              Seja específico para nos ajudar a melhorar a análise.
            </p>
          </div>

          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !message.trim()}
              className="flex-1 flex items-center gap-2"
            >
              <Send className="h-4 w-4" />
              {isSubmitting ? 'Enviando...' : 'Enviar Feedback'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
