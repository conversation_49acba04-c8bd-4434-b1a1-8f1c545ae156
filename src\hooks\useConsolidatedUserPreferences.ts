/**
 * 🎯 HOOK CONSOLIDADO PARA USER_PREFERENCES
 *
 * Elimina duplicações fazendo uma única query para user_preferences
 * e fornecendo os dados para useUserData, useUserPreferences e useStudyPreferences
 */
import React, { useMemo, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';


export interface ConsolidatedUserPreferences {
  id: string;
  user_id: string;
  welcome_dialog_shown: boolean;
  tutorial_completed: boolean;
  filter_tutorial_completed: boolean;
  cinematic_viewed: boolean;
  target_specialty: string | null;
  study_months: number | null;
  preferences_completed: boolean;
  target_institutions_unknown: boolean;
  target_institutions?: Array<{ id: string; name: string }>; // ✅ NOVO: Instituições selecionadas
  dark_mode_enabled: boolean; // ✅ NOVO: Preferência de modo noturno
  created_at: string;
  updated_at: string;
}

export const useConsolidatedUserPreferences = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();





  // ✅ QUERY ÚNICA para todos os campos de user_preferences
  const {
    data: preferences,
    isLoading,
    error
  } = useQuery({
    queryKey: ['consolidated-user-preferences', user?.id],
    queryFn: async (): Promise<ConsolidatedUserPreferences | null> => {
      if (!user?.id) {
        return null;
      }

      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .maybeSingle();

        // ✅ LIMPEZA: Log removido - resultado de query rotineiro

        if (error) {
          // 🔍 LOG: Erro na query
          logQuery(['consolidated-user-preferences', user.id], 'useConsolidatedUserPreferences', 'ERROR', {
            error: error.message,
            errorCode: error.code,
            userId: user.id
          });
          throw error;
        }

        // Se não existe registro, criar com valores padrão
        if (!data) {
          
          const defaultPreferences = {
            user_id: user.id,
            welcome_dialog_shown: false,
            tutorial_completed: false,
            filter_tutorial_completed: false,
            cinematic_viewed: false,
            target_specialty: null,
            study_months: null,
            preferences_completed: false,
            target_institutions_unknown: false,
            dark_mode_enabled: false
          };

          const { data: newData, error: createError } = await supabase
            .from('user_preferences')
            .upsert(defaultPreferences, {
              onConflict: 'user_id'
            })
            .select('*')
            .single();

          if (createError) {
            throw createError;
          }

          // ✅ NOVO: Adicionar array vazio de instituições para novo registro
          return {
            ...newData,
            target_institutions: []
          };
        }

        // ✅ NOVO: Buscar instituições selecionadas pelo usuário
        const { data: userInstitutions, error: institutionsError } = await supabase
          .from('user_target_institutions')
          .select(`
            institution_id,
            exam_locations!inner(id, name)
          `)
          .eq('user_id', user.id);

        if (institutionsError) {
          // Não falhar por causa das instituições, apenas retornar array vazio
        }

        const targetInstitutions = userInstitutions?.map(ui => ({
          id: ui.exam_locations.id,
          name: ui.exam_locations.name
        })) || [];

        const result = {
          ...data,
          target_institutions: targetInstitutions // ✅ NOVO: Incluir instituições
        };

        // ✅ LIMPEZA: Log removido - retorno de dados completos
        return result;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 2
  });

  // ✅ MUTATION para atualizar preferências
  const updatePreferences = useMutation({
    mutationFn: async (updates: Partial<ConsolidatedUserPreferences>) => {
      if (!user?.id) throw new Error('User not authenticated');



      const { data, error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          ...updates,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
        .select('*')
        .single();

      if (error) {
        throw error;
      }
      return data;
    },
    onSuccess: (data) => {
      // ✅ CORREÇÃO CRÍTICA: NÃO sobrescrever cache que pode ter instituições
      // O problema estava aqui - setQueryData estava sobrescrevendo dados completos

      // Buscar dados atuais do cache para preservar instituições
      const currentData = queryClient.getQueryData(['consolidated-user-preferences', user?.id]) as ConsolidatedUserPreferences;

      if (currentData?.target_institutions) {
        // Preservar instituições existentes
        const updatedData = {
          ...data,
          target_institutions: currentData.target_institutions
        };
        queryClient.setQueryData(['consolidated-user-preferences', user?.id], updatedData);
      } else {
        // Se não há instituições no cache atual, usar dados recebidos
        queryClient.setQueryData(['consolidated-user-preferences', user?.id], data);
      }
    }
  });

  // ✅ FUNÇÕES DE CONVENIÊNCIA (memoizadas)
  const markWelcomeAsSeen = useCallback(() => {
    return updatePreferences.mutateAsync({ welcome_dialog_shown: true });
  }, [updatePreferences.mutateAsync]);

  const markTutorialAsCompleted = useCallback(() => {
    return updatePreferences.mutateAsync({ tutorial_completed: true });
  }, [updatePreferences.mutateAsync]);

  const markFilterTutorialAsCompleted = useCallback(() => {
    return updatePreferences.mutateAsync({ filter_tutorial_completed: true });
  }, [updatePreferences.mutateAsync]);



  const markCinematicAsViewed = useCallback(() => {
    return updatePreferences.mutateAsync({ cinematic_viewed: true });
  }, [updatePreferences.mutateAsync]);

  const updateDarkModePreference = useCallback((enabled: boolean) => {
    return updatePreferences.mutateAsync({ dark_mode_enabled: enabled });
  }, [updatePreferences.mutateAsync]);

  // ✅ VALORES SEGUROS COM FALLBACK (memoizados)
  const safePreferences = useMemo(() => preferences || {
    id: '',
    user_id: user?.id || '',
    welcome_dialog_shown: false,
    tutorial_completed: false,
    filter_tutorial_completed: false,
    cinematic_viewed: false,
    target_specialty: null,
    study_months: null,
    preferences_completed: false,
    target_institutions_unknown: false,
    target_institutions: [], // ✅ NOVO: Array vazio por padrão
    dark_mode_enabled: false, // ✅ NOVO: Modo noturno desabilitado por padrão
    created_at: '',
    updated_at: ''
  }, [preferences, user?.id]);

  return useMemo(() => ({
    // Dados brutos
    preferences,
    isLoading,
    error,

    // Estados específicos com fallback seguro
    showWelcomeDialog: !safePreferences.welcome_dialog_shown,
    tutorialCompleted: safePreferences.tutorial_completed,
    filterTutorialCompleted: safePreferences.filter_tutorial_completed,
    cinematicViewed: safePreferences.cinematic_viewed,
    preferencesCompleted: safePreferences.preferences_completed,
    targetSpecialty: safePreferences.target_specialty,
    studyMonths: safePreferences.study_months,
    targetInstitutionsUnknown: safePreferences.target_institutions_unknown,
    darkModeEnabled: safePreferences.dark_mode_enabled,

    // Mutations
    updatePreferences: updatePreferences.mutateAsync,
    markWelcomeAsSeen,
    markTutorialAsCompleted,
    markFilterTutorialAsCompleted,
    markCinematicAsViewed,
    updateDarkModePreference,

    // Status das mutations
    isUpdating: updatePreferences.isPending
  }), [
    preferences,
    isLoading,
    error,
    safePreferences,
    updatePreferences.mutateAsync,
    updatePreferences.isPending,
    markWelcomeAsSeen,
    markTutorialAsCompleted,
    markFilterTutorialAsCompleted,
    markCinematicAsViewed,
    updateDarkModePreference
  ]);
};

/**
 * Hook otimizado para dados específicos do useUserData
 */
export const useOptimizedUserPreferencesData = () => {
  const { preferences, isLoading, error, preferencesCompleted } = useConsolidatedUserPreferences();

  return {
    userPreferences: preferences ? { preferences_completed: preferencesCompleted } : null,
    isLoading,
    error,
    hasCompletedPreferences: preferencesCompleted
  };
};

/**
 * Hook otimizado para dados específicos do useStudyPreferences
 */
export const useOptimizedStudyPreferencesData = () => {
  const {
    preferences,
    isLoading,
    error,
    targetSpecialty,
    studyMonths,
    preferencesCompleted,
    targetInstitutionsUnknown
  } = useConsolidatedUserPreferences();

  return {
    studyPreferencesData: preferences ? {
      target_specialty: targetSpecialty,
      study_months: studyMonths,
      preferences_completed: preferencesCompleted,
      target_institutions_unknown: targetInstitutionsUnknown,
      target_institutions: preferences.target_institutions || [] // ✅ NOVO: Incluir instituições
    } : null,
    isLoading,
    error
  };
};
