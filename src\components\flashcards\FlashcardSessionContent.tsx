import { useEffect } from "react";
import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { useFlashcardState } from "./hooks/useFlashcardState";
import { useFlashcardResponse } from "./hooks/useFlashcardResponse";
import { ReviewButtons } from "./ReviewButtons";
import { FlashcardDisplay } from "./FlashcardDisplay";
import { useToast } from "@/components/ui/use-toast";
import { FlashcardSessionHeader } from "./FlashcardSessionHeader";
import type { FlashcardResponse, ReviewMetricsInfo } from "./types";
import type { FlashcardWithHierarchy } from "@/components/collaborate/flashcards/types";
import { useQueryClient } from "@tanstack/react-query";

interface FlashcardSessionContentProps {
  flashcards: FlashcardWithHierarchy[];
  isLoading: boolean;
  sessionId: string;
  onComplete: () => void;
}

export const FlashcardSessionContent = ({
  flashcards,
  isLoading,
  sessionId,
  onComplete
}: FlashcardSessionContentProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const {
    currentIndex,
    isFlipped,
    setIsFlipped,
    sessionStatus,
    advanceCard,
    currentCard
  } = useFlashcardState(flashcards);

  const { handleResponse, isSubmitting } = useFlashcardResponse(sessionId);

  useEffect(() => {
    // Track current card changes
  }, [currentCard]);

  const handleCardResponse = async (response: FlashcardResponse, metricsInfo?: ReviewMetricsInfo) => {
    try {
      if (!currentCard) {
        console.error('❌ [FlashcardSessionContent] No current card available');
        return;
      }

      const { error: cardError } = await supabase
        .from('flashcards_session_cards')
        .upsert({
          session_id: sessionId,
          card_id: currentCard.id,
          response,
          review_status: 'reviewed',
          last_review_time: new Date().toISOString()
        }, {
          onConflict: 'session_id,card_id'
        });

      if (cardError) throw cardError;

      if (currentIndex === flashcards.length - 1) {
        const { error: sessionError } = await supabase
          .from('flashcards_sessions')
          .update({
            status: 'completed',
            end_time: new Date().toISOString(),
          })
          .eq('id', sessionId);

        if (sessionError) throw sessionError;

        // ✅ Invalidar caches após completar sessão de flashcard
        queryClient.invalidateQueries({
          predicate: (query) => {
            const key = query.queryKey[0] as string;
            return key === 'flashcard-sessions' || key === 'user-statistics' || key === 'user-study-stats';
          }
        });

        onComplete();
      } else {
        advanceCard();
      }


    } catch (error: any) {
      console.error('Error handling card response:', error);

    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Loading flashcards...</div>
      </div>
    );
  }

  if (!flashcards.length) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">No flashcards found</h3>
          <p className="text-muted-foreground">
            No flashcards were found with the selected filters.
          </p>
        </div>
      </Card>
    );
  }

  if (sessionStatus === 'completed') {
    return (
      <Card className="p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Session Completed!</h3>
          <p className="text-muted-foreground">
            You have completed all flashcards in this session.
          </p>
        </div>
      </Card>
    );
  }

  if (!currentCard) {
   // console.error("❌ [FlashcardSessionContent] Current card not found");
    return null;
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <FlashcardSessionHeader
        currentCard={currentCard}
        currentIndex={currentIndex}
        totalCards={flashcards.length}
      />

      <FlashcardDisplay
        front={currentCard.front}
        back={currentCard.back}
        frontImage={currentCard.front_image}
        backImage={currentCard.back_image}
        isFlipped={isFlipped}
        onFlip={() => setIsFlipped(!isFlipped)}
        createdAt={currentCard.created_at}
        userId={currentCard.user_id}
        currentCard={currentCard}
        onResponse={handleCardResponse}
        flashcardType={currentCard.flashcard_type || 'qa'}
      />

      {isFlipped && (
        <ReviewButtons
          onResponse={handleCardResponse}
          isDisabled={isSubmitting}
          currentCard={currentCard}
        />
      )}
    </div>
  );
};