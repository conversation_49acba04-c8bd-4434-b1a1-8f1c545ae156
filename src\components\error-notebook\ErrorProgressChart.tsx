import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface ErrorProgressChartProps {
  reviewedErrors: number;
  totalErrors: number;
  topErrorThemes: Array<{
    theme_name: string;
    theme_id: string;
    error_count: number;
  }>;
  topErrorSpecialties: Array<{
    specialty_name: string;
    specialty_id: string;
    error_count: number;
  }>;
}

export const ErrorProgressChart: React.FC<ErrorProgressChartProps> = ({
  reviewedErrors,
  totalErrors,
  topErrorThemes,
  topErrorSpecialties
}) => {
  const progressPercentage = totalErrors > 0 ? (reviewedErrors / totalErrors) * 100 : 0;
  const pendingErrors = totalErrors - reviewedErrors;

  const getProgressIcon = () => {
    if (progressPercentage >= 70) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (progressPercentage >= 30) return <Minus className="h-4 w-4 text-yellow-500" />;
    return <TrendingDown className="h-4 w-4 text-red-500" />;
  };

  const getProgressColor = () => {
    if (progressPercentage >= 70) return 'bg-green-500';
    if (progressPercentage >= 30) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getProgressText = () => {
    if (progressPercentage >= 70) return 'Excelente progresso!';
    if (progressPercentage >= 30) return 'Bom progresso';
    return 'Continue revisando';
  };

  return (
    <div className="space-y-6">
      {/* Progress Overview */}
      <Card className="p-6 bg-white border-2 border-black shadow-card-sm">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold flex items-center gap-2">
            {getProgressIcon()}
            Progresso de Revisão
          </h3>
          <Badge variant="outline" className="text-sm">
            {progressPercentage.toFixed(1)}% concluído
          </Badge>
        </div>
        
        <div className="space-y-4">
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-4 relative overflow-hidden">
            <div 
              className={`h-4 rounded-full transition-all duration-500 ${getProgressColor()}`}
              style={{ width: `${progressPercentage}%` }}
            />
            <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white mix-blend-difference">
              {reviewedErrors} de {totalErrors}
            </div>
          </div>
          
          {/* Progress Stats */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="text-lg font-bold text-green-600">{reviewedErrors}</div>
              <div className="text-xs text-gray-600">Revisadas</div>
            </div>
            <div className="p-3 bg-red-50 rounded-lg">
              <div className="text-lg font-bold text-red-600">{pendingErrors}</div>
              <div className="text-xs text-gray-600">Pendentes</div>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="text-lg font-bold text-blue-600">{totalErrors}</div>
              <div className="text-xs text-gray-600">Total</div>
            </div>
          </div>
          
          <p className="text-sm text-gray-600 text-center">
            {getProgressText()}
          </p>
        </div>
      </Card>

      {/* Rankings Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Error Themes */}
        {topErrorThemes.length > 0 && (
          <Card className="p-6 bg-white border-2 border-black shadow-card-sm">
            <h3 className="text-lg font-bold mb-4">Temas com Mais Erros</h3>
            <div className="space-y-3">
              {topErrorThemes.slice(0, 5).map((theme, index) => {
                const percentage = totalErrors > 0 ? (theme.error_count / totalErrors) * 100 : 0;
                return (
                  <div key={theme.theme_id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs w-8 h-6 flex items-center justify-center">
                          {index + 1}
                        </Badge>
                        <span className="text-sm font-medium truncate max-w-[200px]" title={theme.theme_name}>
                          {theme.theme_name}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">
                          {theme.error_count} ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-red-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        )}

        {/* Top Error Specialties */}
        {topErrorSpecialties.length > 0 && (
          <Card className="p-6 bg-white border-2 border-black shadow-card-sm">
            <h3 className="text-lg font-bold mb-4">Especialidades com Mais Erros</h3>
            <div className="space-y-3">
              {topErrorSpecialties.slice(0, 3).map((specialty, index) => {
                const percentage = totalErrors > 0 ? (specialty.error_count / totalErrors) * 100 : 0;
                return (
                  <div key={specialty.specialty_id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs w-8 h-6 flex items-center justify-center">
                          {index + 1}
                        </Badge>
                        <span className="text-sm font-medium truncate max-w-[200px]" title={specialty.specialty_name}>
                          {specialty.specialty_name}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">
                          {specialty.error_count} ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};
