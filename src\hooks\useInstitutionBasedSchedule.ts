import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@supabase/auth-helpers-react';
import { useInstitutionPrevalence, PrevalenceFilters } from './useInstitutionPrevalence';
import { fetchQuestionsByInstitutions } from '@/utils/rpcHelpers';
import { getWeekDates } from './study-schedule/useScheduleDates';
import type { StudyTopic, AIScheduleOptions } from '@/types/study-schedule';
import { useQueryClient } from '@tanstack/react-query';

export interface InstitutionScheduleOptions extends Omit<AIScheduleOptions, 'domain'> {
  institutionIds: string[];
  startYear?: number;
  endYear?: number;
  domain?: string;
  generationMode: 'random' | 'institution_based';
}

export const useInstitutionBasedSchedule = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const user = useUser();
  const queryClient = useQueryClient();
  const { calculatePrevalence } = useInstitutionPrevalence();

  const generateScheduleByInstitution = async (
    options: InstitutionScheduleOptions,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {


    if (!user) {
      throw new Error('Usuário não autenticado');
    }

    setIsLoading(true);
    setError(null);

    try {
      onProgress?.(10);

      // Se modo aleatório, usar geração padrão
      if (options.generationMode === 'random') {
        return await generateRandomSchedule(options, onProgress);
      }

      // Calcular prevalência para as instituições selecionadas
      const prevalenceFilters: PrevalenceFilters = {
        institutionIds: options.institutionIds,
        startYear: options.startYear,
        endYear: options.endYear,
        domain: options.domain
      };

      // Validar se há instituições selecionadas
      if (!prevalenceFilters.institutionIds || prevalenceFilters.institutionIds.length === 0) {
        throw new Error('Nenhuma instituição foi selecionada. Por favor, selecione pelo menos uma instituição para gerar o cronograma.');
      }



      onProgress?.(30);

      const institutionStats = await calculatePrevalence(prevalenceFilters);

      if (institutionStats.length === 0) {
        console.error('❌ [generateScheduleByInstitution] Nenhuma estatística encontrada para as instituições');
        throw new Error('Nenhuma estatística encontrada para as instituições selecionadas. Verifique se as instituições possuem questões no período e domínio especificados.');
      }

      onProgress?.(50);

      // Combinar estatísticas de todas as instituições
      const combinedStats = combineInstitutionStats(institutionStats);

      // Buscar hierarquia diretamente do banco
      const focusIds = combinedStats.focuses.map(f => f.focus_id);

      // Buscar hierarquia em lotes para evitar URLs muito grandes
      const BATCH_SIZE = 100; // Processar 100 IDs por vez
      const focusData = [];

      for (let i = 0; i < focusIds.length; i += BATCH_SIZE) {
        const batch = focusIds.slice(i, i + BATCH_SIZE);

        const { data: batchData, error: batchError } = await supabase
          .from('study_categories')
          .select('id, name, parent_id')
          .in('id', batch);

        if (batchError) {
          console.error('❌ [generateScheduleByInstitution] Erro ao buscar lote de focos:', batchError);
          throw new Error(`Erro ao buscar focos: ${batchError.message}`);
        }

        if (batchData) {
          focusData.push(...batchData);
        }
      }

      // Buscar temas únicos em lotes
      const themeIds = [...new Set(focusData.map(f => f.parent_id).filter(Boolean))];



      const themeData = [];
      for (let i = 0; i < themeIds.length; i += BATCH_SIZE) {
        const batch = themeIds.slice(i, i + BATCH_SIZE);

        const { data: batchData, error: batchError } = await supabase
          .from('study_categories')
          .select('id, name, parent_id')
          .in('id', batch);

        if (batchError) {
          console.error('❌ [generateScheduleByInstitution] Erro ao buscar lote de temas:', batchError);
          throw new Error(`Erro ao buscar temas: ${batchError.message}`);
        }

        if (batchData) {
          themeData.push(...batchData);
        }
      }



      // Buscar especialidades únicas em lotes
      const specialtyIds = [...new Set(themeData.map(t => t.parent_id).filter(Boolean))];

      const specialtyData = [];
      for (let i = 0; i < specialtyIds.length; i += BATCH_SIZE) {
        const batch = specialtyIds.slice(i, i + BATCH_SIZE);

        const { data: batchData, error: batchError } = await supabase
          .from('study_categories')
          .select('id, name, parent_id')
          .in('id', batch);

        if (batchError) {
          console.error('❌ [generateScheduleByInstitution] Erro ao buscar lote de especialidades:', batchError);
          throw new Error(`Erro ao buscar especialidades: ${batchError.message}`);
        }

        if (batchData) {
          specialtyData.push(...batchData);
        }
      }

      // Criar mapas para busca rápida
      const themeMap = new Map(themeData.map(t => [t.id, t]));
      const specialtyMap = new Map(specialtyData.map(s => [s.id, s]));

      // Criar mapa de hierarquia
      const hierarchyMap = new Map();
      focusData.forEach(focus => {
        const theme = themeMap.get(focus.parent_id);
        const specialty = theme ? specialtyMap.get(theme.parent_id) : null;



        hierarchyMap.set(focus.id, {
          focus_id: focus.id,
          focus_name: focus.name,
          theme_id: theme?.id || '',
          theme_name: theme?.name || '',
          specialty_id: specialty?.id || '',
          specialty_name: specialty?.name || ''
        });
      });



      // Enriquecer focos com hierarquia
      combinedStats.focuses = combinedStats.focuses.map(focus => {
        const hierarchy = hierarchyMap.get(focus.focus_id);
        if (hierarchy) {
          return {
            ...focus,
            focus_name: hierarchy.focus_name,
            specialty_id: hierarchy.specialty_id,
            specialty_name: hierarchy.specialty_name,
            theme_id: hierarchy.theme_id,
            theme_name: hierarchy.theme_name
          };
        }
        console.warn('⚠️ [generateScheduleByInstitution] Foco sem hierarquia:', focus.focus_id);
        return focus;
      });

      // Gerar cronograma baseado na prevalência
      const schedule = await generatePrevalenceBasedSchedule(
        combinedStats,
        institutionStats,
        options,
        themeMap,
        onProgress
      );

      onProgress?.(100);
      return schedule;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      console.error('❌ [generateScheduleByInstitution] Erro completo:', err);
      console.error('❌ [generateScheduleByInstitution] Stack trace:', err instanceof Error ? err.stack : 'N/A');
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const generateRandomSchedule = async (
    options: InstitutionScheduleOptions,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {
    // Para modo aleatório, usar o sistema padrão existente
    // Retornar array vazio e deixar o sistema padrão lidar com isso
    onProgress?.(100);
    return [];
  };

  const combineInstitutionStats = (institutionStats: any[]) => {
    try {
      const combinedSpecialties = new Map<string, { specialty_id: string; name: string; count: number; percentage: number }>();
      const combinedThemes = new Map<string, { theme_id: string; name: string; count: number; percentage: number }>();
      const combinedFocuses = new Map<string, { focus_id: string; focus_name: string; specialty_id: string; theme_id: string; count: number; percentage: number }>();

      let totalQuestions = 0;

    // Somar estatísticas de todas as instituições
    institutionStats.forEach((institution, index) => {


      totalQuestions += institution.total_questions;

      // Processar especialidades
      if (institution.specialties && Array.isArray(institution.specialties)) {
        institution.specialties.forEach((specialty: any) => {
          const key = specialty.specialty_id;
          const current = combinedSpecialties.get(key) || {
            specialty_id: specialty.specialty_id,
            name: specialty.specialty_name,
            count: 0,
            percentage: 0
          };
          combinedSpecialties.set(key, {
            ...current,
            count: current.count + specialty.question_count
          });
        });
      }

      // Processar temas
      if (institution.themes && Array.isArray(institution.themes)) {
        institution.themes.forEach((theme: any) => {
          const key = theme.theme_id;
          const current = combinedThemes.get(key) || {
            theme_id: theme.theme_id,
            name: theme.theme_name,
            count: 0,
            percentage: 0
          };
          combinedThemes.set(key, {
            ...current,
            count: current.count + theme.question_count
          });
        });
      }

      // Processar focos
      if (institution.focuses && Array.isArray(institution.focuses)) {


        institution.focuses.forEach((focus: any) => {
          const key = focus.focus_id;
          const current = combinedFocuses.get(key) || {
            focus_id: focus.focus_id,
            focus_name: focus.focus_name || 'Foco sem nome',
            specialty_id: focus.specialty_id || '',
            theme_id: focus.theme_id || '',
            count: 0,
            percentage: 0
          };
          combinedFocuses.set(key, {
            ...current,
            count: current.count + focus.question_count
          });
        });
      }
    });

    // Recalcular percentuais
    combinedSpecialties.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    combinedThemes.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    combinedFocuses.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    // ✅ ORDENAÇÃO POR PREVALÊNCIA: Ordenar por percentual (maior para menor)
    const sortedSpecialties = Array.from(combinedSpecialties.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);

    const sortedThemes = Array.from(combinedThemes.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);

    const sortedFocuses = Array.from(combinedFocuses.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);



      return {
        specialties: sortedSpecialties,
        themes: sortedThemes,
        focuses: sortedFocuses,
        totalQuestions
      };
    } catch (error) {
      throw error;
    }
  };

  const generatePrevalenceBasedSchedule = async (
    combinedStats: any,
    institutionStats: any[],
    options: InstitutionScheduleOptions,
    themeMap: Map<string, any>,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {


    // Dados recebidos e processados

    const schedule: StudyTopic[] = [];
    const daysOfWeek = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

    // Calcular total de slots disponíveis
    const enabledDays = Object.entries(options.availableDays)
      .filter(([_, config]) => config.enabled);

    // ✅ CORREÇÃO: Expandir períodos baseado na duração do tópico
    const topicDurationMinutes = parseInt(options.topicDuration);
    const expandedEnabledDays = enabledDays.map(([day, config]) => {
      const expandedPeriods = [];

      for (const period of config.periods) {
        const startTime = period.startTime;
        const endTime = period.endTime;

        // Converter para minutos
        const startMinutes = startTime.split(':').map(Number).reduce((h, m) => h * 60 + m);
        const endMinutes = endTime.split(':').map(Number).reduce((h, m) => h * 60 + m);
        const totalMinutes = endMinutes - startMinutes;

        // Calcular quantos tópicos cabem neste período
        const topicsInPeriod = Math.floor(totalMinutes / topicDurationMinutes);

        // Período expandido: ${day} ${startTime}-${endTime} = ${topicsInPeriod} slots

        // Criar slots individuais para cada tópico
        for (let i = 0; i < topicsInPeriod; i++) {
          const slotStartMinutes = startMinutes + (i * topicDurationMinutes);
          const slotStartHours = Math.floor(slotStartMinutes / 60);
          const slotStartMins = slotStartMinutes % 60;
          const slotStartTime = `${slotStartHours.toString().padStart(2, '0')}:${slotStartMins.toString().padStart(2, '0')}`;

          expandedPeriods.push({
            startTime: slotStartTime,
            endTime: period.endTime // Manter o endTime original para referência
          });
        }
      }

      return [day, { ...config, periods: expandedPeriods }];
    });

    const totalSlots = expandedEnabledDays.reduce((total, [_, config]) => {
      return total + config.periods.length;
    }, 0) * options.weeksCount;



    onProgress?.(70);

    // Distribuir tópicos baseado na prevalência
    let currentSlot = 0;

    // ✅ CORREÇÃO: Calcular o próximo week_number baseado nas semanas existentes
    const { data: existingWeeks } = await supabase
      .from('study_schedules')
      .select('week_number')
      .eq('user_id', user.id)
      .order('week_number', { ascending: false })
      .limit(1);

    const nextWeekNumber = existingWeeks && existingWeeks.length > 0 ?
      existingWeeks[0].week_number + 1 : 1;



    // ✅ NOVO: Calcular disponibilidade de focos vs slots necessários (será atualizado após filtro)
    let availableFocusesCount = combinedStats.focuses.length;
    const slotsNeeded = totalSlots;
    let willRepeatFocuses = availableFocusesCount < slotsNeeded;

    // ✅ BUSCAR FOCOS JÁ UTILIZADOS para evitar repetição
    // Buscar todas as semanas do usuário primeiro
    const { data: userSchedules, error: schedulesError } = await supabase
      .from('study_schedules')
      .select('id')
      .eq('user_id', user.id);

    if (schedulesError) {
      // Erro silencioso
    }

    const scheduleIds = userSchedules?.map(s => s.id) || [];


    // Buscar focos já utilizados em todas as semanas
    let existingTopics = [];
    let existingError = null;

    if (scheduleIds.length > 0) {

      const result = await supabase
        .from('study_schedule_items')
        .select('focus_id')
        .in('schedule_id', scheduleIds)
        .not('focus_id', 'is', null);

      existingTopics = result.data || [];
      existingError = result.error;
    } else {

    }

    if (existingError) {
      // Erro silencioso
    }

    const usedFocusIds = new Set(existingTopics?.map(item => item.focus_id) || []);


    // ✅ FILTRAR FOCOS: Remover focos já utilizados da lista (mas permitir reutilização se necessário)
    let availableFocuses = combinedStats.focuses.filter(focus => !usedFocusIds.has(focus.focus_id));
    availableFocusesCount = availableFocuses.length;
    willRepeatFocuses = availableFocusesCount < slotsNeeded;

    // ✅ CORREÇÃO: Se não há focos únicos suficientes, permitir reutilização
    if (availableFocuses.length === 0 || (availableFocuses.length < slotsNeeded && slotsNeeded > 0)) {

      availableFocuses = combinedStats.focuses; // Usar todos os focos disponíveis
      willRepeatFocuses = true;
    }

    // ✅ ALGORITMO BASEADO EM FOCOS: Seguir ordem de prevalência dos focos não utilizados
    const validTopics = [];

    // Iterar pelos focos disponíveis (não utilizados) ordenados por prevalência
    for (const focus of availableFocuses) {
      if (validTopics.length >= totalSlots) break;

      // ✅ USAR HIERARQUIA REAL: Buscar especialidade e tema reais deste foco
      const realSpecialtyId = focus.specialty_id;
      const realThemeId = focus.theme_id;

      // ✅ CORREÇÃO: Usar themeMap em vez de combinedStats.themes
      const realSpecialty = combinedStats.specialties.find(s => s.specialty_id === realSpecialtyId);
      const realTheme = themeMap.get(realThemeId); // ← USAR themeMap que tem os nomes corretos



      if (!realSpecialty || !realTheme) {
        continue;
      }

      validTopics.push({
        specialty: realSpecialty,
        theme: realTheme,
        focus
      });
    }

    if (validTopics.length === 0) {
      console.error('❌ [generatePrevalenceBasedSchedule] NENHUM TÓPICO VÁLIDO CRIADO!');
      console.error('🔍 [generatePrevalenceBasedSchedule] Primeiro foco disponível:', availableFocuses[0]);
      console.error('🔍 [generatePrevalenceBasedSchedule] Especialidades disponíveis:', combinedStats.specialties.map(s => s.specialty_id));
      console.error('🔍 [generatePrevalenceBasedSchedule] Temas disponíveis:', combinedStats.themes.map(t => t.theme_id));
      throw new Error('Nenhum tópico válido foi criado. Verifique a hierarquia dos focos.');
    }

    // ✅ NOVO: Calcular slots válidos considerando dias passados na primeira semana
    const today = new Date();
    const currentDayOfWeek = today.getDay(); // 0 = domingo, 1 = segunda, etc.

    // Mapear dias da semana para números
    const dayNameToNumber = {
      'domingo': 0,
      'segunda-feira': 1,
      'terça-feira': 2,
      'quarta-feira': 3,
      'quinta-feira': 4,
      'sexta-feira': 5,
      'sábado': 6
    };

    // Calcular quantos slots são válidos na primeira semana
    let validSlotsInFirstWeek = 0;
    for (const [dayName] of expandedEnabledDays) {
      const dayNumber = dayNameToNumber[dayName as keyof typeof dayNameToNumber];
      // ✅ CORREÇÃO: Incluir o dia atual (>=) para permitir estudos no mesmo dia
      if (dayNumber >= currentDayOfWeek) {
        validSlotsInFirstWeek++;
      }
    }



    // ✅ CORREÇÃO: Calcular distribuição considerando slots perdidos na primeira semana
    const slotsLostInFirstWeek = expandedEnabledDays.length - validSlotsInFirstWeek;
    const totalValidSlots = totalSlots - slotsLostInFirstWeek;

    // ✅ REDISTRIBUIR: Manter o total de slots, redistribuindo os perdidos
    const slotsPerWeek = Math.floor(totalSlots / options.weeksCount);
    const extraSlots = totalSlots % options.weeksCount;

    // ✅ REUTILIZAR: Usar variáveis já declaradas acima
    // Calcular quantos slots pular na primeira semana (dias que já passaram)
    let slotsToSkipInFirstWeek = 0;
    for (const [dayName] of expandedEnabledDays) {
      const dayNumber = dayNameToNumber[dayName as keyof typeof dayNameToNumber];
      if (dayNumber < currentDayOfWeek) {
        slotsToSkipInFirstWeek++;
      } else {
        break; // Parar no primeiro dia futuro
      }
    }



    // ✅ NOVA LÓGICA: Distribuição inteligente considerando slots perdidos
    let currentSlotInWeek = 0;
    let currentWeekIndex = 0;
    let slotsUsedInCurrentWeek = 0;

    // ✅ CORREÇÃO: Criar tópicos para TODAS as semanas com reciclagem
    let topicIndex = 0; // Índice para reciclar tópicos

    for (let slotGlobal = 0; slotGlobal < totalSlots; slotGlobal++) {
      // ✅ RECICLAGEM: Se acabaram os tópicos únicos, recomeçar do início
      if (topicIndex >= validTopics.length) {
        topicIndex = 0;
      }

      const { specialty, theme, focus } = validTopics[topicIndex];
      topicIndex++; // Avançar para o próximo tópico

      // ✅ LÓGICA INTELIGENTE: Pular slots perdidos apenas na primeira semana
      if (currentWeekIndex === 0 && currentSlotInWeek < slotsToSkipInFirstWeek) {
        // Pular para o próximo slot válido na primeira semana
        currentSlotInWeek = slotsToSkipInFirstWeek;
      }

      // Verificar se precisamos mudar de semana
      if (slotsUsedInCurrentWeek >= slotsPerWeek) {
        currentWeekIndex++;
        currentSlotInWeek = 0;
        slotsUsedInCurrentWeek = 0;

        // Se chegamos ao limite de semanas, parar
        if (currentWeekIndex >= options.weeksCount) {
          break;
        }
      }

      const dayIndex = currentSlotInWeek % expandedEnabledDays.length;
      const [dayName, dayConfig] = expandedEnabledDays[dayIndex];
      const periodIndex = Math.floor(currentSlotInWeek / expandedEnabledDays.length) % dayConfig.periods.length;
      const period = dayConfig.periods[periodIndex];

      const topicWeekNumber = nextWeekNumber + currentWeekIndex;



      // ✅ NOVO: Calcular quais instituições contemplam este foco
      const focusInstitutions = institutionStats.filter(institution =>
        institution.focuses.some((f: any) => f.focus_id === focus.focus_id)
      ).map(institution => ({
        id: institution.institution_id,
        name: institution.institution_name,
        relevance: institution.focuses.find((f: any) => f.focus_id === focus.focus_id)?.question_count || 0,
        percentage: ((institution.focuses.find((f: any) => f.focus_id === focus.focus_id)?.question_count || 0) / institution.total_questions * 100).toFixed(1)
      })).sort((a, b) => b.relevance - a.relevance);



      schedule.push({
        id: crypto.randomUUID(),
        specialty: specialty.name,
        theme: theme.name,
        focus: focus.focus_name,
        specialtyId: specialty.specialty_id,
        themeId: theme.theme_id,
        focusId: focus.focus_id,
        difficulty: 'Médio',
        activity: `Estudo de ${specialty.name} - ${theme.name}`,
        startTime: period.startTime,
        duration: `${options.topicDuration} minutos`,
        day: dayName,
        weekNumber: topicWeekNumber, // ✅ CORREÇÃO: Usar weekNumber calculado
        is_manual: false,
        // ✅ NOVO: Informações das instituições
        institutions: focusInstitutions,
        focusPrevalence: focus.percentage.toFixed(2)
      });

      // ✅ INCREMENTAR: Atualizar contadores
      currentSlotInWeek++;
      slotsUsedInCurrentWeek++;
    }

    // Se ainda há slots disponíveis, usar focos restantes em ordem de prevalência
    if (schedule.length < totalSlots && validTopics.length < totalSlots) {
      // Focos insuficientes
    }

    // Calcular estatísticas de variedade
    const uniqueSpecialties = new Set(schedule.map(topic => topic.specialty));
    const uniqueThemes = new Set(schedule.map(topic => topic.theme));
    const uniqueFocuses = new Set(schedule.map(topic => topic.focus));

    // ✅ NOVO: Verificar distribuição por semana
    const weekDistribution = new Map();
    schedule.forEach(topic => {
      const count = weekDistribution.get(topic.weekNumber) || 0;
      weekDistribution.set(topic.weekNumber, count + 1);
    });






    onProgress?.(90);

    // ✅ ADICIONAR: Criar semanas e inserir tópicos no banco


    if (!user) {
      throw new Error('Usuário não autenticado');
    }

    // Criar semanas se necessário
    if (options.scheduleOption === "new") {

      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) throw new Error('User not authenticated');

      // Verificar qual é o próximo número de semana disponível
      const { data: existingWeeks, error: checkError } = await supabase
        .from('study_schedules')
        .select('week_number, week_end_date')
        .eq('user_id', authUser.id)
        .order('week_number', { ascending: false })
        .limit(1);

      if (checkError) {
        throw new Error(`Erro ao verificar semanas: ${checkError.message}`);
      }

      const nextWeekNumber = existingWeeks && existingWeeks.length > 0 ?
        existingWeeks[0].week_number + 1 : 1;

      const weeksToCreate = [];

      // ✅ CORREÇÃO: Começar sempre na próxima semana para evitar conflitos com dias passados
      let baseDate = new Date();

      // Se já existem semanas, começar após a última
      if (existingWeeks && existingWeeks.length > 0) {
        const lastWeek = existingWeeks[0]; // existingWeeks já está ordenado desc
        const lastEndDate = new Date(lastWeek.week_end_date + 'T12:00:00Z');
        baseDate = new Date(lastEndDate);
        baseDate.setUTCDate(lastEndDate.getUTCDate() + 1); // Próximo domingo
      } else {
        // ✅ CORREÇÃO: Se não há semanas existentes, começar no domingo da semana atual (igual à criação manual)
        const today = new Date();
        const dayOfWeek = today.getDay();
        baseDate.setDate(today.getDate() - dayOfWeek); // Voltar para o domingo da semana atual
      }

      for (let i = 0; i < options.weeksCount; i++) {
        // Calcular a data base para esta semana específica
        const weekBaseDate = new Date(baseDate);
        if (i > 0) {
          weekBaseDate.setDate(baseDate.getDate() + (i * 7));
        }

        // ✅ CORREÇÃO: Usar getWeekDates para garantir domingo-sábado
        const { startDate: weekStart, endDate: weekEnd } = getWeekDates(weekBaseDate, false);

        const weekToCreate = {
          user_id: authUser.id,
          week_number: nextWeekNumber + i,
          week_start_date: weekStart.toISOString().split('T')[0],
          week_end_date: weekEnd.toISOString().split('T')[0],
          status: 'active'
        };



        weeksToCreate.push(weekToCreate);
      }



      const { data: createdWeeks, error: weeksError } = await supabase
        .from('study_schedules')
        .insert(weeksToCreate)
        .select('*');

      if (weeksError) {
        console.error('❌ [generatePrevalenceBasedSchedule] Erro ao criar semanas:', weeksError);
        throw new Error(`Erro ao criar semanas: ${weeksError.message}`);
      }


    }

    // Buscar semanas alvo
    const { data: targetSchedules, error: targetSchedulesError } = await supabase
      .from('study_schedules')
      .select('*')
      .eq('user_id', user.id)
      .order('week_number', { ascending: true });

    if (targetSchedulesError) {
      throw new Error(`Erro ao buscar semanas: ${targetSchedulesError.message}`);
    }

    if (!targetSchedules || targetSchedules.length === 0) {
      throw new Error('Nenhuma semana encontrada para inserir os tópicos');
    }



    // ✅ SOLUÇÃO DEFINITIVA: Verificar se um dia já passou na primeira semana
    const isDayInPast = (dayOfWeek: string, weekNumber: number) => {
      if (weekNumber !== 1) return false; // Só verificar na primeira semana

      // ✅ NOVO: Se o cronograma começa na próxima semana, nenhum dia está no passado
      const firstWeekSchedule = targetSchedules.find(s => s.week_number === 1);
      if (firstWeekSchedule) {
        const firstWeekStart = new Date(firstWeekSchedule.week_start_date + 'T00:00:00Z');
        const today = new Date();

        // Se a primeira semana é futura, nenhum dia está no passado
        if (firstWeekStart > today) {
          return false;
        }
      }

      const today = new Date();
      const currentDayOfWeek = today.getDay(); // 0 = domingo, 1 = segunda, etc.

      // Verificar dia atual

      // Mapear dias da semana para números (suportar múltiplos formatos)
      const dayMap: { [key: string]: number } = {
        'domingo': 0, 'segunda-feira': 1, 'terça-feira': 2, 'quarta-feira': 3,
        'quinta-feira': 4, 'sexta-feira': 5, 'sábado': 6,
        'segunda': 1, 'terça': 2, 'quarta': 3, 'quinta': 4, 'sexta': 5,
        'seg': 1, 'ter': 2, 'qua': 3, 'qui': 4, 'sex': 5, 'sab': 6, 'dom': 0
      };

      const dayNumber = dayMap[dayOfWeek.toLowerCase()];
      if (dayNumber === undefined) {
        console.warn(`⚠️ [isDayInPast] Dia não reconhecido: "${dayOfWeek}"`);
        return false; // Se não reconhecer o dia, não filtrar
      }

      // ✅ LÓGICA SIMPLES: Se o dia da semana é menor que o dia atual, já passou
      const isPast = dayNumber < currentDayOfWeek;

      // ✅ LIMPEZA: Log removido - operação rotineira

      return isPast;
    };

    // ✅ FILTRAR: Remover tópicos de dias passados na primeira semana
    const filteredSchedule = schedule.filter(topic => {
      const dayHasPassed = isDayInPast(topic.day, topic.weekNumber);
      return !dayHasPassed;
    });



    // Inserir tópicos no banco (usando schedule filtrado)
    const itemsToInsert = filteredSchedule.map(topic => {
      // ✅ CORREÇÃO: Usar a última semana criada (maior week_number) para novos tópicos
      const targetSchedule = targetSchedules.find(s => s.week_number === topic.weekNumber) ||
                             targetSchedules.sort((a, b) => b.week_number - a.week_number)[0];



      return {
        schedule_id: targetSchedule.id,
        day_of_week: topic.day.toLowerCase(),
        topic: `${topic.specialty} - ${topic.theme}`,
        specialty_name: topic.specialty,
        specialty_id: topic.specialtyId,
        theme_name: topic.theme,
        theme_id: topic.themeId,
        focus_name: topic.focus,
        focus_id: topic.focusId,
        difficulty: topic.difficulty,
        activity_description: topic.activity,
        start_time: topic.startTime,
        duration: topic.duration,
        type: 'study',
        activity_type: 'study',
        week_number: topic.weekNumber,
        study_status: 'pending',
        // ✅ NOVO: Salvar dados das instituições no metadata
        metadata: {
          institutions: topic.institutions || [],
          focusPrevalence: topic.focusPrevalence,
          isPersonalized: true,
          generationMode: 'institution_based'
        }
      };
    });



    const { data: insertedItems, error: insertError } = await supabase
      .from('study_schedule_items')
      .insert(itemsToInsert)
      .select('*');

    if (insertError) {
      console.error(`❌ [generatePrevalenceBasedSchedule] Erro na inserção:`, insertError);
      throw new Error(`Erro ao inserir tópicos: ${insertError.message}`);
    }

    // ✅ NOVO: Forçar invalidação completa do cache após inserção

    // Invalidar TODOS os caches relacionados
    await queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey[0];
        return key === 'consolidated-schedule-data' ||
               key === 'processed-schedule-data' ||
               key === 'schedule' ||
               key === 'study_schedules' ||
               key === 'study_schedule_items';
      },
      refetchType: 'all'
    });

    // ✅ CORREÇÃO: Invalidação mais agressiva e sequencial
    try {
      // Aguardar e forçar refetch sequencial
      await new Promise(resolve => setTimeout(resolve, 200));

      await queryClient.refetchQueries({
        queryKey: ['schedule', user.id]
      });

      // Aguardar mais um pouco e refetch novamente para garantir
      await new Promise(resolve => setTimeout(resolve, 300));

      await queryClient.refetchQueries({
        predicate: (query) => {
          const key = query.queryKey[0];
          return key === 'schedule' || key === 'study_schedules';
        }
      });

    } catch (error) {
      console.error(`❌ [generatePrevalenceBasedSchedule] Erro na invalidação:`, error);
    }

    // ✅ NOVO: Adicionar informações de repetição ao filteredSchedule para uso posterior
    filteredSchedule.forEach(topic => {
      (topic as any).focusRepetitionWarning = willRepeatFocuses;
      (topic as any).availableFocusesCount = availableFocusesCount;
      (topic as any).totalSlotsCount = slotsNeeded;
    });

    return filteredSchedule;
  };

  return {
    generateScheduleByInstitution,
    isLoading,
    error
  };
};
