/**
 * Helper para importações dinâmicas com fallback e retry
 * Resolve problemas comuns de chunk loading failures
 */

interface RetryOptions {
  maxRetries?: number;
  delay?: number;
  fallback?: () => Promise<any>;
}

/**
 * Importação dinâmica com retry automático
 */
export const dynamicImportWithRetry = async <T>(
  importFn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  const { maxRetries = 3, delay = 1000, fallback } = options;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await importFn();
    } catch (error) {
      console.warn(`🔄 [DynamicImport] Tentativa ${attempt}/${maxRetries} falhou:`, error);
      
      // Se é o último attempt e há fallback, usar fallback
      if (attempt === maxRetries && fallback) {
        console.log('🔄 [DynamicImport] Usando fallback...');
        return await fallback();
      }
      
      // Se não é o último attempt, aguardar e tentar novamente
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
        
        // Cache busting: adicionar timestamp para forçar reload
        if (typeof importFn === 'function') {
          // Limpar cache do módulo se possível
          if ('webpackChunkName' in importFn) {
            console.log('🔄 [DynamicImport] Limpando cache do webpack...');
          }
        }
      } else {
        // Último attempt falhou, re-throw o erro
        throw new Error(`Failed to load module after ${maxRetries} attempts: ${error.message}`);
      }
    }
  }
  
  throw new Error('Unexpected error in dynamic import retry logic');
};

/**
 * Wrapper para React.lazy com retry automático
 */
export const lazyWithRetry = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  return React.lazy(() => 
    dynamicImportWithRetry(importFn, {
      maxRetries: 3,
      delay: 1000,
      fallback: fallback ? () => Promise.resolve({ default: fallback }) : undefined
    })
  );
};

/**
 * Cache busting para forçar reload de chunks
 */
export const clearChunkCache = () => {
  // Limpar cache do service worker se disponível
  if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
    navigator.serviceWorker.controller.postMessage({ type: 'CLEAR_CACHE' });
  }
  
  // Limpar cache do localStorage relacionado a chunks
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.includes('chunk') || key.includes('webpack') || key.includes('vite')) {
      localStorage.removeItem(key);
    }
  });
  
  console.log('🧹 [DynamicImport] Cache de chunks limpo');
};

/**
 * Detectar se o erro é relacionado a chunk loading
 */
export const isChunkLoadError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('loading chunk') ||
    errorMessage.includes('failed to fetch') ||
    errorMessage.includes('dynamically imported module') ||
    errorMessage.includes('loading css chunk')
  );
};

/**
 * Handler global para erros de chunk loading
 */
export const setupChunkErrorHandler = () => {
  // Interceptar erros de chunk loading
  window.addEventListener('error', (event) => {
    if (isChunkLoadError(event.error)) {
      console.warn('🚨 [DynamicImport] Chunk loading error detectado:', event.error);
      
      // Tentar limpar cache e recarregar
      clearChunkCache();
      
      // Mostrar notificação amigável ao usuário
      if (confirm('Houve um problema ao carregar a página. Deseja recarregar?')) {
        window.location.reload();
      }
    }
  });
  
  // Interceptar erros de promise rejection não tratados
  window.addEventListener('unhandledrejection', (event) => {
    if (isChunkLoadError(event.reason)) {
      console.warn('🚨 [DynamicImport] Promise rejection por chunk loading:', event.reason);
      event.preventDefault(); // Prevenir log de erro no console
    }
  });
};

import React from 'react';
