import { useState, useEffect } from "react";
import type { FlashcardWithHierarchy } from "@/components/collaborate/flashcards/types";

export const useFlashcardState = (flashcards: FlashcardWithHierarchy[]) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [sessionStatus, setSessionStatus] = useState<'in_progress' | 'completed'>('in_progress');

  const currentCard = flashcards[currentIndex];

  // ✅ Preload dinâmico quando o índice muda
  useEffect(() => {
    if (flashcards.length > 0 && currentIndex >= 0) {
      preloadNextImages(flashcards, currentIndex + 1, 3);
    }
  }, [currentIndex, flashcards]);

  // ✅ Função para preload dinâmico de imagens
  const preloadNextImages = (cards: FlashcardWithHierarchy[], startIndex: number, count: number) => {
    const imagesToPreload: string[] = [];

    // ✅ Preload próximos cards a partir do índice
    for (let i = startIndex; i < Math.min(startIndex + count, cards.length); i++) {
      const card = cards[i];
      if (card.front_image) imagesToPreload.push(card.front_image);
      if (card.back_image) imagesToPreload.push(card.back_image);
    }

    if (imagesToPreload.length > 0) {
      // ✅ Preload assíncrono sem bloquear UI
      imagesToPreload.forEach(imageUrl => {
        const img = new Image();
        img.src = imageUrl;
      });
    }
  };

  const advanceCard = () => {
    if (currentIndex < flashcards.length - 1) {
      setCurrentIndex(prev => prev + 1);
      setIsFlipped(false);
    } else {
      setSessionStatus('completed');
    }
  };

  return {
    currentIndex,
    setCurrentIndex,
    isFlipped,
    setIsFlipped,
    sessionStatus,
    setSessionStatus,
    currentCard,
    advanceCard,
  };
};