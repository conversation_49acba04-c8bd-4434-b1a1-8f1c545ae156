
import React from "react";
import { motion } from "framer-motion";
import { ArrowR<PERSON>, Check, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";

interface CallToActionProps {
  onStartClick: () => void;
}

export function CallToAction({ onStartClick }: CallToActionProps) {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleStartClick = () => {
    if (user) {
      navigate("/plataformadeestudos");
    } else {
      onStartClick();
    }
  };

  const features = [
    "Plataforma 100% gratuita",
    "Acesso completo ao banco de questões de oftalmologia",
    "Questões comentadas por especialistas"
  ];

  const benefits = [
    { title: "Banco de questões", description: "Questões comentadas por oftalmologistas" },
    { title: "Revisões sistemáticas", description: "Consolide seu conhecimento em oftalmologia" },
    { title: "Interface intuitiva", description: "Fácil de usar em qualquer dispositivo" }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-blue-50 to-blue-100">
      <div className="container mx-auto">
        <motion.div 
          className="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col md:flex-row">
            <div className="flex-1 p-8 md:p-12">
              <h2 className="text-2xl md:text-3xl font-bold mb-4 text-primary">
                Pronto para aprimorar seus conhecimentos em oftalmologia?
              </h2>
              <p className="text-lg mb-6 text-gray-600">
                Acesse nossa plataforma de estudos e tenha questões comentadas por especialistas em oftalmologia.
              </p>
              <ul className="space-y-3 mb-8">
                {features.map((feature, index) => (
                  <motion.li 
                    key={index}
                    className="flex items-center gap-3"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
                      <ArrowRight className="w-3 h-3 text-primary" />
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </motion.li>
                ))}
              </ul>
              <Button 
                onClick={handleStartClick}
                size="lg" 
                className="bg-primary hover:bg-primary/90 text-white px-8 py-6 rounded-lg group"
              >
                <span>Começar Agora</span>
                <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Button>
              
              <p className="mt-4 text-sm text-gray-500">
                Veja a prévia de como funciona o sistema na parte superior da página
              </p>
            </div>
            <div className="flex-1 bg-gradient-to-br from-primary to-blue-600 p-8 md:p-12 text-white flex items-center">
              <div>
                <h3 className="text-xl md:text-2xl font-bold mb-4">
                  O que você vai receber:
                </h3>
                <ul className="space-y-4">
                  {benefits.map((benefit, index) => (
                    <motion.li 
                      key={index}
                      className="flex gap-3"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: 0.3 + (index * 0.1) }}
                    >
                      <div className="mt-0.5">
                        <Check className="w-5 h-5 text-blue-300" />
                      </div>
                      <div>
                        <p className="font-semibold">{benefit.title}</p>
                        <p className="text-white/80 text-sm">{benefit.description}</p>
                      </div>
                    </motion.li>
                  ))}
                </ul>
                
                <motion.div 
                  className="mt-6 bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/20"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                >
                  <p className="text-sm">
                    <span className="font-medium">Tópicos especializados:</span>{" "}
                    <span className="text-blue-300">Catarata</span>, {" "}
                    <span className="text-blue-300">Glaucoma</span>, {" "}
                    <span className="text-blue-300">Retina</span>{" "}
                    <span className="mx-1">|</span>{" "}
                    <span className="text-blue-300">Estrabismo</span>
                  </p>
                </motion.div>
                
                <motion.div 
                  className="mt-6 flex items-center gap-2"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: 0.8 }}
                >
                  <div className="text-sm bg-white/20 px-3 py-1 rounded-full">
                    Banco de questões atualizado
                  </div>
                  <div className="text-sm bg-white/20 px-3 py-1 rounded-full">
                    Acesso imediato
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
