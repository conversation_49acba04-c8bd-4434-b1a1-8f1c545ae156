import React from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  BookOpen, 
  CheckCircle, 
  Circle, 
  Calendar,
  Target,
  X
} from 'lucide-react';
import type { StudyTopic } from '@/types/study-schedule';

interface StudyTopicDialogProps {
  topic: StudyTopic | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onMarkAsStudied?: (topic: StudyTopic) => void;
  onMarkAsPending?: (topicId: string) => void;
}

const StudyTopicDialog: React.FC<StudyTopicDialogProps> = ({
  topic,
  open,
  onOpenChange,
  onMarkAsStudied,
  onMarkAsPending
}) => {
  if (!topic) return null;

  // Função para obter cor baseada na dificuldade
  const getDifficultyColor = (difficulty: string): string => {
    switch (difficulty?.toLowerCase()) {
      case 'fácil':
      case 'easy':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'médio':
      case 'medium':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'difícil':
      case 'hard':
        return 'bg-red-100 text-red-700 border-red-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  // Função para formatar duração
  const formatDuration = (duration: string): string => {
    const match = duration?.match(/(\d+)/);
    return match ? `${match[1]} minutos` : duration || '';
  };

  const isCompleted = topic.studyStatus === 'completed' || topic.study_status === 'completed';

  const handleToggleStatus = () => {
    if (isCompleted) {
      onMarkAsPending?.(topic.id);
      onOpenChange(false);
    } else {
      onMarkAsStudied?.(topic);
      // Não fechar aqui, deixar o TimelineWeekView gerenciar
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md border border-black shadow-xl">
        {/* Botão de fechar no canto superior direito */}
        <button
          onClick={() => onOpenChange(false)}
          className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 transition-colors duration-200 z-20"
          aria-label="Fechar"
        >
          <X className="h-4 w-4 text-gray-500 hover:text-gray-700" />
        </button>

        <DialogHeader className="pb-4 pr-12">
          <div className="flex items-center gap-3 mb-3">
            <div className={`p-3 rounded-xl border-2 ${getDifficultyColor(topic.difficulty)}`}>
              <BookOpen className="h-5 w-5" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-lg font-bold text-gray-800 leading-tight">
                {topic.focusName || topic.focus}
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-600 mt-1">
                {topic.specialtyName || topic.specialty}
              </DialogDescription>
            </div>
          </div>

          {/* Badges de informação */}
          <div className="flex flex-wrap gap-2">
            <Badge className={`px-2 py-1 text-xs font-medium border ${getDifficultyColor(topic.difficulty)}`}>
              {topic.difficulty === 'Fácil' ? 'Fácil' : 
               topic.difficulty === 'Médio' ? 'Médio' : 'Difícil'}
            </Badge>
            
            {isCompleted && (
              <Badge className="px-2 py-1 bg-green-100 text-green-700 border-green-200 text-xs font-medium">
                <CheckCircle className="h-3 w-3 mr-1" />
                Concluído
              </Badge>
            )}
          </div>
        </DialogHeader>

        {/* Informações detalhadas */}
        <div className="space-y-4">
          {/* Horário e duração */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <div>
                  <div className="text-xs text-blue-600 font-medium">Horário</div>
                  <div className="text-sm font-bold text-blue-800">
                    {topic.startTime || '08:00:00'}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-blue-600" />
                <div>
                  <div className="text-xs text-blue-600 font-medium">Duração</div>
                  <div className="text-sm font-bold text-blue-800">
                    {formatDuration(topic.duration)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Atividade */}
          {topic.activity && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <div className="text-xs text-gray-600 font-medium mb-1">Atividade</div>
              <div className="text-sm text-gray-800">
                {topic.activity}
              </div>
            </div>
          )}

          {/* Tema */}
          {(topic.themeName || topic.theme) && (
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
              <div className="text-xs text-purple-600 font-medium mb-1">Tema</div>
              <div className="text-sm text-purple-800 font-medium">
                {topic.themeName || topic.theme}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="mt-6 flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 sm:flex-none border-gray-300 hover:bg-gray-50"
          >
            Fechar
          </Button>
          
          <Button
            onClick={handleToggleStatus}
            className={`flex-1 sm:flex-none transition-all duration-200 ${
              isCompleted
                ? 'bg-gray-500 hover:bg-gray-600 text-white'
                : 'bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl'
            }`}
          >
            {isCompleted ? (
              <>
                <Circle className="h-4 w-4 mr-2" />
                Marcar como Pendente
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Marcar como Estudado
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StudyTopicDialog;
