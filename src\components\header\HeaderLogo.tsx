
import { Link } from "react-router-dom";
import { useDarkMode } from "@/contexts/DarkModeContext";

export const HeaderLogo = () => {
  const { isDarkMode } = useDarkMode();

  return (
    <Link to="/" className="flex items-center gap-2 group shrink-0">
      <img
        src="/logomedevo.webp"
        alt="MedEvo Logo"
        className="w-8 h-8 object-contain"
      />
      <div className="flex flex-col">
        <span className={`text-xl font-black transition-colors duration-200 ${
          isDarkMode ? 'text-gray-200' : 'text-black'
        }`}>
          MedEvo
        </span>
        <span className={`text-xs font-medium -mt-1 transition-colors duration-200 ${
          isDarkMode ? 'text-gray-400' : 'text-gray-600'
        }`}>
          Plataforma de Estudos
        </span>
      </div>
    </Link>
  );
};
