
import { useState, useMemo, useEffect } from "react";
import type { FlashcardWithHierarchy } from "@/types/flashcard";
import { FolderCard } from "./FolderCard";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { FlashcardPreviewCard } from "./FlashcardPreviewCard";
import { supabase } from "@/integrations/supabase/client";
import { ensureUserId } from "@/utils/ensureUserId";
import { safeFlashcardQuery } from "@/utils/flashcardQueryUtils";
import { useFlashcardLoader } from "@/hooks/flashcards/useFlashcardLoader";

interface HierarchyNode {
  id: string;
  name: string;
  level: number;
  children?: HierarchyNode[];
  flashcards?: FlashcardWithHierarchy[];
}

type SelectionPath = {
  specialty?: string;
  theme?: string;
  focus?: string;
  extraFocus?: string;
};

interface MyFlashcardsHierarchyProps {
  onDelete: (id: string) => Promise<void>;
}

function buildHierarchy(cards: FlashcardWithHierarchy[]): HierarchyNode[] {
  const specialties: {[id: string]: HierarchyNode} = {};

  for (const card of cards) {
    const sId = card.hierarchy.specialty?.id || "indefinido";
    const sName = card.hierarchy.specialty?.name || "Indefinido";

    if (!specialties[sId]) {
      specialties[sId] = { id: sId, name: sName, level: 0, children: [], flashcards: [] };
    }

    if (card.hierarchy.theme) {
      const tId = card.hierarchy.theme.id;
      const tName = card.hierarchy.theme.name || "Tema sem nome";

      let themeNode = specialties[sId].children!.find(t => t.id === tId);
      if (!themeNode) {
        themeNode = { id: tId, name: tName, level: 1, children: [], flashcards: [] };
        specialties[sId].children!.push(themeNode);
      }
      if (card.hierarchy.focus) {
        const fId = card.hierarchy.focus.id;
        const fName = card.hierarchy.focus.name || "Foco sem nome";

        let focusNode = themeNode.children!.find(f => f.id === fId);
        if (!focusNode) {
          focusNode = { id: fId, name: fName, level: 2, children: [], flashcards: [] };
          themeNode.children!.push(focusNode);
        }
        if (card.hierarchy.extraFocus) {
          const eId = card.hierarchy.extraFocus.id;
          const eName = card.hierarchy.extraFocus.name || "Foco extra sem nome";
          let extraNode = focusNode.children!.find(e => e.id === eId);
          if (!extraNode) {
            extraNode = { id: eId, name: eName, level: 3, flashcards: [] };
            (focusNode.children as HierarchyNode[]).push(extraNode);
          }
          (extraNode.flashcards ||= []).push(card);
        } else {
          (focusNode.flashcards ||= []).push(card);
        }
      } else {
        (themeNode.flashcards ||= []).push(card);
      }
    } else {
      (specialties[sId].flashcards ||= []).push(card);
    }
  }

  const hierarchyNodes = Object.values(specialties);
  return hierarchyNodes;
}

export const MyFlashcardsHierarchy = ({ onDelete }: MyFlashcardsHierarchyProps) => {
  const [selection, setSelection] = useState<SelectionPath>({});

  // ✅ Usar useFlashcardLoader para carregar dados com hierarquia atualizada
  const { data: loadedFlashcards, isLoading, error } = useFlashcardLoader();
  const [flashcards, setFlashcards] = useState<FlashcardWithHierarchy[]>([]);

  // ✅ Atualizar flashcards quando dados carregarem
  useEffect(() => {
    if (loadedFlashcards) {
      setFlashcards(loadedFlashcards);
    }
  }, [loadedFlashcards]);

  const hierarchy = useMemo(() => buildHierarchy(flashcards), [flashcards]);

  function getCurrentLayerAndFlashcards(): { nodes: HierarchyNode[], flashcards: FlashcardWithHierarchy[] } {
    let nodes = hierarchy;
    let currentFlashcards: FlashcardWithHierarchy[] = [];

    const filterCardsByCurrentLevel = () => {
      if (!selection.specialty) {
        return [];
      }

      if (selection.specialty && !selection.theme) {
        const s = nodes.find(n => n.id === selection.specialty);
        return s?.flashcards || [];
      }

      if (selection.theme && !selection.focus) {
        const s = nodes.find(n => n.id === selection.specialty);
        if (!s) return [];
        const t = s.children?.find(n => n.id === selection.theme);
        return t?.flashcards || [];
      }

      if (selection.focus && !selection.extraFocus) {
        const s = nodes.find(n => n.id === selection.specialty);
        if (!s) return [];
        const t = s.children?.find(n => n.id === selection.theme);
        if (!t) return [];
        const f = t.children?.find(n => n.id === selection.focus);
        return f?.flashcards || [];
      }

      if (selection.extraFocus) {
        const s = nodes.find(n => n.id === selection.specialty);
        if (!s) return [];
        const t = s.children?.find(n => n.id === selection.theme);
        if (!t) return [];
        const f = t.children?.find(n => n.id === selection.focus);
        if (!f) return [];
        const e = f.children?.find(n => n.id === selection.extraFocus);
        return e?.flashcards || [];
      }

      return [];
    };

    if (selection.specialty) {
      const s = nodes.find(n=>n.id === selection.specialty);
      if (!s) return { nodes: [], flashcards: [] };

      currentFlashcards = filterCardsByCurrentLevel();

      if (selection.theme) {
        const t = s.children?.find(n => n.id === selection.theme);
        if (!t) return { nodes: [], flashcards: currentFlashcards };

        if (selection.focus) {
          const f = t.children?.find(n=>n.id === selection.focus);
          if (!f) return { nodes: [], flashcards: currentFlashcards };

          if (selection.extraFocus) {
            const e = f.children?.find(n=>n.id === selection.extraFocus);
            return e
              ? { nodes: [], flashcards: currentFlashcards }
              : { nodes: [], flashcards: currentFlashcards };
          }
          return { nodes: f.children ?? [], flashcards: currentFlashcards };
        }
        return { nodes: t.children ?? [], flashcards: currentFlashcards };
      }
      return { nodes: s.children ?? [], flashcards: currentFlashcards };
    }
    return { nodes, flashcards: currentFlashcards };
  }

  function handleSelectNode(node: HierarchyNode) {
    setSelection(sel => {
      if (node.level === 0) return { specialty: node.id };
      if (node.level === 1) return { specialty: sel.specialty, theme: node.id };
      if (node.level === 2) return { specialty: sel.specialty, theme: sel.theme, focus: node.id };
      if (node.level === 3) return { specialty: sel.specialty, theme: sel.theme, focus: sel.focus, extraFocus: node.id };
      return {};
    });
  }

  function handleBack() {
    if (selection.extraFocus) setSelection(s => ({ ...s, extraFocus: undefined }));
    else if (selection.focus) setSelection(s => ({ ...s, focus: undefined }));
    else if (selection.theme) setSelection(s => ({ ...s, theme: undefined }));
    else if (selection.specialty) setSelection({});
  }

  async function handleDeleteFlashcard(id: string) {
    try {
      const userId = await ensureUserId();

      // ✅ Verificar permissão ANTES da deleção usando maybeSingle para evitar erro 406
      const { data: card, error: cardError } = await supabase
        .from('flashcards_cards')
        .select('user_id')
        .eq('id', id)
        .maybeSingle();

      if (cardError) {
        console.error("❌ [MyFlashcardsHierarchy] Erro ao buscar card:", cardError);
        throw new Error("Não foi possível verificar o flashcard");
      }

      if (!card) {
        console.log("ℹ️ [MyFlashcardsHierarchy] Card já foi deletado");
        setFlashcards(prev => prev.filter(fc => fc.id !== id));
        return;
      }

      if (card.user_id !== userId) {
        console.error("❌ [MyFlashcardsHierarchy] Usuário não autorizado a excluir este card");
        throw new Error("Você só pode excluir seus próprios flashcards");
      }

      const { error: sessionCardsError } = await supabase
        .from('flashcards_session_cards')
        .delete()
        .eq('card_id', id);

      if (sessionCardsError) {
        console.error("❌ [MyFlashcardsHierarchy] Erro ao deletar session_cards:", sessionCardsError);
        // Continue anyway, treat as non-fatal
      }

      const { error: reviewsError } = await supabase
        .from('flashcards_reviews')
        .delete()
        .eq('card_id', id);

      if (reviewsError) {
        console.error("❌ [MyFlashcardsHierarchy] Erro ao deletar reviews:", reviewsError);
        // Continue anyway, treat as non-fatal
      }

      const { error: deleteError } = await supabase
        .from('flashcards_cards')
        .delete()
        .eq('id', id);

      if (deleteError) {
        console.error("❌ [MyFlashcardsHierarchy] Erro ao deletar flashcard:", deleteError);
        throw deleteError;
      }



      setFlashcards(prev => prev.filter(fc => fc.id !== id));

      if (onDelete) {
        await onDelete(id);
      }

    } catch (error) {
      console.error("❌ [MyFlashcardsHierarchy] Erro ao excluir flashcard:", error);
      throw error;
    }
  }

  async function handleUpdateFlashcard(updated: FlashcardWithHierarchy) {
    const { front, back, front_image, back_image } = updated;
    await supabase
      .from('flashcards_cards')
      .update({ front, back, front_image, back_image })
      .eq('id', updated.id);

    setFlashcards(flashcards =>
      flashcards.map(fc => fc.id === updated.id
        ? { ...fc, front, back, front_image, back_image }
        : fc
      )
    );
  }

  const { nodes, flashcards: currentFlashcards } = getCurrentLayerAndFlashcards();

  const LEVEL_LABELS = ["Especialidades", "Temas", "Focos", "Extra Focos"];
  const currentLevel =
    selection.extraFocus ? 3
    : selection.focus ? 2
    : selection.theme ? 1
    : selection.specialty ? 0
    : -1;

  const getCurrentFolderName = () => {
    if (selection.extraFocus) {
      const s = hierarchy.find(n => n.id === selection.specialty);
      if (!s) return "Pasta";
      const t = s.children?.find(n => n.id === selection.theme);
      if (!t) return "Pasta";
      const f = t.children?.find(n => n.id === selection.focus);
      if (!f) return "Pasta";
      const e = f.children?.find(n => n.id === selection.extraFocus);
      return e?.name || "Pasta";
    } else if (selection.focus) {
      const s = hierarchy.find(n => n.id === selection.specialty);
      if (!s) return "Pasta";
      const t = s.children?.find(n => n.id === selection.theme);
      if (!t) return "Pasta";
      const f = t.children?.find(n => n.id === selection.focus);
      return f?.name || "Pasta";
    } else if (selection.theme) {
      const s = hierarchy.find(n => n.id === selection.specialty);
      if (!s) return "Pasta";
      const t = s.children?.find(n => n.id === selection.theme);
      return t?.name || "Pasta";
    } else if (selection.specialty) {
      const s = hierarchy.find(n => n.id === selection.specialty);
      return s?.name || "Pasta";
    }
    return LEVEL_LABELS[0];
  };

  // ✅ Mostrar loading enquanto carrega
  if (isLoading) {
    return (
      <div className="w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <div key={i} className="h-32 w-full rounded-xl bg-gray-100 animate-pulse border-2 border-gray-200"></div>
          ))}
        </div>
      </div>
    );
  }

  // ✅ Mostrar erro se houver
  if (error) {
    return (
      <div className="w-full text-center p-8">
        <div className="text-red-500 mb-2">Erro ao carregar flashcards</div>
        <div className="text-gray-500 text-sm">{error.message}</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-5 flex items-center">
        {currentLevel > -1 && (
          <Button variant="outline" size="sm" onClick={handleBack} className="mr-3 flex items-center gap-1">
            <ChevronLeft className="h-4 w-4" /> Voltar
          </Button>
        )}
        <span className="font-bold text-lg text-gray-700">
          {getCurrentFolderName()}
        </span>
      </div>

      {nodes.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-6">
          {nodes.map(node => (
            <FolderCard
              key={node.id}
              name={node.name}
              onClick={() => handleSelectNode(node)}
              className="h-full"
            />
          ))}
        </div>
      )}

      {currentFlashcards.length > 0 && (
        <div>
          <h3 className="text-md font-medium mb-3 text-gray-600">
            Flashcards {currentLevel > -1 ? "nesta pasta" : ""}
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {currentFlashcards.map(fc =>
              <FlashcardPreviewCard
                key={fc.id}
                card={fc}
                onDelete={handleDeleteFlashcard}
                onUpdate={handleUpdateFlashcard}
              />
            )}
          </div>
        </div>
      )}

      {nodes.length === 0 && currentFlashcards.length === 0 && !isLoading && (
        <div className="text-gray-400 text-center p-8 border border-dashed rounded-lg">
          Nenhum flashcard ou pasta neste local.
        </div>
      )}
    </div>
  );
};
