import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { FlashcardSessionContent } from "./FlashcardSessionContent";
import { FlashcardSessionComplete } from "./FlashcardSessionComplete";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { toast } from "sonner";
import type { FlashcardWithHierarchy } from "@/types/flashcardCollaborate";

const BATCH_SIZE = 50;

export const FlashcardSession = () => {
  const { sessionId } = useParams();
  const [flashcards, setFlashcards] = useState<FlashcardWithHierarchy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCompleted, setIsCompleted] = useState(false);

  useEffect(() => {
    const loadSessionFlashcards = async () => {
      try {


        if (!sessionId) {
          throw new Error("No session ID provided");
        }

        const { data: session, error: sessionError } = await supabase
          .from("flashcards_sessions")
          .select(`
            *,
            flashcards_session_cards(
              card_id,
              response,
              review_status
            )
          `)
          .eq("id", sessionId)
          .single();

        if (sessionError) {
          console.error("❌ [FlashcardSession] Erro ao buscar sessão:", sessionError);
          throw sessionError;
        }



        setIsCompleted(session.status === "completed");

        if (!session.cards?.length) {
          console.warn("⚠️ [FlashcardSession] Nenhum card encontrado na sessão");
          throw new Error("No cards found in session");
        }

        const answeredCardIds = new Set(
          session.flashcards_session_cards
            ?.filter(card => card.review_status === 'reviewed')
            .map(card => card.card_id) || []
        );



        const unansweredCardIds = session.cards
          .filter(id => {
            const isValid = id && typeof id === 'string' && id.trim() !== '';
            const isNotAnswered = !answeredCardIds.has(id);
            return isValid && isNotAnswered;
          });

        if (unansweredCardIds.length === 0) {
          setIsCompleted(true);
          setFlashcards([]);
          setIsLoading(false);
          return;
        }

        let allCards: FlashcardWithHierarchy[] = [];

        for (let i = 0; i < unansweredCardIds.length; i += BATCH_SIZE) {
          const batchIds = unansweredCardIds.slice(i, i + BATCH_SIZE);



          // ✅ Verificar se batchIds não está vazio e não contém undefined
          if (batchIds.length === 0 || batchIds.some(id => !id || typeof id !== 'string' || id.trim() === '')) {
            console.error("❌ [FlashcardSession] Batch contém IDs inválidos:", batchIds);
            continue;
          }



          const { data: batchCards, error: batchError } = await supabase
            .from("flashcards_cards")
            .select(`
              *,
              specialty:flashcards_specialty(*),
              theme:flashcards_theme(*),
              focus:flashcards_focus(*)
            `)
            .in("id", batchIds);

          if (batchError) {
            console.error(`❌ [FlashcardSession] Error fetching batch:`, batchError);
            continue;
          }

          if (batchCards) {
            const formattedBatchCards = batchCards.map(card => ({
              ...card,
              current_state: card.current_state as FlashcardWithHierarchy["current_state"],
              hierarchy: {
                specialty: card.specialty,
                theme: card.theme,
                focus: card.focus
              }
            }));
            allCards = [...allCards, ...formattedBatchCards];
          }
        }

        setFlashcards(allCards);

        // ✅ Preload das próximas 3 imagens para melhor UX
        preloadNextImages(allCards, 3);

      } catch (error: any) {
        toast.error("Error loading flashcards", {
          description: error.message
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSessionFlashcards();
  }, [sessionId]);

  // ✅ Função para preload inteligente de imagens
  const preloadNextImages = (cards: FlashcardWithHierarchy[], startIndex: number = 0, count: number = 3) => {
    const imagesToPreload: string[] = [];

    // ✅ Preload a partir do índice atual + próximos cards
    for (let i = startIndex; i < Math.min(startIndex + count, cards.length); i++) {
      const card = cards[i];
      if (card.front_image) imagesToPreload.push(card.front_image);
      if (card.back_image) imagesToPreload.push(card.back_image);
    }

    // ✅ Preload assíncrono com Promise.all para melhor performance
    const preloadPromises = imagesToPreload.map(imageUrl => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve();
        img.onerror = () => reject(new Error(`Falha ao carregar: ${imageUrl}`));
        img.src = imageUrl;
      });
    });

    Promise.allSettled(preloadPromises).then(results => {
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;


    });
  };

  if (!sessionId) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />

      {/* Espaçamento para StudyNavBar flutuante */}
      <div className="h-16 sm:h-20"></div>

      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in relative z-10">
        {isCompleted ? (
          <FlashcardSessionComplete sessionId={sessionId} />
        ) : (
          <FlashcardSessionContent 
            flashcards={flashcards}
            isLoading={isLoading}
            sessionId={sessionId}
            onComplete={() => setIsCompleted(true)}
          />
        )}
      </div>
    </div>
  );
};
