
import { QuestionTimerProps } from "./types";
import { formatTime } from "@/utils/formatTime";
import { useDarkMode } from "@/contexts/DarkModeContext";

export const QuestionTimer = ({ elapsedTime, onTimeUpdate, isActive = true }: QuestionTimerProps) => {
  // Timer agora é apenas display - o tempo é gerenciado pelo useSessionTimer
  const { isDarkMode } = useDarkMode();



  return (
    <div className={`text-lg font-mono px-4 py-2 rounded-lg border transition-colors duration-200 ${
      isDarkMode
        ? 'bg-gray-700 border-gray-600 text-gray-200'
        : 'bg-gray-50 border-gray-200 text-gray-900'
    }`}>
      <div className="flex items-center gap-2">
        {isActive ? (
          <span className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></span>
        ) : (
          <span className={`text-sm font-bold ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            ⏸️
          </span>
        )}
        {formatTime(elapsedTime)}
      </div>
    </div>
  );
};
