
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { X } from "lucide-react";
import { useDarkMode } from "@/contexts/DarkModeContext";

interface AlternativeStatistics {
  count: number;
  percentage: number;
}

interface AlternativeProps {
  alternatives: string[];
  selectedAnswer: string | null;
  setSelectedAnswer?: (answer: string) => void;
  hasAnswered: boolean;
  correct_answer: number;
  statistics?: AlternativeStatistics[];
  alternativeComments?: Record<number, string>;
  questionId?: string;
  // 🔒 SEGURANÇA: Feedback seguro do backend
  answerFeedback?: {
    isCorrect: boolean;
    correctChoice: string | null;
    selectedIndex: number;
  };
}

export const QuestionAlternatives: React.FC<AlternativeProps> = ({
  alternatives,
  selectedAnswer,
  setSelectedAnswer,
  hasAnswered,
  correct_answer,
  statistics,
  alternativeComments,
  questionId = 'default',
  answerFeedback,
}) => {
  const [crossedAlternativesMap, setCrossedAlternativesMap] = useState<Record<string, number[]>>({});
  const [lastQuestionId, setLastQuestionId] = useState<string>(questionId);
  const crossedAlternatives = crossedAlternativesMap[questionId] || [];
  const { isDarkMode } = useDarkMode();

  // Reset quando a questão muda para evitar flash das cores
  useEffect(() => {
    if (lastQuestionId !== questionId) {
      setLastQuestionId(questionId);
    }
  }, [questionId, lastQuestionId]);

  const formatAlternativeContent = (text: string): string => {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n/g, '<br />');
  };

  const isImageUrl = (text: string): boolean => {
    const trimmedText = text.trim();
    return (
      trimmedText.startsWith('http') &&
      (trimmedText.includes('.jpg') ||
       trimmedText.includes('.jpeg') ||
       trimmedText.includes('.png') ||
       trimmedText.includes('.webp') ||
       trimmedText.includes('.gif'))
    );
  };

  const renderAlternativeContent = (alternative: string) => {
    if (isImageUrl(alternative)) {
      return (
        <img
          src={alternative.trim()}
          alt="Alternativa"
          className="max-w-full h-auto rounded-lg border border-gray-200 shadow-sm"
          style={{ maxHeight: '300px' }}
          onError={(e) => {
            // Fallback para mostrar o texto se a imagem não carregar
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `<span class="text-gray-500 text-sm">Erro ao carregar imagem: ${alternative}</span>`;
            }
          }}
        />
      );
    } else {
      return (
        <div
          className={`whitespace-pre-line transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`}
          dangerouslySetInnerHTML={{ __html: formatAlternativeContent(alternative) }}
        />
      );
    }
  };

  const toggleCrossed = (index: number) => {
    setCrossedAlternativesMap(prev => {
      const currentQuestionCrossed = prev[questionId] || [];

      let newQuestionCrossed;
      if (currentQuestionCrossed.includes(index)) {
        newQuestionCrossed = currentQuestionCrossed.filter(i => i !== index);
      } else {
        newQuestionCrossed = [...currentQuestionCrossed, index];
      }

      return {
        ...prev,
        [questionId]: newQuestionCrossed
      };
    });
  };

  // Determine the correct answer index (0-based) - apenas para fallback
  const correctAnswerIndex = correct_answer !== undefined && correct_answer !== null
    ? (typeof correct_answer === 'number' ? correct_answer : parseInt(String(correct_answer)))
    : undefined;

  return (
    <div className="space-y-4">
      <h3 className={`text-lg font-medium transition-colors duration-200 ${
        isDarkMode ? 'text-gray-200' : 'text-gray-900'
      }`}>Alternativas:</h3>
      <div className="space-y-3">
        {alternatives.map((alternative, index) => {
          const isSelected = selectedAnswer === String(index);
          const isCrossed = crossedAlternatives.includes(index);

          // 🔒 SEGURANÇA: Usar feedback seguro quando disponível
          let isCorrect = false;
          let isIncorrect = false;

          if (hasAnswered && answerFeedback) {
            // 🔒 SEGURANÇA: Backend retorna índice 0-based (0=A, 1=B, 2=C, 3=D)
            const correctChoiceIndex = answerFeedback.correctChoice ? parseInt(answerFeedback.correctChoice) : -1;

            // Alternativa correta (sempre mostrar em verde)
            isCorrect = index === correctChoiceIndex;

            // Alternativa selecionada incorreta (mostrar em vermelho apenas se foi selecionada e está errada)
            isIncorrect = isSelected && !answerFeedback.isCorrect;
          } else if (hasAnswered && correctAnswerIndex !== undefined) {
            // Fallback para compatibilidade (quando correct_answer ainda está disponível)
            isCorrect = index === correctAnswerIndex;
            isIncorrect = isSelected && !isCorrect;
          }

          return (
            <div
              key={index}
              className={cn(
                "p-4 rounded-lg border transition-all",
                hasAnswered
                  ? isCorrect
                    ? isDarkMode ? "bg-green-900 border-green-600" : "bg-green-50 border-green-300"
                    : isIncorrect
                    ? isDarkMode ? "bg-red-900 border-red-600" : "bg-red-50 border-red-300"
                    : isDarkMode ? "bg-gray-700 border-gray-600" : "bg-white border-gray-200"
                  : isSelected
                  ? isDarkMode ? "bg-blue-900 border-blue-600" : "bg-blue-50 border-blue-300"
                  : isDarkMode
                    ? "bg-gray-700 border-gray-600 hover:border-blue-500 hover:bg-blue-900"
                    : "bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50",
                setSelectedAnswer && !hasAnswered
                  ? "cursor-pointer"
                  : "cursor-default"
              )}
              onClick={() => {
                if (setSelectedAnswer && !hasAnswered) {
                  setSelectedAnswer(String(index)); // ✅ CORREÇÃO: Usar indexação 0-based (A=0, B=1, C=2, D=3)
                }
              }}
            >
              <div className="flex items-start gap-3">
                <div
                  className={cn(
                    "flex items-center justify-center w-6 h-6 rounded-full text-sm font-medium mt-0.5",
                    hasAnswered
                      ? isCorrect
                        ? "bg-green-500 text-white"
                        : isIncorrect
                        ? "bg-red-500 text-white"
                        : isSelected
                        ? isDarkMode ? "bg-gray-600 text-gray-200" : "bg-gray-200 text-gray-700"
                        : isDarkMode ? "bg-gray-600 text-gray-200" : "bg-gray-200 text-gray-700"
                      : isSelected
                      ? "bg-blue-500 text-white"
                      : isDarkMode ? "bg-gray-600 text-gray-200" : "bg-gray-200 text-gray-700"
                  )}
                >
                  {String.fromCharCode(65 + index)}
                </div>
                <div className="flex-1">
                  <div
                    className={cn(
                      isCrossed && "line-through text-gray-400"
                    )}
                  >
                    {renderAlternativeContent(alternative)}
                  </div>
                </div>
                {!hasAnswered && (
                  <button
                    type="button"
                    className={cn(
                      "p-1 ml-2 rounded-full transition-colors",
                      isCrossed
                        ? isDarkMode ? "bg-red-900 text-red-400" : "bg-red-100 text-red-500"
                        : isDarkMode
                          ? "bg-gray-600 text-gray-400 hover:text-gray-200 hover:bg-gray-500"
                          : "bg-gray-100 text-gray-400 hover:text-gray-600 hover:bg-gray-200"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleCrossed(index);
                    }}
                  >
                    <X size={16} />
                  </button>
                )}
              </div>

              {hasAnswered && alternativeComments && alternativeComments[index + 1] && (
                <div className={`mt-4 pt-4 border-t transition-colors duration-200 ${
                  isDarkMode ? 'border-gray-600' : 'border-gray-200'
                }`}>
                  <div className={`text-sm transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    {isImageUrl(alternativeComments[index + 1]) ? (
                      <img
                        src={alternativeComments[index + 1].trim()}
                        alt="Comentário da alternativa"
                        className={`max-w-full h-auto rounded-lg border shadow-sm transition-colors duration-200 ${
                          isDarkMode ? 'border-gray-600' : 'border-gray-200'
                        }`}
                        style={{ maxHeight: '200px' }}
                      />
                    ) : (
                      <div
                        className="whitespace-pre-line"
                        dangerouslySetInnerHTML={{ __html: formatAlternativeContent(alternativeComments[index + 1]) }}
                      />
                    )}
                  </div>
                </div>
              )}

              {hasAnswered && statistics && statistics[index] && (
                <div className={`mt-4 pt-4 border-t transition-colors duration-200 ${
                  isDarkMode ? 'border-gray-600' : 'border-gray-200'
                }`}>
                  <div className="flex justify-between items-center">
                    <span className={`text-sm transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      Escolhida por {statistics[index].percentage.toFixed(1)}% ({statistics[index].count} pessoas)
                    </span>
                    <div className={`w-32 h-2 rounded-full overflow-hidden transition-colors duration-200 ${
                      isDarkMode ? 'bg-gray-600' : 'bg-gray-200'
                    }`}>
                      <div
                        className="h-full bg-blue-500"
                        style={{ width: `${statistics[index].percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default QuestionAlternatives;
