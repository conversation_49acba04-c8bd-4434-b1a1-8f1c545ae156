import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import { useLoadingRecovery } from '@/hooks/useLoadingRecovery';


interface UserProfile {
  id: string;
  premium: boolean;
  full_name: string;
}

interface WelcomeFlowProps {
  profile?: UserProfile | null;
  isPremium?: boolean;
  isProfileLoading?: boolean;
}

export const useWelcomeFlow = ({ profile, isPremium, isProfileLoading }: WelcomeFlowProps = {}) => {
  const { user } = useAuth();



  const { preferences, isLoading: preferencesLoading, markWelcomeAsSeen } = useUserPreferences();
  const [showWelcome, setShowWelcome] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Sistema de recovery para loading de dados do usuário
  const recovery = useLoadingRecovery({
    timeoutMs: 15000, // 15 segundos
    maxRetries: 2,
    onTimeout: () => {
      setIsLoading(false);
      setShowWelcome(false);
    },
    onMaxRetriesReached: () => {
      setIsLoading(false);
      setShowWelcome(false);
    }
  });

  useEffect(() => {
    if (!user) {
      recovery.reset();
      setIsLoading(false);
      setShowWelcome(false);
      return;
    }

    // Usar valores padrão se não foram fornecidos
    const profileLoading = isProfileLoading ?? false;

    // Iniciar loading se ainda não iniciou
    if (!recovery.isLoading && !recovery.hasTimedOut && isLoading) {
      recovery.startLoading();
    }

    // Usar dados já carregados
    if (!preferencesLoading && !profileLoading && preferences && profile) {
      recovery.finishLoading();
      checkWelcomeStatus();
    } else if (!preferencesLoading && !profileLoading) {
      recovery.finishLoading();
      setIsLoading(false);
      setShowWelcome(false);
    }
  }, [user?.id, preferences?.welcome_dialog_shown, preferencesLoading, isProfileLoading, profile?.id, isPremium]);

  const checkWelcomeStatus = () => {
    if (!user || !preferences || !profile) {

      setIsLoading(false);
      return;
    }

    // Usar valor padrão se isPremium não foi fornecido
    const userIsPremium = isPremium ?? profile.premium ?? false;

    // Mostrar welcome se:
    // 1. Tem perfil
    // 2. Ainda não viu o welcome dialog (welcome_dialog_shown = false)
    // 3. Para usuários premium, não mostrar welcome (já têm acesso)
    const shouldShowWelcome = profile && preferences && !preferences.welcome_dialog_shown && !userIsPremium;



    if (shouldShowWelcome) {
      setShowWelcome(true);
    }

    setIsLoading(false);
  };

  const markWelcomeAsSeenLocal = async () => {
    if (!user) return;

    try {
      // Usar a função do useUserPreferences para manter consistência
      await markWelcomeAsSeen();
      setShowWelcome(false);
    } catch (error) {
      console.error('Erro ao atualizar status de welcome:', error);
    }
  };

  const grantPremiumAccess = async () => {
    if (!user) return false;

    try {


      // ✅ Apenas atualizar premium no perfil
      // NÃO marcar welcome_dialog_shown como true - deixar false para que apareça
      const { error } = await supabase
        .from('profiles')
        .update({ premium: true })
        .eq('id', user.id);

      if (error) {
        console.error('Erro ao conceder acesso premium:', error);
        return false;
      }

      // Estado será atualizado automaticamente pelo useUserData

      setShowWelcome(false);
      return true;
    } catch (error) {
      console.error('Erro ao conceder acesso premium:', error);
      return false;
    }
  };

  return {
    showWelcome,
    isLoading: isLoading || recovery.isLoading,
    hasError: recovery.hasTimedOut || !!recovery.error,
    error: recovery.error,
    retryCount: recovery.retryCount,
    canRetry: recovery.hasRetriesLeft,
    retry: () => {
      recovery.retry();
      setIsLoading(true);
    },
    userData: { profile, preferences },
    markWelcomeAsSeen: markWelcomeAsSeenLocal,
    grantPremiumAccess,
    userName: profile?.full_name || user?.user_metadata?.full_name || user?.email?.split('@')[0]
  };
};
