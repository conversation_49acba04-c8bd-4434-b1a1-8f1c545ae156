import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useCurrentUserFlashcardsRank } from "@/hooks/useFlashcardsRanking";
import { Brain, TrendingUp, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

/**
 * Widget compacto para mostrar posição do usuário no ranking de flashcards
 * Pode ser usado em dashboards ou outras páginas
 */
export const FlashcardsRankingWidget = () => {
  const navigate = useNavigate();
  
  const { 
    rank: todayRank, 
    cardsReviewed: todayCards, 
    isLoading: todayLoading 
  } = useCurrentUserFlashcardsRank('day');
  
  const { 
    rank: weekRank, 
    cardsReviewed: weekCards,
    isLoading: weekLoading
  } = useCurrentUserFlashcardsRank('week');

  const isLoading = todayLoading || weekLoading;

  if (isLoading) {
    return (
      <Card className="border-2 border-black shadow-card">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Brain className="h-5 w-5 text-blue-600" />
            Ranking de Flashcards
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-16 bg-gray-100 rounded-lg animate-pulse" />
            <div className="h-16 bg-gray-100 rounded-lg animate-pulse" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-2 border-black shadow-card">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Brain className="h-5 w-5 text-blue-600" />
          Ranking de Flashcards
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Stats de hoje */}
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-blue-700">Hoje</span>
            <Badge variant="secondary" className="text-xs">
              {todayRank ? `#${todayRank}` : 'N/A'}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold text-blue-900">{todayCards}</span>
            <span className="text-sm text-blue-600">cards</span>
          </div>
        </div>

        {/* Stats da semana */}
        <div className="p-3 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-green-700">Esta Semana</span>
            <Badge variant="secondary" className="text-xs">
              {weekRank ? `#${weekRank}` : 'N/A'}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold text-green-900">{weekCards}</span>
            <span className="text-sm text-green-600">cards</span>
          </div>
        </div>

        {/* Call to action */}
        <div className="pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/ranking/flashcards')}
            className="w-full border-2 border-black shadow-card-sm hover:shadow-none transition-all"
          >
            Ver Ranking Completo
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Motivational message */}
        {todayCards === 0 && (
          <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <TrendingUp className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
            <p className="text-sm text-yellow-700 font-medium">
              Revise alguns flashcards hoje para entrar no ranking!
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
