
import { FlashcardManager } from "./FlashcardManager";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";

export const FlashcardsStudy = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
      
      {/* Espaçamento para StudyNavBar flutuante */}
      <div className="h-16 sm:h-20"></div>

      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in relative z-10">
        {/* Header Section */}
        <div className="relative">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            <PERSON>rea de Estudo
          </h1>
        </div>

        {/* Custom Study Section */}
        <div 
          className="bg-white border-2 border-black rounded-xl 
          shadow-card-sm p-8 relative overflow-hidden 
          group hover:shadow-card transition-shadow"
        >
          <div 
            className="absolute top-0 right-0 w-64 h-64 
            bg-hackathon-yellow/20 rounded-full 
            transform translate-x-32 -translate-y-32 
            group-hover:rotate-12 transition-transform"
          />
          <div className="relative space-y-6">
            <div>
              <h2 
                className="text-2xl font-bold mb-2 
                bg-gradient-to-r from-black via-gray-800 to-gray-600 
                bg-clip-text text-transparent"
              >
                Nova Sessão de Estudo
              </h2>
              <p 
                className="text-gray-600 
                border-l-4 border-hackathon-yellow pl-4"
              >
                Escolha os tópicos que você deseja estudar e crie uma sessão personalizada.
              </p>
            </div>
            <FlashcardManager />
          </div>
        </div>
      </div>
    </div>
  );
};
