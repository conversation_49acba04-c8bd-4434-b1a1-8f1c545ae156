import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export const useDrWillContextTutorial = () => {
  const { user } = useAuth();
  const [shouldShowTutorial, setShouldShowTutorial] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    checkTutorialStatus();
  }, [user]);

  const checkTutorialStatus = async () => {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('has_seen_drwill_context_tutorial')
        .eq('user_id', user!.id)
        .single();

      if (error) {
        console.error('Erro ao verificar status do tutorial:', error);
        setIsLoading(false);
        return;
      }

      // Se o usuário nunca viu o tutorial, mostrar
      const hasSeenTutorial = data?.has_seen_drwill_context_tutorial || false;
      setShouldShowTutorial(!hasSeenTutorial);
      setIsLoading(false);
    } catch (error) {
      console.error('Erro ao verificar tutorial:', error);
      setIsLoading(false);
    }
  };

  const markTutorialAsCompleted = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          has_seen_drwill_context_tutorial: true
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        console.error('Erro ao marcar tutorial como concluído:', error);
        return;
      }

      setShouldShowTutorial(false);
    } catch (error) {
      console.error('Erro ao salvar progresso do tutorial:', error);
    }
  };

  const skipTutorial = async () => {
    await markTutorialAsCompleted();
  };

  return {
    shouldShowTutorial,
    isLoading,
    markTutorialAsCompleted,
    skipTutorial
  };
};
