import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useOptimizedWeekActivities } from '@/hooks/useConsolidatedDashboardStats';
import { useEffect } from 'react';

export interface StreakStats {
  current_streak: number;
  max_streak: number;
}

export interface WeekActivity {
  activity_date: string;
  has_activity: boolean;
}

/**
 * Hook otimizado para calcular sequências de estudo com fallback
 */
export const useOptimizedStreakStats = (enabled: boolean = true) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['optimized-streak-stats', user?.id],
    queryFn: async (): Promise<StreakStats> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // Obter timezone do usuário
      const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';

      try {
        // Tentar usar função SQL otimizada primeiro
        const { data, error } = await supabase
          .rpc('calculate_user_study_streak', {
            p_user_id: user.id,
            p_timezone: userTimezone
          });

        if (!error && data && data.length > 0) {
          return {
            current_streak: data[0].current_streak || 0,
            max_streak: data[0].max_streak || 0
          };
        }
      } catch (sqlError) {
        // SQL function not available, using JavaScript fallback
      }

      // Fallback para método JavaScript
      try {
        const { calculateImprovedStreakStats } = await import('@/utils/sessionTransformers');
        const result = await calculateImprovedStreakStats(user.id, supabase);

        return {
          current_streak: result.currentStreak,
          max_streak: result.maxStreak
        };
      } catch (jsError) {
        return { current_streak: 0, max_streak: 0 };
      }
    },
    enabled: !!user?.id && enabled,
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 10 * 60 * 1000, // 10 minutos
    retry: 2,
  });
};

/**
 * Hook para obter atividades da semana atual com fallback
 */
export const useWeekActivities = (weekStart?: Date, enabled: boolean = true) => {
  const { user } = useAuth();

  // ✅ OTIMIZADO: Tentar usar dados consolidados primeiro
  const { answersThisWeek, sessionsThisWeek, isLoading: consolidatedLoading } = useOptimizedWeekActivities();

  return useQuery({
    queryKey: ['week-activities', user?.id, weekStart?.toISOString()],
    queryFn: async (): Promise<WeekActivity[]> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // ✅ OTIMIZADO: Se temos dados consolidados e não há weekStart específico, usar eles
      if (!weekStart && !consolidatedLoading && (answersThisWeek.length > 0 || sessionsThisWeek.length > 0)) {
        return await calculateWeekActivitiesFromConsolidatedData(answersThisWeek, sessionsThisWeek);
      }

      // Obter timezone do usuário
      const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';

      try {
        // Tentar usar função SQL primeiro (domingo à sábado)
        const { data, error } = await supabase
          .rpc('get_user_week_activities_sunday', {
            p_user_id: user.id,
            p_timezone: userTimezone
          });

        if (!error && data) {
          return data;
        }
      } catch (sqlError) {
        // SQL function not available, using JavaScript fallback
      }

      // Fallback para método JavaScript
      try {
        return await calculateWeekActivitiesJS(user.id, weekStart, userTimezone);
      } catch (jsError) {
        return [];
      }
    },
    enabled: !!user?.id && enabled,
    staleTime: 2 * 60 * 1000, // 2 minutos
    cacheTime: 5 * 60 * 1000, // 5 minutos
    retry: 1,
  });
};

/**
 * ✅ NOVA: Função para processar dados consolidados
 */
const calculateWeekActivitiesFromConsolidatedData = async (
  answersThisWeek: any[],
  sessionsThisWeek: any[]
): Promise<WeekActivity[]> => {
  const today = new Date();
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - today.getDay()); // Domingo
  startDate.setHours(0, 0, 0, 0);

  const activities: WeekActivity[] = [];

  // Processar atividades dos 7 dias da semana
  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);

    const dayStart = new Date(currentDate);
    dayStart.setHours(0, 0, 0, 0);

    const dayEnd = new Date(currentDate);
    dayEnd.setHours(23, 59, 59, 999);

    // Verificar se há atividades neste dia
    const hasAnswers = answersThisWeek.some(answer => {
      const answerDate = new Date(answer.created_at);
      return answerDate >= dayStart && answerDate <= dayEnd;
    });

    const hasSessions = sessionsThisWeek.some(session => {
      const sessionDate = new Date(session.completed_at);
      return sessionDate >= dayStart && sessionDate <= dayEnd;
    });

    const hasActivity = hasAnswers || hasSessions;
    activities.push({
      activity_date: currentDate.toISOString().split('T')[0],
      has_activity: hasActivity
    });
  }

  return activities;
};

/**
 * Fallback JavaScript para calcular atividades da semana
 */
const calculateWeekActivitiesJS = async (
  userId: string,
  weekStart?: Date,
  timezone: string = 'UTC'
): Promise<WeekActivity[]> => {
  // Determinar início da semana (domingo)
  const today = new Date();
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - today.getDay()); // Domingo = 0
  startDate.setHours(0, 0, 0, 0);

  // Gerar array de 7 dias (domingo a sábado)
  const weekDays: WeekActivity[] = [];
  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);
    weekDays.push({
      activity_date: currentDate.toISOString().split('T')[0],
      has_activity: false
    });
  }

  // Buscar atividades do usuário na semana atual
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);
  endDate.setHours(23, 59, 59, 999);

  // Buscar atividades da semana atual
  const [sessionsResult, answersResult, scheduleResult] = await Promise.all([
    supabase
      .from('study_sessions')
      .select('completed_at')
      .eq('user_id', userId)
      .eq('status', 'completed')
      .not('completed_at', 'is', null)
      .gte('completed_at', startDate.toISOString())
      .lte('completed_at', endDate.toISOString()),

    supabase
      .from('user_answers')
      .select('created_at')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString()),

    supabase
      .from('study_schedule_items')
      .select('last_revision_date, study_schedules!inner(user_id)')
      .eq('study_schedules.user_id', userId)
      .eq('study_status', 'completed')
      .not('last_revision_date', 'is', null)
      .gte('last_revision_date', startDate.toISOString())
      .lte('last_revision_date', endDate.toISOString())
  ]);

  // Processar atividades e marcar os dias
  const activityDates = new Set<string>();

  // Adicionar sessões completadas
  sessionsResult.data?.forEach(session => {
    if (session.completed_at) {
      const date = new Date(session.completed_at);
      const formatter = new Intl.DateTimeFormat('en-CA', {
        timeZone: 'America/Sao_Paulo',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
      activityDates.add(formatter.format(date));
    }
  });

  // Adicionar respostas de questões
  answersResult.data?.forEach(answer => {
    const date = new Date(answer.created_at);
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: 'America/Sao_Paulo',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    activityDates.add(formatter.format(date));
  });

  // Adicionar itens de cronograma estudados
  scheduleResult.data?.forEach(item => {
    if (item.last_revision_date) {
      const date = new Date(item.last_revision_date);
      const formatter = new Intl.DateTimeFormat('en-CA', {
        timeZone: 'America/Sao_Paulo',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
      activityDates.add(formatter.format(date));
    }
  });

  // Marcar os dias com atividade
  weekDays.forEach(day => {
    if (activityDates.has(day.activity_date)) {
      day.has_activity = true;
    }
  });

  return weekDays;
};

/**
 * Formatar data para timezone local
 */
const formatDateToLocal = (date: Date, timezone: string): string => {
  try {
    return new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(date);
  } catch (error) {
    return date.toISOString().split('T')[0];
  }
};

/**
 * Hook para forçar atualização das estatísticas de sequência
 */
export const useUpdateStreakStats = () => {
  const { user } = useAuth();

  const updateStats = async (): Promise<StreakStats | null> => {
    if (!user?.id) {
      return null;
    }

    try {
      const { data, error } = await supabase
        .rpc('update_user_study_statistics', {
          p_user_id: user.id
        });

      if (error) {
        throw error;
      }

      return {
        current_streak: data?.current_streak || 0,
        max_streak: data?.max_streak || 0
      };
    } catch (error) {
      throw error;
    }
  };

  return { updateStats };
};

/**
 * Hook para escutar notificações em tempo real de mudanças nas estatísticas
 */
export const useStreakStatsRealtime = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!user?.id) return;

    // Configurar listener para notificações de mudança
    const channel = supabase
      .channel('streak-stats-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'study_sessions',
        filter: `user_id=eq.${user.id}`
      }, () => {
        // Invalidar cache quando há mudanças
        queryClient.invalidateQueries({ queryKey: ['optimized-streak-stats', user.id] });
        queryClient.invalidateQueries({ queryKey: ['week-activities', user.id] });
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'user_answers',
        filter: `user_id=eq.${user.id}`
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['optimized-streak-stats', user.id] });
        queryClient.invalidateQueries({ queryKey: ['week-activities', user.id] });
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'study_schedule_items',
        filter: `user_id=eq.${user.id}`
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['optimized-streak-stats', user.id] });
        queryClient.invalidateQueries({ queryKey: ['week-activities', user.id] });
      })
      .subscribe();

    return () => {
      try {
        if (channel) {
          supabase.removeChannel(channel);
        }
      } catch (error) {
        // Error cleaning up WebSocket
      }
    };
  }, [user?.id, queryClient]);
};

/**
 * Hook combinado que fornece todas as funcionalidades de sequência
 */
export const useStreakSystem = (enabled: boolean = true) => {
  const streakStats = useOptimizedStreakStats(enabled);
  const weekActivities = useWeekActivities(undefined, enabled);
  const { updateStats } = useUpdateStreakStats();

  // Ativar escuta em tempo real apenas se enabled
  if (enabled) {
    useStreakStatsRealtime();
  }

  const refreshStats = async () => {
    try {
      await updateStats();
      // Invalidar cache para forçar refetch
      await Promise.all([
        streakStats.refetch(),
        weekActivities.refetch()
      ]);
    } catch (error) {
      // Error refreshing stats
    }
  };

  const result = {
    // Dados
    currentStreak: streakStats.data?.current_streak || 0,
    maxStreak: streakStats.data?.max_streak || 0,
    weekActivities: weekActivities.data || [],

    // Estados de loading
    isLoadingStreak: streakStats.isLoading,
    isLoadingWeek: weekActivities.isLoading,
    isLoading: streakStats.isLoading || weekActivities.isLoading,

    // Estados de erro
    streakError: streakStats.error,
    weekError: weekActivities.error,
    hasError: !!streakStats.error || !!weekActivities.error,

    // Funções
    refreshStats,
    refetchStreak: streakStats.refetch,
    refetchWeek: weekActivities.refetch,
  };



  return result;
};

/**
 * Utilitário para determinar se um dia específico tem atividade
 */
export const isDayActive = (date: Date, weekActivities: WeekActivity[]): boolean => {
  // Usar data local ao invés de UTC para evitar problemas de timezone
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const dateStr = `${year}-${month}-${day}`;

  const isActive = weekActivities.some(activity =>
    activity.activity_date === dateStr && activity.has_activity
  );



  return isActive;
};

/**
 * Utilitário para obter estatísticas de atividade da semana
 */
export const getWeekStats = (weekActivities: WeekActivity[]) => {
  const totalDays = weekActivities.length;
  const activeDays = weekActivities.filter(activity => activity.has_activity).length;
  const completionRate = totalDays > 0 ? (activeDays / totalDays) * 100 : 0;

  return {
    totalDays,
    activeDays,
    inactiveDays: totalDays - activeDays,
    completionRate: Math.round(completionRate)
  };
};
