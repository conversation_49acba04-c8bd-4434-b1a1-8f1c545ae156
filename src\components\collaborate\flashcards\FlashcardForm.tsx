
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { HierarchySelect } from "./HierarchySelect";
import { FlashcardInputs } from "./form/FlashcardInputs";
import { useHierarchyData } from "./hooks/useHierarchyData";
import type { FlashcardFormProps } from "./types";
import { CheckCircle, ArrowRight } from "lucide-react";

export const FlashcardForm = ({
  selectedSpecialty,
  selectedTheme,
  selectedFocus,
  onCreateSuccess
}: FlashcardFormProps) => {
  const [front, setFront] = useState('');
  const [back, setBack] = useState('');
  const [frontImage, setFrontImage] = useState('');
  const [backImage, setBackImage] = useState('');
  const [localSelectedSpecialty, setLocalSelectedSpecialty] = useState(selectedSpecialty);
  const [localSelectedTheme, setLocalSelectedTheme] = useState(selectedTheme);
  const [localSelectedFocus, setLocalSelectedFocus] = useState(selectedFocus);
  const [selectedExtraFocus, setSelectedExtraFocus] = useState<string>('');
  const [showSuccessFeedback, setShowSuccessFeedback] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: boolean}>({});

  const navigate = useNavigate();

  const {
    specialties,
    themes,
    focuses,
    extraFocuses
  } = useHierarchyData(
    localSelectedSpecialty || undefined,
    localSelectedTheme || undefined,
    localSelectedFocus || undefined
  );

  // ✅ Controla o feedback de sucesso
  useEffect(() => {
    if (showSuccessFeedback) {
      const timer = setTimeout(() => {
        setShowSuccessFeedback(false);
      }, 5000); // 5 segundos

      return () => clearTimeout(timer);
    }
  }, [showSuccessFeedback]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('Usuário não autenticado');
        return;
      }

      // ✅ Validação com feedback visual
      const errors: {[key: string]: boolean} = {};

      if (!localSelectedSpecialty) {
        errors.specialty = true;
      }
      if (!front.trim()) {
        errors.front = true;
      }
      if (!back.trim()) {
        errors.back = true;
      }

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        toast.error('Preencha os campos obrigatórios');

        // Remove os erros após 3 segundos
        setTimeout(() => {
          setValidationErrors({});
        }, 3000);

        return;
      }

      // Limpa erros se chegou até aqui
      setValidationErrors({});

      // Log detalhado antes da inserção
      console.log('🔍 [FlashcardForm] Tentando inserir flashcard:', {
        specialty_id: localSelectedSpecialty,
        theme_id: localSelectedTheme || null,
        focus_id: localSelectedFocus || null,
        extrafocus_id: selectedExtraFocus || null,
        front_preview: front?.substring(0, 50) + '...',
        back_preview: back?.substring(0, 50) + '...'
      });

      // 1º: cria o flashcard sem origin_id (será atualizado após o insert)
      const { data: insertData, error } = await supabase
        .from('flashcards_cards')
        .insert({
          user_id: user.id,
          front: front.trim(),
          back: back.trim(),
          front_image: frontImage || null,
          back_image: backImage || null,
          specialty_id: localSelectedSpecialty,
          theme_id: localSelectedTheme || null,
          focus_id: localSelectedFocus || null,
          extrafocus_id: selectedExtraFocus || null,
          current_state: 'available',
          is_shared: false, // Mudança aqui: compartilhamento desativado por padrão
          origin_id: null
        })
        .select('id')
        .single();

      if (error || !insertData) {
        console.error('❌ [FlashcardForm] Erro ao criar flashcard:', {
          error,
          insertData: {
            specialty_id: localSelectedSpecialty,
            theme_id: localSelectedTheme || null,
            focus_id: localSelectedFocus || null,
            extrafocus_id: selectedExtraFocus || null
          }
        });
        throw error;
      }

      // 2º: atualiza origin_id = id
      await supabase
        .from('flashcards_cards')
        .update({ origin_id: insertData.id })
        .eq('id', insertData.id);

      toast.success('Flashcard criado com sucesso!');

      // ✅ Ativa o feedback visual
      setShowSuccessFeedback(true);

      // Reset form
      setFront('');
      setBack('');
      setFrontImage('');
      setBackImage('');
      setSelectedExtraFocus('');

      if (onCreateSuccess) {
        onCreateSuccess();
      }
      
    } catch {
      toast.error('Erro ao criar flashcard');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <FlashcardInputs
        front={front}
        setFront={setFront}
        back={back}
        setBack={setBack}
        frontImage={frontImage}
        setFrontImage={setFrontImage}
        backImage={backImage}
        setBackImage={setBackImage}
      />

      <div className="grid gap-4">
        <HierarchySelect
          label="Especialidade *"
          value={localSelectedSpecialty}
          onChange={setLocalSelectedSpecialty}
          options={specialties}
          placeholder="Selecione uma especialidade"
          hasError={validationErrors.specialty}
        />

        <HierarchySelect
          label="Tema"
          value={localSelectedTheme}
          onChange={setLocalSelectedTheme}
          options={themes}
          placeholder="Selecione um tema"
          disabled={!localSelectedSpecialty}
        />

        <HierarchySelect
          label="Foco"
          value={localSelectedFocus}
          onChange={setLocalSelectedFocus}
          options={focuses}
          placeholder="Selecione um foco"
          disabled={!localSelectedTheme}
        />


      </div>

      <Button type="submit" className="w-full">
        Criar Flashcard
      </Button>

      {/* ✅ Feedback Visual de Sucesso */}
      {showSuccessFeedback && (
        <div className="mt-4 p-4 bg-hackathon-green/10 border-2 border-hackathon-green rounded-xl animate-in slide-in-from-bottom-2 duration-300">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-1 rounded-full bg-hackathon-green">
              <CheckCircle className="w-4 h-4 text-white" />
            </div>
            <div>
              <h4 className="font-bold text-hackathon-green text-sm">
                Flashcard criado com sucesso! 🎉
              </h4>
              <p className="text-xs text-gray-600 mt-0.5">
                Seu flashcard foi adicionado à sua coleção
              </p>
            </div>
          </div>

          <button
            onClick={() => navigate('/flashcards/study')}
            className="flex items-center gap-2 text-xs text-gray-700 bg-white/50 rounded-lg p-2 border border-hackathon-green/20 hover:bg-hackathon-green/5 hover:border-hackathon-green/30 transition-all w-full cursor-pointer"
          >
            <ArrowRight className="w-3 h-3 text-hackathon-green" />
            <span>
              <strong>Próximo passo:</strong> Vá para{" "}
              <span className="font-bold text-hackathon-green">
                Nova Sessão de Estudo
              </span>{" "}
              para começar a estudar!
            </span>
          </button>
        </div>
      )}
    </form>
  );
};
