import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  ArrowLeft, 
  Eye, 
  RotateCcw, 
  Search, 
  Calendar,
  CheckCircle,
  Clock,
  Filter
} from 'lucide-react';
import { ErrorQuestion } from '@/hooks/useErrorNotebook';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface QuestionSelectorProps {
  questions: ErrorQuestion[];
  mode: 'view' | 'retry';
  onBack: () => void;
  onSelectQuestion: (question: ErrorQuestion) => void;
  isLoading?: boolean;
}

export const QuestionSelector: React.FC<QuestionSelectorProps> = ({
  questions,
  mode,
  onBack,
  onSelectQuestion,
  isLoading = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredQuestions, setFilteredQuestions] = useState(questions);
  const [currentPage, setCurrentPage] = useState(1);
  const questionsPerPage = 10;

  useEffect(() => {
    let filtered = questions;

    // Filtrar por termo de busca
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(q => 
        q.question_content.toLowerCase().includes(term) ||
        q.specialty_name.toLowerCase().includes(term) ||
        q.theme_name.toLowerCase().includes(term)
      );
    }

    // Para modo refazer, priorizar questões não revisadas
    if (mode === 'retry') {
      filtered = filtered.sort((a, b) => {
        if (a.revisado && !b.revisado) return 1;
        if (!a.revisado && b.revisado) return -1;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });
    }

    setFilteredQuestions(filtered);
    setCurrentPage(1);
  }, [questions, searchTerm, mode]);

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ptBR 
      });
    } catch {
      return 'Data inválida';
    }
  };

  const getModeInfo = () => {
    if (mode === 'view') {
      return {
        title: 'Selecionar Questão para Visualizar',
        icon: <Eye className="h-5 w-5 text-blue-500" />,
        description: 'Escolha uma questão para revisar com todas as informações detalhadas',
        buttonText: 'Visualizar',
        buttonIcon: <Eye className="h-4 w-4" />
      };
    } else {
      return {
        title: 'Selecionar Questão para Refazer',
        icon: <RotateCcw className="h-5 w-5 text-purple-500" />,
        description: 'Escolha uma questão para tentar responder novamente',
        buttonText: 'Refazer',
        buttonIcon: <RotateCcw className="h-4 w-4" />
      };
    }
  };

  const modeInfo = getModeInfo();

  // Paginação
  const totalPages = Math.ceil(filteredQuestions.length / questionsPerPage);
  const startIndex = (currentPage - 1) * questionsPerPage;
  const endIndex = startIndex + questionsPerPage;
  const currentQuestions = filteredQuestions.slice(startIndex, endIndex);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
          <div className="flex items-center gap-2">
            {modeInfo.icon}
            <h2 className="text-2xl font-bold">{modeInfo.title}</h2>
          </div>
        </div>
        
        <Card className="p-8 bg-white border-2 border-black shadow-card-sm">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-3">
              <RotateCcw className="h-6 w-6 animate-spin text-blue-500" />
              <span className="text-lg font-medium">Carregando questões...</span>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={onBack}
          className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar
        </Button>
        
        <div className="flex items-center gap-2">
          {modeInfo.icon}
          <h2 className="text-2xl font-bold">{modeInfo.title}</h2>
        </div>
      </div>

      {/* Description */}
      <Card className="p-4 bg-blue-50 border-2 border-blue-200">
        <p className="text-blue-800">{modeInfo.description}</p>
      </Card>

      {/* Search */}
      <Card className="p-4 bg-white border-2 border-black shadow-card-sm">
        <div className="flex items-center gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Buscar por conteúdo, especialidade ou tema..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 border-2 border-gray-300"
            />
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Filter className="h-4 w-4" />
            {filteredQuestions.length} questão{filteredQuestions.length !== 1 ? 'ões' : ''}
          </div>
        </div>
      </Card>

      {/* Questions List */}
      {currentQuestions.length === 0 ? (
        <Card className="p-8 bg-white border-2 border-black shadow-card-sm text-center">
          <div className="max-w-md mx-auto">
            <div className="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
              <Search className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-xl font-bold mb-2">Nenhuma questão encontrada</h3>
            <p className="text-gray-600">
              {searchTerm 
                ? 'Tente ajustar os termos de busca.' 
                : 'Não há questões disponíveis para este modo.'}
            </p>
          </div>
        </Card>
      ) : (
        <div className="space-y-4">
          {currentQuestions.map((question, index) => (
            <Card key={question.id} className="p-6 bg-white border-2 border-black shadow-card-sm hover:shadow-lg transition-all">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="text-xs">
                        #{startIndex + index + 1}
                      </Badge>
                      <Badge variant={question.revisado ? "default" : "destructive"} className="text-xs">
                        {question.revisado ? (
                          <>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Revisada
                          </>
                        ) : (
                          <>
                            <Clock className="h-3 w-3 mr-1" />
                            Pendente
                          </>
                        )}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {question.exam_year}
                      </Badge>
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-2">
                      <span className="font-medium">{question.specialty_name}</span>
                      {question.theme_name && (
                        <>
                          <span className="mx-2">•</span>
                          <span>{question.theme_name}</span>
                        </>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(question.created_at)}
                  </div>
                </div>

                {/* Question Preview */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm font-medium line-clamp-2">
                    {question.question_content}
                  </p>
                </div>

                {/* Action */}
                <div className="flex justify-end">
                  <Button
                    onClick={() => onSelectQuestion(question)}
                    className={`${
                      mode === 'view' 
                        ? 'bg-blue-500 hover:bg-blue-600' 
                        : 'bg-purple-500 hover:bg-purple-600'
                    } text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all`}
                  >
                    {modeInfo.buttonIcon}
                    <span className="ml-2">{modeInfo.buttonText}</span>
                  </Button>
                </div>
              </div>
            </Card>
          ))}

          {/* Pagination */}
          {totalPages > 1 && (
            <Card className="p-4 bg-white border-2 border-black shadow-card-sm">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Página {currentPage} de {totalPages}
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage <= 1}
                    className="text-xs"
                  >
                    Anterior
                  </Button>
                  
                  <span className="text-sm text-gray-600">
                    {currentPage} / {totalPages}
                  </span>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage >= totalPages}
                    className="text-xs"
                  >
                    Próxima
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};
