import React, { useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  XCircle,
  BookOpen,
  Target,
  TrendingUp,
  Clock,
  Award,
  BarChart3
} from 'lucide-react';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface SessionSummaryData {
  totalQuestions: number;
  answeredQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  markedAsStudied: number;
  improvementRate: number; // Porcentagem de questões que melhoraram
  sessionDuration: string;
}

interface SessionSummaryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  summaryData: SessionSummaryData;
  onBackToDashboard: () => void;
}

export const SessionSummaryDialog: React.FC<SessionSummaryDialogProps> = ({
  open,
  onOpenChange,
  summaryData,
  onBackToDashboard
}) => {
  const { isDarkMode } = useDarkMode();

  const {
    totalQuestions,
    answeredQuestions,
    correctAnswers,
    incorrectAnswers,
    markedAsStudied,
    improvementRate,
    sessionDuration
  } = summaryData;

  const accuracyRate = answeredQuestions > 0 ? Math.round((correctAnswers / answeredQuestions) * 100) : 0;
  const completionRate = Math.round((answeredQuestions / totalQuestions) * 100);

  const getPerformanceMessage = () => {
    if (accuracyRate >= 80) {
      return {
        message: "Excelente desempenho! 🎉",
        color: "text-green-600",
        bgColor: "bg-green-50"
      };
    } else if (accuracyRate >= 60) {
      return {
        message: "Bom progresso! 👍",
        color: "text-blue-600",
        bgColor: "bg-blue-50"
      };
    } else if (accuracyRate >= 40) {
      return {
        message: "Continue estudando! 📚",
        color: "text-yellow-600",
        bgColor: "bg-yellow-50"
      };
    } else {
      return {
        message: "Foque nos estudos! 💪",
        color: "text-orange-600",
        bgColor: "bg-orange-50"
      };
    }
  };

  const performance = getPerformanceMessage();

  // Body scroll lock quando dialog está aberto
  useEffect(() => {
    if (open) {
      // Salvar o scroll atual
      const scrollY = window.scrollY;

      // Aplicar overflow hidden no body
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';

      return () => {
        // Restaurar scroll quando dialog fechar
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        window.scrollTo(0, scrollY);
      };
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl sm:text-2xl font-bold text-center flex items-center justify-center gap-2">
            <Award className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-500" />
            <span className="text-center">Resumo da Sessão</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Performance Geral */}
          <Card className={`${performance.bgColor} border-2`}>
            <CardContent className="p-4">
              <div className="text-center">
                <h3 className={`text-xl font-bold ${performance.color}`}>
                  {performance.message}
                </h3>
                <p className="text-gray-600 mt-1">
                  Você acertou {accuracyRate}% das questões respondidas
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Estatísticas Principais */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            <Card className="border-2">
              <CardContent className="p-3 sm:p-4 text-center">
                <BookOpen className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500 mx-auto mb-1 sm:mb-2" />
                <div className="text-lg sm:text-2xl font-bold text-blue-600">{totalQuestions}</div>
                <div className="text-xs sm:text-sm text-gray-600">Total</div>
              </CardContent>
            </Card>

            <Card className="border-2">
              <CardContent className="p-3 sm:p-4 text-center">
                <Target className="h-6 w-6 sm:h-8 sm:w-8 text-purple-500 mx-auto mb-1 sm:mb-2" />
                <div className="text-lg sm:text-2xl font-bold text-purple-600">{answeredQuestions}</div>
                <div className="text-xs sm:text-sm text-gray-600">Respondidas</div>
              </CardContent>
            </Card>

            <Card className="border-2">
              <CardContent className="p-3 sm:p-4 text-center">
                <CheckCircle className="h-6 w-6 sm:h-8 sm:w-8 text-green-500 mx-auto mb-1 sm:mb-2" />
                <div className="text-lg sm:text-2xl font-bold text-green-600">{correctAnswers}</div>
                <div className="text-xs sm:text-sm text-gray-600">Acertos</div>
              </CardContent>
            </Card>

            <Card className="border-2">
              <CardContent className="p-3 sm:p-4 text-center">
                <XCircle className="h-6 w-6 sm:h-8 sm:w-8 text-red-500 mx-auto mb-1 sm:mb-2" />
                <div className="text-lg sm:text-2xl font-bold text-red-600">{incorrectAnswers}</div>
                <div className="text-xs sm:text-sm text-gray-600">Erros</div>
              </CardContent>
            </Card>
          </div>

          {/* Métricas Detalhadas */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
            <Card className="border-2">
              <CardContent className="p-3 sm:p-4">
                <div className="flex items-center gap-2 sm:gap-3">
                  <BarChart3 className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <div className="text-base sm:text-lg font-bold">{completionRate}%</div>
                    <div className="text-xs sm:text-sm text-gray-600">Taxa de Conclusão</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2">
              <CardContent className="p-3 sm:p-4">
                <div className="flex items-center gap-2 sm:gap-3">
                  <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-green-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <div className="text-base sm:text-lg font-bold">{markedAsStudied}</div>
                    <div className="text-xs sm:text-sm text-gray-600">Estudadas</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2">
              <CardContent className="p-3 sm:p-4">
                <div className="flex items-center gap-2 sm:gap-3">
                  <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-orange-500 flex-shrink-0" />
                  <div className="min-w-0">
                    <div className="text-base sm:text-lg font-bold">{sessionDuration}</div>
                    <div className="text-xs sm:text-sm text-gray-600">Tempo</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Badges de Conquistas */}
          <div className="flex flex-wrap gap-2 justify-center">
            {accuracyRate >= 80 && (
              <Badge variant="default" className="bg-green-500 text-white">
                🏆 Mestre das Questões
              </Badge>
            )}
            {completionRate === 100 && (
              <Badge variant="default" className="bg-blue-500 text-white">
                ✅ Sessão Completa
              </Badge>
            )}
            {correctAnswers >= 5 && (
              <Badge variant="default" className="bg-purple-500 text-white">
                🎯 Acertador
              </Badge>
            )}
            {markedAsStudied >= totalQuestions * 0.5 && (
              <Badge variant="default" className="bg-orange-500 text-white">
                📚 Estudioso
              </Badge>
            )}
          </div>

          {/* Botões de Ação */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="border-2 border-gray-300 w-full sm:w-auto"
            >
              Continuar Revisando
            </Button>
            <Button
              onClick={onBackToDashboard}
              className="bg-blue-600 hover:bg-blue-700 text-white border-2 border-blue-500 w-full sm:w-auto"
            >
              Voltar ao Dashboard
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
