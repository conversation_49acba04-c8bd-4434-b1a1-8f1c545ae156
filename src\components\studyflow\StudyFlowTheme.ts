/**
 * StudyFlow Design System
 * Sistema de design moderno e consistente para o novo cronograma
 */

export const studyFlowTheme = {
  // Paleta de cores principal
  colors: {
    // Cores primárias
    primary: {
      50: '#EEF2FF',
      100: '#E0E7FF', 
      200: '#C7D2FE',
      300: '#A5B4FC',
      400: '#818CF8',
      500: '#6366F1', // Primary principal
      600: '#4F46E5',
      700: '#4338CA',
      800: '#3730A3',
      900: '#312E81'
    },
    
    // Cores de sucesso (estudado)
    success: {
      50: '#ECFDF5',
      100: '#D1FAE5',
      200: '#A7F3D0',
      300: '#6EE7B7',
      400: '#34D399',
      500: '#10B981', // Success principal
      600: '#059669',
      700: '#047857',
      800: '#065F46',
      900: '#064E3B'
    },
    
    // Cores de aviso
    warning: {
      50: '#FFFBEB',
      100: '#FEF3C7',
      200: '#FDE68A',
      300: '#FCD34D',
      400: '#FBBF24',
      500: '#F59E0B', // Warning principal
      600: '#D97706',
      700: '#B45309',
      800: '#92400E',
      900: '#78350F'
    },
    
    // Cores de erro
    error: {
      50: '#FEF2F2',
      100: '#FEE2E2',
      200: '#FECACA',
      300: '#FCA5A5',
      400: '#F87171',
      500: '#EF4444', // Error principal
      600: '#DC2626',
      700: '#B91C1C',
      800: '#991B1B',
      900: '#7F1D1D'
    },
    
    // Cores neutras
    neutral: {
      50: '#F9FAFB',
      100: '#F3F4F6',
      200: '#E5E7EB',
      300: '#D1D5DB',
      400: '#9CA3AF',
      500: '#6B7280', // Neutral principal
      600: '#4B5563',
      700: '#374151',
      800: '#1F2937',
      900: '#111827'
    },
    
    // Cores especiais
    background: '#FAFBFC',
    surface: '#FFFFFF',
    border: '#E5E7EB',
    overlay: 'rgba(0, 0, 0, 0.5)'
  },
  
  // Gradientes
  gradients: {
    primary: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
    success: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
    warning: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
    error: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',
    warm: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
    cool: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)',
    sunset: 'linear-gradient(135deg, #F59E0B 0%, #EF4444 100%)',
    ocean: 'linear-gradient(135deg, #06B6D4 0%, #6366F1 100%)'
  },
  
  // Sombras
  shadows: {
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none'
  },
  
  // Tipografia
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Menlo', 'Monaco', 'monospace']
    },
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
      '6xl': '3.75rem'  // 60px
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800'
    },
    lineHeight: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75'
    }
  },
  
  // Espaçamento
  spacing: {
    px: '1px',
    0: '0',
    1: '0.25rem',   // 4px
    2: '0.5rem',    // 8px
    3: '0.75rem',   // 12px
    4: '1rem',      // 16px
    5: '1.25rem',   // 20px
    6: '1.5rem',    // 24px
    8: '2rem',      // 32px
    10: '2.5rem',   // 40px
    12: '3rem',     // 48px
    16: '4rem',     // 64px
    20: '5rem',     // 80px
    24: '6rem',     // 96px
    32: '8rem',     // 128px
    40: '10rem',    // 160px
    48: '12rem',    // 192px
    56: '14rem',    // 224px
    64: '16rem'     // 256px
  },
  
  // Border radius
  borderRadius: {
    none: '0',
    sm: '0.125rem',   // 2px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    '3xl': '1.5rem',  // 24px
    full: '9999px'
  },
  
  // Animações
  animation: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
      slower: '500ms'
    },
    easing: {
      linear: 'linear',
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
      custom: 'cubic-bezier(0.4, 0, 0.2, 1)'
    },
    scale: {
      hover: '1.02',
      active: '0.98',
      focus: '1.05'
    }
  },
  
  // Z-index
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800
  },
  
  // Breakpoints
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  }
};

// Utilitários para usar o tema
export const getColor = (colorPath: string) => {
  const keys = colorPath.split('.');
  let value: any = studyFlowTheme.colors;
  
  for (const key of keys) {
    value = value[key];
    if (value === undefined) return colorPath;
  }
  
  return value;
};

export const getShadow = (size: keyof typeof studyFlowTheme.shadows) => {
  return studyFlowTheme.shadows[size];
};

export const getGradient = (name: keyof typeof studyFlowTheme.gradients) => {
  return studyFlowTheme.gradients[name];
};

// Tipos TypeScript
export type StudyFlowColor = keyof typeof studyFlowTheme.colors;
export type StudyFlowShadow = keyof typeof studyFlowTheme.shadows;
export type StudyFlowGradient = keyof typeof studyFlowTheme.gradients;

// CSS Variables para uso global
export const studyFlowCSSVariables = `
  :root {
    --sf-primary: ${studyFlowTheme.colors.primary[500]};
    --sf-success: ${studyFlowTheme.colors.success[500]};
    --sf-warning: ${studyFlowTheme.colors.warning[500]};
    --sf-error: ${studyFlowTheme.colors.error[500]};
    --sf-neutral: ${studyFlowTheme.colors.neutral[500]};
    --sf-background: ${studyFlowTheme.colors.background};
    --sf-surface: ${studyFlowTheme.colors.surface};
    --sf-border: ${studyFlowTheme.colors.border};
    
    --sf-shadow-sm: ${studyFlowTheme.shadows.sm};
    --sf-shadow-md: ${studyFlowTheme.shadows.md};
    --sf-shadow-lg: ${studyFlowTheme.shadows.lg};
    
    --sf-gradient-primary: ${studyFlowTheme.gradients.primary};
    --sf-gradient-success: ${studyFlowTheme.gradients.success};
    --sf-gradient-warning: ${studyFlowTheme.gradients.warning};
  }
`;
