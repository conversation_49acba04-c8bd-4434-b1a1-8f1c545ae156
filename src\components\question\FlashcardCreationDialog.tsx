import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Loader2, Sparkles, BookOpen, Brain, CheckCircle, XCircle } from "lucide-react";
import { useQuestionFlashcardCreation } from '@/hooks/useQuestionFlashcardCreation';
import { useCurrentQuestionInfo } from '@/contexts/CurrentQuestionContext';
import { toast } from "sonner";

interface FlashcardCreationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// 🎯 Tipos de flashcards disponíveis
const FLASHCARD_TYPES = [
  {
    id: 'qa',
    name: 'Pergunta e Resposta',
    description: 'Flashcard tradicional com pergunta na frente e resposta no verso',
    icon: BookOpen,
    color: 'bg-blue-100 text-blue-700 border-blue-200'
  },
  {
    id: 'vf',
    name: 'Verdadeiro ou Falso',
    description: 'Afirmação para julgar como verdadeira ou falsa',
    icon: CheckCircle,
    color: 'bg-green-100 text-green-700 border-green-200'
  },
  {
    id: 'multipla',
    name: 'Múltipla Escolha',
    description: 'Pergunta com 4 alternativas de resposta',
    icon: Brain,
    color: 'bg-purple-100 text-purple-700 border-purple-200'
  },
  {
    id: 'cloze',
    name: 'Cloze Deletion',
    description: 'Texto com lacunas para completar',
    icon: XCircle,
    color: 'bg-orange-100 text-orange-700 border-orange-200'
  }
];

export const FlashcardCreationDialog: React.FC<FlashcardCreationDialogProps> = ({
  open,
  onOpenChange
}) => {
  const {
    isGenerating,
    isImporting,
    generateFlashcards,
    createManualFlashcard,
    importSingleFlashcard,
    clearGeneratedCards,
    hasCurrentQuestion
  } = useQuestionFlashcardCreation();

  const { questionNumber, totalQuestions } = useCurrentQuestionInfo();

  // 🎯 Estados do componente
  const [flashcards, setFlashcards] = useState<Array<{id: string, front: string, back: string, isGenerated?: boolean}>>([
    { id: '1', front: '', back: '', isGenerated: false }
  ]);
  const [savedFlashcardIds, setSavedFlashcardIds] = useState<Set<string>>(new Set());
  const [selectedType, setSelectedType] = useState<string>('qa');
  const [quantity, setQuantity] = useState(1);

  // 🎯 Função para gerar flashcards com IA
  const handleAIGeneration = async () => {
    if (!hasCurrentQuestion) {
      return;
    }

    try {
      const generated = await generateFlashcards({
        types: [selectedType],
        quantity: quantity
      });

      // Substituir flashcards atuais pelos gerados
      if (generated && generated.length > 0) {
        const newFlashcards = generated.map((card, index) => ({
          id: `generated-${index}`,
          front: card.front,
          back: card.back,
          isGenerated: true
        }));
        setFlashcards(newFlashcards);
      }

    } catch (error) {
      // Erro já tratado no hook
      console.error('Erro ao gerar flashcards:', error);
    }
  };

  // 🎯 Função para adicionar novo flashcard
  const addFlashcard = () => {
    const newId = (flashcards.length + 1).toString();
    setFlashcards([...flashcards, { id: newId, front: '', back: '', isGenerated: false }]);
  };

  // 🎯 Função para remover flashcard
  const removeFlashcard = (id: string) => {
    if (flashcards.length > 1) {
      setFlashcards(flashcards.filter(card => card.id !== id));
    }
  };

  // 🎯 Função para atualizar flashcard
  const updateFlashcard = (id: string, field: 'front' | 'back', value: string) => {
    setFlashcards(flashcards.map(card =>
      card.id === id ? { ...card, [field]: value } : card
    ));
  };

  // 🎯 Função para salvar todos os flashcards
  const handleSaveAll = async () => {
    const validFlashcards = flashcards.filter(card =>
      card.front.trim() && card.back.trim()
    );

    if (validFlashcards.length === 0) {
      return;
    }

    try {
      for (const card of validFlashcards) {
        await createManualFlashcard({
          front: card.front.trim(),
          back: card.back.trim()
        });
      }

      // Limpar e fechar após sucesso
      setFlashcards([{ id: '1', front: '', back: '', isGenerated: false }]);
      onOpenChange(false);

    } catch (error) {
      // Erro já tratado no hook
      console.error('Erro ao criar flashcards:', error);
    }
  };

  // 🎯 Função para salvar flashcard individual
  const handleSaveSingle = async (flashcard: {id: string, front: string, back: string}) => {
    try {
      await importSingleFlashcard({
        front: flashcard.front,
        back: flashcard.back
      });

      // Marcar como salvo
      setSavedFlashcardIds(prev => new Set(prev).add(flashcard.id));

      toast.success('Flashcard salvo com sucesso!');
    } catch (error) {
      console.error('Erro ao salvar flashcard:', error);
      toast.error('Erro ao salvar flashcard');
    }
  };

  // 🎯 Função para fechar dialog e limpar estado
  const handleClose = (open: boolean) => {
    if (!open) {
      // Limpar estado quando fechar
      setFlashcards([{ id: '1', front: '', back: '', isGenerated: false }]);
      setSavedFlashcardIds(new Set());
      setSelectedType('qa');
      setQuantity(1);
      clearGeneratedCards();
    }
    onOpenChange(open);
  };

  // 🎯 Validar se há flashcards válidos (excluindo os já salvos)
  const validFlashcardsCount = flashcards.filter(card =>
    card.front.trim() && card.back.trim() && !savedFlashcardIds.has(card.id)
  ).length;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] p-0 flex flex-col bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700"
        aria-describedby="flashcard-dialog-description"
      >
        <DialogHeader className="sr-only">
          <DialogTitle>Criar Flashcards</DialogTitle>
          <DialogDescription id="flashcard-dialog-description">
            Crie flashcards personalizados a partir desta questão usando IA ou manualmente
          </DialogDescription>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            {/* 🎯 Título Mobile */}
            <div className="text-center">
              <h2 className="text-lg font-semibold flex items-center justify-center gap-2 text-gray-900 dark:text-gray-100">
                <BookOpen className="h-5 w-5" />
                {hasCurrentQuestion && totalQuestions > 0
                  ? `Flashcards da Questão ${questionNumber}/${totalQuestions}`
                  : 'Flashcards da Questão'
                }
              </h2>
            </div>
          {/* 🎯 Geração com IA */}
          <div className="bg-blue-50 dark:bg-blue-950/50 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <h3 className="font-medium text-blue-900 dark:text-blue-100">Gerar com IA</h3>
            </div>

            <div className="space-y-4 mb-4">
              <div>
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Tipo de Flashcard</Label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  disabled={!hasCurrentQuestion || isGenerating || isImporting}
                >
                  <option value="qa">Pergunta e Resposta</option>
                  <option value="vf">Verdadeiro ou Falso</option>
                  <option value="multipla">Múltipla Escolha</option>
                  <option value="cloze">Completar Lacuna</option>
                </select>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Quantidade</Label>
                <div className="flex items-center justify-center gap-3 mt-1">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1 || isGenerating || isImporting}
                    className="h-8 w-8 p-0"
                  >
                    -
                  </Button>
                  <span className="px-4 py-2 border rounded text-sm min-w-[3rem] text-center font-medium bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100">
                    {quantity}
                  </span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setQuantity(Math.min(5, quantity + 1))}
                    disabled={quantity >= 5 || isGenerating || isImporting}
                    className="h-8 w-8 p-0"
                  >
                    +
                  </Button>
                </div>
              </div>
            </div>

            <Button
              onClick={handleAIGeneration}
              disabled={isGenerating || !hasCurrentQuestion}
              className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 text-white"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Gerando...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Gerar {quantity} Flashcard{quantity !== 1 ? 's' : ''}
                </>
              )}
            </Button>
          </div>

          {/* 🎯 Lista de Flashcards */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900 dark:text-gray-100">Flashcards</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addFlashcard}
                disabled={!hasCurrentQuestion || isGenerating}
              >
                <BookOpen className="h-4 w-4 mr-1" />
                Adicionar
              </Button>
            </div>

            <div className="space-y-3">
              {flashcards.map((card, index) => (
                <div key={card.id} className="border rounded-lg p-3 space-y-3 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Flashcard {index + 1}
                      {card.isGenerated && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          IA
                        </Badge>
                      )}
                    </span>
                    <div className="flex items-center gap-2">
                      {/* Botão de importação individual */}
                      {card.front.trim() && card.back.trim() && (
                        <Button
                          type="button"
                          variant={savedFlashcardIds.has(card.id) ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleSaveSingle(card)}
                          disabled={isImporting || !hasCurrentQuestion || savedFlashcardIds.has(card.id)}
                          className={savedFlashcardIds.has(card.id)
                            ? "text-green-600 bg-green-50 border-green-200"
                            : "text-blue-600 hover:text-blue-700"
                          }
                        >
                          {isImporting ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : savedFlashcardIds.has(card.id) ? (
                            <>
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Importado
                            </>
                          ) : (
                            <>
                              <BookOpen className="h-3 w-3 mr-1" />
                              Importar
                            </>
                          )}
                        </Button>
                      )}
                      {/* Botão de remoção */}
                      {flashcards.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFlashcard(card.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div>
                      <Label className="text-xs text-gray-600 dark:text-gray-400">Frente</Label>
                      <Textarea
                        value={card.front}
                        onChange={(e) => updateFlashcard(card.id, 'front', e.target.value)}
                        placeholder="Digite a pergunta..."
                        className="mt-1 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                        rows={2}
                        disabled={!hasCurrentQuestion || isGenerating}
                      />
                    </div>

                    <div>
                      <Label className="text-xs text-gray-600 dark:text-gray-400">Verso</Label>
                      <Textarea
                        value={card.back}
                        onChange={(e) => updateFlashcard(card.id, 'back', e.target.value)}
                        placeholder="Digite a resposta..."
                        className="mt-1 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                        rows={2}
                        disabled={!hasCurrentQuestion || isGenerating}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 🎯 Botões de Ação */}
          {validFlashcardsCount > 0 && (
            <div className="flex flex-col gap-3 pt-6 border-t border-gray-200 dark:border-gray-700 mt-6">
              <Button
                onClick={handleSaveAll}
                disabled={validFlashcardsCount === 0 || isImporting || !hasCurrentQuestion}
                className="w-full"
              >
                {isImporting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <BookOpen className="h-4 w-4 mr-2" />
                    Importar {validFlashcardsCount} Flashcard{validFlashcardsCount !== 1 ? 's' : ''}
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => handleClose(false)}
                className="w-full"
              >
                Fechar
              </Button>
            </div>
          )}

          </div> {/* Fim do space-y-6 */}
        </div> {/* Fim do scroll container */}
      </DialogContent>
    </Dialog>
  );
};
