/**
 * Sistema de Reset Diário Simplificado para Insights
 * Reseta apenas quando o dia muda e o usuário acessa pela primeira vez
 */

import type { TemperatureCategory } from '@/types/insights';

/**
 * Gera identificador único do dia atual (YYYY-MM-DD) usando timezone do Brasil
 */
export const getCurrentDateKey = (): string => {
  const today = new Date();
  // Usar timezone do Brasil para determinar o dia atual
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'America/Sao_Paulo',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
  return formatter.format(today);
};

/**
 * Verifica se é um novo dia comparado com o último acesso
 * @param lastAccessDate - Data do último acesso (YYYY-MM-DD)
 * @returns true se é um novo dia
 */
export const isNewDay = (lastAccessDate?: string): boolean => {
  const today = getCurrentDateKey();
  const isNew = !lastAccessDate || lastAccessDate !== today;

  // ✅ LIMPEZA: Log removido - novo dia detectado

  return isNew;
};

/**
 * Salva a data do último acesso no localStorage
 */
export const saveLastAccessDate = (): void => {
  const today = getCurrentDateKey();
  localStorage.setItem('insights_last_access', today);
  // ✅ LIMPEZA: Log removido - data de acesso salva
};

/**
 * Recupera a data do último acesso do localStorage
 */
export const getLastAccessDate = (): string | null => {
  return localStorage.getItem('insights_last_access');
};

/**
 * Limpa o cache de insights (forçar nova busca)
 */
export const clearInsightsCache = (): void => {
  // Limpar cache do localStorage se existir
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.startsWith('insights_cache_')) {
      localStorage.removeItem(key);
    }
  });

  // ✅ LIMPEZA: Log removido - limpeza de cache rotineira
};

/**
 * Executa reset diário completo
 * Deve ser chamado quando um novo dia é detectado
 */
export const performDailyReset = (): void => {
  // ✅ LIMPEZA: Logs removidos - reset diário rotineiro

  // Limpar cache
  clearInsightsCache();

  // Salvar nova data de acesso
  saveLastAccessDate();
};

/**
 * Verifica se precisa fazer reset e executa se necessário
 * @returns true se foi feito reset, false caso contrário
 */
export const checkAndPerformDailyReset = (): boolean => {
  const lastAccess = getLastAccessDate();

  if (isNewDay(lastAccess)) {
    performDailyReset();
    return true;
  }

  return false;
};

/**
 * Gera chave de cache simples baseada na data
 */
export const getDailyCacheKey = (baseKey: string): string => {
  const dateKey = getCurrentDateKey();
  return `${baseKey}_${dateKey}`;
};
