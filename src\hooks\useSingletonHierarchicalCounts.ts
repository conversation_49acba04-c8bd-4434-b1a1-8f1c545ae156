/**
 * 🎯 SINGLETON CACHE PARA CONTAGENS HIERÁRQUICAS
 * 
 * Evita 100% das requisições duplicadas usando cache singleton
 * e coordenação entre múltiplas instâncias do hook.
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useDomain } from '@/hooks/useDomain';
import type { SelectedFilters } from '@/types/question';

// Cache singleton global para coordenar entre instâncias
class HierarchicalCountsCache {
  private static instance: HierarchicalCountsCache;
  private cache = new Map<string, any>();
  private pendingQueries = new Map<string, Promise<any>>();

  static getInstance(): HierarchicalCountsCache {
    if (!HierarchicalCountsCache.instance) {
      HierarchicalCountsCache.instance = new HierarchicalCountsCache();
    }
    return HierarchicalCountsCache.instance;
  }

  generateKey(selectedFilters: SelectedFilters, targetLevel: string, domain: string): string {
    return [
      targetLevel,
      domain,
      selectedFilters.specialties?.sort().join(',') || 'none',
      selectedFilters.themes?.sort().join(',') || 'none',
      selectedFilters.focuses?.sort().join(',') || 'none',
      selectedFilters.locations?.sort().join(',') || 'none',
      selectedFilters.years?.sort().join(',') || 'none'
    ].join('|');
  }

  async getCounts(
    selectedFilters: SelectedFilters,
    targetLevel: 'locations' | 'years' | 'formats' | 'types',
    domain: string
  ): Promise<Record<string, number>> {
    const key = this.generateKey(selectedFilters, targetLevel, domain);
    
    // Se já está em cache, retornar imediatamente
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    // Se já há uma query pendente, aguardar ela
    if (this.pendingQueries.has(key)) {
      return this.pendingQueries.get(key);
    }

    // Verificar se deve fazer fetch
    const shouldFetch = this.shouldFetch(selectedFilters, targetLevel);
    if (!shouldFetch) {
      const emptyResult = {};
      this.cache.set(key, emptyResult);
      return emptyResult;
    }

    // Criar nova query
    const queryPromise = this.executeQuery(selectedFilters, targetLevel, domain);
    
    // Armazenar promise pendente
    this.pendingQueries.set(key, queryPromise);

    try {
      const result = await queryPromise;
      
      // Armazenar no cache
      this.cache.set(key, result);
      
      // Remover da lista de pendentes
      this.pendingQueries.delete(key);
      
      // Limpar cache antigo (manter apenas últimas 10 entradas)
      if (this.cache.size > 10) {
        const firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
      }
      
      return result;
    } catch (error) {
      // Remover da lista de pendentes em caso de erro
      this.pendingQueries.delete(key);
      throw error;
    }
  }

  private shouldFetch(selectedFilters: SelectedFilters, targetLevel: string): boolean {
    const hasCategoryFilters = (
      (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
      (selectedFilters.themes && selectedFilters.themes.length > 0) ||
      (selectedFilters.focuses && selectedFilters.focuses.length > 0)
    );

    const hasLocationFilters = selectedFilters.locations && selectedFilters.locations.length > 0;
    const hasYearFilters = selectedFilters.years && selectedFilters.years.length > 0;

    if (targetLevel === 'locations') {
      return hasCategoryFilters;
    }
    
    if (targetLevel === 'years') {
      return hasCategoryFilters || hasLocationFilters;
    }
    
    if (targetLevel === 'formats') {
      return hasCategoryFilters || hasLocationFilters || hasYearFilters;
    }

    if (targetLevel === 'types') {
      return hasCategoryFilters || hasLocationFilters || hasYearFilters;
    }

    return false;
  }

  private async executeQuery(
    selectedFilters: SelectedFilters,
    targetLevel: 'locations' | 'years' | 'formats' | 'types',
    domain: string
  ): Promise<Record<string, number>> {
    const counts: Record<string, number> = {};

    // Construir query base
    let query = supabase
      .from('questions')
      .select(targetLevel === 'locations' ? 'exam_location' :
             targetLevel === 'years' ? 'exam_year' :
             targetLevel === 'types' ? 'assessment_type' : 'question_format')
      .eq('knowledge_domain', domain || 'residencia');

    // Aplicar filtros de categoria
    const hasCategoryFilters = (
      (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
      (selectedFilters.themes && selectedFilters.themes.length > 0) ||
      (selectedFilters.focuses && selectedFilters.focuses.length > 0)
    );

    if (hasCategoryFilters) {
      const categoryConditions = [];
      
      if (selectedFilters.specialties?.length > 0) {
        categoryConditions.push(`specialty_id.in.(${selectedFilters.specialties.join(',')})`);
      }
      
      if (selectedFilters.themes?.length > 0) {
        categoryConditions.push(`theme_id.in.(${selectedFilters.themes.join(',')})`);
      }
      
      if (selectedFilters.focuses?.length > 0) {
        categoryConditions.push(`focus_id.in.(${selectedFilters.focuses.join(',')})`);
      }
      
      if (categoryConditions.length > 0) {
        query = query.or(categoryConditions.join(','));
      }
    }

    // Aplicar filtros de localização (para anos e formatos)
    const hasLocationFilters = selectedFilters.locations && selectedFilters.locations.length > 0;
    if ((targetLevel === 'years' || targetLevel === 'formats') && hasLocationFilters) {
      // ✅ CORREÇÃO: Limitar locations para evitar URLs longas
      if (selectedFilters.locations.length > 30) {
        console.warn(`⚠️ [useSingletonHierarchicalCounts] Muitas locations (${selectedFilters.locations.length}), limitando para 30`);
        query = query.in('exam_location', selectedFilters.locations.slice(0, 30));
      } else {
        query = query.in('exam_location', selectedFilters.locations);
      }
    }

    // Aplicar filtros de ano (para formatos e types)
    const hasYearFilters = selectedFilters.years && selectedFilters.years.length > 0;
    if ((targetLevel === 'formats' || targetLevel === 'types') && hasYearFilters) {
      query = query.in('exam_year', selectedFilters.years.map(y => parseInt(y)));
    }

    const { data, error } = await query;

    if (error) {
      console.error('❌ [SingletonCache] Erro na query:', error);
      throw error;
    }

    if (data) {
      data.forEach((item: any) => {
        const key = targetLevel === 'locations' ? item.exam_location :
                   targetLevel === 'years' ? item.exam_year?.toString() :
                   targetLevel === 'types' ? item.assessment_type :
                   item.question_format;

        if (key) {
          counts[key] = (counts[key] || 0) + 1;
        }
      });
    }


    return counts;
  }

  clearCache(): void {
    this.cache.clear();
    this.pendingQueries.clear();
  }
}

// Hook que usa o singleton cache
export const useSingletonHierarchicalCounts = (
  selectedFilters: SelectedFilters,
  targetLevel: 'locations' | 'years' | 'formats' | 'types'
) => {
  const { domain } = useDomain();
  const cache = HierarchicalCountsCache.getInstance();

  return useQuery({
    queryKey: [
      'singleton-hierarchical-counts',
      cache.generateKey(selectedFilters, targetLevel, domain || 'residencia')
    ],
    queryFn: () => cache.getCounts(selectedFilters, targetLevel, domain || 'residencia'),
    enabled: !!domain,
    staleTime: 10 * 60 * 1000, // 10 minutos
    gcTime: 60 * 60 * 1000, // 1 hora
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1
  });
};

// Hooks específicos
export const useSingletonYearCounts = (selectedFilters: SelectedFilters) => {
  return useSingletonHierarchicalCounts(selectedFilters, 'years');
};

export const useSingletonFormatCounts = (selectedFilters: SelectedFilters) => {
  return useSingletonHierarchicalCounts(selectedFilters, 'formats');
};

export const useSingletonLocationCounts = (selectedFilters: SelectedFilters) => {
  return useSingletonHierarchicalCounts(selectedFilters, 'locations');
};

export const useSingletonTypeCounts = (selectedFilters: SelectedFilters) => {
  return useSingletonHierarchicalCounts(selectedFilters, 'types');
};
