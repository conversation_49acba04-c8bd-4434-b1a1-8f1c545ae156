
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { FlashcardWithHierarchy, SelectedFilters } from "@/types/flashcard";
import { useStaticFlashcardHierarchy } from "@/hooks/useStaticDataCache";
import { useUser } from "@supabase/auth-helpers-react";

export const useFlashcardLoader = (filters?: SelectedFilters) => {
  const { data: hierarchyData, isLoading: hierarchyLoading } = useStaticFlashcardHierarchy();
  const user = useUser();

  return useQuery({
    queryKey: ['flashcards', filters, hierarchyData ? 'with-hierarchy' : 'loading-hierarchy'],
    queryFn: async () => {
      // Aguardar carregamento da hierarquia antes de processar flashcards
      if (!hierarchyData) {
        throw new Error('Aguardando carregamento da hierarquia...');
      }
      // Otimizado: Buscar apenas dados dos flashcards sem joins para evitar múltiplas requisições
      let query = supabase
        .from('flashcards_cards')
        .select('*')
        .eq('user_id', user?.id)  // ✅ SEGURANÇA: Filtrar apenas cards do usuário
        .order('created_at', { ascending: false });

      // Apply filters if provided
      if (filters) {
        if (filters.specialties && filters.specialties.length > 0) {
          query = query.in('specialty_id', filters.specialties);
        }
        if (filters.themes && filters.themes.length > 0) {
          query = query.in('theme_id', filters.themes);
        }
        if (filters.focuses && filters.focuses.length > 0) {
          query = query.in('focus_id', filters.focuses);
        }
      }

      const { data: cards, error } = await query;

      if (error) {
        throw error;
      }

      // Resolver nomes da hierarquia usando dados estáticos
      const transformedCards = cards?.map(card => ({
        ...card,
        hierarchy: {
          specialty: card.specialty_id ? {
            id: card.specialty_id,
            name: hierarchyData?.specialties.find(s => s.id === card.specialty_id)?.name || 'Especialidade Desconhecida'
          } : null,
          theme: card.theme_id ? {
            id: card.theme_id,
            name: hierarchyData?.themes.find(t => t.id === card.theme_id)?.name || 'Tema Desconhecido'
          } : null,
          focus: card.focus_id ? {
            id: card.focus_id,
            name: hierarchyData?.focuses.find(f => f.id === card.focus_id)?.name || 'Foco Desconhecido'
          } : null,
          extraFocus: card.extrafocus_id ? {
            id: card.extrafocus_id,
            name: hierarchyData?.extrafocuses.find(e => e.id === card.extrafocus_id)?.name || 'Extra Foco Desconhecido'
          } : null
        }
      })) as FlashcardWithHierarchy[];

      return transformedCards;
    },
    enabled: !!hierarchyData, // Só executa quando a hierarquia estiver carregada
  });
};
