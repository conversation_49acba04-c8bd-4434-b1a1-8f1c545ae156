import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Flag } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useDarkMode } from "@/contexts/DarkModeContext";

interface QuestionReportDialogProps {
  questionId: string;
  userId: string;
  children?: React.ReactNode;
}

export const QuestionReportDialog = ({ questionId, userId, children }: QuestionReportDialogProps) => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { isDarkMode } = useDarkMode();

  const handleSubmit = async () => {
    if (!message.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, descreva o problema encontrado.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const { error } = await supabase
        .from("question_reports")
        .insert([
          {
            question_id: questionId,
            user_id: userId,
            message: message.trim(),
          },
        ]);

      if (error) throw error;

      toast({
        title: "Report enviado",
        description: "Obrigado por nos ajudar a melhorar a plataforma!",
      });

      setMessage("");
      setOpen(false);
    } catch (error) {
      toast({
        title: "Erro ao enviar report",
        description: "Não foi possível enviar seu report. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button
            variant="outline"
            size="sm"
            className="flex gap-2"
          >
            <Flag className="h-4 w-4" />
            Reportar
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className={`sm:max-w-[425px] ${
        isDarkMode
          ? 'bg-gradient-to-br from-gray-800 via-gray-900 to-gray-800 border-gray-600'
          : 'bg-gradient-to-br from-white via-blue-50 to-slate-50 border-black'
      }`}>
        <DialogHeader>
          <DialogTitle className={isDarkMode ? 'text-gray-100' : 'text-gray-900'}>
            Reportar Questão
          </DialogTitle>
          <DialogDescription className={isDarkMode ? 'text-gray-300' : 'text-muted-foreground'}>
            Descreva o problema encontrado nesta questão. Sua contribuição nos ajuda a melhorar a plataforma.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Textarea
            placeholder="Descreva o problema encontrado..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className={`min-h-[100px] ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder:text-gray-400'
                : 'bg-background border-input text-foreground'
            }`}
          />
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isSubmitting}
            className={isDarkMode
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-primary hover:bg-primary/90 text-primary-foreground'
            }
          >
            {isSubmitting ? "Enviando..." : "Enviar Report"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};