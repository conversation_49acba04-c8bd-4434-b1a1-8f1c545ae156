
import React from "react";
import { Button } from "@/components/ui/button";

interface PanelStepOneProps {
  totalQuestions: number;
  incorrectQuestions: number;
  selectedType: string;
  setSelectedType: (type: string) => void;
  flashcardType: "cloze" | "vf" | "option" | "qa";
  setFlashcardType: (type: "cloze" | "vf" | "option" | "qa") => void;
  quantity: number;
  setQuantity: (qty: number) => void;
  incrementQuantity: () => void;
  decrementQuantity: () => void;
  handleGenerateFlashcards: () => void;
  setIsExpanded: (val: boolean) => void;
}

export const FlashcardPanelStepOne: React.FC<PanelStepOneProps> = ({
  totalQuestions,
  incorrectQuestions,
  selectedType,
  setSelectedType,
  flashcardType,
  setFlashcardType,
  quantity,
  setQuantity,
  incrementQuantity,
  decrementQuantity,
  handleGenerateFlashcards,
  setIsExpanded
}) => {
  const flashcardTypes = [
    { value: "cloze", label: "Lacunas" },
    { value: "vf", label: "V/F" },
    { value: "multipla", label: "Múl<PERSON>la" },
    { value: "qa", label: "Pergunta" }
  ];

  return (
    <div className="space-y-4">
      <p className="text-sm font-semibold text-gray-700 uppercase tracking-wider">
        Fonte dos Flashcards
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <button
          className={`w-full p-4 text-left rounded-xl border-2 transition-all duration-300 ${
            selectedType === "all"
              ? "border-primary bg-primary/10 shadow-sm"
              : "border-gray-200 hover:border-primary/50 hover:bg-primary/5"
          }`}
          onClick={() => setSelectedType("all")}
        >
          <div className="flex items-center space-x-3">
            <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
              selectedType === "all"
                ? "bg-primary text-white"
                : "bg-gray-200 text-gray-500"
            }`}>
              {totalQuestions}
            </div>
            <p className="font-semibold text-gray-800">Todas as Questões Respondidas</p>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            Criar flashcards baseados em todas as {totalQuestions} questões respondidas nesta sessão
          </p>
        </button>

        <button
          className={`w-full p-4 text-left rounded-xl border-2 transition-all duration-300 ${
            selectedType === "incorrect"
              ? "border-red-500 bg-red-50/50 shadow-sm"
              : "border-gray-200 hover:border-red-500/50 hover:bg-red-50/20"
          }`}
          onClick={() => setSelectedType("incorrect")}
          disabled={incorrectQuestions === 0}
        >
          <div className="flex items-center space-x-3">
            <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
              selectedType === "incorrect"
                ? "bg-red-500 text-white"
                : "bg-gray-200 text-gray-500"
            }`}>
              {incorrectQuestions}
            </div>
            <p className="font-semibold text-gray-800">Questões Incorretas</p>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            {incorrectQuestions > 0
              ? `Criar flashcards baseados nas ${incorrectQuestions} questões com erro`
              : "Sem questões incorretas nesta sessão"}
          </p>
        </button>
      </div>

      <div className="space-y-4 mt-6">
        <p className="text-sm font-semibold text-gray-700 uppercase tracking-wider">
          Tipo e Quantidade
        </p>
        <div className="flex flex-col sm:flex-row gap-4 items-stretch">
          <div className="flex-1">
            <label className="sr-only" htmlFor="flashcard-type-select">Tipo de Flashcard</label>
            <select
              id="flashcard-type-select"
              value={flashcardType}
              onChange={(e) => setFlashcardType(e.target.value as any)}
              className="w-full rounded-xl border-2 border-[#aab6ff] bg-[#eef2fa] py-3 px-4 text-base font-medium text-[#373f60] focus:outline-none focus:ring-2 focus:ring-[#aab6ff] transition-shadow shadow-sm"
            >
              {flashcardTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center justify-between bg-[#eef2fa] rounded-xl px-4 py-2 border-2 border-[#aab6ff] min-w-[140px]">
            <button
              onClick={decrementQuantity}
              className="p-2 rounded-full hover:bg-[#dde3fb] transition-colors"
              disabled={quantity <= 1}
              aria-label="Diminuir quantidade"
              type="button"
            >
              <span className="sr-only">Diminuir</span>
              <svg className="h-5 w-5 text-[#7482be]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
                <path d="M5 12h14" />
              </svg>
            </button>
            <span className="text-lg font-bold text-[#35395e]">{quantity}</span>
            <button
              onClick={incrementQuantity}
              className="p-2 rounded-full hover:bg-[#dde3fb] transition-colors"
              disabled={quantity >= 20}
              aria-label="Aumentar quantidade"
              type="button"
            >
              <span className="sr-only">Aumentar</span>
              <svg className="h-5 w-5 text-[#7482be]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
                <path d="M5 12h14" />
                <path d="M12 5v14" />
              </svg>
            </button>
          </div>
        </div>
        <p className="text-xs text-gray-500 text-right">
          Máximo: 20 flashcards por geração
        </p>
      </div>

      <div className="mt-8 flex justify-end space-x-3">
        <Button
          variant="ghost"
          onClick={() => setIsExpanded(false)}
        >
          Cancelar
        </Button>
        <Button
          onClick={handleGenerateFlashcards}
          disabled={incorrectQuestions === 0 && selectedType === "incorrect"}
          variant="default"
        >
          Gerar Flashcards
        </Button>
      </div>
    </div>
  );
};
