import React, { useState, useEffect, useRef, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import { useNavigate } from "react-router-dom";
import { toast as sonnerToast } from "sonner";
import { BookOpen, BarChart3, Calendar, Brain, Sparkles, ArrowRight, CheckCircle, X, Bot, MessageSquare } from "lucide-react";

type TutorialStep = {
  title: string;
  description: string;
  targetSelector?: string;
  position?: "top" | "bottom" | "left" | "right";
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
  badge?: string;
};

export const StudyPlatformTutorial = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { tutorialCompleted, markTutorialAsCompleted, isLoading } = useUserPreferences();
  const [currentStep, setCurrentStep] = useState(0);
  const [showTutorial, setShowTutorial] = useState(false);
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const [showFinalDialog, setShowFinalDialog] = useState(false);
  const positionUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tutorialRef = useRef<HTMLDivElement>(null);

  const tutorialSteps: TutorialStep[] = [
    {
      title: "📚 Banco de Questões",
      description: "Comece aqui! Acesse milhares de questões organizadas por especialidade, com comentários detalhados gerados por IA e análise de desempenho em tempo real.",
      targetSelector: "[data-tutorial='questions']",
      icon: BookOpen,
      color: "from-blue-500 to-indigo-600"
    },
    {
      title: "🧠 Flashcards Inteligentes",
      description: "Funcionalidade em desenvolvimento! Em breve você poderá memorizar conceitos com repetição espaçada baseada em IA. No momento, mantenha o foco nas questões.",
      targetSelector: "[data-tutorial='flashcards']",
      icon: Brain,
      color: "from-gray-400 to-gray-600",
      badge: "EM BREVE"
    },
    {
      title: "🩺 Dr. Will - IA Médica",
      description: "Converse com nossa inteligência artificial especializada em medicina! O Dr. Will pode esclarecer dúvidas, explicar conceitos e ajudar com casos clínicos.",
      targetSelector: "[data-tutorial='dr-will']",
      icon: Bot,
      color: "from-purple-500 to-indigo-600"
    },
    {
      title: "📊 Central de Progresso",
      description: "Acompanhe sua evolução com analytics detalhados, métricas de desempenho e insights personalizados sobre seus estudos e acertos.",
      targetSelector: "[data-tutorial='progress']",
      icon: BarChart3,
      color: "from-green-500 to-emerald-600"
    },
    {
      title: "🏆 Ranking Competitivo",
      description: "Compare seu desempenho com outros estudantes, veja sua posição no ranking e motive-se a alcançar o topo da classificação.",
      targetSelector: "[data-tutorial='ranking']",
      icon: CheckCircle,
      color: "from-yellow-500 to-orange-600"
    },
    {
      title: "📅 Cronograma Personalizado",
      description: "Organize seus estudos com cronogramas adaptativos, lembretes automáticos e planejamento personalizado para sua especialidade-alvo.",
      targetSelector: "[data-tutorial='schedule']",
      icon: Calendar,
      color: "from-purple-500 to-violet-600"
    },
    {
      title: "💬 Sistema de Feedback",
      description: "Sua opinião é importante! Use o sistema de feedback para reportar bugs, sugerir melhorias ou compartilhar sua experiência com a plataforma.",
      targetSelector: "[data-tutorial='feedback']",
      icon: MessageSquare,
      color: "from-orange-500 to-red-600"
    },
    {
      title: "📈 Estudos de Hoje",
      description: "Veja o que está programado para hoje, acompanhe seu progresso diário e mantenha sua sequência de estudos ativa para maximizar resultados.",
      targetSelector: "[data-tutorial='today']",
      icon: CheckCircle,
      color: "from-emerald-500 to-green-600"
    },
    {
      title: "📋 Histórico de Sessões",
      description: "Revise todas as suas sessões anteriores de questões e flashcards. Acompanhe sua evolução ao longo do tempo e identifique padrões.",
      targetSelector: "[data-tutorial='history']",
      icon: BarChart3,
      color: "from-teal-500 to-cyan-600"
    },
    {
      title: "🎉 Seja Bem-vindo ao MedEvo!",
      description: "Agora você conhece todas as funcionalidades! Está pronto para começar sua jornada de estudos e conquistar sua aprovação?",
      icon: Sparkles,
      color: "from-pink-500 to-rose-600",
      badge: "PRONTO!"
    }
  ];

  // Usar dados do hook consolidado
  useEffect(() => {
    if (!isLoading && user) {
      if (!tutorialCompleted) {
        setShowTutorial(true);
      }
    }
  }, [tutorialCompleted, isLoading, user]);

  const updatePosition = useCallback(() => {
    if (!tutorialCompleted && showTutorial) {
      if (positionUpdateTimeoutRef.current) {
        clearTimeout(positionUpdateTimeoutRef.current);
      }

      positionUpdateTimeoutRef.current = setTimeout(() => {
        const currentTarget = tutorialSteps[currentStep]?.targetSelector;
        if (currentTarget) {
          const element = document.querySelector(currentTarget);
          if (element) {
            const rect = element.getBoundingClientRect();
            setTargetRect(rect);

            const isInViewport = (
              rect.top >= 0 &&
              rect.left >= 0 &&
              rect.bottom <= window.innerHeight &&
              rect.right <= window.innerWidth
            );

            if (!isInViewport) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          } else {
            console.log(`Elemento com seletor ${currentTarget} não encontrado`);
          }
        } else {
          setTargetRect(null);
        }
      }, 100);
    }
  }, [currentStep, tutorialSteps, tutorialCompleted, showTutorial]);

  useEffect(() => {
    if (showTutorial) {
      updatePosition();

      window.addEventListener('resize', updatePosition);

      window.addEventListener('scroll', updatePosition, { passive: true });

      return () => {
        window.removeEventListener('resize', updatePosition);
        window.removeEventListener('scroll', updatePosition);
        if (positionUpdateTimeoutRef.current) {
          clearTimeout(positionUpdateTimeoutRef.current);
        }
      };
    }
  }, [updatePosition, showTutorial]);

  // Scroll para o topo quando o dialog final aparecer
  useEffect(() => {
    if (showFinalDialog) {
      // Pequeno delay para garantir que o DOM foi atualizado
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);
    }
  }, [showFinalDialog]);

  const handleTutorialComplete = async () => {
    try {
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);

      await markTutorialAsCompleted();

      sonnerToast.success("Tutorial concluído!", {
        description: "Agora você já sabe como usar a plataforma"
      });
    } catch (err) {
      toast({
        title: "Erro",
        description: "Não foi possível salvar o status do tutorial",
        variant: "destructive",
      });
    }
  };

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Último passo - scroll para o topo e mostrar dialog final
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setShowFinalDialog(true);
    }
  };

  const handleNavigateToPage = (path: string) => {
    // Scroll para o topo sempre (desktop e mobile)
    window.scrollTo({ top: 0, behavior: 'smooth' });

    setShowTutorial(false);
    setShowFinalDialog(false);
    handleTutorialComplete();

    // Se for cronograma, redirecionar com tutorial guiado
    if (path === "/schedule") {
      navigate("/schedule?guided=true&from=tutorial&step=1");
    } else {
      navigate(path);
    }
  };

  const handleStayOnDashboard = () => {
    // Scroll para o topo sempre (desktop e mobile)
    window.scrollTo({ top: 0, behavior: 'smooth' });

    setShowTutorial(false);
    setShowFinalDialog(false);
    handleTutorialComplete();
  };

  if (tutorialCompleted || (!showTutorial && !showFinalDialog) || !user) {
    return null;
  }

  const getTooltipPosition = () => {
    if (!targetRect) {
      return {
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)'
      };
    }

    return {
      top: `${Math.max(16, targetRect.top - 16 - 220)}px`,
      left: `${targetRect.left + targetRect.width / 2}px`,
      transform: 'translateX(-50%)'
    };
  };

  const tooltipStyle = currentStep === 0
    ? { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }
    : getTooltipPosition();

  const cardStyle = {
    maxWidth: window.innerWidth < 640 ? '90%' : '350px',
    width: '100%',
    zIndex: 9999,
  };

  const preventScrollStyle = {
    position: 'fixed' as const,
    inset: 0,
    width: '100vw',
    height: '100vh',
    zIndex: 1000,
    overscrollBehavior: 'none',
  };

  return (
    <div
      ref={tutorialRef}
      className="fixed inset-0 z-[1000] touch-none"
      style={preventScrollStyle}
    >
      <svg className="fixed inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1001 }}>
        <defs>
          <mask id="tutorial-mask">
            <rect width="100%" height="100%" fill="white" />
            {targetRect && (
              <rect
                x={targetRect.left}
                y={targetRect.top}
                width={targetRect.width}
                height={targetRect.height}
                fill="black"
              />
            )}
          </mask>
        </defs>
        <rect
          width="100%"
          height="100%"
          fill="rgba(0, 0, 0, 0.7)"
          mask="url(#tutorial-mask)"
        />
      </svg>

      <div className="absolute inset-0 z-[1002]" onClick={(e) => e.preventDefault()} />

      {targetRect && currentStep > 0 && (
        <div
          className="absolute z-[1003] border-2 border-primary animate-pulse rounded-md pointer-events-none"
          style={{
            top: targetRect.top,
            left: targetRect.left,
            width: targetRect.width,
            height: targetRect.height
          }}
        />
      )}

      <Card
        className="p-0 bg-white rounded-2xl shadow-2xl z-[1004] animate-in fade-in-50 slide-in-from-bottom-4 fixed border-2 border-gray-100 overflow-hidden"
        style={{...tooltipStyle, ...cardStyle}}
      >
        {/* Header com gradiente */}
        <div className={`bg-gradient-to-r ${tutorialSteps[currentStep].color || 'from-blue-500 to-purple-600'} p-4 text-white relative`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {tutorialSteps[currentStep].icon && (
                <div className="bg-white/20 p-2 rounded-lg">
                  {React.createElement(tutorialSteps[currentStep].icon!, { className: "h-5 w-5" })}
                </div>
              )}
              <div>
                <h3 className="text-lg font-bold">
                  {tutorialSteps[currentStep].title}
                </h3>
                {tutorialSteps[currentStep].badge && (
                  <Badge className="bg-white/20 text-white text-xs mt-1 border-white/30">
                    {tutorialSteps[currentStep].badge}
                  </Badge>
                )}
              </div>
            </div>
            <div className="bg-white/20 px-3 py-1 rounded-full">
              <span className="text-sm font-medium">
                {currentStep + 1}/{tutorialSteps.length}
              </span>
            </div>
          </div>
        </div>

        {/* Conteúdo */}
        <div className="p-6 space-y-4">
          <p className="text-gray-700 leading-relaxed">
            {tutorialSteps[currentStep].description}
          </p>

          {/* Barra de progresso */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`bg-gradient-to-r ${tutorialSteps[currentStep].color || 'from-blue-500 to-purple-600'} h-2 rounded-full transition-all duration-500`}
              style={{ width: `${((currentStep + 1) / tutorialSteps.length) * 100}%` }}
            />
          </div>

          {/* Botões */}
          <div className="flex justify-between items-center pt-2">
            <div className="text-xs text-gray-500">
              {currentStep === 0 ? "Vamos começar!" :
               currentStep === tutorialSteps.length - 1 ? "Quase lá!" :
               "Continue explorando"}
            </div>
            <Button
              onClick={handleNext}
              className={`bg-gradient-to-r ${tutorialSteps[currentStep].color || 'from-blue-500 to-purple-600'} hover:opacity-90 text-white font-medium px-6 py-2 rounded-lg transition-all duration-200 flex items-center gap-2`}
            >
              {currentStep < tutorialSteps.length - 1 ? (
                <>
                  Próximo
                  <ArrowRight className="h-4 w-4" />
                </>
              ) : (
                <>
                  Começar!
                  <CheckCircle className="h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>

      {/* Dialog Final - Escolha de próxima ação */}
      {showFinalDialog && (
        <Card
          className="p-0 bg-white rounded-2xl shadow-2xl z-[1005] animate-in fade-in-50 slide-in-from-bottom-4 fixed border-2 border-pink-200 overflow-hidden"
          style={{
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            maxWidth: window.innerWidth < 640 ? '90vw' : '500px',
            width: '100%',
            zIndex: 10000,
          }}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-pink-500 to-rose-600 p-6 text-white text-center relative">
            <div className="flex items-center justify-center gap-3 mb-2">
              <div className="bg-white/20 p-3 rounded-full">
                <Sparkles className="h-6 w-6" />
              </div>
              <div>
                <h3 className="text-xl font-bold">Parabéns! 🎉</h3>
                <Badge className="bg-white/20 text-white text-xs mt-1 border-white/30">
                  TUTORIAL CONCLUÍDO
                </Badge>
              </div>
            </div>
          </div>

          {/* Conteúdo */}
          <div className="p-6 space-y-4">
            {/* Chamada Principal Chamativa */}
            <div className="text-center mb-6">
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-xl p-4 mb-4">
                <h4 className="text-lg font-bold text-purple-800 mb-2">
                  🤔 Não, espera! É sério?
                </h4>
                <p className="text-purple-700 leading-relaxed text-sm">
                  Você vai <strong>montar meu cronograma</strong> baseado nas minhas <strong>instituições alvo</strong> e eu vou ter facilidade para fazer essas questões <strong>diariamente</strong> clicando em apenas um botão? E ainda vai <strong>marcar a revisão</strong>?
                </p>
              </div>

              <Button
                onClick={() => handleNavigateToPage("/schedule")}
                className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:opacity-90 text-white font-bold py-4 rounded-xl transition-all duration-200 flex items-center justify-center gap-3 text-lg shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Calendar className="h-6 w-6" />
                🚀 Criar cronograma
              </Button>
            </div>

            {/* Outras Opções */}
            <div className="space-y-3 pt-4 border-t border-gray-200">
              <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3 text-center">
                📚 Ou explore outras funcionalidades
              </div>

              <Button
                onClick={() => handleNavigateToPage("/questions")}
                className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:opacity-90 text-white font-medium py-3 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
              >
                <BookOpen className="h-4 w-4" />
                Praticar Questões Agora
              </Button>
            </div>

            {/* Opções Secundárias */}
            <div className="space-y-2 pt-2">
              <div className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2">
                📊 Outras opções
              </div>

              <Button
                onClick={() => handleNavigateToPage("/progress")}
                variant="outline"
                className="w-full text-xs py-2 border-gray-300 text-gray-600 hover:bg-gray-50 flex items-center justify-center gap-1"
              >
                <BarChart3 className="h-3 w-3" />
                Ver meu Progresso
              </Button>

              <Button
                onClick={handleStayOnDashboard}
                variant="outline"
                className="w-full text-xs py-2 border-gray-300 text-gray-500 hover:bg-gray-50 flex items-center justify-center gap-1"
              >
                <X className="h-3 w-3" />
                Explorar Dashboard por conta própria
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
