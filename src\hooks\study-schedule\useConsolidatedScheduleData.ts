/**
 * 🎯 HOOK CONSOLIDADO PARA DADOS DO CRONOGRAMA
 * 
 * Elimina duplicações consolidando queries para:
 * - study_schedules (semana atual + dados completos)
 * - study_categories (múltiplas queries otimizadas)
 */
import React from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface ConsolidatedScheduleData {
  // Dados de cronogramas
  schedules: {
    currentWeek: any | null;
    allSchedules: any[];
  };
  
  // Dados de categorias otimizados
  categories: {
    used: any[]; // Categorias já usadas no cronograma
    available: any[]; // Categorias disponíveis para seleção
  };
  
  // Dados brutos para compatibilidade
  rawData: {
    allSchedules: any[];
    allCategories: any[];
  };
}

export const useConsolidatedScheduleData = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ['consolidated-schedule-data', user?.id],
    queryFn: async (): Promise<ConsolidatedScheduleData> => {
      if (!user?.id) {
        return {
          schedules: { currentWeek: null, allSchedules: [] },
          categories: { used: [], available: [] },
          rawData: { allSchedules: [], allCategories: [] }
        };
      }



      // ✅ QUERY ÚNICA para study_schedules (todos os campos necessários)
      const currentDate = new Date();
      const dateStr = currentDate.toISOString().split('T')[0];

      const { data: allSchedules, error: schedulesError } = await supabase
        .from('study_schedules')
        .select('id, week_number, week_start_date, week_end_date, status, ai_recommendations, user_adjustments, performance_data')
        .eq('user_id', user.id)
        .order('week_number', { ascending: true });

      if (schedulesError) {
        console.error('❌ [useConsolidatedScheduleData] Erro ao buscar cronogramas:', schedulesError);
        throw schedulesError;
      }

      // Encontrar semana atual
      const currentWeek = allSchedules?.find(schedule => 
        schedule.week_start_date <= dateStr && schedule.week_end_date >= dateStr
      ) || null;



      // ✅ QUERY OTIMIZADA para study_categories
      // Estratégia: Carregar apenas categorias essenciais em vez de todas (82.6 kB)
      
      // Primeiro, extrair IDs de categorias usadas nos cronogramas
      const usedCategoryIds = new Set<string>();
      const usedSpecialtyIds = new Set<string>();
      const usedThemeIds = new Set<string>();

      allSchedules?.forEach(schedule => {
        // Extrair IDs das recomendações da IA
        if (schedule.ai_recommendations) {
          const recommendations = Array.isArray(schedule.ai_recommendations) 
            ? schedule.ai_recommendations 
            : schedule.ai_recommendations.recommendations || [];
            
          recommendations.forEach((day: any) => {
            if (day.topics) {
              day.topics.forEach((topic: any) => {
                if (topic.specialtyId) {
                  usedCategoryIds.add(topic.specialtyId);
                  usedSpecialtyIds.add(topic.specialtyId);
                }
                if (topic.themeId) {
                  usedCategoryIds.add(topic.themeId);
                  usedThemeIds.add(topic.themeId);
                }
                if (topic.focusId) {
                  usedCategoryIds.add(topic.focusId);
                }
              });
            }
          });
        }
      });

      // Query para categorias usadas + suas hierarquias
      let categoriesData: any[] = [];

      if (usedCategoryIds.size > 0) {
        // Query 1: Categorias já usadas
        const { data: usedCategories, error: usedError } = await supabase
          .from('study_categories')
          .select('id, name, type, parent_id')
          .in('id', Array.from(usedCategoryIds));

        if (usedError) {
          console.error('❌ [useConsolidatedScheduleData] Erro ao buscar categorias usadas:', usedError);
        } else {
          categoriesData.push(...(usedCategories || []));
        }

        // Query 2: Temas das especialidades usadas (para expansão)
        if (usedSpecialtyIds.size > 0) {
          const { data: relatedThemes, error: themesError } = await supabase
            .from('study_categories')
            .select('id, name, type, parent_id')
            .eq('type', 'theme')
            .in('parent_id', Array.from(usedSpecialtyIds))
            .limit(100);

          if (!themesError && relatedThemes) {
            categoriesData.push(...relatedThemes);
          }
        }

        // Query 3: Focos dos temas usados (para expansão)
        if (usedThemeIds.size > 0) {
          const { data: relatedFocuses, error: focusesError } = await supabase
            .from('study_categories')
            .select('id, name, type, parent_id')
            .eq('type', 'focus')
            .in('parent_id', Array.from(usedThemeIds))
            .limit(200);

          if (!focusesError && relatedFocuses) {
            categoriesData.push(...relatedFocuses);
          }
        }
      }

      // ✅ OTIMIZADO: Se não há categorias usadas, usar cache centralizado
      if (categoriesData.length === 0) {
        // Tentar buscar do cache centralizado primeiro
        const cachedCategories = queryClient.getQueryData(['study-categories-all']) as any[];
        if (cachedCategories) {
          const specialties = cachedCategories
            .filter(cat => cat.type === 'specialty')
            .slice(0, 50);

          if (specialties.length > 0) {
            categoriesData = specialties;
          }
        }

        // Fallback apenas se cache não disponível
        if (categoriesData.length === 0) {
          const { data: basicCategories, error: basicError } = await supabase
            .from('study_categories')
            .select('id, name, type, parent_id')
            .eq('type', 'specialty')
            .limit(50);

          if (!basicError && basicCategories) {
            categoriesData = basicCategories;
          }
        }
      }

      // Remover duplicatas
      const uniqueCategories = categoriesData.filter((category, index, self) =>
        index === self.findIndex(c => c.id === category.id)
      );



      // Separar categorias usadas vs disponíveis
      const usedCategories = uniqueCategories.filter(cat => usedCategoryIds.has(cat.id));
      const availableCategories = uniqueCategories;

      return {
        schedules: {
          currentWeek,
          allSchedules: allSchedules || []
        },
        categories: {
          used: usedCategories,
          available: availableCategories
        },
        rawData: {
          allSchedules: allSchedules || [],
          allCategories: uniqueCategories
        }
      };
    },
    enabled: !!user?.id,
    staleTime: 0, // ✅ CORREÇÃO: Sempre considerar dados como stale para refetch imediato
    gcTime: 1 * 60 * 1000, // ✅ CORREÇÃO: Reduzir cache time para 1 minuto
    refetchOnWindowFocus: true, // ✅ CORREÇÃO: Refetch ao focar na janela
    refetchOnReconnect: true,
    refetchOnMount: true, // ✅ CORREÇÃO: Sempre refetch ao montar
    retry: 2
  });



  return query;
};

/**
 * Hook otimizado para dados de cronogramas
 */
export const useOptimizedScheduleData = () => {
  const { data, isLoading, error } = useConsolidatedScheduleData();

  return {
    // Dados específicos para compatibilidade
    currentWeekSchedule: data?.schedules.currentWeek || null,
    allSchedules: data?.schedules.allSchedules || [],
    
    // Estados
    isLoading,
    error,
    
    // Dados brutos
    rawScheduleData: data?.rawData.allSchedules || []
  };
};

/**
 * Hook otimizado para categorias de estudo
 */
export const useOptimizedCategoriesData = () => {
  const { data, isLoading, error } = useConsolidatedScheduleData();

  return {
    // Categorias organizadas
    usedCategories: data?.categories.used || [],
    availableCategories: data?.categories.available || [],
    
    // Estados
    isLoading,
    error,
    
    // Dados brutos para compatibilidade
    allCategories: data?.rawData.allCategories || []
  };
};

/**
 * Hook para cache de categorias estáticas (fallback)
 */
export const useStaticCategoriesCache = (enabled: boolean = false) => {
  return useQuery({
    queryKey: ['static-categories-cache'],
    queryFn: async () => {
      console.log('🔄 [useStaticCategoriesCache] Carregando cache estático de categorias');

      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name, type, parent_id')
        .order('type, name')
        .limit(1000); // Limite razoável

      if (error) {
        console.error('❌ [useStaticCategoriesCache] Erro:', error);
        throw error;
      }

      console.log('✅ [useStaticCategoriesCache] Cache carregado:', data?.length || 0);
      return data || [];
    },
    enabled,
    staleTime: 30 * 60 * 1000, // 30 minutos
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
    refetchOnWindowFocus: false,
    retry: 1
  });
};

/**
 * Hook que processa dados consolidados para formato WeeklySchedule
 */
export const useProcessedScheduleData = () => {
  const { data, isLoading, error } = useConsolidatedScheduleData();
  const { user } = useAuth();

  return useQuery({
    queryKey: ['processed-schedule-data', user?.id, data?.schedules.allSchedules.length],
    queryFn: async () => {
      if (!data?.schedules.allSchedules.length) {
        return { recommendations: [] };
      }

      // Buscar study_schedule_items para todos os cronogramas
      const scheduleIds = data.schedules.allSchedules.map(s => s.id);

      const { data: studyItems, error: studyItemsError } = await supabase
        .from('study_schedule_items')
        .select(`
          id,
          schedule_id,
          day_of_week,
          specialty_name,
          theme_name,
          focus_name,
          specialty_id,
          theme_id,
          focus_id,
          difficulty,
          activity_description,
          start_time,
          duration,
          study_status,
          is_manual,
          metadata
        `)
        .in('schedule_id', scheduleIds)
        .order('day_order', { ascending: true });

      if (studyItemsError) {
        console.error('❌ [useProcessedScheduleData] Erro ao buscar itens:', studyItemsError);
        throw studyItemsError;
      }

      // ✅ CORREÇÃO: Processar dados para formato WeeklySchedule com informações das semanas
      const daySchedules = [];
      const weekDays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];



      // Criar DaySchedule para cada semana e dia
      for (const schedule of data.schedules.allSchedules) {
        for (const day of weekDays) {
          // Encontrar todos os itens para esta semana e dia
          const items = (studyItems || []).filter(
            item => item.schedule_id === schedule.id && item.day_of_week === day
          );

          const topics = items.map(item => {
            // ✅ CORREÇÃO: Extrair dados do metadata
            const metadata = item.metadata as any;
            const institutions = metadata?.institutions || [];
            const focusPrevalence = metadata?.focusPrevalence;

            return {
              id: item.id,
              scheduleId: item.schedule_id,
              specialty: item.specialty_name,
              theme: item.theme_name,
              focus: item.focus_name,
              specialtyId: item.specialty_id,
              themeId: item.theme_id,
              focusId: item.focus_id,
              difficulty: item.difficulty as 'Fácil' | 'Médio' | 'Difícil',
              activity: item.activity_description || "",
              startTime: item.start_time,
              duration: item.duration,
              study_status: item.study_status as 'pending' | 'completed',
              weekNumber: schedule.week_number,
              day: item.day_of_week,
              is_manual: item.is_manual,
              // ✅ CORREÇÃO: Adicionar dados das instituições
              institutions,
              focusPrevalence,
              metadata: item.metadata
            };
          });

          // ✅ CORREÇÃO: Incluir informações das semanas
          daySchedules.push({
            day,
            scheduleId: schedule.id,
            weekStartDate: schedule.week_start_date,
            weekEndDate: schedule.week_end_date,
            weekNumber: schedule.week_number,
            totalHours: topics.reduce((acc, t) => {
              if (!t.duration) return acc;
              const minutesMatch = t.duration.match(/(\d+)/);
              const minutes = minutesMatch ? parseInt(minutesMatch[1], 10) : 0;
              return acc + minutes;
            }, 0),
            topics
          });
        }
      }

      // Ordenar por semana e dia
      daySchedules.sort((a, b) => {
        if (a.weekNumber !== b.weekNumber) {
          return a.weekNumber - b.weekNumber;
        }
        return weekDays.indexOf(a.day) - weekDays.indexOf(b.day);
      });

      const recommendations = daySchedules;

      return { recommendations };
    },
    enabled: !!data?.schedules.allSchedules.length && !isLoading,
    staleTime: 0, // ✅ CORREÇÃO: Sempre considerar dados como stale
    gcTime: 1 * 60 * 1000, // ✅ CORREÇÃO: Reduzir cache time
    refetchOnWindowFocus: true, // ✅ CORREÇÃO: Refetch ao focar
    refetchOnMount: true, // ✅ CORREÇÃO: Sempre refetch ao montar
    retry: 2
  });
};
