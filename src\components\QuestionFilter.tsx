
import { useState, useEffect, use<PERSON><PERSON><PERSON>, useCallback } from "react";
import { Card } from "./ui/card";
import { useToast } from "./ui/use-toast";
import { FilterHeader } from "./filters/components/FilterHeader";
import { FilterContent } from "./filters/components/FilterContent";
import { FilterSummary } from "./filters/components/FilterSummary";
import { FilterActions } from "./filters/components/FilterActions";
import { FilterAccordion } from "./filters/components/FilterAccordion";
import { useQuestionMetadata } from "@/hooks/useQuestionMetadata";
import { useFilteredQuestions } from "@/hooks/useFilteredQuestions";
import { useFilterState } from "@/hooks/useFilterState";
import { useOptimizedFilterSelection } from "@/hooks/useOptimizedFilterSelection";

import { useSessionCreation } from "@/components/filters/hooks/useSessionCreation";
import type { SelectedFilters, QuestionMetadata } from "@/types/question";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Loader2, Play, Sparkles, Target, Info } from "lucide-react";
import { motion } from "framer-motion";
import RandomQuestionsDialog from "./RandomQuestionsDialog";
import { useDomain } from "@/hooks/useDomain";
import { useAnsweredQuestions } from "@/hooks/useAnsweredQuestions";
import { Skeleton } from "@/components/ui/skeleton";

import { QuestionFilterLoading } from "./filters/QuestionFilterLoading";

interface QuestionFilterProps {
  selectedFilters: SelectedFilters;
  setSelectedFilters: (filters: SelectedFilters) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  setQuestions: (questions: any[]) => void;
  onSessionCreated: (sessionId: string) => void;
  onShowRandomDialog: () => void;
  domain?: string;
}

const QuestionFilter = ({
  selectedFilters: initialFilters,
  setSelectedFilters: parentSetSelectedFilters,
  searchTerm,
  setSearchTerm,
  setQuestions,
  onSessionCreated,
  onShowRandomDialog
}: QuestionFilterProps) => {
  const { toast } = useToast();
  const { domain, isResidencia, isReady } = useDomain();
  const [openSection, setOpenSection] = useState<"specialty" | "institution" | "year" | "question_type" | "question_format">("specialty");
  const [showTitleDialog, setShowTitleDialog] = useState(false);
  const [showRandomDialog, setShowRandomDialog] = useState(false);
  const [sessionTitle, setSessionTitle] = useState("");
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [page, setPage] = useState(1);
  const [hasAppliedFilters, setHasAppliedFilters] = useState(false);
  const [excludeAnswered, setExcludeAnswered] = useState(false);
  const [answeredQuestionsCount, setAnsweredQuestionsCount] = useState(0);
  const { handleCreateSession } = useSessionCreation();
  const { data: answeredQuestions } = useAnsweredQuestions();
  const MAX_QUESTIONS_ALLOWED = 300;



  useEffect(() => {
    if (isReady && domain) {
      // Domain monitoring
    }
  }, [domain, isResidencia, isReady]);

  // Atualizar contador de questões acertadas
  useEffect(() => {
    if (answeredQuestions?.count) {
      setAnsweredQuestionsCount(answeredQuestions.count);
    } else {
      setAnsweredQuestionsCount(0);
    }
  }, [answeredQuestions]);

  const {
    selectedFilters,
    setSelectedFilters,
    expandedItems,
    handleToggleFilter,
    toggleExpand
  } = useFilterState();

  // Hook otimizado para seleção sem delay
  const {
    questionCount: optimizedQuestionCount,
    isUpdating,
    isFetching: isOptimizedFetching,
    handleFilterToggle: optimizedHandleToggle,
    setSelectedFilters: setOptimizedFilters
  } = useOptimizedFilterSelection({
    initialFilters: selectedFilters,
    onFiltersChange: (filters) => {
      setSelectedFilters(filters);
      parentSetSelectedFilters(filters);
    },
    debounceMs: 300
  });



  const { data: metadata, isLoading: isLoadingMetadata } = useQuestionMetadata();

  // ✅ Carregar questões quando há filtros aplicados E quando dialogs estão abertos
  const shouldLoadQuestions = hasAppliedFilters && (showTitleDialog || showRandomDialog);

  const {
    data: filteredData,
    isLoading: isLoadingQuestions,
    isFetching: isFetchingQuestions,
    refetch
  } = useFilteredQuestions(selectedFilters, page, 50, shouldLoadQuestions);

  // Usar a contagem otimizada quando disponível, senão usar a contagem dos dados filtrados
  const totalQuestions = hasAppliedFilters ?
    (optimizedQuestionCount > 0 ? optimizedQuestionCount : (filteredData?.total_count || 0)) : 0;
  const questions = hasAppliedFilters ? (filteredData?.questions || []) : [];
  const filteredExcludedCount = filteredData?.excluded_questions_count || 0;

  // Separar loading inicial (metadata) do loading de questões filtradas
  const isInitialLoading = isLoadingMetadata;
  const isQuestionsLoading = isLoadingQuestions || isFetchingQuestions;

  useEffect(() => {
    if (questions && questions.length > 0) {
      setQuestions(questions);
    } else if (hasAppliedFilters) {
      // No questions found for current filters
    }
  }, [questions, setQuestions, domain, hasAppliedFilters]);

  // Removido o refetch automático - só vamos carregar questões quando necessário
  // const lastFiltersHash = useRef("");

  // useEffect(() => {
  //   const hasAnyFilter = Object.values(selectedFilters).some(f =>
  //     Array.isArray(f) && f.length > 0
  //   );
  //   const currentHash = JSON.stringify(selectedFilters);

  //   if (hasAnyFilter && currentHash !== lastFiltersHash.current) {
  //     lastFiltersHash.current = currentHash;
  //     refetch();
  //   }
  // }, [selectedFilters, refetch]);

  useEffect(() => {
    const hasAnyFilter = Object.values(selectedFilters).some(f =>
      Array.isArray(f) && f.length > 0
    );
    if (hasAppliedFilters !== hasAnyFilter) {
      setHasAppliedFilters(hasAnyFilter);
    }
  }, [selectedFilters, hasAppliedFilters]);

  // Build question counts for filtering
  const buildQuestionCounts = () => {
    const totalCounts = {};
    const filteredCounts = {};

    if (metadata) {
      metadata.specialties.forEach(specialty => {
        totalCounts[specialty.id] = specialty.count;
      });
      metadata.themes.forEach(theme => {
        totalCounts[theme.id] = theme.count;
      });
      metadata.focuses.forEach(focus => {
        totalCounts[focus.id] = focus.count;
      });
    }

    // Adicionar contagens reais para question_formats baseadas nos dados do banco
    if (domain === 'oftalmologia') {
      // Contagens específicas para oftalmologia
      totalCounts['ALTERNATIVAS'] = 4716;
      // Contagens para tipos de prova de oftalmologia
      totalCounts['teorica-1'] = 1218;
      totalCounts['teorica-2'] = 2658;
      totalCounts['teorico-pratica'] = 840;


    } else {
      // Contagens para residência/revalida
      totalCounts['ALTERNATIVAS'] = 111662;
      totalCounts['VERDADEIRO_FALSO'] = 2161;
      totalCounts['DISSERTATIVA'] = 1468;


    }

    if (questions) {
      const counts = {};

      questions.forEach(question => {
        if (question.specialty?.id) {
          counts[question.specialty.id] = (counts[question.specialty.id] || 0) + 1;
        }
        if (question.theme?.id) {
          counts[question.theme.id] = (counts[question.theme.id] || 0) + 1;
        }
        if (question.focus?.id) {
          counts[question.focus.id] = (counts[question.focus.id] || 0) + 1;
        }
        // Adicionar contagem para question_format
        if (question.question_format) {
          counts[question.question_format] = (counts[question.question_format] || 0) + 1;
        }
        // Adicionar contagem para assessment_type (tipos de prova)
        if (question.assessment_type) {
          counts[question.assessment_type] = (counts[question.assessment_type] || 0) + 1;
        }
      });

      Object.assign(filteredCounts, counts);
    }

    return { totalCounts, filteredCounts };
  };

  const questionCounts = useMemo(() => buildQuestionCounts(), [metadata, questions]);

  const handleStartStudy = async () => {
    const actionId = Math.random().toString(36).substr(2, 9);
    console.log(`🎯 [QuestionFilter-${actionId}] Usuário clicou em "Começar Estudo":`, {
      sessionTitle: sessionTitle.trim(),
      selectedFilters: {
        specialties: selectedFilters.specialties?.length || 0,
        themes: selectedFilters.themes?.length || 0,
        focuses: selectedFilters.focuses?.length || 0,
        locations: selectedFilters.locations?.length || 0,
        years: selectedFilters.years?.length || 0,
        question_types: selectedFilters.question_types?.length || 0,
        question_formats: selectedFilters.question_formats?.length || 0,
        excludeAnswered: selectedFilters.excludeAnswered
      },
      totalQuestions,
      domain,
      timestamp: new Date().toISOString()
    });

    if (!sessionTitle.trim()) {
      console.warn(`⚠️ [QuestionFilter-${actionId}] Título vazio - mostrando erro`);
      toast({
        title: "Erro",
        description: "Por favor, insira um título para a sessão de estudos",
        variant: "destructive"
      });
      return;
    }

    if (totalQuestions === 0) {
      console.warn(`⚠️ [QuestionFilter-${actionId}] Nenhuma questão selecionada - mostrando erro`);
      toast({
        title: "Erro",
        description: "Selecione pelo menos uma questão para iniciar os estudos",
        variant: "destructive"
      });
      return;
    }

    if (totalQuestions > MAX_QUESTIONS_ALLOWED) {
      console.warn(`⚠️ [QuestionFilter-${actionId}] Muitas questões (${totalQuestions}) - mostrando erro`);
      toast({
        title: "Erro",
        description: `O limite máximo é de ${MAX_QUESTIONS_ALLOWED} questões por sessão de estudos`,
        variant: "destructive"
      });
      return;
    }

    setIsCreatingSession(true);
    console.log(`⏳ [QuestionFilter-${actionId}] Iniciando criação da sessão...`);

    try {
      const result = await handleCreateSession(selectedFilters, sessionTitle);

      console.log(`📋 [QuestionFilter-${actionId}] Resultado handleCreateSession:`, {
        success: !!result?.id,
        sessionId: result?.id,
        questionCount: result?.total_questions
      });

      if (result?.id) {
        console.log(`✅ [QuestionFilter-${actionId}] Sessão criada com sucesso, navegando...`);
        setShowTitleDialog(false);
        setSessionTitle("");
        onSessionCreated(result.id);
      } else {
        console.error(`❌ [QuestionFilter-${actionId}] Falha - resultado inválido:`, result);
        throw new Error("Failed to create session");
      }
    } catch (error: any) {
      console.error(`💥 [QuestionFilter-${actionId}] Erro na criação da sessão:`, {
        error: error.message,
        stack: error.stack
      });
      toast({
        title: "Erro ao criar sessão",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsCreatingSession(false);
      console.log(`🏁 [QuestionFilter-${actionId}] Processo finalizado`);
    }
  };

  const handleShowRandomDialog = () => {
    setShowRandomDialog(true);
  };

  const handleClearAllFilters = useCallback(() => {
    const emptyFilters = {
      specialties: [],
      themes: [],
      focuses: [],
      locations: [],
      years: [],
      question_types: [],
      question_formats: [],
      excludeAnswered: false
    };
    setSelectedFilters(emptyFilters);
    setOptimizedFilters(emptyFilters);
    setExcludeAnswered(false);
    parentSetSelectedFilters(emptyFilters);
  }, [setSelectedFilters, setOptimizedFilters, parentSetSelectedFilters]);

  // Mostrar loading completo apenas no carregamento inicial
  if (!isReady || isInitialLoading) {
    return <QuestionFilterLoading />;
  }

  const getSectionTitle = () => {
      if (isResidencia || domain === "revalida") return "Especialidades";
      return "Temas";
  };

  const isResidenciaOrRevalida = isResidencia || domain === "revalida";
  const shouldShowInstitution = domain !== "revalida";
  const shouldShowQuestionType = !isResidencia && domain !== "revalida";

  // Safely provide the metadata
  const safeMetadata: QuestionMetadata = metadata || {
    specialties: [],
    themes: [],
    focuses: [],
    locations: [],
    years: []
  };

  return (
    <div className="space-y-6 p-0 sm:p-6">
      {/* Header Section - Redesigned */}
      <div className="relative">
        <div className="bg-gradient-to-r from-[#FF6B00] to-[#FF8800] rounded-2xl p-4 sm:p-6 border-2 border-black shadow-lg">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
            <div className="text-center lg:text-left">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2">
                🎯 Banco de Questões
              </h1>
              <p className="text-white/90 text-sm lg:text-base">
                Filtre e encontre as questões perfeitas para seu estudo
              </p>
            </div>
            <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 sm:p-4 border-2 border-black min-w-[140px] sm:min-w-[160px] text-center">
              <div className="text-xs text-gray-600 mb-1">Questões Filtradas</div>
              <div className="text-xl sm:text-2xl font-bold text-[#FF6B00]">
                {isUpdating || isOptimizedFetching ? '...' :
                 totalQuestions === 0 ? (
                   <span className="text-sm text-gray-500 font-normal">Selecione filtros</span>
                 ) : (
                   totalQuestions.toLocaleString()
                 )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filter Summary - Redesigned */}
      <FilterSummary
        selectedFilters={selectedFilters}
        availableFilters={safeMetadata}
        onRemoveFilter={optimizedHandleToggle}
        onClearAllFilters={handleClearAllFilters}
        totalQuestions={totalQuestions}
        isLoading={isQuestionsLoading}
      />

      {/* Filter Sections - Redesigned Grid Layout */}
      <div className="space-y-3">
        {isResidenciaOrRevalida && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-yellow-400/70 shadow-md overflow-hidden" data-tutorial="specialty-section">
            <FilterAccordion
              title={`🧠 ${getSectionTitle()}`}
              count={selectedFilters.specialties?.length || 0 + selectedFilters.themes?.length || 0}
              isOpen={openSection === "specialty"}
              onToggle={() => setOpenSection(openSection === "specialty" ? null : "specialty")}
            >
              <FilterContent
                activeTab="specialty"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}

        {!isResidenciaOrRevalida && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-yellow-400/70 shadow-md overflow-hidden" data-tutorial="specialty-section">
            <FilterAccordion
              title="📚 Temas"
              count={selectedFilters.themes?.length || 0}
              isOpen={openSection === "specialty"}
              onToggle={() => setOpenSection(openSection === "specialty" ? null : "specialty")}
            >
              <FilterContent
                activeTab="specialty"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}

        {shouldShowInstitution && isResidenciaOrRevalida && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-blue-400/70 shadow-md overflow-hidden" data-tutorial="institution-section">
            <FilterAccordion
              title="🏥 Instituições"
              count={selectedFilters.locations?.length || 0}
              isOpen={openSection === "institution"}
              onToggle={() => setOpenSection(openSection === "institution" ? null : "institution")}
            >
              <FilterContent
                activeTab="location"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}

        <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-green-400/70 shadow-md overflow-hidden" data-tutorial="year-section">
          <FilterAccordion
            title="📅 Anos"
            count={selectedFilters.years?.length || 0}
            isOpen={openSection === "year"}
            onToggle={() => setOpenSection(openSection === "year" ? null : "year")}
          >
            <FilterContent
              activeTab="year"
              filters={safeMetadata}
              selectedFilters={selectedFilters}
              expandedItems={expandedItems}
              onToggleExpand={toggleExpand}
              onToggleFilter={optimizedHandleToggle}
              questionCounts={questionCounts}
              isLoading={isLoadingMetadata}
            />
          </FilterAccordion>
        </div>

        {shouldShowQuestionType && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-purple-400/70 shadow-md overflow-hidden" data-tutorial="question-type-section">
            <FilterAccordion
              title="📝 Tipo de Prova"
              count={selectedFilters.question_types?.length || 0}
              isOpen={openSection === "question_type"}
              onToggle={() => setOpenSection(openSection === "question_type" ? null : "question_type")}
            >
              <FilterContent
                activeTab="question_type"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}

        {/* Filtro de formato de questão - oculto para oftalmologia pois só tem alternativas */}
        {domain !== 'oftalmologia' && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-pink-400/70 shadow-md overflow-hidden" data-tutorial="question-format-section">
            <FilterAccordion
              title="🎯 Formato de Questão"
              count={selectedFilters.question_formats?.length || 0}
              isOpen={openSection === "question_format"}
              onToggle={() => setOpenSection(openSection === "question_format" ? null : "question_format")}
            >
              <FilterContent
                activeTab="question_format"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}
      </div>

      {/* Action Section - Redesigned */}
      <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-[#FF6B00]/70 shadow-lg p-6" data-tutorial="study-options">
        <FilterActions
          questionCount={totalQuestions}
          onShowRandomDialog={handleShowRandomDialog}
          onStartStudy={() => setShowTitleDialog(true)}
          excludeAnswered={excludeAnswered}
          onExcludeAnsweredChange={(value) => {
            setExcludeAnswered(value);
            const newFilters = {
              ...selectedFilters,
              excludeAnswered: value
            };
            setSelectedFilters(newFilters);
            setTimeout(() => {
              setOptimizedFilters(newFilters);
            }, 0);
          }}
          answeredQuestionsCount={answeredQuestionsCount}
          filteredExcludedCount={filteredExcludedCount}
        />


      </div>

      {/* Dialogs */}
      <Dialog open={showTitleDialog} onOpenChange={setShowTitleDialog}>
          <DialogContent className="!fixed !left-[50%] !top-[50%] !translate-x-[-50%] !translate-y-[-50%] w-[85dvw] sm:max-w-lg max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto relative bg-gradient-to-br from-white to-green-50 border-2 border-black shadow-2xl">
            {isCreatingSession && (
              <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center rounded-2xl sm:rounded-xl">
                <div className="flex flex-col items-center gap-3 p-6">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <div className="text-center">
                    <p className="font-medium text-gray-900">Criando sua sessão de estudos...</p>
                    <p className="text-sm text-gray-600 mt-1">Preparando {totalQuestions} questões</p>
                  </div>
                </div>
              </div>
            )}

            <DialogHeader className="text-center pb-6 border-b border-green-200">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="flex items-center justify-center gap-3 mb-3"
              >
                <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-full border-2 border-black">
                  <Play className="h-6 w-6 text-white" />
                </div>
                <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                  Iniciar Sessão de Estudos
                </DialogTitle>
              </motion.div>
              <DialogDescription className="text-base text-gray-600">
                Digite um título personalizado para identificar esta sessão de estudos
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-8 py-6">
              {/* Seção do Título */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-4"
              >
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-green-500" />
                  <Label htmlFor="session-title" className="text-sm font-bold text-gray-700">
                    Título da Sessão
                  </Label>
                </div>

                {/* Card com informações */}
                <div className="bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-lg p-4">
                  <Input
                    id="session-title"
                    placeholder="Ex: Revisão Cardiologia, Prova Final, Estudo Intensivo..."
                    value={sessionTitle}
                    onChange={(e) => setSessionTitle(e.target.value)}
                    className="w-full border-2 border-green-300 focus:border-green-500 rounded-lg px-4 py-3 text-base font-medium transition-all duration-200 bg-white/80 backdrop-blur-sm"
                  />

                  {/* Informação sobre questões */}
                  <div className="mt-3 flex items-center gap-2 text-sm text-green-700">
                    <Target className="h-4 w-4" />
                    <span className="font-medium">
                      {totalQuestions} questões selecionadas para esta sessão
                    </span>
                  </div>
                </div>

                {/* Dica */}
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 flex items-start gap-2">
                  <Info className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div className="text-xs text-amber-700">
                    <p className="font-medium">Dica:</p>
                    <p>Use títulos descritivos como "Revisão Cardiologia" ou "Simulado Final" para facilitar a identificação posterior.</p>
                  </div>
                </div>
              </motion.div>
              {/* Botões de Ação */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t border-green-200"
              >
                <Button
                  variant="outline"
                  onClick={() => setShowTitleDialog(false)}
                  disabled={isCreatingSession}
                  className="w-full sm:w-auto order-2 sm:order-1 border-2 border-gray-300 hover:border-gray-400 font-medium transition-all duration-200"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleStartStudy}
                  disabled={isCreatingSession || !sessionTitle.trim()}
                  className="w-full sm:w-auto order-1 sm:order-2 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold border-2 border-black shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5 py-3 text-base"
                >
                  {isCreatingSession ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Criando Sessão...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-5 w-5" />
                      Começar Estudo
                    </>
                  )}
                </Button>
              </motion.div>
            </div>
          </DialogContent>
        </Dialog>

      <RandomQuestionsDialog
        open={showRandomDialog}
        onOpenChange={setShowRandomDialog}
        domain={domain}
        filteredQuestions={questions}
        totalQuestionCount={totalQuestions}
        filters={selectedFilters}
      />
    </div>
  );
};

export default QuestionFilter;
