import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import {
  fetchInstitutionsByIds,
  fetchQuestionsByInstitutions,
  fetchCategoriesByIds
} from '@/utils/rpcHelpers';

// ✅ REMOVIDO: Função movida para rpcHelpers.ts

/**
 * ✅ FUNÇÃO AUXILIAR: Buscar categorias com chunking
 * Fallback para casos onde RPC falha ou não está disponível
 */
const fetchCategoriesWithChunking = async (categoryIds: string[], selectFields: string, chunkSize: number = 50) => {
  const chunks = chunkArray(categoryIds, chunkSize);
  const allResults: any[] = [];



  for (const chunk of chunks) {
    const { data, error } = await supabase
      .from("study_categories")
      .select(selectFields)
      .in("id", chunk);

    if (error) {
      throw error;
    }

    if (data) {
      allResults.push(...data);
    }
  }

  return allResults;
};

export interface PrevalenceData {
  specialty_id: string;
  specialty_name: string;
  theme_id: string;
  theme_name: string;
  focus_id: string;
  focus_name: string;
  question_count: number;
  percentage: number;
}

export interface InstitutionStats {
  institution_id: string;
  institution_name: string;
  total_questions: number;
  specialties: PrevalenceData[];
  themes: PrevalenceData[];
  focuses: PrevalenceData[];
}

export interface PrevalenceFilters {
  institutionIds: string[];
  startYear?: number;
  endYear?: number;
  domain?: string;
}

export const useInstitutionPrevalence = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const calculatePrevalence = async (filters: PrevalenceFilters): Promise<InstitutionStats[]> => {

    setIsLoading(true);
    setError(null);

    try {
      // ✅ CORREÇÃO: Usar RPC helper para buscar instituições
      const { data: institutions, error: institutionsError } = await fetchInstitutionsByIds(filters.institutionIds);

      if (institutionsError) {
        console.error('❌ [useInstitutionPrevalence] Erro ao buscar instituições:', institutionsError);
        throw new Error(`Erro ao buscar instituições: ${institutionsError.message}`);
      }

      const institutionMap = new Map(institutions?.map(inst => [inst.id, inst.name]) || []);



      // ✅ CORREÇÃO: Usar RPC helper para buscar questões
      const { data: allQuestions, error: questionsError } = await fetchQuestionsByInstitutions(
        filters.institutionIds,
        {
          startYear: filters.startYear,
          endYear: filters.endYear,
          domain: filters.domain
        }
      );

      if (questionsError) {
        console.error('❌ [useInstitutionPrevalence] Erro ao buscar questões:', questionsError);
        throw new Error(`Erro ao buscar questões: ${questionsError.message}`);
      }

      // ✅ OTIMIZAÇÃO: Buscar TODAS as categorias de uma vez
      const allSpecialtyIds = [...new Set(allQuestions?.map(q => q.specialty_id).filter(Boolean))];
      const allThemeIds = [...new Set(allQuestions?.map(q => q.theme_id).filter(Boolean))];
      const allFocusIds = [...new Set(allQuestions?.map(q => q.focus_id).filter(Boolean))];

      // ✅ REMOVIDO: Função obsoleta - agora usando RPC helpers

      const [specialtyNames, themeNames, focusNames, focusHierarchy] = await Promise.all([
        fetchCategoriesByIds(allSpecialtyIds, 'id, name'),
        fetchCategoriesByIds(allThemeIds, 'id, name'),
        fetchCategoriesByIds(allFocusIds, 'id, name'),
        fetchCategoriesByIds(allFocusIds, 'id, name, parent_id')
      ]);

      // Criar mapas globais
      const specialtyNameMap = new Map(specialtyNames.data?.map(s => [s.id, s.name]) || []);
      const themeNameMap = new Map(themeNames.data?.map(t => [t.id, t.name]) || []);
      const focusNameMap = new Map(focusNames.data?.map(f => [f.id, f.name]) || []);



      // Processar hierarquia de focos
      const focusHierarchyMap = new Map();

      if (focusHierarchy.data) {
        const themeIds = [...new Set(focusHierarchy.data.map(f => f.parent_id).filter(Boolean))];

        // ✅ CORREÇÃO: Usar sempre query direta para evitar problemas de estrutura
        const { data: themeData, error: themeError } = await supabase
          .from('study_categories')
          .select('id, name, parent_id')
          .in('id', themeIds);

        if (themeError) {
          console.error(`❌ [useInstitutionPrevalence] Erro ao buscar temas:`, themeError);
        }

        // ✅ REMOVIDO: Logs de debug

        const specialtyIds = [...new Set(themeData?.map(t => t.parent_id).filter(Boolean) || [])];

        // ✅ CORREÇÃO: Usar sempre query direta para evitar problemas de estrutura
        const { data: specialtyData, error: specialtyError } = await supabase
          .from('study_categories')
          .select('id, name')
          .in('id', specialtyIds);

        if (specialtyError) {
          console.error(`❌ [useInstitutionPrevalence] Erro ao buscar especialidades:`, specialtyError);
        }

        const themeMap = new Map(themeData?.map(t => [t.id, t]) || []);
        const specialtyMap = new Map(specialtyData?.map(s => [s.id, s]) || []);

        focusHierarchy.data.forEach((focus) => {
          const theme = themeMap.get(focus.parent_id);
          const specialty = theme ? specialtyMap.get(theme.parent_id) : null;

          focusHierarchyMap.set(focus.id, {
            focus_id: focus.id,
            focus_name: focus.name,
            theme_id: theme?.id || '',
            theme_name: theme?.name || '',
            specialty_id: specialty?.id || '',
            specialty_name: specialty?.name || ''
          });
        });
      }

      // ✅ OTIMIZAÇÃO: Processar dados por instituição
      const results: InstitutionStats[] = [];

      for (const institutionId of filters.institutionIds) {
        const institutionName = institutionMap.get(institutionId) || 'Desconhecida';
        const questions = allQuestions?.filter(q => q.exam_location === institutionId) || [];
        const totalQuestions = questions.length;



        if (totalQuestions === 0) {
          results.push({
            institution_id: institutionId,
            institution_name: institutionName,
            total_questions: 0,
            specialties: [],
            themes: [],
            focuses: []
          });
          // Pular para próxima instituição se não há questões
          continue;
        }

        // Calcular prevalência por especialidade (usando mapas globais)
        const specialtyStats = new Map<string, { name: string; count: number }>();
        const themeStats = new Map<string, { name: string; count: number }>();
        const focusStats = new Map<string, { name: string; count: number }>();

        questions.forEach(question => {
          // Especialidades
          if (question.specialty_id) {
            const key = question.specialty_id;
            const name = specialtyNameMap.get(key) || 'Desconhecido';
            const current = specialtyStats.get(key) || { name, count: 0 };
            specialtyStats.set(key, { ...current, count: current.count + 1 });
          }

          // Temas
          if (question.theme_id) {
            const key = question.theme_id;
            const name = themeNameMap.get(key) || 'Desconhecido';
            const current = themeStats.get(key) || { name, count: 0 };
            themeStats.set(key, { ...current, count: current.count + 1 });
          }

          // Focos
          if (question.focus_id) {
            const key = question.focus_id;
            const name = focusNameMap.get(key) || 'Desconhecido';
            const current = focusStats.get(key) || { name, count: 0 };
            focusStats.set(key, { ...current, count: current.count + 1 });
          }
        });

        // Converter para arrays ordenados por prevalência
        const specialties: PrevalenceData[] = Array.from(specialtyStats.entries())
          .map(([id, data]) => ({
            specialty_id: id,
            specialty_name: data.name,
            theme_id: '',
            theme_name: '',
            focus_id: '',
            focus_name: '',
            question_count: data.count,
            percentage: (data.count / totalQuestions) * 100
          }))
          .sort((a, b) => b.percentage - a.percentage);

        const themes: PrevalenceData[] = Array.from(themeStats.entries())
          .map(([id, data]) => ({
            specialty_id: '',
            specialty_name: '',
            theme_id: id,
            theme_name: data.name,
            focus_id: '',
            focus_name: '',
            question_count: data.count,
            percentage: (data.count / totalQuestions) * 100
          }))
          .sort((a, b) => b.percentage - a.percentage);





        const focuses: PrevalenceData[] = Array.from(focusStats.entries())
          .map(([id, data]) => {
            const hierarchy = focusHierarchyMap.get(id) || {
              focus_id: id,
              focus_name: data.name,
              theme_id: '',
              theme_name: '',
              specialty_id: '',
              specialty_name: ''
            };

            return {
              specialty_id: hierarchy.specialty_id,
              specialty_name: hierarchy.specialty_name,
              theme_id: hierarchy.theme_id,
              theme_name: hierarchy.theme_name,
              focus_id: id,
              focus_name: data.name,
              question_count: data.count,
              percentage: (data.count / totalQuestions) * 100
            };
          })
          .sort((a, b) => b.percentage - a.percentage);





        results.push({
          institution_id: institutionId,
          institution_name: institutionName,
          total_questions: totalQuestions,
          specialties,
          themes,
          focuses
        });
      }



      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getInstitutions = async (): Promise<{ id: string; name: string }[]> => {
    const { data, error } = await supabase
      .from('exam_locations')
      .select('id, name')
      .order('name');

    if (error) {
      throw new Error(`Erro ao buscar instituições: ${error.message}`);
    }

    return data || [];
  };

  return {
    calculatePrevalence,
    getInstitutions,
    isLoading,
    error
  };
};
