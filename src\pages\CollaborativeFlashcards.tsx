
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CommunityHierarchyTree } from "@/components/collaborate/flashcards/hierarchy/CommunityHierarchyTree";
import { useCollaborativeFlashcards } from "@/hooks/useCollaborativeFlashcards";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { ImportFeedback } from "@/components/ui/ImportFeedback";


const SORT_OPTIONS = [
  { value: "recent", label: "Mais recentes" },
  { value: "oldest", label: "Mais antigos" },
  { value: "likes", label: "Mais curtidos" },
  { value: "dislikes_desc", label: "Mais descurtidos" },
  { value: "most_imported", label: "Mais importados" },
];

const CollaborativeFlashcards = () => {
  const [filters, setFilters] = useState({});
  const [sortBy, setSortBy] = useState("recent");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [isAddingToDeck, setIsAddingToDeck] = useState(false);
  const [activeTab] = useState<"tree">("tree");
  const [showImported, setShowImported] = useState(false);
  const [showImportFeedback, setShowImportFeedback] = useState<number | null>(null);
  const [flashcardType, setFlashcardType] = useState<string>("all");

  const {
    flashcards,
    isLoading,
    totalCount,
    totalPages,
    loadFlashcards,
    importedCardIds,
    refreshImportedCards,
    refreshImportedCounts,
    likeDislikeCard
  } = useCollaborativeFlashcards();

  useEffect(() => {
    // Usar limite maior para visualização hierárquica
    loadFlashcards(currentPage, { ...filters, showImported, flashcardType }, sortBy, 200);
  }, [currentPage, filters, sortBy, showImported, flashcardType, loadFlashcards]);

  const toggleCardSelection = (cardId: string) => {
    setSelectedCards(prev =>
      prev.includes(cardId)
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  const handleLikeCard = async (cardId: string) => {

    const res = await likeDislikeCard(cardId, "like");
    if (res?.error) {
      console.error("❌ [CollaborativeFlashcards] Error liking card:", res.error);
      toast.error("Não foi possível dar like neste card");
    } else {

      toast("Obrigado pelo feedback!");
    }
  };

  const handleDislikeCard = async (cardId: string) => {

    const res = await likeDislikeCard(cardId, "dislike");
    if (res?.error) {
      console.error("❌ [CollaborativeFlashcards] Error disliking card:", res.error);
      toast.error("Não foi possível dar dislike neste card");
    } else {

      toast("Obrigado pelo feedback!");
    }
  };

  const addSelectedToMyDeck = async (cards?: string[]) => {
    const cardsToAdd = cards || selectedCards;

    if (cardsToAdd.length === 0) {
      toast.error("Selecione pelo menos um flashcard");
      return;
    }

    try {

      setIsAddingToDeck(true);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não autenticado");

      const { error: fnError } = await supabase
        .rpc('add_cards_to_user_deck', {
          p_user_id: user.id,
          p_card_ids: cardsToAdd
        });

      if (fnError) throw fnError;

      // Atualizar estado local imediatamente
      if (refreshImportedCards) {
        await refreshImportedCards();
      }

      if (refreshImportedCounts) {
        await refreshImportedCounts(cardsToAdd);
      }

      setSelectedCards([]);

      // Mostrar feedback visual clean
      setShowImportFeedback(cardsToAdd.length);

      // Toast mais informativo
      toast.success(`🎉 ${cardsToAdd.length} flashcard(s) importado(s)!`, {
        description: "Os cards foram adicionados à sua coleção. Você pode estudá-los criando uma nova sessão.",
        duration: 5000
      });

    } catch (error: any) {

    } finally {
      setIsAddingToDeck(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />

      {/* Espaçamento para StudyNavBar flutuante */}
      <div className="h-16 sm:h-20"></div>

      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in relative z-10">
        <div className="relative">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Cards Compartilhados
          </h1>
        </div>



        {/* Filtros e ordenação */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
          <div className="flex items-center gap-4">

              {/* ✅ Select de ordenação */}
              <Select
                value={sortBy}
                onValueChange={setSortBy}
              >
                <SelectTrigger className="w-[220px] bg-white border-2 border-black rounded-lg shadow-button hover:shadow-sm hover:translate-y-0.5 transition-all font-medium">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent className="border-2 border-black rounded-lg shadow-button">
                  {SORT_OPTIONS.map(option => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="font-medium hover:bg-hackathon-lightBg focus:bg-hackathon-lightBg"
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* ✅ Filtro por tipo de flashcard */}
              <Select
                value={flashcardType}
                onValueChange={setFlashcardType}
              >
                <SelectTrigger className="w-[200px] bg-white border-2 border-black rounded-lg shadow-button hover:shadow-sm hover:translate-y-0.5 transition-all font-medium">
                  <SelectValue placeholder="Tipo de card" />
                </SelectTrigger>
                <SelectContent className="border-2 border-black rounded-lg shadow-button">
                  <SelectItem value="all" className="font-medium hover:bg-hackathon-lightBg focus:bg-hackathon-lightBg">
                    Todos os tipos
                  </SelectItem>
                  <SelectItem value="qa" className="font-medium hover:bg-hackathon-lightBg focus:bg-hackathon-lightBg">
                    Pergunta e Resposta (Q&A/Padrão)
                  </SelectItem>
                  <SelectItem value="cloze" className="font-medium hover:bg-hackathon-lightBg focus:bg-hackathon-lightBg">
                    Cloze Deletion (Lacuna)
                  </SelectItem>
                  <SelectItem value="multipla" className="font-medium hover:bg-hackathon-lightBg focus:bg-hackathon-lightBg">
                    Alternativas (Múltipla Escolha)
                  </SelectItem>
                  <SelectItem value="vf" className="font-medium hover:bg-hackathon-lightBg focus:bg-hackathon-lightBg">
                    Verdadeiro ou Falso (V/F)
                  </SelectItem>
                  <SelectItem value="null" className="font-medium hover:bg-hackathon-lightBg focus:bg-hackathon-lightBg">
                    Sem tipo definido
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 font-medium text-sm">
                <input
                  type="checkbox"
                  checked={showImported}
                  onChange={e => setShowImported(e.target.checked)}
                />
                Mostrar importados
              </label>
            </div>
          </div>

        <CommunityHierarchyTree
          flashcards={flashcards}
          onImportCards={addSelectedToMyDeck}
          isLoading={isLoading}
          importedCardIds={importedCardIds || []}
          onLikeCard={handleLikeCard}
          onDislikeCard={handleDislikeCard}
        />
      </div>

      {/* Feedback visual de importação */}
      {showImportFeedback && (
        <ImportFeedback
          count={showImportFeedback}
          onComplete={() => setShowImportFeedback(null)}
        />
      )}
    </div>
  );
};

export default CollaborativeFlashcards;
