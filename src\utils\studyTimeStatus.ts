import { format, differenceInMinutes, differenceInHours } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export interface StudyTimeStatus {
  status: 'atrasado' | 'urgente' | 'agora' | 'no-prazo' | 'cedo' | 'madrugada';
  message: string;
  timeInfo: string;
  color: string;
  bgColor: string;
  icon: string;
  priority: number; // 1 = mais urgente, 5 = menos urgente
}

export const getStudyTimeStatus = (topicStartTime?: string): StudyTimeStatus => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTime = format(now, 'HH:mm');



  // Se há um horário específico do tópico, usar ele para calcular status
  if (topicStartTime) {
    const [topicHour, topicMinute] = topicStartTime.split(':').map(Number);
    const topicTimeInMinutes = topicHour * 60 + topicMinute;
    const currentTimeInMinutes = currentHour * 60 + currentMinute;
    const diffInMinutes = currentTimeInMinutes - topicTimeInMinutes;

    // Muito atrasado (mais de 2h)
    if (diffInMinutes > 120) {
      const hoursLate = Math.floor(diffInMinutes / 60);
      return {
        status: 'atrasado',
        message: `${hoursLate}h atrasado! (${topicStartTime}) Ainda dá tempo! 💪`,
        timeInfo: `Programado para ${topicStartTime}`,
        color: 'text-red-600',
        bgColor: 'bg-red-50 border-red-200',
        icon: '🚨',
        priority: 1
      };
    }

    // Atrasado (30min - 2h)
    if (diffInMinutes > 30) {
      const minutesLate = diffInMinutes;
      const hoursLate = Math.floor(minutesLate / 60);
      const remainingMinutes = minutesLate % 60;

      const lateText = hoursLate > 0
        ? `${hoursLate}h${remainingMinutes > 0 ? `${remainingMinutes}min` : ''} atrasado`
        : `${minutesLate}min atrasado`;

      return {
        status: 'atrasado',
        message: `${lateText}! (${topicStartTime}) Vamos estudar! ⚡`,
        timeInfo: `Programado para ${topicStartTime}`,
        color: 'text-orange-600',
        bgColor: 'bg-orange-50 border-orange-200',
        icon: '⏰',
        priority: 1
      };
    }

    // No horário (até 30min de atraso)
    if (diffInMinutes >= -30 && diffInMinutes <= 30) {
      return {
        status: 'agora',
        message: `Horário perfeito! (${topicStartTime}) 🎯`,
        timeInfo: `Programado para ${topicStartTime}`,
        color: 'text-green-600',
        bgColor: 'bg-green-50 border-green-200',
        icon: '🎯',
        priority: 1
      };
    }

    // Cedo (mais de 30min antes)
    if (diffInMinutes < -30) {
      const minutesEarly = Math.abs(diffInMinutes);
      const hoursEarly = Math.floor(minutesEarly / 60);
      const remainingMinutes = minutesEarly % 60;

      const earlyText = hoursEarly > 0
        ? `${hoursEarly}h${remainingMinutes > 0 ? `${remainingMinutes}min` : ''}`
        : `${minutesEarly}min`;

      return {
        status: 'cedo',
        message: `Que dedicação! (${topicStartTime} em ${earlyText}) 🌟`,
        timeInfo: `Programado para ${topicStartTime} (em ${earlyText})`,
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50 border-indigo-200',
        icon: '🌟',
        priority: 3
      };
    }
  }

  // Definir horários ideais para estudo
  const idealStudyPeriods = [
    { start: 6, end: 9, name: 'Manhã' },    // 6h-9h: Melhor para estudos teóricos
    { start: 14, end: 17, name: 'Tarde' },  // 14h-17h: Bom para revisões
    { start: 19, end: 22, name: 'Noite' }   // 19h-22h: Ideal para questões
  ];

  // Verificar se está em horário de madrugada (não recomendado)
  if (currentHour >= 23 || currentHour < 6) {
    return {
      status: 'madrugada',
      message: 'Que tal descansar? 😴',
      timeInfo: 'Horário não recomendado para estudos',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 border-purple-200',
      icon: '🌙',
      priority: 5
    };
  }

  // Encontrar o período ideal mais próximo
  let nextIdealPeriod = null;
  let currentIdealPeriod = null;
  let lastIdealPeriod = null;

  for (const period of idealStudyPeriods) {
    if (currentHour >= period.start && currentHour < period.end) {
      currentIdealPeriod = period;
      break;
    } else if (currentHour < period.start) {
      nextIdealPeriod = period;
      break;
    }
    lastIdealPeriod = period;
  }

  // Se está em um período ideal
  if (currentIdealPeriod) {
    const endHour = currentIdealPeriod.end;
    const minutesLeft = (endHour - currentHour) * 60 - currentMinute;
    
    if (minutesLeft <= 30) {
      return {
        status: 'urgente',
        message: 'Últimos minutos do horário ideal! ⚡',
        timeInfo: `Restam ${minutesLeft} min do período ${currentIdealPeriod.name.toLowerCase()}`,
        color: 'text-orange-600',
        bgColor: 'bg-orange-50 border-orange-200',
        icon: '⚡',
        priority: 2
      };
    }

    return {
      status: 'agora',
      message: 'Horário ideal para estudar! 🎯',
      timeInfo: `Período ${currentIdealPeriod.name.toLowerCase()} (até ${endHour}h)`,
      color: 'text-green-600',
      bgColor: 'bg-green-50 border-green-200',
      icon: '🎯',
      priority: 1
    };
  }

  // Se passou de um período ideal (atrasado)
  if (lastIdealPeriod && currentHour >= lastIdealPeriod.end) {
    const hoursLate = currentHour - lastIdealPeriod.end;
    
    if (hoursLate >= 2) {
      return {
        status: 'atrasado',
        message: 'Ainda dá tempo de estudar! 💪',
        timeInfo: `${hoursLate}h após o período ${lastIdealPeriod.name.toLowerCase()}`,
        color: 'text-red-600',
        bgColor: 'bg-red-50 border-red-200',
        icon: '⏰',
        priority: 2
      };
    }

    return {
      status: 'no-prazo',
      message: 'Bom momento para estudar 📚',
      timeInfo: `Logo após o período ${lastIdealPeriod.name.toLowerCase()}`,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 border-blue-200',
      icon: '📚',
      priority: 3
    };
  }

  // Se está antes de um período ideal (cedo)
  if (nextIdealPeriod) {
    const hoursUntil = nextIdealPeriod.start - currentHour;
    
    if (hoursUntil >= 3) {
      return {
        status: 'cedo',
        message: 'Ainda é cedo, mas que dedicação! 🌟',
        timeInfo: `${hoursUntil}h até o período ${nextIdealPeriod.name.toLowerCase()}`,
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50 border-indigo-200',
        icon: '🌟',
        priority: 4
      };
    }

    return {
      status: 'no-prazo',
      message: 'Preparando para o horário ideal 🚀',
      timeInfo: `${hoursUntil}h até o período ${nextIdealPeriod.name.toLowerCase()}`,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 border-blue-200',
      icon: '🚀',
      priority: 3
    };
  }

  // Fallback
  return {
    status: 'no-prazo',
    message: 'Bom momento para estudar 📚',
    timeInfo: `Horário atual: ${currentTime}`,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 border-blue-200',
    icon: '📚',
    priority: 3
  };
};

export const getTimeBasedMotivation = (status: StudyTimeStatus['status']): string => {
  const motivations = {
    atrasado: [
      'Melhor tarde do que nunca! 💪',
      'Cada minuto conta! ⚡',
      'Vamos recuperar o tempo! 🚀'
    ],
    urgente: [
      'Aproveite estes minutos! ⚡',
      'Foco total agora! 🎯',
      'Últimos minutos preciosos! ⏰'
    ],
    agora: [
      'Momento perfeito! 🎯',
      'Seu cérebro está pronto! 🧠',
      'Horário ideal para aprender! ✨'
    ],
    'no-prazo': [
      'Bom momento para estudar! 📚',
      'Vamos começar! 🚀',
      'Hora de focar! 💡'
    ],
    cedo: [
      'Que dedicação! 🌟',
      'Começando cedo! 🌅',
      'Aproveitando o dia! ☀️'
    ],
    madrugada: [
      'Hora de descansar! 😴',
      'Seu cérebro precisa de sono! 🌙',
      'Amanhã será melhor! 💤'
    ]
  };

  const options = motivations[status];
  return options[Math.floor(Math.random() * options.length)];
};
