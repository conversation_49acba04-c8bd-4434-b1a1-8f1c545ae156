import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { handleOAuthCallback } from '@/utils/authHelpers';
import { AuthLoadingScreen } from '@/components/common/AuthLoadingScreen';
import { useFeedbackDialog } from '@/components/ui/feedback-dialog';

const OAuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { showFeedback } = useFeedbackDialog();

  useEffect(() => {
    const processCallback = async () => {
      try {
        setIsProcessing(true);
        
        // Verificar se há erro nos parâmetros da URL
        const errorParam = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');
        
        if (errorParam) {
          throw new Error(errorDescription || 'Erro na autenticação OAuth');
        }

        // Processar callback OAuth
        const result = await handleOAuthCallback();
        
        if (result.error) {
          throw result.error;
        }

        if (!result.user) {
          throw new Error('Usuário não encontrado após autenticação');
        }

        // Mostrar mensagem de sucesso
        const welcomeMessage = result.isNewUser 
          ? `Bem-vindo, ${result.user.user_metadata?.full_name || result.user.email}! Sua conta foi criada com sucesso.`
          : `Bem-vindo de volta, ${result.user.user_metadata?.full_name || result.user.email}!`;

        showFeedback(
          'success',
          'Login realizado com sucesso! 🎉',
          welcomeMessage
        );

        // Aguardar um pouco para mostrar a mensagem
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Redirecionar para a página apropriada
        navigate(result.redirectTo, { replace: true });

      } catch (error: any) {
        console.error('Erro no callback OAuth:', error);
        
        const errorMessage = error.message || 'Erro desconhecido na autenticação';
        setError(errorMessage);
        
        showFeedback(
          'error',
          'Erro na Autenticação',
          errorMessage
        );

        // Redirecionar para login após erro
        setTimeout(() => {
          navigate('/login', { replace: true });
        }, 3000);
      } finally {
        setIsProcessing(false);
      }
    };

    processCallback();
  }, [navigate, searchParams, showFeedback]);

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#FEF7CD] via-[#FEF7CD] to-hackathon-yellow/20 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-50 border-2 border-red-200 rounded-lg p-6 mb-4">
            <div className="text-red-600 text-4xl mb-4">⚠️</div>
            <h2 className="text-xl font-bold text-red-800 mb-2">
              Erro na Autenticação
            </h2>
            <p className="text-red-700 text-sm">
              {error}
            </p>
          </div>
          <p className="text-gray-600 text-sm">
            Redirecionando para a página de login...
          </p>
        </div>
      </div>
    );
  }

  return (
    <AuthLoadingScreen 
      message={isProcessing ? "Processando autenticação..." : "Redirecionando..."}
    />
  );
};

export default OAuthCallback;
