import { useRobustSessionTimer } from './useRobustSessionTimer';

/**
 * 🔄 HOOK DE MIGRAÇÃO - Compatibilidade com Sistema Antigo
 * 
 * Mantém a mesma interface do useSessionTimer antigo
 * mas usa o novo sistema robusto por baixo
 */

export const useSessionTimer = (sessionId: string, userId: string) => {
  const timer = useRobustSessionTimer(sessionId, userId);

  // ✅ Interface compatível simplificada
  return {
    // Interface principal
    totalElapsedTime: timer.totalElapsedTime,
    currentQuestionTime: timer.currentQuestionTime,
    isTimerActive: timer.isTimerActive,
    startQuestionTimer: timer.startQuestionTimer,
    pauseCurrentTimer: timer.pauseCurrentTimer,
    finishSession: timer.finishSession,
    saveOnAnswer: timer.saveOnAnswer,

    // Compatibilidade com código antigo
    isActive: timer.isTimerActive,
    getTotalSessionTime: () => timer.totalElapsedTime,
    getActiveStudyTime: () => timer.totalElapsedTime,
    getQuestionTime: () => timer.currentQuestionTime
  };
};
