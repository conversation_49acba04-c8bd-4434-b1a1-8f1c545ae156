import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

/**
 * 🎯 SISTEMA DE TIMER SIMPLES E ROBUSTO
 *
 * PRINCÍPIOS:
 * - Tempo acumulado incremental (não baseado em timestamps)
 * - Estado simples com única fonte de verdade
 * - Pausa real (para completamente o incremento)
 * - Sem race conditions ou cálculos complexos
 */

interface SimpleTimerState {
  sessionId: string;
  totalElapsedSeconds: number; // ✅ ÚNICA fonte de verdade
  isActive: boolean; // ✅ Estado de pausa/ativo
  currentQuestionId: string | null;
  lastSaveTime: number; // ✅ Para controle de salvamento
}

const STORAGE_KEY_PREFIX = 'simple_timer_';

export const useRobustSessionTimer = (sessionId: string, userId: string) => {
  // ✅ ESTADO INICIAL SIMPLES - Banco será carregado depois
  const [state, setState] = useState<SimpleTimerState>(() => ({
    sessionId,
    totalElapsedSeconds: 0, // Sempre inicia do zero, banco carrega depois
    isActive: true,
    currentQuestionId: null,
    lastSaveTime: 0
  }));

  // ✅ TIMER PRINCIPAL - Incrementa a cada segundo se ativo
  useEffect(() => {
    const interval = setInterval(() => {
      setState(prev => {
        if (!prev.isActive) {
          return prev; // ⏸️ Não incrementa se pausado
        }

        const newTime = prev.totalElapsedSeconds + 1;

        return {
          ...prev,
          totalElapsedSeconds: newTime
        };
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  /**
   * 💾 SALVAR NO LOCALSTORAGE
   */
  const saveToLocalStorage = useCallback((newState: SimpleTimerState) => {
    try {
      const storageKey = `${STORAGE_KEY_PREFIX}${sessionId}`;
      localStorage.setItem(storageKey, JSON.stringify(newState));
    } catch (error) {
      // Erro silencioso
    }
  }, [sessionId]);

  /**
   * 💾 SALVAR NO BANCO
   */
  const saveToDatabase = useCallback(async (timeToSave: number) => {
    try {
      // ✅ LIMPEZA: Log removido - salvamento de timer

      const { error } = await supabase
        .from('study_sessions')
        .update({
          total_time_spent: Math.floor(timeToSave),
          last_activity: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) throw error;
    } catch (error) {
      // Erro silencioso - continuar funcionando
    }
  }, [sessionId]);

  // ✅ SALVAMENTO AUTOMÁTICO NO LOCALSTORAGE
  useEffect(() => {
    saveToLocalStorage(state);
  }, [state, saveToLocalStorage]);

  // ✅ SALVAMENTO PERIÓDICO NO BANCO (a cada 30 segundos)
  useEffect(() => {
    const interval = setInterval(() => {
      if (state.totalElapsedSeconds > state.lastSaveTime + 30) {
        saveToDatabase(state.totalElapsedSeconds);
        setState(prev => ({ ...prev, lastSaveTime: prev.totalElapsedSeconds }));
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [state.totalElapsedSeconds, state.lastSaveTime, saveToDatabase]);

  // ✅ SEMPRE CARREGAR TEMPO INICIAL DO BANCO (fonte de verdade)
  useEffect(() => {
    const loadInitialTime = async () => {
      try {
        const { data: session } = await supabase
          .from('study_sessions')
          .select('total_time_spent')
          .eq('id', sessionId)
          .single();

        if (session?.total_time_spent && session.total_time_spent > 0) {
          setState(prev => ({
            ...prev,
            totalElapsedSeconds: session.total_time_spent,
            lastSaveTime: session.total_time_spent
          }));
        }
      } catch (error) {
        // Erro silencioso - continuar com estado inicial
      }
    };

    loadInitialTime();
  }, [sessionId]);

  // ✅ FUNÇÕES SIMPLES DE CONTROLE
  const startQuestionTimer = useCallback((questionId: string) => {
    setState(prev => ({
      ...prev,
      currentQuestionId: questionId,
      isActive: true
    }));
  }, []);

  const pauseCurrentTimer = useCallback(() => {
    setState(prev => ({
      ...prev,
      isActive: false
    }));
  }, []);

  const saveOnAnswer = useCallback(() => {
    saveToDatabase(state.totalElapsedSeconds);
    setState(prev => ({ ...prev, lastSaveTime: prev.totalElapsedSeconds }));
  }, [state.totalElapsedSeconds, saveToDatabase]);

  const finishSession = useCallback(async () => {
    await saveToDatabase(state.totalElapsedSeconds);
    const storageKey = `${STORAGE_KEY_PREFIX}${sessionId}`;
    localStorage.removeItem(storageKey);
  }, [sessionId, state.totalElapsedSeconds, saveToDatabase]);

  // ✅ RETORNO SIMPLIFICADO
  return {
    totalElapsedTime: state.totalElapsedSeconds,
    currentQuestionTime: 0, // Simplificado - não rastrear por questão
    isTimerActive: state.isActive,
    startQuestionTimer,
    pauseCurrentTimer,
    saveOnAnswer,
    finishSession
  };
};
