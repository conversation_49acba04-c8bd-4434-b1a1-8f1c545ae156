
import { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { QuestionSolver } from "@/components/question/QuestionSolver";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import type { Question } from "@/types/question";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import QuestionList from "@/components/QuestionList";
import { useDomain } from "@/hooks/useDomain";
import { useTransformedQuestions } from "@/hooks/useTransformedQuestions";
import { Loader2 } from "lucide-react";
import { useDarkMode } from "@/contexts/DarkModeContext";
import { useStudyMode } from "@/hooks/useStudyMode";

const Questions = () => {
  const { sessionId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { domain } = useDomain();
  const { isDarkMode } = useDarkMode();
  useStudyMode(); // Ativar modo de estudo quando estiver nesta página
  const [questionsData, setQuestionsData] = useState<any[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [lastSessionId, setLastSessionId] = useState<string | null>(null);

  // Usar hook otimizado para transformação
  const questions = useTransformedQuestions(questionsData);

  useEffect(() => {
    // Evitar execuções duplicadas
    if (!sessionId) {
      return;
    }

    // 🎯 Detectar mudança de sessão ou navegação forçada
    const isSessionChange = lastSessionId && lastSessionId !== sessionId;
    const isForceRefresh = location.state?.forceRefresh;

    if (isSessionChange || isForceRefresh) {
      // Limpar dados anteriores para forçar recarregamento
      setQuestionsData(null);
      setIsLoading(true);
    }

    // Evitar execução se já carregou questões para esta sessão (e não é mudança forçada)
    if (questions.length > 0 && sessionId && !isSessionChange && !isForceRefresh) {
      return;
    }

    const loadData = async () => {
      try {
        setIsLoading(true);

        const {
          data: { user },
          error: userError
        } = await supabase.auth.getUser();

        if (userError) {
          throw userError;
        }

        setUserId(user?.id || null);

        if (!sessionId) {
          setIsLoading(false);
          return;
        }

        //console.log(`🔍 [Questions] Loading session ${sessionId} with domain: ${domain}`);

        const { data: session, error: sessionError } = await supabase
          .from("study_sessions")
          .select("*, knowledge_domain")
          .eq("id", sessionId)
          .eq("user_id", user?.id)  // ✅ SEGURANÇA: Verificar ownership
          .maybeSingle();

        if (sessionError) {
          throw sessionError;
        }

        if (!session) {
          toast({
            title: "Sessão não encontrada",
            description: "A sessão que você está tentando acessar não existe",
            variant: "destructive",
          });
          navigate("/questions");
          return;
        }

        if (!session.questions || session.questions.length === 0) {
          throw new Error("Nenhuma questão encontrada nesta sessão");
        }

        // Use the domain from the session if available, otherwise use the domain from useDomain
        const queryDomain = session.knowledge_domain || domain;
        // 🔒 SEGURANÇA: Carregando questões de forma segura para domain: ${queryDomain}, sessionId: ${sessionId}

        // 🔒 SEGURANÇA: Usar função RPC segura que não expõe respostas corretas
        let questionsData;
        let questionsError;

        if (session.questions.length > 100) {
          // Use RPC function for large lists
          const { data: rpcData, error: rpcError } = await supabase.rpc('get_questions_by_ids_secure', {
            question_ids: session.questions,
            domain_filter: queryDomain
          });

          if (rpcError) {
            questionsError = rpcError;
          } else {
            // Transform RPC result to match expected format
            questionsData = rpcData?.map((q: any) => ({
              ...q,
              specialty: q.specialty_name ? { id: q.specialty_id, name: q.specialty_name } : null,
              theme: q.theme_name ? { id: q.theme_id, name: q.theme_name } : null,
              focus: q.focus_name ? { id: q.focus_id, name: q.focus_name } : null,
              location: q.location_name ? { id: q.exam_location, name: q.location_name } : null
            }));
          }
        } else {
          // Use secure RPC function for smaller lists
          const { data: rpcData, error: rpcError } = await supabase.rpc('get_questions_by_ids_secure', {
            question_ids: session.questions,
            domain_filter: queryDomain
          });

          if (rpcError) {
            questionsError = rpcError;
          } else {
            questionsData = rpcData?.map((q: any) => ({
              ...q,
              specialty: q.specialty_name ? { id: q.specialty_id, name: q.specialty_name } : null,
              theme: q.theme_name ? { id: q.theme_id, name: q.theme_name } : null,
              focus: q.focus_name ? { id: q.focus_id, name: q.focus_name } : null,
              location: q.location_name ? { id: q.exam_location, name: q.location_name } : null
            }));
          }
        }

        if (questionsError) {
          throw questionsError;
        }

        if (!questionsData?.length) {
          throw new Error("Nenhuma questão encontrada para esta sessão");
        }

        // 🔒 SEGURANÇA: Questões carregadas de forma segura: ${questionsData.length} questions for domain: ${queryDomain}

        // Armazenar dados brutos - a transformação será feita pelo hook seguro
        setQuestionsData(questionsData);

        // 🎯 Atualizar lastSessionId após carregamento bem-sucedido
        setLastSessionId(sessionId);

      } catch (error: any) {
        toast({
          title: "Erro ao carregar questões",
          description: error.message || "Ocorreu um erro ao carregar as questões",
          variant: "destructive",
        });
        navigate("/questions");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [sessionId, lastSessionId, location.state]); // Adicionadas dependências para detectar mudanças

  if (isLoading) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-8" />
        <div className={`container mx-auto px-4 pt-16 md:py-8 transition-colors duration-200 ${
          isDarkMode ? 'bg-gray-900' : 'bg-transparent'
        }`}>
          <div className={`flex flex-col items-center justify-center p-8 rounded-lg shadow-sm backdrop-blur-sm transition-colors duration-200 ${
            isDarkMode ? 'bg-gray-800/30' : 'bg-white/30'
          }`}>
            <Loader2 className={`h-8 w-8 animate-spin mb-4 transition-colors duration-200 ${
              isDarkMode ? 'text-blue-400' : 'text-primary'
            }`} />
            <h2 className={`text-xl font-semibold mb-2 transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-900'
            }`}>Carregando questões...</h2>
            <p className={`text-center transition-colors duration-200 ${
              isDarkMode ? 'text-gray-400' : 'text-muted-foreground'
            }`}>Aguarde enquanto preparamos suas questões de estudo</p>
          </div>
        </div>
      </>
    );
  }

  if (!sessionId) {
    return <QuestionList />;
  }

  if (!questions.length) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-8" />
        <div className={`container mx-auto px-4 pt-16 md:py-8 transition-colors duration-200 ${
          isDarkMode ? 'bg-gray-900' : 'bg-transparent'
        }`}>
          <h2 className={`text-xl font-semibold mb-4 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>
            Nenhuma questão encontrada
          </h2>
          <Button onClick={() => navigate("/questions")}>
            Voltar para Filtros
          </Button>
        </div>
      </>
    );
  }

  return (
    <>
      <Header />
      <StudyNavBar className="mb-8" />
      <div className={`container mx-auto px-4 pt-16 md:py-8 transition-colors duration-200 ${
        isDarkMode ? 'bg-gray-900' : 'bg-transparent'
      }`} style={{paddingRight:0,paddingLeft:0}}>
        {userId && sessionId && (
          <QuestionSolver
            questions={questions}
            sessionId={sessionId}
            userId={userId}
          />
        )}
      </div>
    </>
  );
};

export default Questions;
