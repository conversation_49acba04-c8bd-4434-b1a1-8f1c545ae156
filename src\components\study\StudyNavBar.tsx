
import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, Menu, Activity, Home, Flame, Moon, Sun } from 'lucide-react';
import { motion } from 'framer-motion';
import { useIsMobile } from '@/hooks/use-mobile';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface StudyNavBarProps {
  className?: string;
}

const StudyNavBar: React.FC<StudyNavBarProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();
  const { isDarkMode, toggleDarkMode } = useDarkMode();
  const [headerHeight, setHeaderHeight] = useState(0);
  const [scrolled, setScrolled] = useState(false);

  // Detectar se está em sessão de estudo (apenas para mostrar o botão)
  const isInStudySession = location.pathname.startsWith('/questions/') ||
                          location.pathname.startsWith('/plataformadeestudos/questions/');

  // Só aplicar modo noturno se estiver em sessão de estudo
  const shouldUseDarkMode = isDarkMode && isInStudySession;

  const handleBackButton = () => {
    navigate(-1);
  };

  useEffect(() => {
    const headerElement = document.querySelector('.header') as HTMLElement;
    if (headerElement) {
      setHeaderHeight(headerElement.offsetHeight);
    }

    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className={`fixed md:sticky top-0 md:top-${headerHeight}px z-40 w-full transition-all duration-300 ${
        scrolled
          ? shouldUseDarkMode ? 'bg-gray-900 shadow-sm' : 'bg-white shadow-sm'
          : 'bg-transparent'
      } ${className}`}
    >
      <div className="absolute left-1/2 -translate-x-1/2 w-[94%] sm:w-[92%] md:w-[85%] lg:w-[75%] flex items-center justify-center">
        <motion.div 
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="relative w-full"
        >
          <div className={`mx-auto rounded-xl border-2 shadow-card-sm transition-colors duration-200 ${
            shouldUseDarkMode
              ? 'bg-gray-800 border-gray-600'
              : 'bg-white border-black'
          }`}>
            <div className="flex items-center justify-between h-12 sm:h-14 px-2 sm:px-4">
              <div className="flex items-center space-x-1 sm:space-x-2">
                <button
                  onClick={handleBackButton}
                  className={`p-1.5 sm:p-2 rounded-full transition-colors duration-200 border-2 ${
                    shouldUseDarkMode
                      ? 'hover:bg-gray-700 border-gray-600'
                      : 'hover:bg-gray-100 border-black'
                  }`}
                  aria-label="Voltar"
                >
                  <ArrowLeft className={`w-4 h-4 sm:w-5 sm:h-5 ${shouldUseDarkMode ? 'text-gray-200' : 'text-gray-800'}`} />
                </button>

                <button
                  onClick={() => navigate('/plataformadeestudos')}
                  className={`flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full transition-all duration-200 border-2 ${
                    shouldUseDarkMode
                      ? 'hover:bg-gray-700 border-gray-600'
                      : 'hover:bg-gray-100 border-black'
                  }`}
                >
                  <Menu className={`w-4 h-4 sm:w-5 sm:h-5 ${shouldUseDarkMode ? 'text-gray-200' : 'text-gray-800'}`} />
                  <span className={`text-xs sm:text-sm font-bold hidden sm:inline ${shouldUseDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>Menu</span>
                </button>

                <button 
                  onClick={() => navigate('/progress')}
                  className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full border-2 border-black bg-hackathon-green text-black transition-all duration-200 hover:bg-hackathon-green/90 shadow-button hover:translate-y-0.5 hover:shadow-sm"
                >
                  <Activity className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="text-xs sm:text-sm font-bold hidden sm:inline">Progresso</span>
                </button>
              </div>

              <div className="flex items-center gap-1 sm:gap-2">
                {isInStudySession && (
                  <div className="relative">
                    <button
                      onClick={toggleDarkMode}
                      className={`p-1.5 sm:p-2 rounded-full transition-all duration-200 border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm ${
                        shouldUseDarkMode
                          ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700'
                          : 'bg-yellow-400 text-gray-800 hover:bg-yellow-300'
                    }`}
                      aria-label={shouldUseDarkMode ? "Ativar modo claro" : "Ativar modo noturno"}
                    >
                      {shouldUseDarkMode ? <Sun className="w-4 h-4 sm:w-5 sm:h-5" /> : <Moon className="w-4 h-4 sm:w-5 sm:h-5" />}
                    </button>
                    {/* Badge bem discreta "NOVO" */}
                    <div className="absolute -top-0.5 -right-0.5 bg-red-500 text-white text-[6px] sm:text-[7px] font-medium px-0.5 py-0 rounded-full shadow-sm">
                      NOVO
                    </div>
                  </div>
                )}

                <button
                  onClick={() => navigate('/questions')}
                  className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full border-2 border-black bg-hackathon-red text-white hover:bg-hackathon-red/90 transition-all duration-200 shadow-button hover:translate-y-0.5 hover:shadow-sm"
                >
                  <Flame className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="text-xs sm:text-sm font-bold hidden sm:inline">Praticar</span>
                </button>

                <button
                  onClick={() => navigate('/')}
                  className={`flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full border-2 transition-all duration-200 shadow-button hover:translate-y-0.5 hover:shadow-sm ${
                    shouldUseDarkMode
                      ? 'border-gray-600 bg-gray-700 hover:bg-gray-600'
                      : 'border-black bg-white hover:bg-gray-50'
                  }`}
                >
                  <Home className={`w-4 h-4 sm:w-5 sm:h-5 ${shouldUseDarkMode ? 'text-gray-200' : 'text-gray-800'}`} />
                  <span className={`text-xs sm:text-sm font-bold hidden sm:inline ${shouldUseDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>Início</span>
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default StudyNavBar;
