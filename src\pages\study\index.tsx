import React, { useEffect, useState, lazy, Suspense } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { BookOpen, Brain, TrendingUp, BarChart3, Calendar, Info, Zap, Users, HeadphonesIcon, Bot, MessageCircle, Sparkles, X } from "lucide-react";
import Header from "@/components/Header";
import { motion } from "framer-motion";
import { useAuth } from "@/contexts/AuthContext";
import { StudyPlatformTutorial } from "@/components/study/StudyPlatformTutorial";
import { WelcomeDialog } from "@/components/welcome/WelcomeDialog";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import MobileProfileButton from "@/components/mobile/MobileProfileButton";
import { useErrorNotebook } from "@/hooks/useErrorNotebook";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";

// ✅ OTIMIZADO: Carregamento automático em vez de lazy loading
import LazySection from '@/components/LazySection';
import LazyStudyStreak from '@/components/study/LazyStudyStreak';
import TodayStudies from '@/components/home/<USER>';
import { SessionHistory } from '@/components/study-session/SessionHistory';
import MobileUnifiedBanner from '@/components/study/MobileUnifiedBanner';
import UnifiedBanner from '@/components/study/UnifiedBanner';
import { RefreshWarningBanner } from '@/components/ui/RefreshWarningBanner';
import { useHomePageRefresh } from '@/hooks/usePageVisibility';
import { useIntelligentCachePrefetch } from '@/hooks/useIntelligentCachePrefetch';

const Study = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showWelcomeDialog, markWelcomeAsSeen, isLoading } = useUserPreferences();

  const [showRankingDialog, setShowRankingDialog] = useState(false);
  const { errorStats, isLoading: errorStatsLoading, fetchErrorStats } = useErrorNotebook(false);

  // ✅ OTIMIZADO: Estado para carregamento automático de conteúdo secundário
  const [loadSecondaryContent, setLoadSecondaryContent] = useState(false);

  // ✅ NOVO: Prefetch inteligente para evitar queries duplicadas
  useIntelligentCachePrefetch();

  // Auto-refresh quando o usuário volta para a aba
  useHomePageRefresh();

  // Carregar estatísticas do caderno de erros quando necessário
  useEffect(() => {
    if (user?.id && !errorStatsLoading) {
      fetchErrorStats();
    }
  }, [user?.id]);

  // ✅ OTIMIZADO: Carregar conteúdo secundário após dados essenciais
  useEffect(() => {
    if (!isLoading && user) {
      // Delay de 1.5s após dados essenciais carregarem
      const timer = setTimeout(() => {
        setLoadSecondaryContent(true);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [isLoading, user]);

  return (
    <div className="study-container bg-[#FEF7CD]">
      <Header />

      {/* Tutorial só aparece depois que o welcome dialog for fechado */}
      {!showWelcomeDialog && !isLoading && <StudyPlatformTutorial />}

      <div className="container max-w-6xl mx-auto px-4 study-header space-y-8 animate-fade-in">
        {/* Header Section */}
        <header className="relative">
          {/* Mobile Header - Unified Banner */}
          <div className="block sm:hidden">
            <div className="mb-6">
              <MobileUnifiedBanner />
            </div>
          </div>

          {/* Desktop Header - Complete */}
          <div className="hidden sm:block">
            {/* Banner Unificado Horizontal */}
            <div className="mb-8">
              <UnifiedBanner />
            </div>
          </div>
        </header>

        {/* Study Tools Section */}
        <section className="space-y-6">
          <div className="flex items-center justify-center sm:justify-start">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-800 border-2 border-black inline-block px-4 py-2 bg-white shadow-card-sm">
              Ferramentas de Estudo
            </h2>
          </div>

          {/* Study Tools Grid */}
          <div className="study-tools-grid">
            {/* 1ª LINHA */}
            {/* Card QUESTÕES */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="group cursor-pointer"
              onClick={() => navigate("/questions")}
              data-tutorial="questions"
            >
              <div className="study-card bg-hackathon-yellow overflow-hidden rounded-lg border-2 border-black shadow-card-sm hover:shadow-lg transition-all duration-300 h-full">
                <div className="study-card-content">
                  <div className="p-4 flex items-center justify-center border-b-2 border-black">
                    <BookOpen className="h-10 w-10 text-black" />
                  </div>
                  <div className="study-card-body p-5 text-center">
                    <h3 className="text-xl font-bold text-gray-800 mb-2">Questões</h3>
                    <p className="text-gray-700 mb-6 flex-grow">Pratique com questões selecionadas e analise seu desempenho em tempo real.</p>
                    <Button
                      className="bg-black hover:bg-black/90 text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      Iniciar Prática
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Card FLASHCARDS */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15 }}
              className="group cursor-pointer"
              onClick={() => navigate("/flashcards")}
              data-tutorial="flashcards"
            >
              <div className="study-card bg-purple-600 overflow-hidden rounded-lg border-2 border-black shadow-card-sm hover:shadow-lg transition-all duration-300 h-full relative">
                {/* Badge LANÇAMENTO */}
                <div className="absolute top-2 right-2 z-10">
                  <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full border border-black shadow-sm">
                    LANÇAMENTO
                  </span>
                </div>

                <div className="study-card-content">
                  <div className="p-4 flex items-center justify-center border-b-2 border-black">
                    <Brain className="h-10 w-10 text-white" />
                  </div>
                  <div className="study-card-body p-5 text-center">
                    <h3 className="text-xl font-bold text-white mb-2">Flashcards</h3>
                    <p className="text-white/90 mb-4 flex-grow">Crie, compartilhe e estude com flashcards inteligentes da comunidade.</p>
                    <Button
                      className="bg-white hover:bg-gray-200 text-purple-600 border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Estudar Agora
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Card CADERNO DE ERROS */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="group cursor-pointer"
              onClick={() => navigate("/caderno-erros")}
              data-tutorial="error-notebook"
            >
              <div className="study-card bg-red-500 overflow-hidden rounded-lg border-2 border-black shadow-card-sm hover:shadow-lg transition-all duration-300 h-full relative">
                {/* Badge NOVO */}
                <div className="absolute top-2 right-2 z-10">
                  <span className="bg-hackathon-yellow text-black text-xs font-bold px-2 py-1 rounded-full border border-black shadow-sm">
                    NOVO
                  </span>
                </div>

                <div className="study-card-content">
                  <div className="p-4 flex items-center justify-center border-b-2 border-black">
                    <X className="h-10 w-10 text-white" />
                  </div>
                  <div className="study-card-body p-5 text-center">
                    <h3 className="text-xl font-bold text-white mb-2">Caderno de Erros</h3>
                    <p className="text-white/90 mb-4 flex-grow">
                      Revise suas questões erradas organizadas por tema e especialidade.
                    </p>
                    {!errorStatsLoading && errorStats.totalErrors > 0 && (
                      <div className="bg-white/20 rounded-lg p-3 mb-4">
                        <div className="text-white text-sm font-medium">
                          {errorStats.pendingErrors} questões para revisar
                        </div>
                        {errorStats.topPendingTheme && (
                          <div className="text-white/80 text-xs mt-1">
                            Foco: {errorStats.topPendingTheme.theme_name}
                          </div>
                        )}
                      </div>
                    )}
                    <Button
                      className="bg-white hover:bg-gray-200 text-red-500 border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Revisar Erros
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>


            {/* Card DR. WILL - IA MÉDICA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.25 }}
              className="group cursor-pointer"
              onClick={() => navigate("/dr-will")}
              data-tutorial="dr-will"
            >
              <div className="study-card bg-gradient-to-br from-purple-500 to-indigo-600 overflow-hidden rounded-lg border-2 border-black shadow-card-sm hover:shadow-lg transition-all duration-300 h-full relative">

                <div className="study-card-content">
                  <div className="p-4 flex items-center justify-center border-b-2 border-black relative">
                    <div className="relative">
                      <Bot className="h-10 w-10 text-white" />
                      <Sparkles className="h-4 w-4 text-yellow-300 absolute -top-1 -right-1 animate-pulse" />
                    </div>
                  </div>
                  <div className="study-card-body p-5 text-center">
                    <h3 className="text-xl font-bold text-white mb-2">Dr. Will</h3>
                    <p className="text-white/90 mb-4 flex-grow">
                      Converse com nossa IA. Tire dúvidas e discuta casos clínicos.
                    </p>
                    <Button
                      className="bg-white hover:bg-gray-200 text-purple-600 border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Conversar
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Card PROGRESSO */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="group cursor-pointer"
              onClick={() => navigate("/progress")}
              data-tutorial="progress"
            >
              <div className="study-card bg-hackathon-green overflow-hidden rounded-lg border-2 border-black shadow-card-sm hover:shadow-lg transition-all duration-300 h-full">
                <div className="study-card-content">
                  <div className="p-4 flex items-center justify-center border-b-2 border-black">
                    <BarChart3 className="h-10 w-10 text-black" />
                  </div>
                  <div className="study-card-body p-5 text-center">
                    <h3 className="text-xl font-bold text-black mb-2">Progresso</h3>
                    <p className="text-black/80 mb-6 flex-grow">
                      Visualize gráficos detalhados do seu desempenho e evolução no tempo.
                    </p>
                    <Button
                      className="bg-black hover:bg-black/90 text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      Ver Progresso
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>



            {/* 2ª LINHA */}
            {/* Card RANKING (azul) - LANÇADO */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.25 }}
              className="group cursor-pointer"
              onClick={() => navigate('/ranking')}
              data-tutorial="ranking"
            >
              <div className="study-card bg-[#0EA5E9] overflow-hidden rounded-lg border-2 border-black shadow-card-sm hover:shadow-lg transition-all duration-300 h-full relative">

                <div className="study-card-content">
                  <div className="p-4 flex items-center justify-center border-b-2 border-black">
                    <TrendingUp className="h-10 w-10 text-white" />
                  </div>
                  <div className="study-card-body p-5 text-center">
                    <h3 className="text-xl font-bold text-white mb-2">Ranking</h3>
                    <p className="text-white/80 mb-4 flex-grow">
                      Compare seu desempenho com outros estudantes e acompanhe sua posição no ranking.
                    </p>
                    <Button
                      className="w-full bg-white hover:bg-gray-200 text-[#0EA5E9] border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all pointer-events-none"
                    >
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Acessar Ranking
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Card CRONOGRAMA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="group cursor-pointer"
              onClick={() => navigate("/schedule")}
              data-tutorial="schedule"
            >
              <div className="study-card bg-[#7E69AB] overflow-hidden rounded-lg border-2 border-black shadow-card-sm hover:shadow-lg transition-all duration-300 h-full">
                <div className="study-card-content">
                  <div className="p-4 flex items-center justify-center border-b-2 border-black">
                    <Calendar className="h-10 w-10 text-white" />
                  </div>
                  <div className="study-card-body p-5 text-center">
                    <h3 className="text-xl font-bold text-white mb-2">Cronograma</h3>
                    <p className="text-white/90 mb-4 flex-grow">
                      Organize seus estudos com cronogramas personalizados e acompanhe seu planejamento semanal.
                    </p>
                    <Button
                      className="bg-white hover:bg-gray-200 text-[#7E69AB] border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      Ver Cronograma
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Card FEEDBACK & SUPORTE */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.35 }}
              className="group cursor-pointer"
              onClick={() => navigate("/feedback-suporte")}
              data-tutorial="feedback"
            >
              <div className="study-card bg-[#FF6B6B] overflow-hidden rounded-lg border-2 border-black shadow-card-sm hover:shadow-lg transition-all duration-300 h-full">
                <div className="study-card-content">
                  <div className="p-4 flex items-center justify-center border-b-2 border-black">
                    <HeadphonesIcon className="h-10 w-10 text-white" />
                  </div>
                  <div className="study-card-body p-5 text-center">
                    <h3 className="text-xl font-bold text-white mb-2">Feedback & Suporte</h3>
                    <p className="text-white/90 mb-4 flex-grow">
                      Deixe seu feedback, sugestões ou entre em contato com nosso suporte para ajuda.
                    </p>
                    <Button
                      className="bg-white hover:bg-gray-200 text-[#FF6B6B] border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                    >
                      Deixar Feedback
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Dashboard Section */}
        <section className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="w-full" data-tutorial="history">
              {loadSecondaryContent ? (
                <SessionHistory />
              ) : (
                <div className="bg-white rounded-lg border-2 border-black shadow-card-sm">
                  <div className="flex items-center justify-between p-4 sm:p-6 border-b-2 border-black">
                    <div className="flex items-center gap-2 sm:gap-3">
                      <div className="p-2 sm:p-3 bg-gradient-to-r from-red-400 to-red-300 rounded-full border-2 border-black animate-pulse">
                        <div className="h-5 w-5 sm:h-6 sm:w-6 bg-white opacity-70 rounded"></div>
                      </div>
                      <div className="h-5 sm:h-6 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-40 sm:w-48"></div>
                    </div>
                  </div>
                  <div className="p-4 sm:p-6 space-y-4">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="p-4 bg-white border-2 border-black rounded-lg shadow-card-sm animate-pulse">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                          <div className="flex flex-col space-y-3 flex-1">
                            <div className="h-4 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-32"></div>
                            <div className="h-3 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse w-24"></div>
                          </div>
                          <div className="h-8 w-8 bg-gradient-to-r from-red-300 to-red-200 rounded animate-pulse border-2 border-black"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="w-full" data-tutorial="today">
              {/* ✅ NOVO: Banner de aviso sobre refresh manual */}
              <RefreshWarningBanner />

              {loadSecondaryContent ? (
                <TodayStudies enabled={true} />
              ) : (
                <div className="bg-white rounded-xl p-4 sm:p-5 shadow-sm border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="h-5 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-32"></div>
                    <div className="h-6 w-6 bg-gradient-to-r from-blue-300 to-blue-200 rounded-full animate-pulse"></div>
                  </div>
                  <div className="space-y-3">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="flex items-center gap-3 p-3 rounded-lg border border-gray-100">
                        <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-300 rounded-full animate-pulse"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gradient-to-r from-gray-400 to-gray-300 rounded animate-pulse w-3/4 mb-2"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse w-1/2"></div>
                        </div>
                        <div className="h-6 w-16 bg-gradient-to-r from-gray-300 to-gray-200 rounded animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>
      </div>

      {/* Welcome Dialog - aparece antes do tutorial */}
      <WelcomeDialog
        open={showWelcomeDialog && !isLoading}
        onContinue={markWelcomeAsSeen}
      />



      {/* Mobile Profile Button - só aparece no mobile */}
      <div className="sm:hidden">
        <MobileProfileButton variant="floating" />
      </div>

      {/* Footer - aparece em mobile e desktop */}
      <footer className="bg-hackathon-yellow py-3 border-t-2 border-black mt-8">
        <div className="container max-w-7xl mx-auto px-4">
          <div className="flex justify-center items-center gap-4">
            <a
              href="https://pedb.com.br/"
              target="_blank"
              rel="noopener noreferrer"
              className="transition-transform hover:scale-105"
            >
              <img
                src="/faviconx.webp"
                alt="PedBook"
                className="h-6"
              />
            </a>
            <a
              href="https://medunity.com.br/"
              target="_blank"
              rel="noopener noreferrer"
              className="transition-transform hover:scale-105"
            >
              <img
                src="/logo-med-unity-sem-fund.webp"
                alt="Med Unity"
                className="h-6"
              />
            </a>
            <span className="text-xs text-gray-600 ml-2">
              © 2025 Med EVO
            </span>
          </div>
        </div>
      </footer>



      {/* Ranking Info Dialog */}
      <Dialog open={showRankingDialog} onOpenChange={setShowRankingDialog}>
        <DialogContent className="max-w-[95vw] sm:max-w-lg mx-auto max-h-[90dvh] overflow-y-auto rounded-2xl">
          <DialogHeader className="text-center pr-0">
            <DialogTitle className="flex items-center justify-center gap-2 text-xl sm:text-2xl pr-12">
              <TrendingUp className="h-6 w-6 text-blue-500" />
              Sistema de Ranking
            </DialogTitle>
            <DialogDescription>
              Descubra como funciona nosso sistema de classificação competitiva
            </DialogDescription>
          </DialogHeader>
          <div className="text-center space-y-4 pt-4">
            <div className="bg-blue-50 p-4 rounded-xl border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-3 flex items-center justify-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Ranking Competitivo
              </h4>
              <p className="text-blue-700 text-sm leading-relaxed">
                Sistema completo de <strong>ranking estudantil</strong> que permite comparar seu desempenho
                com outros estudantes, acompanhar sua evolução e manter a motivação através da competição saudável.
              </p>
            </div>

            <div className="bg-green-50 p-4 rounded-xl border border-green-200">
              <h4 className="font-semibold text-green-800 mb-3 text-center">🏆 Como Funciona:</h4>
              <div className="text-green-700 text-sm space-y-2">
                <div className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">•</span>
                  <span><strong>Pontuação por Questões:</strong> Cada questão respondida corretamente gera pontos</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">•</span>
                  <span><strong>Períodos Diferentes:</strong> Rankings diário, semanal e geral</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">•</span>
                  <span><strong>Top 3 Destacado:</strong> Pódio especial para os melhores estudantes</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">•</span>
                  <span><strong>Posição Pessoal:</strong> Acompanhe sua classificação em tempo real</span>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-xl border border-purple-200">
              <h4 className="font-semibold text-purple-800 mb-3 flex items-center justify-center gap-2">
                <Users className="h-5 w-5" />
                Recursos Sociais
              </h4>
              <div className="text-purple-700 text-sm space-y-2">
                <div className="flex items-start gap-2">
                  <span className="text-purple-600 font-bold">•</span>
                  <span><strong>Perfis Públicos:</strong> Veja o desempenho de outros estudantes</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-purple-600 font-bold">•</span>
                  <span><strong>Competição Saudável:</strong> Motive-se através da comparação</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-purple-600 font-bold">•</span>
                  <span><strong>Estatísticas Detalhadas:</strong> Análise completa do seu progresso</span>
                </div>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-xl border border-green-200">
              <h4 className="font-semibold text-green-800 mb-3 flex items-center justify-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Status: Lançado! 🎉
              </h4>
              <p className="text-green-700 text-sm leading-relaxed">
                O sistema de ranking está <strong>oficialmente lançado</strong> e totalmente funcional!
                Acesse agora mesmo e veja sua posição entre os estudantes da plataforma.
              </p>
            </div>

            <div className="bg-orange-50 p-4 rounded-xl border border-orange-200">
              <h4 className="font-semibold text-orange-800 mb-3 text-center">🚀 Próximas Atualizações:</h4>
              <div className="text-orange-700 text-sm space-y-1">
                <div>• <strong>Sistema de Badges:</strong> Conquistas e medalhas especiais</div>
                <div>• <strong>Ligas Competitivas:</strong> Divisões por nível de habilidade</div>
                <div>• <strong>Desafios Semanais:</strong> Competições temáticas especiais</div>
                <div>• <strong>Ranking por Especialidade:</strong> Classificações específicas por área</div>
              </div>
            </div>

            <div className="text-center pt-4 space-y-3">
              <Button
                onClick={() => {
                  setShowRankingDialog(false);
                  navigate('/ranking');
                }}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-xl font-bold"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Acessar Ranking Agora! 🏆
              </Button>
              <Button
                onClick={() => setShowRankingDialog(false)}
                variant="outline"
                className="w-full border-gray-300 text-gray-600 hover:bg-gray-50 px-8 py-2 rounded-xl"
              >
                Fechar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Study;
