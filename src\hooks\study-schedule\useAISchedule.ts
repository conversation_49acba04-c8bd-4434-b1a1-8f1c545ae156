import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import type { AIScheduleOptions } from "@/types/study-schedule";
import type { GenerationStats } from "./types";
import { useState } from "react";
import { useDomain } from "@/hooks/useDomain";
import { useQueryClient } from '@tanstack/react-query';
import { useInstitutionBasedSchedule } from "@/hooks/useInstitutionBasedSchedule";
import { useStudyPreferences } from "@/hooks/useStudyPreferences";

export const useAISchedule = (
  setGenerationStats: (stats: GenerationStats | null) => void,
  addWeeks: (numberOfWeeks: number) => Promise<boolean>
) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { domain: userDomain } = useDomain();
  const { generateScheduleByInstitution } = useInstitutionBasedSchedule();
  const { preferences } = useStudyPreferences(); // ✅ CORREÇÃO: Usar useStudyPreferences para ter acesso às instituições
  const queryClient = useQueryClient();

  const generateAISchedule = async (options: AIScheduleOptions) => {
    setIsLoading(true);
    setGenerationStats(null);
    const startTime = performance.now();

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Se modo baseado em instituições OU se usuário tem preferências de instituições, usar o novo sistema
      const shouldUseInstitutionBased = options.generationMode === 'institution_based' ||
        (preferences?.target_institutions && preferences.target_institutions.length > 0 && !preferences.target_institutions_unknown);

      if (shouldUseInstitutionBased) {
        // Usar preferências do usuário se não foram especificadas nas opções
        const extractedInstitutionIds = options.institutionIds?.length ? options.institutionIds :
          preferences?.target_institutions?.map(inst => inst.id) || [];

        const institutionOptions = {
          ...options,
          generationMode: 'institution_based' as const,
          institutionIds: extractedInstitutionIds,
          startYear: options.startYear,
          endYear: options.endYear
        };

        const result = await generateInstitutionBasedSchedule(institutionOptions, startTime);

        // Invalidar queries após geração baseada em instituições
        await queryClient.invalidateQueries({ queryKey: ['schedule'] });
        await queryClient.invalidateQueries({ queryKey: ['schedule', user.id] });
        await queryClient.invalidateQueries({ queryKey: ['consolidated-schedule'] });

        // Refetch para atualizar a UI
        await queryClient.refetchQueries({ queryKey: ['schedule', user.id] });



        return result;
      }



      // Caso contrário, usar o sistema aleatório padrão (código existente)

      // Handle existing week or create new weeks
      if (options.scheduleOption === "existing" && options.targetWeek) {
        const { data: existingSchedules, error } = await supabase
          .from('study_schedules')
          .select('*')
          .eq('user_id', user.id)
          .eq('week_number', options.targetWeek);

        if (error) {
          throw new Error(`Error checking week ${options.targetWeek}: ${error.message}`);
        }

        if (!existingSchedules || existingSchedules.length === 0) {
          throw new Error(`Week ${options.targetWeek} not found. Please create this week first or select a different week.`);
        }
      }
      else if (options.scheduleOption === "new") {
        const weeksCreated = await addWeeks(options.weeksCount);

        if (!weeksCreated) {
          throw new Error('Failed to create new weeks');
        }

        // Semanas criadas com sucesso
      }

      // Use the domain from options if provided, otherwise use the user's domain from the hook
      const domain = options.domain || userDomain || 'residencia';

      // Fetch all categories (specialties, themes, focuses)
      const { data: domainSpecialties, error: specialtiesError } = await supabase
        .from('study_categories')
        .select(`
          id,
          name,
          type,
          parent_id
        `)
        .eq('type', 'specialty');

      if (specialtiesError) {
        console.error('🤖 [useAISchedule] Erro ao buscar especialidades:', specialtiesError);
        throw specialtiesError;
      }



      if (!domainSpecialties || domainSpecialties.length === 0) {
        throw new Error('No specialties found');
      }




      // ✅ CORREÇÃO: Usar RPC otimizada para evitar URLs muito longas
      const specialtyIds = domainSpecialties.map(t => t.id);
      let allThemes: any[] = [];

      if (specialtyIds.length > 50) {
        // Usar RPC otimizada para muitos IDs
        try {
          const { data: themesData, error: themesError } = await supabase.rpc('get_categories_by_parent_ids', {
            parent_ids: specialtyIds,
            category_type: 'theme'
          });

          if (themesError) {
            console.error('🤖 [useAISchedule] Erro ao buscar temas (RPC):', themesError);
            throw themesError;
          }

          allThemes = themesData || [];
        } catch (error) {
          console.error('🤖 [useAISchedule] Fallback para chunking de temas:', error);

          // Fallback para chunking se RPC falhar
          const chunkSize = 50;
          const chunks = [];
          for (let i = 0; i < specialtyIds.length; i += chunkSize) {
            chunks.push(specialtyIds.slice(i, i + chunkSize));
          }

          for (const chunk of chunks) {
            const { data: chunkThemes, error: chunkError } = await supabase
              .from('study_categories')
              .select(`id, name, type, parent_id`)
              .eq('type', 'theme')
              .in('parent_id', chunk);

            if (chunkError) throw chunkError;
            if (chunkThemes) allThemes.push(...chunkThemes);
          }
        }
      } else {
        // Query normal para poucos IDs
        const { data: themesData, error: themesError } = await supabase
          .from('study_categories')
          .select(`id, name, type, parent_id`)
          .eq('type', 'theme')
          .in('parent_id', specialtyIds);

        if (themesError) {
          console.error('🤖 [useAISchedule] Erro ao buscar temas:', themesError);
          throw themesError;
        }

        allThemes = themesData || [];
      }

      if (!allThemes || allThemes.length === 0) {
        throw new Error('No themes found for the specialties');
      }



      // ✅ CORREÇÃO: Usar RPC otimizada para focos também
      const themeIds = allThemes.map(t => t.id);
      let allFocuses: any[] = [];

      if (themeIds.length > 50) {
        // Usar RPC otimizada para muitos IDs
        try {
          const { data: focusesData, error: focusesError } = await supabase.rpc('get_categories_by_parent_ids', {
            parent_ids: themeIds,
            category_type: 'focus'
          });

          if (focusesError) {
            console.error('🤖 [useAISchedule] Erro ao buscar focos (RPC):', focusesError);
            throw focusesError;
          }

          allFocuses = focusesData || [];
        } catch (error) {
          console.error('🤖 [useAISchedule] Fallback para chunking de focos:', error);

          // Fallback para chunking se RPC falhar
          const chunkSize = 50;
          const chunks = [];
          for (let i = 0; i < themeIds.length; i += chunkSize) {
            chunks.push(themeIds.slice(i, i + chunkSize));
          }

          for (const chunk of chunks) {
            const { data: chunkFocuses, error: chunkError } = await supabase
              .from('study_categories')
              .select(`id, name, type, parent_id`)
              .eq('type', 'focus')
              .in('parent_id', chunk);

            if (chunkError) throw chunkError;
            if (chunkFocuses) allFocuses.push(...chunkFocuses);
          }
        }
      } else {
        // Query normal para poucos IDs
        const { data: focusesData, error: focusesError } = await supabase
          .from('study_categories')
          .select(`id, name, type, parent_id`)
          .eq('type', 'focus')
          .in('parent_id', themeIds);

        if (focusesError) {
          console.error('🤖 [useAISchedule] Erro ao buscar focos:', focusesError);
          throw focusesError;
        }

        allFocuses = focusesData || [];
      }

      const isOftalmologiaDomain = domain === 'oftalmologia';

      if (!isOftalmologiaDomain && (!allFocuses || allFocuses.length === 0)) {
        throw new Error('No focuses found for the themes');
      }



      // Filter categories by domain

      const { data: questionsInDomain, error: questionsError } = await supabase
        .from('questions')
        .select(`
          id,
          specialty_id,
          theme_id,
          focus_id,
          knowledge_domain
        `)
        .eq('knowledge_domain', domain);

      if (questionsError) {
        console.error('🤖 [useAISchedule] Erro ao buscar questões:', questionsError);
        throw questionsError;
      }

      // Create sets of valid category IDs based on questions
      const validSpecialtyIds = new Set(questionsInDomain?.map(q => q.specialty_id) || []);
      const validThemeIds = new Set(questionsInDomain?.map(q => q.theme_id) || []);
      const validFocusIds = new Set(questionsInDomain?.map(q => q.focus_id) || []);

      // Filter categories based on which ones have questions
      const filteredSpecialties = domainSpecialties.filter(s => validSpecialtyIds.has(s.id));
      let filteredThemes = allThemes.filter(t => validThemeIds.has(t.id));
      let filteredFocuses = (allFocuses || []).filter(f => validFocusIds.has(f.id));



      // ✅ VERIFICAR TEMAS/FOCOS JÁ EXISTENTES NO CRONOGRAMA
      let existingThemeIds = new Set<string>();
      let existingFocusIds = new Set<string>();

      if (options.scheduleOption === "existing" || options.scheduleOption === "new") {
        try {
          // Buscar temas/focos já utilizados no cronograma atual
          const { data: existingItems, error: existingError } = await supabase
            .from('study_schedule_items')
            .select('theme_id, focus_id')
            .eq('user_id', user.id);

          if (!existingError && existingItems) {
            existingThemeIds = new Set(existingItems.map(item => item.theme_id).filter(Boolean));
            existingFocusIds = new Set(existingItems.map(item => item.focus_id).filter(Boolean));


          }
        } catch (error) {
          console.warn('⚠️ [useAISchedule] Erro ao buscar itens existentes, continuando sem filtro:', error);
        }
      }

      // ✅ FILTRAR ITENS JÁ EXISTENTES (mas permitir reutilização se necessário)
      const originalThemesCount = filteredThemes.length;
      const originalFocusesCount = filteredFocuses.length;

      // Primeiro, tentar usar apenas temas/focos não utilizados
      let availableThemes = filteredThemes.filter(t => !existingThemeIds.has(t.id));
      let availableFocuses = filteredFocuses.filter(f => !existingFocusIds.has(f.id));

      // Calcular quantos slots serão necessários (estimativa)
      const enabledDaysCount = Object.values(options.availableDays).filter(day => day.enabled).length;
      const estimatedSlotsNeeded = enabledDaysCount * 4; // Estimativa de 4 tópicos por dia

      // Se não há itens únicos suficientes, permitir reutilização
      if (isOftalmologiaDomain) {
        if (availableThemes.length === 0 || (availableThemes.length < estimatedSlotsNeeded && estimatedSlotsNeeded > 0)) {

          availableThemes = filteredThemes; // Usar todos os temas
        }
        filteredThemes = availableThemes;
      } else {
        if (availableFocuses.length === 0 || (availableFocuses.length < estimatedSlotsNeeded && estimatedSlotsNeeded > 0)) {

          availableFocuses = filteredFocuses; // Usar todos os focos
        }
        filteredFocuses = availableFocuses;
      }



      if (filteredSpecialties.length === 0 || filteredThemes.length === 0) {
        throw new Error(`Not enough categories found for domain ${domain}`);
      }

      if (!isOftalmologiaDomain && filteredFocuses.length === 0) {
        throw new Error(`Not enough categories found for domain ${domain}`);
      }

      // Prepare day schedules
      // Declarar topicDurationMinutes antes de usar
      const topicDurationMinutes = parseInt(options.topicDuration);

      const getConvertedDayName = (dayName: string) => {
        const conversion = {
          'Domingo': 'domingo',
          'Segunda-feira': 'segunda-feira',
          'Terça-feira': 'terça-feira',
          'Quarta-feira': 'quarta-feira',
          'Quinta-feira': 'quinta-feira',
          'Sexta-feira': 'sexta-feira',
          'Sábado': 'sábado'
        };
        return conversion[dayName as keyof typeof conversion] || dayName.toLowerCase();
      };

      const daySchedules: {[key: string]: {startTime: string, endTime: string}[]} = {};

      Object.entries(options.availableDays).forEach(([day, config]) => {
        if (config.enabled && config.periods.length > 0) {
          const validPeriods = config.periods.filter(p => {
            if (!p.startTime || !p.endTime) return false;

            // Validar formato de horário
            const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
            if (!timeRegex.test(p.startTime) || !timeRegex.test(p.endTime)) return false;

            // Converter para minutos
            const startMinutes = p.startTime.split(':').map(Number).reduce((h, m) => h * 60 + m);
            const endMinutes = p.endTime.split(':').map(Number).reduce((h, m) => h * 60 + m);

            // Validar que início é menor que fim
            if (startMinutes >= endMinutes) return false;

            // Validar que tem tempo suficiente para pelo menos 1 tópico
            const periodMinutes = endMinutes - startMinutes;
            return periodMinutes >= topicDurationMinutes;
          });

          if (validPeriods.length > 0) {
            daySchedules[getConvertedDayName(day)] = validPeriods;
          }
        }
      });

      // Get target schedules (weeks) - refetch to make sure we have the latest data
      const { data: schedules, error: schedulesError } = await supabase
        .from('study_schedules')
        .select('*')
        .eq('user_id', user.id)
        .order('week_number', { ascending: true });

      if (schedulesError) {
        console.error('🤖 [useAISchedule] Erro ao buscar cronogramas:', schedulesError);
        throw schedulesError;
      }

      if (!schedules || schedules.length === 0) {
        throw new Error('No weeks available for schedule generation. Please try adding weeks again.');
      }

      let targetSchedules = schedules;

      if (options.scheduleOption === "existing" && options.targetWeek) {
        targetSchedules = schedules.filter(s => s.week_number === options.targetWeek);
      } else if (options.scheduleOption === "new") {
        // Get the newly added weeks (the last n weeks, where n is options.weeksCount)
        targetSchedules = schedules.slice(-options.weeksCount);
      }

      if (!targetSchedules.length) {
        throw new Error('No weeks available for schedule generation. Please try adding weeks again.');
      }



      // Generate topics for each day
      const daysOfWeek = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];

      let topicsCreated = 0;
      // Track which specialties, themes, and focuses are actually used
      const specialtiesUsed = new Set<string>();
      const themesUsed = new Set<string>();
      const focusesUsed = new Set<string>();

      // ✅ CORREÇÃO: Função melhorada para reciclar temas/focos quando necessário
      const getRandomItems = <T>(items: T[], count: number): T[] => {
        const result: T[] = [];

        // Se não há itens disponíveis, retornar array vazio
        if (items.length === 0) {
          console.warn('🔄 [getRandomItems] Nenhum item disponível para seleção');
          return result;
        }

        let copyItems = [...items];

        for (let i = 0; i < count; i++) {
          // ✅ RECICLAGEM: Se acabaram os itens únicos, reciclar todos
          if (copyItems.length === 0) {

            copyItems = [...items];
          }

          // Se ainda não há itens após reciclagem, algo está errado
          if (copyItems.length === 0) {
            console.error('❌ [getRandomItems] Erro: Lista de itens vazia mesmo após reciclagem');
            break;
          }

          const randomIndex = Math.floor(Math.random() * copyItems.length);
          result.push(copyItems[randomIndex]);
          copyItems.splice(randomIndex, 1);
        }


        return result;
      };

      // Generate topics for each schedule (week)

      for (const schedule of targetSchedules) {
        for (const [dayName, periods] of Object.entries(daySchedules)) {
          const dayIndex = daysOfWeek.indexOf(dayName);
          if (dayIndex === -1) continue;

          const weekStartDate = new Date(schedule.week_start_date);
          const dayDate = new Date(weekStartDate);
          dayDate.setDate(weekStartDate.getDate() + dayIndex);

          for (const period of periods) {
            if (!period.startTime || !period.endTime) {
              continue;
            }

            const startTimeParts = period.startTime.split(':').map(Number);
            const endTimeParts = period.endTime.split(':').map(Number);

            const startDate = new Date(dayDate);
            startDate.setHours(startTimeParts[0], startTimeParts[1], 0);

            const endDate = new Date(dayDate);
            endDate.setHours(endTimeParts[0], endTimeParts[1], 0);

            const periodMinutes = (endDate.getTime() - startDate.getTime()) / (1000 * 60);
            const topicsCount = Math.floor(periodMinutes / topicDurationMinutes);

            if (topicsCount <= 0) {
              continue;
            }

            let randomItems;
            if (isOftalmologiaDomain) {
              randomItems = getRandomItems(filteredThemes, topicsCount);
            } else {
              randomItems = getRandomItems(filteredFocuses, topicsCount);
            }

            let currentTime = new Date(startDate);
            for (let i = 0; i < randomItems.length; i++) {
              const item = randomItems[i];
              let parentSpecialty, parentTheme, topic;

              if (isOftalmologiaDomain) {
                parentTheme = item;
                parentSpecialty = filteredSpecialties.find(s => s.id === parentTheme.parent_id);

                if (!parentSpecialty) continue;

                topic = {
                  specialty_name: parentSpecialty.name,
                  specialty_id: parentSpecialty.id,
                  theme_name: parentTheme.name,
                  theme_id: parentTheme.id,
                  focus_name: "Geral",
                  focus_id: null
                };



                specialtiesUsed.add(parentSpecialty.name);
                themesUsed.add(parentTheme.name || 'TEMA_UNDEFINED');
              } else {
                const focus = item;
                parentTheme = filteredThemes.find(t => t.id === focus.parent_id);
                if (!parentTheme) continue;

                parentSpecialty = filteredSpecialties.find(s => s.id === parentTheme.parent_id);
                if (!parentSpecialty) continue;

                topic = {
                  specialty_name: parentSpecialty.name,
                  specialty_id: parentSpecialty.id,
                  theme_name: parentTheme.name,
                  theme_id: parentTheme.id,
                  focus_name: focus.name,
                  focus_id: focus.id
                };



                specialtiesUsed.add(parentSpecialty.name);
                themesUsed.add(parentTheme.name || 'TEMA_UNDEFINED');
                focusesUsed.add(focus.name);
              }

              const topicStartTime = currentTime.toTimeString().substring(0, 5);

              currentTime = new Date(currentTime.getTime() + topicDurationMinutes * 60 * 1000);

              const difficulty = ['Fácil', 'Médio', 'Difícil'][Math.floor(Math.random() * 3)] as 'Fácil' | 'Médio' | 'Difícil';

              const duration = `${topicDurationMinutes} minutos`;

              const activitiesOpcoes = [
                "Estudo Teórico",
                "Resolver Questões",
                "Estudo Teórico + Resolver Questões",
                "Revisão de Conteúdo",
                "Resumo do Tema"
              ];

              const activity = activitiesOpcoes[Math.floor(Math.random() * activitiesOpcoes.length)];



              // ✅ CRÍTICO: Verificar se theme_name está correto antes da inserção
              if (topic.theme_name === 'Desconhecido' || !topic.theme_name) {
                console.error('🚨 [generateAISchedule] PROBLEMA ENCONTRADO - theme_name inválido:', {
                  topic_theme_name: topic.theme_name,
                  topic_theme_id: topic.theme_id,
                  topic_focus_name: topic.focus_name,
                  parentTheme_name: parentTheme?.name,
                  parentTheme_id: parentTheme?.id,
                  fullTopic: topic
                });
              }

              const newItem = {
                schedule_id: schedule.id,
                day_of_week: dayName,
                topic: `${topic.specialty_name} - ${topic.theme_name}`,
                specialty_name: topic.specialty_name,
                specialty_id: topic.specialty_id,
                theme_name: topic.theme_name,
                theme_id: topic.theme_id,
                focus_name: topic.focus_name,
                focus_id: topic.focus_id,
                difficulty,
                activity_description: activity,
                start_time: topicStartTime,
                duration,
                type: 'study',
                activity_type: 'study',
                week_number: schedule.week_number,
                study_status: 'pending'
              };



              // ✅ NOVO: Aplicar filtro de dias passados igual à geração com IA
              const today = new Date();
              const currentDayOfWeek = today.getDay(); // 0 = domingo, 1 = segunda, etc.

              // Mapear dias da semana para números
              const dayMap: { [key: string]: number } = {
                'domingo': 0, 'segunda-feira': 1, 'terça-feira': 2, 'quarta-feira': 3,
                'quinta-feira': 4, 'sexta-feira': 5, 'sábado': 6
              };

              // ✅ VERIFICAR: Se é um dia que já passou na primeira semana
              const shouldSkip = (() => {
                // Só verificar na primeira semana
                if (schedule.week_number !== targetSchedules[0]?.week_number) return false;

                const dayNumber = dayMap[dayName.toLowerCase()];
                if (dayNumber === undefined) return false;

                // Se o dia da semana é menor que o dia atual, já passou
                const isPast = dayNumber < currentDayOfWeek;

                if (isPast) {

                }

                return isPast;
              })();

              // ✅ PULAR: Se o dia já passou, não inserir
              if (shouldSkip) {
                continue;
              }

              try {

                const { error } = await supabase
                  .from('study_schedule_items')
                  .insert(newItem);

                if (error) {
                  console.error('❌ [generateAISchedule] Erro ao inserir item:', error);
                  continue;
                }

                topicsCreated++;
              } catch (insertError) {
                console.error('❌ [generateAISchedule] Erro na inserção:', insertError);
                // Silently continue on insert errors
              }
            }
          }
        }
      }

      const endTime = performance.now();
      const timeSpent = (endTime - startTime) / 1000;

      setGenerationStats({
        totalTopics: topicsCreated,
        totalWeeks: targetSchedules.length,
        specialties: Array.from(specialtiesUsed),
        themes: Array.from(themesUsed),
        focuses: Array.from(focusesUsed),
        totalHours: 0, // Será calculado depois se necessário
        generationTime: timeSpent,
        domain: domain,
        totalSpecialties: filteredSpecialties.length,
        totalThemes: filteredThemes.length,
        totalFocuses: filteredFocuses.length
      });

      // ✅ CORREÇÃO: Invalidar TODAS as queries relacionadas ao cronograma

      // 1. Invalidar queries específicas
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedule'] }),
        queryClient.invalidateQueries({ queryKey: ['consolidated-schedule-data'] }),
        queryClient.invalidateQueries({ queryKey: ['processed-schedule-data'] }),
        queryClient.invalidateQueries({ queryKey: ['study-schedule'] }),
      ]);

      // 2. Invalidar todas as queries relacionadas
      await queryClient.invalidateQueries({
        predicate: (query) => {
          const key = query.queryKey[0];
          const shouldInvalidate = key === 'schedule' ||
                                  key === 'consolidated-schedule-data' ||
                                  key === 'processed-schedule-data' ||
                                  key === 'study-schedule' ||
                                  key === 'study_schedules' ||
                                  key === 'study_schedule_items';
          return shouldInvalidate;
        },
        refetchType: 'all'
      });

      // 3. Forçar refetch das queries principais
      await queryClient.refetchQueries({ queryKey: ['schedule'] });

      console.log('✅ [generateAISchedule] Queries invalidadas e refetchadas!');

      return true; // Return true to indicate success
    } catch (error: any) {


      toast({
        title: "Erro ao gerar cronograma",
        description: error.message,
        variant: "destructive"
      });

      return false; // Return false to indicate failure
    } finally {
      setIsLoading(false);
    }
  };

  const generateInstitutionBasedSchedule = async (options: AIScheduleOptions, startTime: number) => {
    try {
      // Converter AIScheduleOptions para InstitutionScheduleOptions
      const currentYear = new Date().getFullYear();
      const institutionOptions = {
        ...options,
        institutionIds: options.institutionIds || [],
        startYear: options.startYear || currentYear - 5,
        endYear: options.endYear || currentYear,
        generationMode: options.generationMode || 'random' as const,
        domain: options.domain || userDomain || 'residencia'
      };

      // Usar o hook de geração baseada em instituições
      const schedule = await generateScheduleByInstitution(institutionOptions);

      const endTime = performance.now();
      const timeSpent = (endTime - startTime) / 1000;

      // Calcular estatísticas básicas
      const specialtiesUsed = new Set(schedule.map(topic => topic.specialty));
      const themesUsed = new Set(schedule.map(topic => topic.theme));
      const focusesUsed = new Set(schedule.map(topic => topic.focus));

      // ✅ CORREÇÃO: Converter array de tópicos em estrutura de cronograma
      if (Array.isArray(schedule) && schedule.length > 0) {
        // Organizar tópicos por dias
        const daysOfWeek = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
        const recommendations = daysOfWeek.map(day => {
          const dayTopics = schedule.filter(topic => topic.day.toLowerCase() === day);
          return {
            day,
            topics: dayTopics
          };
        });



        // ✅ NOVO: Extrair informações de repetição do primeiro tópico (todos têm as mesmas)
        const firstTopic = schedule[0] as any;

        setGenerationStats({
          totalTopics: schedule.length,
          totalWeeks: options.weeksCount,
          specialties: Array.from(specialtiesUsed),
          themes: Array.from(themesUsed),
          focuses: Array.from(focusesUsed),
          totalHours: 0,
          generationTime: timeSpent,
          domain: options.domain || userDomain || 'residencia',
          totalSpecialties: specialtiesUsed.size,
          totalThemes: themesUsed.size,
          totalFocuses: focusesUsed.size,
          // ✅ NOVO: Informações de repetição
          focusRepetitionWarning: firstTopic?.focusRepetitionWarning || false,
          availableFocusesCount: firstTopic?.availableFocusesCount || 0,
          totalSlotsCount: firstTopic?.totalSlotsCount || 0
        });

        // ✅ CORREÇÃO: Retornar estrutura de cronograma correta
        return { recommendations };
      } else {

        return { recommendations: [] };
      }
    } catch (error: any) {
      toast({
        title: "Erro ao gerar cronograma baseado em instituições",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  };

  return {
    isLoading,
    generateAISchedule
  };
};
