import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Brain, Eye, HelpCircle, Flag } from 'lucide-react';
import { useAICommentary } from '@/hooks/useAICommentary';
import { useDarkMode } from '@/contexts/DarkModeContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface ErrorQuestionAICommentaryProps {
  question: {
    id: string;
    question_id: string;
    question_content: string;
    response_choices: string[];
    correct_choice: string;
    specialty_name: string;
    ai_commentary?: any;
    question_format?: string;
  };
  onCommentaryGenerated?: (commentary: any) => void;
}

interface AICommentaryResponse {
  alternativas: Array<{
    texto: string;
    comentario: string;
    correta: boolean;
  }>;
  comentario_final: string;
  possivel_erro_no_gabarito?: boolean;
  justificativa_erro_gabarito?: string;
}

const HelpDialog = () => {
  const { isDarkMode } = useDarkMode();

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
        >
          <HelpCircle className="h-4 w-4 text-gray-500" />
        </Button>
      </DialogTrigger>
      <DialogContent className={`max-w-[90vw] max-h-[80vh] w-full sm:max-w-md rounded-lg overflow-y-auto transition-colors duration-200 ${
        isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
      }`} style={{ background: isDarkMode ? '#1f2937' : '#ffffff' }}>
        <DialogHeader>
          <DialogTitle className={`flex items-center gap-2 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>
            <Brain className={`h-5 w-5 transition-colors duration-200 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`} />
            Como funciona a Análise da IA
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className={`font-semibold mb-2 transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-900'
            }`}>📝 Gerar Análise vs Ver Análise</h4>
            <p className={`leading-relaxed transition-colors duration-200 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-600'
            }`}>
              <strong>"Gerar Análise"</strong> aparece quando a questão ainda não possui uma análise da IA.
              Ao clicar, nossa IA criará uma explicação detalhada para você.
            </p>
            <p className={`leading-relaxed mt-2 transition-colors duration-200 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-600'
            }`}>
              <strong>"Ver Análise"</strong> aparece quando a questão já possui uma análise pronta.
            </p>
          </div>

          <div>
            <h4 className={`font-semibold mb-2 transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-900'
            }`}>🎯 O que a IA analisa</h4>
            <ul className={`space-y-1 leading-relaxed transition-colors duration-200 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-600'
            }`}>
              <li>• <strong>Cada alternativa</strong> individualmente</li>
              <li>• <strong>Por que</strong> cada uma está certa ou errada</li>
              <li>• <strong>Conceitos importantes</strong> da questão</li>
              <li>• <strong>Dicas</strong> para questões similares</li>
            </ul>
          </div>

          <div>
            <h4 className={`font-semibold mb-2 transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-900'
            }`}>💡 Dica</h4>
            <p className={`leading-relaxed transition-colors duration-200 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-600'
            }`}>
              A análise é salva automaticamente. Você pode acessá-la sempre que quiser!
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const ErrorQuestionAICommentary: React.FC<ErrorQuestionAICommentaryProps> = ({
  question,
  onCommentaryGenerated
}) => {
  // Verificar se é questão discursiva
  const isDiscursive = question.question_format === 'DISSERTATIVA';
  const { isDarkMode } = useDarkMode();
  const { generateCommentary, isLoading } = useAICommentary();
  const [savedCommentary, setSavedCommentary] = useState<AICommentaryResponse | null>(null);
  const [showCommentary, setShowCommentary] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [localError, setLocalError] = useState<string | null>(null);
  const currentQuestionRef = useRef<string | null>(null);

  // Verificar se já existe análise salva
  const existingCommentary = question.ai_commentary;

  const hasValidAICommentary = useCallback((commentary: any): boolean => {
    if (!commentary) return false;

    if (typeof commentary === 'string') {
      return commentary.trim().length > 0;
    }

    if (typeof commentary === 'object') {
      return !!(
        commentary.comentario_final ||
        (commentary.alternativas && commentary.alternativas.length > 0)
      );
    }

    return false;
  }, []);

  useEffect(() => {
    const checkQuestionCommentary = async () => {
      currentQuestionRef.current = question.id;
      setIsInitialLoading(false);
      setLocalError(null);

      const questionAiCommentary = question.ai_commentary || null;
      const hasQuestionCommentary = hasValidAICommentary(questionAiCommentary);

      // 🔍 BUSCA ATIVA: Verificar se existe comentário salvo no banco
      let feedbackCommentary = null;
      let hasFeedbackCommentary = false;

      try {
        const { data, error } = await supabase.rpc('get_question_feedback_info', {
          question_id: question.question_id
        });

        if (!error && data?.ai_commentary) {
          feedbackCommentary = data.ai_commentary;
          hasFeedbackCommentary = hasValidAICommentary(feedbackCommentary);
        }
      } catch (error) {
        // Silenciar erro - continuar com verificação local
      }

      // Priorizar comentário do feedback (mais atualizado) ou comentário da questão
      if (hasFeedbackCommentary) {
        const typedCommentary = feedbackCommentary as AICommentaryResponse;
        if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(typedCommentary)) {
          setSavedCommentary(typedCommentary);
          setShowCommentary(false); // Não mostrar automaticamente, deixar usuário decidir
        }
      } else if (hasQuestionCommentary) {
        const typedCommentary = questionAiCommentary as AICommentaryResponse;
        if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(typedCommentary)) {
          setSavedCommentary(typedCommentary);
          setShowCommentary(false); // Não mostrar automaticamente, deixar usuário decidir
        }
      } else {
        setSavedCommentary(null);
        setShowCommentary(false);
      }
    };

    checkQuestionCommentary();
  }, [question.id, question.question_id, question.ai_commentary, hasValidAICommentary]);

  const saveCommentaryToQuestion = async (commentary: AICommentaryResponse) => {
    try {
      // Salvar na tabela questions usando question_id
      const { error } = await supabase
        .from('questions')
        .update({ ai_commentary: commentary })
        .eq('id', question.question_id);

      if (error) {
        console.error('Erro ao salvar análise:', error);
        toast({
          title: "Erro ao salvar análise",
          description: "A análise foi gerada mas não foi salva. Tente novamente.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Análise salva",
          description: "A análise foi gerada e salva com sucesso!",
          variant: "default",
        });
      }
    } catch (error) {
      console.error('Erro ao salvar análise:', error);
      toast({
        title: "Erro ao salvar análise",
        description: "A análise foi gerada mas não foi salva. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  const handleGenerateCommentary = useCallback(async () => {
    if (isLoading) return;

    const alternatives = question.response_choices || [];

    if (!alternatives || alternatives.length === 0) return;

    try {
      const correctAnswerIndex = parseInt(question.correct_choice);
      if (isNaN(correctAnswerIndex)) {
        setLocalError('Informações da questão não estão disponíveis para análise.');
        return;
      }

      const generatedCommentary = await generateCommentary(
        question.question_id,
        question.question_content,
        alternatives,
        correctAnswerIndex,
        undefined, // sessionId
        question.specialty_name || 'Medicina Geral',
        [] // mediaAttachments
      );

      if (generatedCommentary && currentQuestionRef.current === question.id) {
        setSavedCommentary(generatedCommentary);
        setShowCommentary(true);

        if (onCommentaryGenerated) {
          onCommentaryGenerated(generatedCommentary);
        }

        await saveCommentaryToQuestion(generatedCommentary);
      }
    } catch (error) {
      setLocalError('Erro ao gerar análise. Tente novamente.');
    }
  }, [
    isLoading,
    question.id,
    question.question_id,
    question.question_content,
    question.response_choices,
    question.correct_choice,
    question.specialty_name,
    generateCommentary,
    onCommentaryGenerated
  ]);

  const handleShowSavedCommentary = () => {
    setShowCommentary(true);
  };

  const handleHideCommentary = () => {
    setShowCommentary(false);
  };

  const { activeCommentary, hasExistingCommentary } = useMemo(() => {
    const active = savedCommentary || existingCommentary;
    const hasExisting = hasValidAICommentary(active);

    return {
      activeCommentary: active,
      hasExistingCommentary: hasExisting
    };
  }, [savedCommentary, existingCommentary, hasValidAICommentary]);

  if (localError) {
    return (
      <div className="mb-6">
        <div className="bg-red-50 border-2 border-red-200 rounded-lg p-4">
          <p className="text-red-600">{localError}</p>
          <Button
            onClick={() => setLocalError(null)}
            variant="outline"
            size="sm"
            className="mt-2"
          >
            Tentar novamente
          </Button>
        </div>
      </div>
    );
  }

  // Não mostrar para questões discursivas
  if (isDiscursive) {
    return null;
  }

  return (
    <div className="mb-6">
      {!isInitialLoading && !hasExistingCommentary && (
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleGenerateCommentary}
              disabled={isLoading}
              variant="hackYellow"
              size="hack"
              className="flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px] text-sm sm:text-base"
            >
              <Brain className="h-4 w-4" />
              {isLoading ? "Gerando análise..." : "Gerar análise"}
            </Button>
            <HelpDialog />
          </div>
        </div>
      )}

      {!isInitialLoading && hasExistingCommentary && !showCommentary && (
        <div className="mb-6">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleShowSavedCommentary}
              variant="hackGreen"
              size="hack"
              className="flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px] text-sm sm:text-base"
            >
              <Eye className="h-4 w-4" />
              Ver Análise da IA
            </Button>
            <HelpDialog />
          </div>
        </div>
      )}

      {!isInitialLoading && hasExistingCommentary && showCommentary && (
        <div className="space-y-4">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleHideCommentary}
              variant="outline"
              size="hack"
              className={`flex items-center justify-center gap-2 border-2 max-w-[240px] ${
                isDarkMode
                  ? 'border-gray-600 bg-gray-700 text-gray-200 hover:bg-gray-600 hover:text-white'
                  : 'border-black hover:bg-gray-50'
              }`}
            >
              <Eye className="h-4 w-4" />
              Ocultar Análise da IA
            </Button>
            <HelpDialog />
          </div>

          <Card className={`border-2 transition-colors duration-200 ${
            isDarkMode ? 'border-gray-600 bg-gray-800' : 'border-black bg-white'
          }`}>
            <CardHeader className={`pb-2 transition-colors duration-200 ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            }`}>
              <div className="flex items-center justify-between">
                <h3 className={`text-lg font-bold flex items-center gap-2 transition-colors duration-200 ${
                  isDarkMode ? 'text-gray-200' : 'text-gray-900'
                }`}>
                  <Brain className={`h-5 w-5 transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`} />
                  Análise da IA
                </h3>
              </div>
            </CardHeader>
            <CardContent className={`space-y-3 p-0 sm:p-4 transition-colors duration-200 ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            }`}>
              {activeCommentary?.alternativas?.map((alt: any, index: number) => (
                <div
                  key={index}
                  className={`p-3 sm:p-4 border-2 rounded-none sm:rounded-lg transition-all duration-200 transform hover:translate-y-[-2px] ${
                    alt.correta
                      ? isDarkMode
                        ? 'border-green-600 bg-green-900/20 hover:bg-green-900/30'
                        : 'border-hackathon-green bg-green-50/50 hover:bg-green-50'
                      : isDarkMode
                        ? 'border-red-600 bg-red-900/20 hover:bg-red-900/30'
                        : 'border-hackathon-red bg-red-50/50 hover:bg-red-50'
                  }`}
                >
                  <div className="block">
                    <h4 className={`font-bold mb-3 transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>
                      Alternativa {String.fromCharCode(65 + index)}{alt.correta ? ' (Correta)' : ''}
                    </h4>
                    <div className={`leading-relaxed transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      <div dangerouslySetInnerHTML={{ __html: alt.comentario }} />
                    </div>
                  </div>
                </div>
              ))}

              {activeCommentary?.comentario_final && (
                <div className={`rounded-lg p-4 border-2 transition-all duration-200 transform hover:translate-y-[-2px] ${
                  isDarkMode
                    ? 'border-gray-600 bg-gray-700 hover:bg-gray-600/80'
                    : 'border-gray-300 bg-gray-50 hover:bg-gray-100'
                }`}>
                  <h4 className={`font-bold mb-2 flex items-center gap-2 transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-900'
                  }`}>
                    <Brain className={`h-4 w-4 transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`} />
                    Comentário Final
                  </h4>
                  <div className={`leading-relaxed transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    <div dangerouslySetInnerHTML={{ __html: activeCommentary.comentario_final }} />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
