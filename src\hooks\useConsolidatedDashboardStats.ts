/**
 * 🎯 HOOK CONSOLIDADO PARA DASHBOARD
 * 
 * Elimina duplicações fazendo uma única query para user_answers e study_sessions
 * e fornecendo os dados para useTodayStats e useOptimizedStreakStats
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

/**
 * ✅ ARREDONDAMENTO INTELIGENTE DE TEMPO
 * Regra: até 1min30s = 1min, 1min31s+ = 2min
 */
const smartTimeRounding = (seconds: number): number => {
  const minutes = seconds / 60;
  const wholeMinutes = Math.floor(minutes);
  const remainingSeconds = seconds % 60;

  // Se tem menos de 30 segundos extras, arredonda para baixo
  // Se tem 30+ segundos extras, arredonda para cima
  return remainingSeconds < 30 ? wholeMinutes : wholeMinutes + 1;
};

interface ConsolidatedStats {
  // Para useTodayStats
  todayStats: {
    questionsAnswered: number;
    timeStudied: number; // em minutos
    flashcardsStudied: number; // cards estudados hoje
  };
  
  // Para useOptimizedStreakStats
  weekActivities: {
    answersThisWeek: any[];
    sessionsThisWeek: any[];
  };
  
  // Dados brutos para outros usos
  rawData: {
    allAnswers: any[];
    allSessions: any[];
  };
}

export const useConsolidatedDashboardStats = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['consolidated-dashboard-stats', user?.id],
    queryFn: async (): Promise<ConsolidatedStats> => {
  

      if (!user?.id) {
        return {
          todayStats: { questionsAnswered: 0, timeStudied: 0, flashcardsStudied: 0 },
          weekActivities: { answersThisWeek: [], sessionsThisWeek: [] },
          rawData: { allAnswers: [], allSessions: [] }
        };
      }

      // Calcular datas
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
      
      // Calcular início da semana (domingo)
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      endOfWeek.setHours(23, 59, 59, 999);



      // ✅ CORREÇÃO CRÍTICA: QUERY ÚNICA para user_answers (buscar toda a semana)
      const { data: answersData, error: answersError } = await supabase
        .from('user_answers')
        .select('id, time_spent, created_at')
        .eq('user_id', user.id)
        .gte('created_at', startOfWeek.toISOString()) // Buscar desde início da semana
        .lte('created_at', endOfWeek.toISOString()); // 🚨 CORREÇÃO: Buscar até final da semana, não só hoje!

      if (answersError) {
        console.error('❌ [useConsolidatedDashboardStats] Error fetching answers:', answersError);
        throw answersError;
      }

  

      // ✅ CORREÇÃO CRÍTICA: QUERY ÚNICA para study_sessions (buscar toda a semana)
      // 🚀 NOVA LÓGICA: Incluir TODAS as sessões (completas e em andamento) para contabilizar tempo parcial
      const { data: sessionsData, error: sessionsError } = await supabase
        .from('study_sessions')
        .select('total_time_spent, completed_at, started_at, status')
        .eq('user_id', user.id)
        .gte('started_at', startOfWeek.toISOString()) // Buscar desde início da semana
        .lte('started_at', endOfWeek.toISOString()) // Buscar até final da semana
        .gt('total_time_spent', 0); // Só sessões com tempo > 0

      if (sessionsError) {
        console.error('❌ [useConsolidatedDashboardStats] Error fetching sessions:', sessionsError);
        throw sessionsError;
      }

      // ✅ NOVA QUERY: Flashcards estudados hoje
      const { data: flashcardsData, error: flashcardsError } = await supabase
        .from('flashcards_session_cards')
        .select('id, created_at, session_id, flashcards_sessions!inner(user_id)')
        .eq('flashcards_sessions.user_id', user.id)
        .gte('created_at', startOfDay.toISOString())
        .lte('created_at', endOfDay.toISOString());

      if (flashcardsError) {
        console.error('❌ [useConsolidatedDashboardStats] Error fetching flashcards:', flashcardsError);
        throw flashcardsError;
      }

      // Processar dados para TODAY STATS
      const todayAnswers = answersData?.filter(answer => {
        const answerDate = new Date(answer.created_at);
        return answerDate >= startOfDay && answerDate <= endOfDay;
      }) || [];

      const todaySessions = sessionsData?.filter(session => {
        // 🚀 NOVA LÓGICA: Usar started_at para incluir sessões em andamento
        const sessionDate = new Date(session.started_at);
        return sessionDate >= startOfDay && sessionDate <= endOfDay;
      }) || [];

      const questionsAnswered = todayAnswers.length;

      // Calcular tempo estudado hoje
      let totalTimeMinutes = 0;

      // ✅ LIMPEZA: Log removido - dados encontrados

      if (todaySessions.length > 0) {
        // ✅ LIMPEZA: Log removido - sessões de hoje

        const totalTimeSeconds = todaySessions.reduce((sum, session) => {
          return sum + (session.total_time_spent || 0);
        }, 0);
        totalTimeMinutes = smartTimeRounding(totalTimeSeconds); // ✅ USAR ARREDONDAMENTO INTELIGENTE

        // ✅ LIMPEZA: Log removido - resultado de arredondamento
      } else {
        // ✅ LIMPEZA: Log removido - fallback para respostas

        // Fallback: usar tempo das respostas individuais
        const timeFromAnswers = todayAnswers.reduce((sum, answer) => {
          return sum + (answer.time_spent || 0);
        }, 0);
        totalTimeMinutes = smartTimeRounding(timeFromAnswers); // ✅ USAR ARREDONDAMENTO INTELIGENTE

        // ✅ LIMPEZA: Log removido - resultado das respostas
      }

      // Calcular flashcards estudados hoje
      const flashcardsStudied = flashcardsData?.length || 0;

      // Processar dados para WEEK ACTIVITIES
      const answersThisWeek = answersData?.map(answer => ({
        created_at: answer.created_at
      })) || [];

      const sessionsThisWeek = sessionsData?.map(session => ({
        // 🚀 NOVA LÓGICA: Usar started_at para incluir sessões em andamento
        completed_at: session.completed_at || session.started_at // Usar completed_at se existe, senão started_at
      })) || [];



      return {
        todayStats: {
          questionsAnswered,
          timeStudied: totalTimeMinutes,
          flashcardsStudied
        },
        weekActivities: {
          answersThisWeek,
          sessionsThisWeek
        },
        rawData: {
          allAnswers: answersData || [],
          allSessions: sessionsData || []
        }
      };
    },
    enabled: !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: 2
  });
};

/**
 * Hook otimizado para estatísticas de hoje
 * Usa dados do hook consolidado
 */
export const useOptimizedTodayStats = () => {
  const { data, isLoading, error } = useConsolidatedDashboardStats();

  return {
    questionsAnswered: data?.todayStats.questionsAnswered || 0,
    timeStudied: data?.todayStats.timeStudied || 0,
    flashcardsStudied: data?.todayStats.flashcardsStudied || 0,
    isLoading,
    error: error ? 'Erro ao carregar estatísticas' : null
  };
};

/**
 * Hook otimizado para atividades da semana
 * Usa dados do hook consolidado
 */
export const useOptimizedWeekActivities = () => {
  const { data, isLoading, error } = useConsolidatedDashboardStats();

  return {
    answersThisWeek: data?.weekActivities.answersThisWeek || [],
    sessionsThisWeek: data?.weekActivities.sessionsThisWeek || [],
    isLoading,
    error
  };
};
