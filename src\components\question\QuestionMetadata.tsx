
import React, { useState } from 'react';
import { cn } from "@/lib/utils";
import type { Question } from "@/types/question";
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface QuestionMetadataProps {
  question: Question;
  className?: string;
}

export const QuestionMetadata: React.FC<QuestionMetadataProps> = ({
  question,
  className
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const { isDarkMode } = useDarkMode();
  
  // Função auxiliar para formatar o tipo de questão para exibição
  const formatQuestionType = (type: string | undefined) => {
    if (!type) return '';
    
    // Mapeia os tipos de questão para formatos mais legíveis
    const typeMap: Record<string, string> = {
      'teorica-1': 'Teórica I',
      'teorica-2': 'Teórica II',
      'teorico-pratica': 'Teórico-Prática'
    };
    
    return typeMap[type] || type;
  }

  const toggleDetails = () => {
    setShowDetails(prev => !prev);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between items-center">
        <div className="flex flex-wrap items-center gap-2">
          {/* Sempre mostrar número da questão e tipo */}
          {question.question_number && (
            <div className="flex items-center gap-1">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-blue-900/50 text-blue-300'
                  : 'bg-primary/10 text-primary'
              }`}>
                #{question.question_number}
              </span>

              {question.question_type && (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border transition-colors duration-200 ${
                  isDarkMode
                    ? 'bg-blue-900/30 text-blue-300 border-blue-700'
                    : 'bg-blue-50 text-blue-700 border-blue-100'
                }`}>
                  {formatQuestionType(question.question_type)}
                </span>
              )}
            </div>
          )}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleDetails}
          className={`text-xs px-2 h-7 transition-colors duration-200 ${
            isDarkMode
              ? 'text-gray-300 hover:text-gray-100 hover:bg-gray-700'
              : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
          }`}
        >
          {showDetails ? (
            <span className="flex items-center gap-1">
              Ocultar detalhes <ChevronUp size={14} />
            </span>
          ) : (
            <span className="flex items-center gap-1">
              Mostrar detalhes <ChevronDown size={14} />
            </span>
          )}
        </Button>
      </div>
      
      {showDetails && (
        <div className={`flex flex-wrap gap-2 text-sm transition-colors duration-200 ${
          isDarkMode ? 'text-gray-400' : 'text-muted-foreground'
        }`}>
          {/* Especialidade */}
          {question.specialty && (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
              isDarkMode ? 'bg-blue-900/40 text-blue-300' : 'bg-blue-100 text-blue-800'
            }`}>
              {question.specialty.name}
            </span>
          )}

          {/* Tema */}
          {question.theme && (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
              isDarkMode ? 'bg-green-900/40 text-green-300' : 'bg-green-100 text-green-800'
            }`}>
              {question.theme.name}
            </span>
          )}

          {/* Foco */}
          {question.focus && question.focus.name !== question.theme?.name && (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
              isDarkMode ? 'bg-purple-900/40 text-purple-300' : 'bg-purple-100 text-purple-800'
            }`}>
              {question.focus.name}
            </span>
          )}

          {/* Origem/Local */}
          {question.location && (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
              isDarkMode ? 'bg-yellow-900/40 text-yellow-300' : 'bg-yellow-100 text-yellow-800'
            }`}>
              {question.location.name}
            </span>
          )}
          
          {/* Ano */}
          {question.year && (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
              isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-800'
            }`}>
              {question.year}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default QuestionMetadata;
