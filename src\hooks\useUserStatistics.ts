import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { UserStatistics } from '@/types/statistics';
import { useAuth } from '@/contexts/AuthContext';
import { calculateSessionStats, calculateImprovedStreakStats } from '@/utils/sessionTransformers';
import { useStaticStudyCategories } from '@/hooks/useStaticDataCache';

export const useUserStatistics = () => {
  const { user } = useAuth();
  const { data: staticCategories } = useStaticStudyCategories();

  return useQuery({
    queryKey: ['user-statistics', user?.id, !!staticCategories],
    queryFn: async (): Promise<UserStatistics> => {
      if (!user?.id) {
        console.error('❌ [useUserStatistics] Usuário não autenticado');
        throw new Error('User not authenticated');
      }

      // 🚀 CORREÇÃO: Incluir TODAS as sessões (completas e em andamento) para contabilizar tempo parcial
      const { data: sessionsData, error: sessionsError } = await supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', user.id)
        .gt('total_time_spent', 0) // Só sessões com tempo > 0
        .order('started_at', { ascending: false });

      if (sessionsError) {
        console.error('Error fetching sessions:', sessionsError);
        throw sessionsError;
      }

      // Get all user answers without using join relations
      const { data: answersData, error: answersError } = await supabase
        .from('user_answers')
        .select('id, is_correct, time_spent, created_at, question_id, year, specialty_id, theme_id, focus_id')
        .eq('user_id', user.id)
;

      if (answersError) {
        throw answersError;
      }







      // We'll use a Map to ensure we count each question only once, keeping the most recent answer
      const uniqueAnswersMap = new Map();

      // Sort answers by created_at in descending order (newest first)
      const sortedAnswers = [...(answersData || [])].sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      // Keep only the most recent answer for each question
      for (const answer of sortedAnswers) {
        if (!uniqueAnswersMap.has(answer.question_id)) {
          uniqueAnswersMap.set(answer.question_id, answer);
        }
      }

      // Get unique answers array
      const uniqueAnswers = Array.from(uniqueAnswersMap.values());



      // Calculate streak data - try improved method with fallback
      let currentStreak = 0;
      let maxStreak = 0;

      try {
        const improvedStats = await calculateImprovedStreakStats(user.id, supabase);
        currentStreak = improvedStats.currentStreak;
        maxStreak = improvedStats.maxStreak;
      } catch (error) {
        console.warn('⚠️ [useUserStatistics] Improved streak calculation failed, using legacy method:', error);
        const legacyStats = calculateSessionStats(sessionsData || []);
        currentStreak = legacyStats.currentStreak;
        maxStreak = legacyStats.maxStreak;
      }

      // Get week boundaries for weekly stats
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday
      startOfWeek.setHours(0, 0, 0, 0);

      const startOfPreviousWeek = new Date(startOfWeek);
      startOfPreviousWeek.setDate(startOfPreviousWeek.getDate() - 7);

      const endOfPreviousWeek = new Date(startOfWeek);
      endOfPreviousWeek.setMilliseconds(-1);

      // Calculate current and previous week statistics
      const currentWeekAnswers = uniqueAnswers.filter(answer =>
        new Date(answer.created_at) >= startOfWeek
      ) || [];

      const previousWeekAnswers = uniqueAnswers.filter(answer =>
        new Date(answer.created_at) >= startOfPreviousWeek &&
        new Date(answer.created_at) < startOfWeek
      ) || [];

      const currentWeekCorrect = currentWeekAnswers.filter(a => a.is_correct).length;
      const currentWeekIncorrect = currentWeekAnswers.filter(a => !a.is_correct).length;

      const previousWeekCorrect = previousWeekAnswers.filter(a => a.is_correct).length;
      const previousWeekIncorrect = previousWeekAnswers.filter(a => !a.is_correct).length;

      // Calculate statistics from unique answers only
      const totalQuestions = uniqueAnswers.length;
      const correctAnswers = uniqueAnswers.filter(a => a.is_correct).length;
      const incorrectAnswers = totalQuestions - correctAnswers;




      // ✅ Calcular tempo total incluindo sessões de estudo
      const totalTimeFromAnswers = uniqueAnswers.reduce((sum, answer) => {
        return sum + (answer.time_spent || 0);
      }, 0);

      // ✅ Calcular tempo total das sessões de estudo (total_time_spent já está em segundos)
      const totalTimeFromSessions = (sessionsData || []).reduce((sum, session) => {
        // APENAS usar total_time_spent quando é maior que 0 (dados válidos)
        if (session.total_time_spent && session.total_time_spent > 0) {
          return sum + session.total_time_spent; // ✅ CORRIGIDO: total_time_spent já está em segundos
        }
        // NÃO fazer fallback para timestamps - ignorar sessões sem tempo válido
        return sum;
      }, 0);

      // ✅ CORREÇÃO: Usar tempo das sessões para tempo total de estudo
      // Usar tempo das respostas apenas para cálculo de tempo médio por questão
      const totalStudyTime = totalTimeFromSessions; // Tempo total de estudo (inclui pausas)
      const totalTimeSpent = totalTimeFromAnswers;  // Tempo efetivo respondendo



      // ✅ CORRIGIDO: Calculate average response time usando tempo total das sessões
      // ✅ USAR TEMPO TOTAL DAS SESSÕES em vez de tempo individual das respostas
      const avgResponseTime = totalQuestions > 0
        ? totalTimeFromSessions / totalQuestions
        : 0;





      // Process answers by category
      const bySpecialty: Record<string, { id: string; name: string; correct: number; total: number }> = {};
      const byTheme: Record<string, { id: string; name: string; specialty_id: string; correct: number; total: number }> = {};
      const byFocus: Record<string, { id: string; name: string; theme_id: string; correct: number; total: number }> = {};
      const byYear: Record<string, { year: number; correct: number; total: number }> = {};

      // ✅ Usar dados estáticos em cache em vez de query separada
      if (!staticCategories) {
        throw new Error("Static categories not loaded");
      }

      // ✅ Criar mapeamento usando dados estáticos em cache
      const allCategories = [
        ...staticCategories.specialties,
        ...staticCategories.themes,
        ...staticCategories.focuses
      ];

      const categoryMap = allCategories.reduce((acc, cat) => {
        acc[cat.id] = cat;
        return acc;
      }, {} as Record<string, any>);

      // Process answers by category
      for (const answer of uniqueAnswers) {
        // Process by specialty
        if (answer.specialty_id) {
          const specialtyId = answer.specialty_id;
          const specialty = categoryMap[specialtyId];

          if (specialty) {
            if (!bySpecialty[specialtyId]) {
              bySpecialty[specialtyId] = {
                id: specialtyId,
                name: specialty.name,
                correct: 0,
                total: 0
              };
            }

            bySpecialty[specialtyId].total++;
            if (answer.is_correct) {
              bySpecialty[specialtyId].correct++;
            }
          }
        }

        // Process by theme
        if (answer.theme_id) {
          const themeId = answer.theme_id;
          const theme = categoryMap[themeId];

          if (theme) {
            if (!byTheme[themeId]) {
              byTheme[themeId] = {
                id: themeId,
                name: theme.name,
                specialty_id: theme.parent_id || '',
                correct: 0,
                total: 0
              };
            }

            byTheme[themeId].total++;
            if (answer.is_correct) {
              byTheme[themeId].correct++;
            }
          }
        }

        // Process by focus
        if (answer.focus_id) {
          const focusId = answer.focus_id;
          const focus = categoryMap[focusId];

          if (focus) {
            if (!byFocus[focusId]) {
              byFocus[focusId] = {
                id: focusId,
                name: focus.name,
                theme_id: focus.parent_id || '',
                correct: 0,
                total: 0
              };
            }

            byFocus[focusId].total++;
            if (answer.is_correct) {
              byFocus[focusId].correct++;
            }
          }
        }

        // Process by year
        if (answer.year) {
          const yearKey = answer.year.toString();

          if (!byYear[yearKey]) {
            byYear[yearKey] = {
              year: answer.year,
              correct: 0,
              total: 0
            };
          }

          byYear[yearKey].total++;
          if (answer.is_correct) {
            byYear[yearKey].correct++;
          }
        }
      }

      // Build main statistics object
      const stats: UserStatistics = {
        user_id: user.id,
        total_questions: totalQuestions, // Questões únicas
        total_answers: answersData?.length || 0, // ✅ Total de respostas (incluindo repetidas)
        correct_answers: correctAnswers,
        incorrect_answers: incorrectAnswers,
        avg_response_time: avgResponseTime,
        total_study_time: totalStudyTime, // ✅ Usar tempo das sessões para tempo total
        by_specialty: bySpecialty,
        by_theme: byTheme,
        by_focus: byFocus,
        by_year: byYear,
        by_institution: {},
        streak_days: currentStreak,
        max_streak: maxStreak,
        weekly_stats: {
          current_week: {
            correct: currentWeekCorrect,
            incorrect: currentWeekIncorrect,
            sessions: currentWeekAnswers.length > 0 ? 1 : 0
          },
          previous_week: {
            correct: previousWeekCorrect,
            incorrect: previousWeekIncorrect,
            sessions: previousWeekAnswers.length > 0 ? 1 : 0
          }
        }
      };

      return stats;
    },
    enabled: !!user?.id && !!staticCategories,
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 10 * 60 * 1000, // 10 minutos
  });
};
