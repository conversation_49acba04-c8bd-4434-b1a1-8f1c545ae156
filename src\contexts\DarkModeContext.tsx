import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

interface DarkModeContextType {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
  isStudyModeActive?: boolean;
  syncWithDatabase: (updateFunction: (enabled: boolean) => Promise<void>) => void;
}

const DarkModeContext = createContext<DarkModeContextType | undefined>(undefined);

export const useDarkMode = () => {
  const context = useContext(DarkModeContext);
  if (context === undefined) {
    throw new Error('useDarkMode must be used within a DarkModeProvider');
  }
  return context;
};

interface DarkModeProviderProps {
  children: React.ReactNode;
}

export const DarkModeProvider: React.FC<DarkModeProviderProps> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isStudyModeActive, setIsStudyModeActive] = useState(false);
  const [databaseSyncFunction, setDatabaseSyncFunction] = useState<((enabled: boolean) => Promise<void>) | null>(null);

  // Carregar preferência do localStorage (fallback)
  useEffect(() => {
    const savedMode = localStorage.getItem('darkMode');
    if (savedMode === 'true') {
      setIsDarkMode(true);
    }

    // Listener para sincronização com banco de dados
    const handleDatabaseSync = (event: CustomEvent) => {
      setIsDarkMode(event.detail.enabled);
    };

    window.addEventListener('darkModeFromDatabase', handleDatabaseSync as EventListener);
    return () => window.removeEventListener('darkModeFromDatabase', handleDatabaseSync as EventListener);
  }, []);

  // Escutar evento de desativação forçada do modo de estudo
  useEffect(() => {
    const handleForceDeactivate = () => {
      if (isStudyModeActive) {
        setIsStudyModeActive(false);

        // Limpar caches relacionados ao modo noturno
        try {
          // Limpar cache global do FloatingChatButton
          if ((window as any).globalFormatCache) {
            (window as any).globalFormatCache.clear();
          }

          // Disparar evento para outros componentes limparem seus caches
          window.dispatchEvent(new CustomEvent('clearDarkModeCache'));
        } catch (error) {
          console.warn('Erro ao limpar cache do modo noturno:', error);
        }
      }
    };

    window.addEventListener('forceDeactivateStudyMode', handleForceDeactivate);
    return () => window.removeEventListener('forceDeactivateStudyMode', handleForceDeactivate);
  }, [isStudyModeActive]);

  // Escutar mudanças no modo de estudo
  useEffect(() => {
    const handleStudyModeChange = (event: CustomEvent) => {
      setIsStudyModeActive(event.detail.active);
    };

    window.addEventListener('studyModeChange', handleStudyModeChange as EventListener);

    return () => {
      window.removeEventListener('studyModeChange', handleStudyModeChange as EventListener);
    };
  }, []);

  // Aplicar modo noturno apenas em sessões de estudo
  useEffect(() => {
    // Salvar no localStorage sempre que mudar
    localStorage.setItem('darkMode', isDarkMode.toString());

    // Só aplicar modo noturno se estiver em sessão de estudo
    const shouldApplyDarkMode = isDarkMode && isStudyModeActive;

    if (shouldApplyDarkMode) {
      document.documentElement.classList.add('dark-mode');
      document.body.classList.add('dark-mode');
    } else {
      document.documentElement.classList.remove('dark-mode');
      document.body.classList.remove('dark-mode');
    }
  }, [isDarkMode, isStudyModeActive]);

  const toggleDarkMode = async () => {
    const newValue = !isDarkMode;
    setIsDarkMode(newValue);

    // Salvar no banco de dados se a função estiver disponível
    if (databaseSyncFunction) {
      try {
        await databaseSyncFunction(newValue);
      } catch (error) {
        console.error('Erro ao salvar preferência de modo noturno:', error);
      }
    }
  };

  const syncWithDatabase = useCallback((updateFunction: (enabled: boolean) => Promise<void>) => {
    setDatabaseSyncFunction(() => updateFunction);
  }, []);

  const setStudyMode = (active: boolean) => {
    setIsStudyModeActive(active);
  };

  return (
    <DarkModeContext.Provider value={{
      isDarkMode: isDarkMode && isStudyModeActive, // Só retorna true se estiver em sessão de estudo
      toggleDarkMode,
      isStudyModeActive,
      syncWithDatabase
    }}>
      {children}
    </DarkModeContext.Provider>
  );
};
