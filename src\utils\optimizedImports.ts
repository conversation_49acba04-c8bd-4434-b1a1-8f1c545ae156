/**
 * Utilitários para imports otimizados e lazy loading
 * Reduz o bundle size importando apenas o necessário
 */

// ===== DATE-FNS OTIMIZADO =====
// Em vez de importar toda a biblioteca, importar apenas funções específicas
export { format } from 'date-fns/format';
export { startOfWeek } from 'date-fns/startOfWeek';
export { addDays } from 'date-fns/addDays';
export { isToday } from 'date-fns/isToday';
export { isSameDay } from 'date-fns/isSameDay';
export { differenceInDays } from 'date-fns/differenceInDays';
export { ptBR } from 'date-fns/locale/pt-BR';

// ===== LUCIDE ICONS OTIMIZADO =====
// Lazy loading de ícones para reduzir bundle inicial
export const lazyIcons = {
  ChevronRight: () => import('lucide-react/dist/esm/icons/chevron-right').then(m => m.ChevronRight),
  Sparkles: () => import('lucide-react/dist/esm/icons/sparkles').then(m => m.Sparkles),
  BookOpen: () => import('lucide-react/dist/esm/icons/book-open').then(m => m.BookOpen),
  Calendar: () => import('lucide-react/dist/esm/icons/calendar').then(m => m.Calendar),
  BrainCircuit: () => import('lucide-react/dist/esm/icons/brain-circuit').then(m => m.BrainCircuit),
  CheckCircle: () => import('lucide-react/dist/esm/icons/check-circle').then(m => m.CheckCircle),
  TrendingUp: () => import('lucide-react/dist/esm/icons/trending-up').then(m => m.TrendingUp),
  Zap: () => import('lucide-react/dist/esm/icons/zap').then(m => m.Zap),
  Trophy: () => import('lucide-react/dist/esm/icons/trophy').then(m => m.Trophy),
  Clock: () => import('lucide-react/dist/esm/icons/clock').then(m => m.Clock),
  Target: () => import('lucide-react/dist/esm/icons/target').then(m => m.Target),
  BarChart: () => import('lucide-react/dist/esm/icons/bar-chart').then(m => m.BarChart),
  ArrowUp: () => import('lucide-react/dist/esm/icons/arrow-up').then(m => m.ArrowUp),
  ArrowDown: () => import('lucide-react/dist/esm/icons/arrow-down').then(m => m.ArrowDown),
  Brain: () => import('lucide-react/dist/esm/icons/brain').then(m => m.Brain),
  Info: () => import('lucide-react/dist/esm/icons/info').then(m => m.Info),
  HelpCircle: () => import('lucide-react/dist/esm/icons/help-circle').then(m => m.HelpCircle),
  Flame: () => import('lucide-react/dist/esm/icons/flame').then(m => m.Flame),
  RefreshCw: () => import('lucide-react/dist/esm/icons/refresh-cw').then(m => m.RefreshCw),
  Menu: () => import('lucide-react/dist/esm/icons/menu').then(m => m.Menu),
  X: () => import('lucide-react/dist/esm/icons/x').then(m => m.X),
  Settings: () => import('lucide-react/dist/esm/icons/settings').then(m => m.Settings),
  User: () => import('lucide-react/dist/esm/icons/user').then(m => m.User),
  LogOut: () => import('lucide-react/dist/esm/icons/log-out').then(m => m.LogOut),
};

// ===== FRAMER MOTION OTIMIZADO =====
// Lazy loading de componentes de animação
export const lazyMotion = {
  motion: () => import('framer-motion').then(m => ({ motion: m.motion })),
  AnimatePresence: () => import('framer-motion').then(m => ({ AnimatePresence: m.AnimatePresence })),
  useAnimation: () => import('framer-motion').then(m => ({ useAnimation: m.useAnimation })),
  useInView: () => import('framer-motion').then(m => ({ useInView: m.useInView })),
};

// ===== RECHARTS OTIMIZADO =====
// Lazy loading de componentes de gráficos
export const lazyCharts = {
  LineChart: () => import('recharts').then(m => ({ LineChart: m.LineChart })),
  BarChart: () => import('recharts').then(m => ({ BarChart: m.BarChart })),
  PieChart: () => import('recharts').then(m => ({ PieChart: m.PieChart })),
  ResponsiveContainer: () => import('recharts').then(m => ({ ResponsiveContainer: m.ResponsiveContainer })),
  XAxis: () => import('recharts').then(m => ({ XAxis: m.XAxis })),
  YAxis: () => import('recharts').then(m => ({ YAxis: m.YAxis })),
  CartesianGrid: () => import('recharts').then(m => ({ CartesianGrid: m.CartesianGrid })),
  Tooltip: () => import('recharts').then(m => ({ Tooltip: m.Tooltip })),
  Legend: () => import('recharts').then(m => ({ Legend: m.Legend })),
  Line: () => import('recharts').then(m => ({ Line: m.Line })),
  Bar: () => import('recharts').then(m => ({ Bar: m.Bar })),
  Cell: () => import('recharts').then(m => ({ Cell: m.Cell })),
};

// ===== HOOK PARA LAZY LOADING DE COMPONENTES =====
import { useState, useEffect, useRef } from 'react';

export const useLazyComponent = <T>(
  importFn: () => Promise<{ [key: string]: T }>,
  componentName: string
) => {
  const [Component, setComponent] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const loadedRef = useRef(false);

  const loadComponent = async () => {
    if (loadedRef.current || loading) return;
    
    setLoading(true);
    setError(null);
    loadedRef.current = true;
    
    try {
      const module = await importFn();
      setComponent(module[componentName]);
    } catch (err) {
      setError(err as Error);
      loadedRef.current = false;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadComponent();
  }, []);

  return { Component, loading, error };
};

// ===== HOOK PARA LAZY LOADING DE ÍCONES =====
export const useLazyIcon = (iconName: keyof typeof lazyIcons) => {
  return useLazyComponent(lazyIcons[iconName], iconName);
};

// ===== HOOK PARA LAZY LOADING DE GRÁFICOS =====
export const useLazyChart = (chartName: keyof typeof lazyCharts) => {
  return useLazyComponent(lazyCharts[chartName], chartName);
};

// ===== PRELOAD DE COMPONENTES CRÍTICOS =====
export const preloadCriticalComponents = () => {
  // Preload componentes que são usados na primeira tela
  const criticalComponents = [
    lazyIcons.BookOpen,
    lazyIcons.Calendar,
    lazyIcons.TrendingUp,
    lazyMotion.motion,
  ];

  criticalComponents.forEach(loader => {
    loader().catch(() => {
      // Silently handle preload failures
    });
  });
};

// ===== UTILITÁRIO PARA BUNDLE ANALYSIS =====
export const analyzeBundleUsage = () => {
  // Apenas executar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') return;

  if (typeof window !== 'undefined' && 'performance' in window) {
    const entries = performance.getEntriesByType('navigation');
    const loadTime = entries[0]?.loadEventEnd - entries[0]?.loadEventStart;


  }
};

// ===== DYNAMIC IMPORTS PARA PÁGINAS =====
export const lazyPages = {
  Study: () => import('@/pages/study'),
  Results: () => import('@/pages/Results'),
  Progress: () => import('@/pages/Progress'),
  Questions: () => import('@/pages/Questions'),
  Flashcards: () => import('@/pages/Flashcards'),
  Settings: () => import('@/pages/Settings'),
  HowItWorks: () => import('@/pages/HowItWorks'),
  AdminDashboard: () => import('@/pages/admin/AdminDashboard'),
  FocusOptimization: () => import('@/pages/admin/FocusOptimization'),
  FocusConsolidation: () => import('@/pages/admin/FocusConsolidation'),
};

// ===== PRELOAD BASEADO EM ROTA =====
export const preloadRouteComponents = (currentRoute: string) => {
  const routePreloadMap: Record<string, (() => Promise<any>)[]> = {
    '/': [lazyPages.Study], // Na home, preload da página de estudos
    '/plataformadeestudos': [lazyPages.Results, lazyPages.Progress], // Na plataforma, preload de resultados
    '/questions': [lazyPages.Flashcards], // Em questões, preload de flashcards
  };

  const preloadFunctions = routePreloadMap[currentRoute];
  if (preloadFunctions) {
    preloadFunctions.forEach(preload => {
      preload().catch(err => console.warn('Failed to preload route component:', err));
    });
  }
};
