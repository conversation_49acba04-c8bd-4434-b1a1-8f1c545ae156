
import { useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useStudySession } from '@/hooks/useStudySession';
import { useFilterQuery } from './useFilterQuery';
import { ensureUserId } from '@/utils/ensureUserId';
import { useUserData } from '@/hooks/useUserData';
import { supabase } from '@/integrations/supabase/client';
import type { SelectedFilters } from '@/types/question';
import type { StudySessionRow } from '@/types/study-session';
import { useDomain } from '@/hooks/useDomain';

export const useSessionCreation = () => {
  const { toast } = useToast();
  const { createSession } = useStudySession();
  const { buildQuery } = useFilterQuery();
  const { user } = useUserData();
  const { domain } = useDomain();

  const handleCreateSession = useCallback(async (
    filters: SelectedFilters,
    title: string,
    preFilteredQuestions?: any[]
  ): Promise<StudySessionRow | null> => {
    const sessionId = Math.random().toString(36).substr(2, 9);
    console.log(`🚀 [useSessionCreation-${sessionId}] Iniciando criação de sessão:`, {
      title,
      hasPreFilteredQuestions: !!preFilteredQuestions,
      preFilteredCount: preFilteredQuestions?.length || 0,
      filters: {
        specialties: filters.specialties?.length || 0,
        themes: filters.themes?.length || 0,
        focuses: filters.focuses?.length || 0,
        locations: filters.locations?.length || 0,
        years: filters.years?.length || 0,
        question_types: filters.question_types?.length || 0,
        question_formats: filters.question_formats?.length || 0,
        excludeAnswered: filters.excludeAnswered
      },
      domain,
      timestamp: new Date().toISOString()
    });

    try {
      // If we have pre-filtered questions, use them directly
      if (preFilteredQuestions && preFilteredQuestions.length > 0) {
        console.log(`📋 [useSessionCreation-${sessionId}] Usando questões pré-filtradas:`, {
          count: preFilteredQuestions.length,
          firstQuestionId: preFilteredQuestions[0]?.id,
          questionIds: preFilteredQuestions.slice(0, 3).map(q => q.id)
        });

        const questionIds = preFilteredQuestions.map(q => q.id);
        const userId = await ensureUserId();

        console.log(`👤 [useSessionCreation-${sessionId}] UserId obtido:`, userId);

        const session = await createSession(userId, questionIds, title);

        console.log(`✅ [useSessionCreation-${sessionId}] Resultado createSession:`, {
          success: !!session?.id,
          sessionId: session?.id,
          questionCount: session?.total_questions
        });

        if (!session?.id) {
          console.error(`❌ [useSessionCreation-${sessionId}] Falha ao criar sessão - session inválida`);
          toast({
            title: "Erro ao criar sessão",
            description: "Não foi possível criar a sessão de estudos",
            variant: "destructive"
          });
          return null;
        }
        return session;
      }

      // Otherwise, fetch questions based on filters
      console.log(`🔍 [useSessionCreation-${sessionId}] Buscando questões com filtros...`);
      let questions, error;

      if (filters.excludeAnswered && user?.id) {
        console.log(`🚫 [useSessionCreation-${sessionId}] Excluindo questões respondidas...`);

        // Use function that excludes answered questions
        const rpcParams = {
          specialty_ids: filters.specialties || [],
          theme_ids: filters.themes || [],
          focus_ids: filters.focuses || [],
          location_ids: filters.locations || [],
          years: (filters.years || []).map(Number),
          question_types: filters.question_types || [],
          question_formats: filters.question_formats || [],
          page_number: 1,
          items_per_page: 1000, // Get all questions for session
          domain_filter: domain,
          user_id: user.id
        };

        console.log(`📡 [useSessionCreation-${sessionId}] Chamando get_filtered_questions_excluding_answered_secure:`, {
          rpcParams,
          userId: user.id
        });

        // 🔒 SEGURANÇA: Usar função segura que exclui questões respondidas
        const { data: rpcData, error: rpcError } = await supabase.rpc('get_filtered_questions_excluding_answered_secure', rpcParams);

        console.log(`📡 [useSessionCreation-${sessionId}] Resposta get_filtered_questions_excluding_answered_secure:`, {
          hasData: !!rpcData,
          questionsCount: rpcData?.questions?.length || 0,
          totalCount: rpcData?.total_count || 0,
          error: rpcError?.message || null,
          rpcData: rpcData ? { ...rpcData, questions: `[${rpcData.questions?.length || 0} questões]` } : null
        });

        if (rpcError) {
          error = rpcError;
        } else {
          questions = rpcData?.questions || [];
        }
      } else {
        console.log(`📋 [useSessionCreation-${sessionId}] Buscando questões sem excluir respondidas...`);

        // Use regular function
        const rpcParams = {
          specialty_ids: filters.specialties || [],
          theme_ids: filters.themes || [],
          focus_ids: filters.focuses || [],
          location_ids: filters.locations || [],
          years: (filters.years || []).map(Number),
          question_types: filters.question_types || [],
          question_formats: filters.question_formats || [],
          page_number: 1,
          items_per_page: 1000, // Get all questions for session
          domain_filter: domain
        };

        console.log(`📡 [useSessionCreation-${sessionId}] Chamando get_filtered_questions_secure:`, {
          rpcParams
        });

        // 🔒 SEGURANÇA: Usar função segura que não expõe respostas corretas
        const { data: rpcData, error: rpcError } = await supabase.rpc('get_filtered_questions_secure', rpcParams);

        console.log(`📡 [useSessionCreation-${sessionId}] Resposta get_filtered_questions_secure:`, {
          hasData: !!rpcData,
          questionsCount: rpcData?.questions?.length || 0,
          totalCount: rpcData?.total_count || 0,
          error: rpcError?.message || null,
          rpcData: rpcData ? { ...rpcData, questions: `[${rpcData.questions?.length || 0} questões]` } : null
        });

        if (rpcError) {
          error = rpcError;
        } else {
          questions = rpcData?.questions || [];
        }
      }

      if (error) {
        console.error(`❌ [useSessionCreation-${sessionId}] Erro na busca de questões:`, error);
        throw error;
      }

      console.log(`📊 [useSessionCreation-${sessionId}] Questões encontradas:`, {
        count: questions?.length || 0,
        firstQuestionId: questions?.[0]?.id,
        questionIds: questions?.slice(0, 3)?.map(q => q.id) || []
      });

      if (!questions?.length) {
        console.warn(`⚠️ [useSessionCreation-${sessionId}] Nenhuma questão encontrada com os filtros aplicados`);
        toast({
          title: "Nenhuma questão encontrada",
          description: "Tente outros filtros",
          variant: "destructive"
        });
        return null;
      }

      const userId = await ensureUserId();
      console.log(`👤 [useSessionCreation-${sessionId}] UserId obtido:`, userId);

      const questionIds = questions.map(q => q.id);
      console.log(`🎯 [useSessionCreation-${sessionId}] Criando sessão:`, {
        userId,
        questionCount: questionIds.length,
        title,
        questionIds: questionIds.slice(0, 3)
      });

      const session = await createSession(userId, questionIds, title);

      console.log(`✅ [useSessionCreation-${sessionId}] Resultado final createSession:`, {
        success: !!session?.id,
        sessionId: session?.id,
        questionCount: session?.total_questions,
        status: session?.status
      });

      if (!session?.id) {
        console.error(`❌ [useSessionCreation-${sessionId}] Falha ao criar sessão - session inválida`);
        toast({
          title: "Erro ao criar sessão",
          description: "Não foi possível criar a sessão de estudos",
          variant: "destructive"
        });
        return null;
      }

      return session;

    } catch (error: any) {
      console.error(`💥 [useSessionCreation-${sessionId}] Erro geral na criação da sessão:`, {
        error: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString()
      });
      toast({
        title: "Erro ao criar sessão",
        description: error.message,
        variant: "destructive"
      });
      return null;
    }
  }, [toast, createSession, user?.id, domain]);

  return { handleCreateSession };
};
