// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

import { getSupabaseAuthConfig } from '@/utils/domainConfig';

const SUPABASE_URL = "https://bxedpdmgvgatjdfxgxij.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo";

// Obter configurações de domínio
const authConfig = getSupabaseAuthConfig();

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    // Configurações de persistência otimizadas para máxima duração
    persistSession: true,
    autoRefreshToken: true,
    storageKey: 'studywise-auth-storage',
    detectSessionInUrl: true,

    // Storage persistente entre sessões do navegador
    storage: window.localStorage,

    // Configurações de segurança e refresh
    flowType: 'pkce',

    // Configurações de timeout otimizadas
    debug: false, // Desabilitar logs de debug
  },
  // Configurações de performance
  global: {
    headers: {},
  },
  // Configurações de rede otimizadas
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Supabase client configurado e pronto para uso