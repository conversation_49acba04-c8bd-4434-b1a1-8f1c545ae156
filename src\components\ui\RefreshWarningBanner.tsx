/**
 * ✅ BANNER DE AVISO SOBRE REFRESH MANUAL
 * 
 * Aparece na página principal após gerar cronograma
 */
import React from 'react';
import { RefreshCw, X } from 'lucide-react';
import { useRefreshWarning } from '@/contexts/RefreshWarningContext';
import { motion, AnimatePresence } from 'framer-motion';

export const RefreshWarningBanner: React.FC = () => {
  const { showWarning, hideWarning } = useRefreshWarning();

  if (!showWarning) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50, height: 0 }}
        animate={{ opacity: 1, y: 0, height: 'auto' }}
        exit={{ opacity: 0, y: -50, height: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6 shadow-sm"
      >
        <div className="flex items-start gap-3">
          <RefreshCw className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <div className="text-amber-800 text-sm">
              <strong>💡 Cronograma gerado com sucesso!</strong> Se os tópicos não aparecerem automaticamente, 
              pressione <kbd className="px-2 py-1 bg-amber-200 rounded text-xs font-mono mx-1">F5</kbd> para atualizar a página.
            </div>
          </div>
          <button
            onClick={hideWarning}
            className="text-amber-600 hover:text-amber-800 transition-colors"
            aria-label="Fechar aviso"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
