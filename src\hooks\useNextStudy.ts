import { useMemo, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@supabase/auth-helpers-react';
import { supabase } from '@/integrations/supabase/client';
import type { StudyTopic } from '@/types/study-schedule';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useLocation } from 'react-router-dom';
import { adjustToBrazilTimezone } from '@/utils/formatTime';

interface NextStudy {
  topic: StudyTopic | null;
  isLoading: boolean;
  hasStudies: boolean;
  completedCount: number;
  totalCount: number;
}

/**
 * Hook para obter o próximo estudo do usuário
 * Retorna o primeiro tópico não estudado do dia
 */
export const useNextStudy = (): NextStudy => {
  const user = useUser();
  const queryClient = useQueryClient();
  const location = useLocation();

  // ✅ OTIMIZADO: Usar React Query com cache agressivo para evitar requests desnecessárias
  const { data: weeklySchedule, isLoading } = useQuery({
    queryKey: ['study-schedule', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;





      const { data: schedules, error } = await supabase
        .from('study_schedules')
        .select(`
          id,
          week_start_date,
          week_number,
          user_id,
          study_schedule_items (
            id,
            day_of_week,
            focus_name,
            theme_name,
            specialty_name,
            focus_id,
            theme_id,
            specialty_id,
            study_status,
            difficulty,
            activity_description,
            start_time,
            duration,
            is_manual,
            revision_number,
            last_revision_date,
            next_revision_date
          )
        `)
        .eq('user_id', user.id)
        .order('week_start_date', { ascending: true });

      if (error) {
        console.error('❌ [useNextStudy] Erro ao buscar schedule:', error);
        throw error;
      }



      if (!schedules || schedules.length === 0) {
        return null;
      }

      // ✅ CORRIGIDO: Filtrar apenas a semana atual
      const recommendations = [];
      const weekDays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];

      // Determinar qual é a semana atual usando timezone do Brasil
      const today = adjustToBrazilTimezone(new Date());
      const currentWeekStart = new Date(today);
      currentWeekStart.setDate(today.getDate() - today.getDay()); // Domingo da semana atual
      currentWeekStart.setHours(0, 0, 0, 0);



      // Encontrar o schedule da semana atual
      const currentWeekSchedule = schedules.find(schedule => {
        const scheduleDate = new Date(schedule.week_start_date + 'T00:00:00');
        return scheduleDate.getTime() === currentWeekStart.getTime();
      });

      if (!currentWeekSchedule) {
        return { recommendations: [] };
      }

      // Criar estrutura de 7 dias da semana APENAS para a semana atual
      for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
        const dayName = weekDays[dayIndex];

        // Coletar tópicos APENAS do schedule da semana atual
        const dayTopics = currentWeekSchedule.study_schedule_items?.filter((item: any) => {
          const matches = item.day_of_week.toLowerCase() === dayName.toLowerCase();
          return matches;
        }) || [];


        recommendations.push({
          day: dayName,
          topics: dayTopics.map((item: any) => ({
            id: item.id,
            focus: item.focus_name,
            theme: item.theme_name,
            specialty: item.specialty_name,
            focusId: item.focus_id,
            themeId: item.theme_id,
            specialtyId: item.specialty_id,
            study_status: item.study_status,
            isStudied: item.study_status === 'completed',
            difficulty: item.difficulty,
            activity: item.activity_description,
            startTime: item.start_time,
            duration: item.duration,
            is_manual: item.is_manual || false,
            revision_number: item.revision_number || 0,
            last_revision_date: item.last_revision_date,
            next_revision_date: item.next_revision_date
          }))
        });
      }

      return { recommendations };
    },
    enabled: !!user?.id,
    staleTime: 30 * 1000, // ✅ REDUZIDO: 30 segundos para atualizar mais rápido
    gcTime: 5 * 60 * 1000, // ✅ AJUSTADO: 5 minutos
    refetchOnWindowFocus: true, // ✅ ATIVADO: Refetch ao focar na aba para garantir atualização
    refetchOnMount: true // ✅ ATIVADO: Refetch ao montar para garantir dados frescos
  });

  // ✅ NOVO: Listener seletivo para mudanças importantes no cronograma
  useEffect(() => {
    if (!user?.id) return;

    // Listener apenas para mudanças em study_schedule_items (novos tópicos)
    const channel = supabase
      .channel('schedule-items-changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT', // ✅ Só novos tópicos
          schema: 'public',
          table: 'study_schedule_items'
        },
        () => {
          queryClient.invalidateQueries({ queryKey: ['study-schedule', user.id] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id, queryClient]);

  // ✅ NOVO: Invalidar cache quando navega para a home para garantir dados frescos
  useEffect(() => {
    if (location.pathname === '/' && user?.id) {
      queryClient.invalidateQueries({ queryKey: ['study-schedule', user.id] });
      // Forçar refetch imediato
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['study-schedule', user.id] });
      }, 100);
    }
  }, [location.pathname, user?.id, queryClient]);

  const nextStudy = useMemo(() => {
    if (isLoading || !weeklySchedule) {
      return {
        topic: null,
        isLoading,
        hasStudies: false,
        completedCount: 0,
        totalCount: 0
      };
    }

    // ✅ USAR EXATAMENTE A MESMA LÓGICA DO TODAYSTUDIES COM TIMEZONE DO BRASIL
    const currentDay = format(adjustToBrazilTimezone(new Date()), 'EEEE', { locale: ptBR });



    const todayStudies = weeklySchedule.recommendations?.find(day => {
      const normalizedDayName = day.day.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");
      const normalizedCurrentDay = currentDay.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");



      return normalizedDayName === normalizedCurrentDay;
    });



    const todayTopics = todayStudies?.topics || [];

    if (todayTopics.length === 0) {
      return {
        topic: null,
        isLoading: false,
        hasStudies: false,
        completedCount: 0,
        totalCount: 0
      };
    }

    // Encontrar o primeiro tópico não estudado
    const nextTopic = todayTopics.find(topic =>
      topic.study_status !== 'completed' && !topic.isStudied
    );

    // Contar estudos concluídos
    const completedCount = todayTopics.filter(topic =>
      topic.study_status === 'completed' || topic.isStudied
    ).length;



    const result = {
      topic: nextTopic || null,
      isLoading: false,
      hasStudies: true,
      completedCount,
      totalCount: todayTopics.length
    };

    return result;
  }, [weeklySchedule, isLoading]);

  return nextStudy;
};

/**
 * Função helper para obter o título do tópico
 */
export const getTopicTitle = (topic: StudyTopic): string => {
  if (topic.focus && topic.focus.trim() !== '') {
    return topic.focus;
  }
  
  if (topic.theme && topic.theme.trim() !== '') {
    return topic.theme;
  }
  
  if (topic.specialty && topic.specialty.trim() !== '') {
    return topic.specialty;
  }
  
  return 'Tópico de Estudo';
};

/**
 * Função helper para obter a cor da temperatura
 */
export const getTemperatureColor = (temperature?: string): string => {
  switch (temperature?.toLowerCase()) {
    case 'vulcanico':
    case 'vulcânico':
      return 'bg-purple-100 text-purple-700 border-purple-200';
    case 'quente':
      return 'bg-red-100 text-red-700 border-red-200';
    case 'morno':
      return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'frio':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};
