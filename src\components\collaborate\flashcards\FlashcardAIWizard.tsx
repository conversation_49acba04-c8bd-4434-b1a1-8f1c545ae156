
import { Card } from "@/components/ui/card";
import { FlashcardAIWizardFilters } from "./FlashcardAIWizardFilters";
import { FlashcardAIWizardCardList } from "./FlashcardAIWizardCardList";
import { useFlashcardWizardState } from "./hooks/useFlashcardWizardState";
import { useFlashcardGeneration } from "./hooks/useFlashcardGeneration";
import { useFlashcardImport } from "./hooks/useFlashcardImport";
import { toast } from "sonner";

const FlashcardAIWizard = () => {
  const state = useFlashcardWizardState();
  const { suggested, loading, generateFlashcards, setSuggested } = useFlashcardGeneration();
  const { importFlashcard, importAllFlashcards, resetImportState, ...importState } = useFlashcardImport();

  const handleGenerate = () => {
    // Reset import state when generating new flashcards
    resetImportState();

    generateFlashcards(
      state.specialty,
      state.theme,
      state.focus,
      state.extrafocus,
      state.quantity,
      state.fcType
    );
  };

  const handleEdit = (index: number) => {
    state.setEditingIndex(index);
    state.setEditingFront(suggested[index].front);
    state.setEditingBack(suggested[index].back);
  };

  const handleSaveEdit = () => {
    if (state.editingIndex === null) return;
    if (!state.editingFront.trim() || !state.editingBack.trim()) {
      toast.error("Preencha ambos os lados do flashcard.");
      return;
    }
    const updated = suggested.map((fc, idx) =>
      idx === state.editingIndex 
        ? { front: state.editingFront, back: state.editingBack } 
        : fc
    );
    setSuggested(updated);
    state.setEditingIndex(null);
    toast.success("Flashcard atualizado!");
  };

  const handleImport = async (index: number) => {
    const cardToImport = suggested[index];
    if (!cardToImport) return;

    // Map wizard types to database types
    const mapFlashcardType = (fcType: string) => {
      switch (fcType) {
        case 'cloze': return 'cloze';
        case 'vf': return 'vf';
        case 'multipla': return 'multipla';
        case 'qa': return 'qa';
        default: return 'qa';
      }
    };

    await importFlashcard(
      cardToImport,
      index,
      state.specialty,
      state.theme,
      state.focus,
      state.extrafocus,
      mapFlashcardType(state.fcType) // Use the type selected by user
    );
  };

  const handleImportAll = async () => {
    // Map wizard types to database types
    const mapFlashcardType = (fcType: string) => {
      switch (fcType) {
        case 'cloze': return 'cloze';
        case 'vf': return 'vf';
        case 'multipla': return 'multipla';
        case 'qa': return 'qa';
        default: return 'qa';
      }
    };

    await importAllFlashcards(
      suggested,
      state.specialty,
      state.theme,
      state.focus,
      state.extrafocus,
      mapFlashcardType(state.fcType) // Use the type selected by user
    );
  };

  return (
    <Card className="p-6 space-y-8">
      <FlashcardAIWizardFilters
        {...state}
        loading={loading}
        onGenerate={handleGenerate}
      />

      {suggested.length > 0 && (
        <FlashcardAIWizardCardList
          suggested={suggested}
          editingIndex={state.editingIndex}
          editingFront={state.editingFront}
          editingBack={state.editingBack}
          importedIndexes={importState.importedIndexes}
          importingIndex={importState.importingIndex}
          importingAll={importState.importingAll}
          onEdit={handleEdit}
          onEditFront={state.setEditingFront}
          onEditBack={state.setEditingBack}
          onSaveEdit={handleSaveEdit}
          onCancelEdit={() => state.setEditingIndex(null)}
          onImport={handleImport}
          onImportAll={handleImportAll}
        />
      )}
    </Card>
  );
};

export default FlashcardAIWizard;
