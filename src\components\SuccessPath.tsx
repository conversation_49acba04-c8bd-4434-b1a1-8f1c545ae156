
import { motion } from "framer-motion";
import { BookOpen, PenSquare, BarChart3, TrendingUp, GraduationCap } from "lucide-react";
import { useDarkMode } from "@/contexts/DarkModeContext";

const steps = [
  {
    icon: BookOpen,
    title: "Estude",
    description: "Acesse conteúdo de qualidade",
    bgColor: "bg-blue-500",
    textColor: "text-blue-600",
    lightBg: "bg-blue-50"
  },
  {
    icon: PenSquare,
    title: "Pratique",
    description: "Resolva questões específicas",
    bgColor: "bg-green-500",
    textColor: "text-green-600",
    lightBg: "bg-green-50"
  },
  {
    icon: BarChart3,
    title: "Avalie",
    description: "Identifique seus pontos fracos",
    bgColor: "bg-amber-500",
    textColor: "text-amber-600",
    lightBg: "bg-amber-50"
  },
  {
    icon: TrendingUp,
    title: "Aprimore",
    description: "Foque nas suas deficiências",
    bgColor: "bg-purple-500",
    textColor: "text-purple-600",
    lightBg: "bg-purple-50"
  },
  {
    icon: GraduationCap,
    title: "Aprove",
    description: "Conquiste sua residência",
    bgColor: "bg-rose-500",
    textColor: "text-rose-600",
    lightBg: "bg-rose-50"
  }
];

const SuccessPath = () => {
  const { isDarkMode } = useDarkMode();

  return (
    <div className="py-3 px-4 my-4">
      <div className={`max-w-6xl mx-auto rounded-xl border-2 shadow-card-sm p-3 transition-colors duration-200 ${
        isDarkMode
          ? 'bg-gray-800 border-gray-600'
          : 'bg-white border-black'
      }`}>
        <div className="text-center mb-2">
          <h2 className={`text-base font-bold transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>Caminho para o Sucesso</h2>
          <p className={`text-[10px] transition-colors duration-200 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>Siga estes passos para conquistar sua aprovação</p>
        </div>
        
        <div className="relative">
          {/* Path line with gradient */}
          <div className="absolute top-1/2 left-0 w-full h-1 bg-gradient-to-r from-blue-300 via-purple-300 to-rose-300 -translate-y-1/2 z-0 rounded-full" />
          
          <div className="flex justify-between relative z-10">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 5 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col items-center text-center"
              >
                <div className="relative">
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className={`w-9 h-9 ${step.bgColor} border-2 border-black rounded-full flex items-center justify-center mb-1 shadow-button`}
                  >
                    <step.icon size={14} className="text-white" />
                  </motion.div>
                  
                  {/* Direction arrow for all except last */}
                  {index < steps.length - 1 && (
                    <div className="absolute top-1/2 left-full -translate-y-1/2 w-[120%] flex items-center justify-center">
                      <motion.div 
                        initial={{ width: 0 }}
                        animate={{ width: '70%' }}
                        transition={{ delay: index * 0.1 + 0.3, duration: 0.3 }}
                        className={`h-0.5 ${step.bgColor} rounded-full`}
                      />
                      <motion.div 
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: index * 0.1 + 0.6 }}
                        className="w-2 h-2 rotate-45 border-t-2 border-r-2 border-black"
                      />
                    </div>
                  )}
                </div>
                
                {/* Text container with light background */}
                <div className={`${step.lightBg} px-1 py-0.5 rounded-md border border-gray-200 mt-1`}>
                  <h3 className={`font-bold text-[10px] ${step.textColor}`}>{step.title}</h3>
                  <p className="text-[8px] text-gray-600 max-w-[70px]">{step.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessPath;
