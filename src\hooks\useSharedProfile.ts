import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';


interface SharedProfile {
  id: string;
  full_name: string;
  avatar_url: string | null;
  formation_area: string;
  graduation_year: string;
  is_student: boolean;
  preparation_type: string | null;
  specialty: string | null;
  is_admin: boolean;
  premium: boolean;
  premium_requested: boolean;
  premium_requested_history: boolean;
  created_at: string;
}

/**
 * Hook compartilhado para dados do perfil
 * Evita múltiplas chamadas para a mesma query
 */
export const useSharedProfile = () => {
  const { user, loading: authLoading } = useAuth();



  return useQuery({
    queryKey: ['shared-profile', user?.id],
    queryFn: async (): Promise<SharedProfile | null> => {
      if (!user?.id) {
        return null;
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        // 🔍 LOG: Erro na query
        logQuery(['shared-profile', user.id], 'useSharedProfile', 'ERROR', {
          error: error.message,
          errorCode: error.code,
          userId: user.id
        });

        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      // 🔍 LOG: Sucesso na query
      logQuery(['shared-profile', user.id], 'useSharedProfile', 'SUCCESS', {
        userId: user.id,
        hasData: !!data,
        dataKeys: data ? Object.keys(data) : []
      });

      return data as SharedProfile;
    },
    enabled: !!user?.id && !authLoading,
    staleTime: 15 * 60 * 1000, // 15 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 2,
  });
};
