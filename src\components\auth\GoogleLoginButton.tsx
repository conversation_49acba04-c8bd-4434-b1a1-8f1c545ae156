import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { useFeedbackDialog } from '@/components/ui/feedback-dialog';
import { Loader2 } from 'lucide-react';

interface GoogleLoginButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  disabled?: boolean;
}

const GoogleIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="mr-2"
  >
    <path
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      fill="#4285F4"
    />
    <path
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      fill="#34A853"
    />
    <path
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      fill="#FBBC05"
    />
    <path
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      fill="#EA4335"
    />
  </svg>
);

export const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({
  onSuccess,
  onError,
  variant = 'outline',
  size = 'default',
  className = '',
  disabled = false,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showRedirectDialog, setShowRedirectDialog] = useState(false);
  const { showFeedback } = useFeedbackDialog();

  const getRedirectUrl = () => {
    const currentUrl = window.location.origin;
    
    // Para desenvolvimento (localhost:8080 ou localhost:5173)
    if (currentUrl.includes('localhost')) {
      return `${currentUrl}/auth/callback`;
    }
    
    // Para produção (medevo.com.br)
    return 'https://medevo.com.br/auth/callback';
  };

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      setShowRedirectDialog(true);

      // Aguardar um pouco para mostrar o dialog
      await new Promise(resolve => setTimeout(resolve, 800));

      const redirectTo = getRedirectUrl();

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        throw error;
      }

      // O redirecionamento será automático, então não precisamos fazer nada aqui
      // A função onSuccess será chamada na página de callback

    } catch (error: any) {
      console.error('Erro no login com Google:', error);

      const errorMessage = error.message || 'Erro ao fazer login com Google';

      setShowRedirectDialog(false);

      showFeedback(
        'error',
        'Erro no Login',
        errorMessage
      );

      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        type="button"
        variant={variant}
        size={size}
        className={`w-full ${className}`}
        onClick={handleGoogleLogin}
        disabled={disabled || isLoading}
      >
        {isLoading ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <GoogleIcon />
        )}
        {isLoading ? 'Conectando...' : 'Continuar com Google'}
      </Button>

      {/* Dialog de redirecionamento */}
      <Dialog open={showRedirectDialog} onOpenChange={() => {}}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
              Redirecionando para o Google
            </DialogTitle>
            <DialogDescription className="text-center py-4">
              Você será redirecionado para fazer login com sua conta Google.
              <br />
              <span className="text-sm text-gray-500 mt-2 block">
                Aguarde um momento...
              </span>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default GoogleLoginButton;
