import { useEffect, useState, useRef, useCallback, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Brain, Eye, HelpCircle, Flag } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useAICommentary } from "@/hooks/useAICommentary";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Question, AICommentaryResponse } from "@/types/question";
import { supabase } from "@/integrations/supabase/client";

import { CheckCircle, XCircle } from "lucide-react";
import { QuestionAICommentaryFeedback } from './QuestionAICommentaryFeedback';
import { useDarkMode } from '@/contexts/DarkModeContext';

// 🔧 Função para limpar JSON mal formatado que foi salvo incorretamente
const cleanMalformedJson = (text: string): string => {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Verificar se o texto contém JSON mal formatado
  if (text.includes('```json') || (text.includes('"alternativas"') && text.includes('"comentario_final"'))) {
    try {
      // Tentar extrair o JSON do texto
      let jsonText = text;

      // Remover marcadores de código markdown
      jsonText = jsonText.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Remover tags HTML se existirem
      jsonText = jsonText.replace(/<[^>]*>/g, '');

      // Tentar parsear como JSON
      const parsed = JSON.parse(jsonText);

      // Se conseguiu parsear e tem a estrutura esperada, extrair o comentário final
      if (parsed.comentario_final && typeof parsed.comentario_final === 'string') {
        return parsed.comentario_final;
      }

      // Se não tem comentário final, tentar extrair de alternativas
      if (parsed.alternativas && Array.isArray(parsed.alternativas)) {
        const comments = parsed.alternativas
          .map((alt: any) => alt.comentario)
          .filter(Boolean)
          .join('<br><br>');
        return comments || 'Análise não disponível.';
      }

    } catch (e) {
      // Fallback: tentar extrair texto útil usando regex
      const cleanText = text
        .replace(/```json/g, '')
        .replace(/```/g, '')
        .replace(/<[^>]*>/g, '')
        .replace(/\{[\s\S]*"comentario_final":\s*"([^"]*)"[\s\S]*\}/g, '$1')
        .trim();

      return cleanText || 'Análise não disponível.';
    }
  }

  return text;
};

// 🎨 Função para melhorar a formatação do comentário final
const formatFinalCommentary = (text: string): string => {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Primeiro, limpar JSON mal formatado se necessário
  let cleanText = cleanMalformedJson(text);

  // Melhorar formatação de listas numeradas - apenas uma quebra de linha
  cleanText = cleanText.replace(/(\d+\.\s*<strong>)/g, '<br>$1');

  // Adicionar quebras de linha antes de palavras-chave organizacionais
  cleanText = cleanText.replace(/(Primeiro,|Segundo,|Terceiro,)/g, '<br>$1');

  // Adicionar quebras de linha antes de traços de lista
  cleanText = cleanText.replace(/(\s-\s<strong>)/g, '<br>$1');

  // Adicionar quebra de linha antes de frases importantes
  cleanText = cleanText.replace(/(Segura essa dica que vale ouro:|Presta atenção nesse detalhe!)/g, '<br>$1');

  // Adicionar quebras de linha antes de "Além disso" e conectores similares
  cleanText = cleanText.replace(/(Além disso,|Portanto,|No entanto,)/g, '<br>$1');

  // Remover quebras de linha no início
  cleanText = cleanText.replace(/^(<br>)+/, '');

  return cleanText;
};



// 🛡️ COMPONENTE ESTÁVEL: Definido fora para evitar re-criação
const HelpDialog = () => {
  const { isDarkMode } = useDarkMode();

  return (
  <Dialog>
    <DialogTrigger asChild>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
      >
        <HelpCircle className="h-4 w-4 text-gray-500" />
      </Button>
    </DialogTrigger>
    <DialogContent className={`max-w-[90vw] max-h-[80vh] w-full sm:max-w-md rounded-lg overflow-y-auto transition-colors duration-200 ${
      isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
    }`} style={{ background: isDarkMode ? '#1f2937' : '#ffffff' }}>
      <DialogHeader>
        <DialogTitle className={`flex items-center gap-2 transition-colors duration-200 ${
          isDarkMode ? 'text-gray-200' : 'text-gray-900'
        }`}>
          <Brain className={`h-5 w-5 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-300' : 'text-gray-700'
          }`} />
          Como funciona a Análise da IA
        </DialogTitle>
      </DialogHeader>
      <div className="space-y-4 text-sm">
        <div>
          <h4 className={`font-semibold mb-2 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>📝 Gerar Análise vs Ver Análise</h4>
          <p className={`leading-relaxed transition-colors duration-200 ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            <strong>"Gerar Análise"</strong> aparece quando a questão ainda não possui uma análise da IA.
            Ao clicar, nossa IA criará uma explicação detalhada para você.
          </p>
          <p className={`leading-relaxed mt-2 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            <strong>"Ver Análise"</strong> aparece quando a questão já possui uma análise pronta.
            Isso significa que outro usuário já gerou a análise anteriormente.
          </p>
        </div>

        <div>
          <h4 className={`font-semibold mb-2 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>🤝 Análise Compartilhada</h4>
          <p className={`leading-relaxed transition-colors duration-200 ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Quando você gera uma análise, ela fica disponível para todos os outros usuários.
            Isso economiza tempo e garante que todos tenham acesso às mesmas explicações de qualidade.
          </p>
        </div>

        <div>
          <h4 className={`font-semibold mb-2 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>🔄 Melhoria Contínua</h4>
          <p className={`leading-relaxed transition-colors duration-200 ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            As análises são constantemente aprimoradas com base no feedback dos usuários.
            Se você encontrar algo incorreto ou pouco relevante, pode nos ajudar!
          </p>
        </div>

        <div className={`p-3 rounded-lg border transition-colors duration-200 ${
          isDarkMode
            ? 'bg-blue-900/20 border-blue-600'
            : 'bg-blue-50 border-blue-200'
        }`}>
          <h4 className={`font-semibold mb-2 flex items-center gap-2 transition-colors duration-200 ${
            isDarkMode ? 'text-blue-300' : 'text-blue-800'
          }`}>
            <Flag className="h-4 w-4" />
            Como reportar problemas
          </h4>
          <p className={`text-sm leading-relaxed transition-colors duration-200 ${
            isDarkMode ? 'text-blue-200' : 'text-blue-700'
          }`}>
            Se uma análise não estiver correta ou relevante, clique no ícone de bandeira
            <Flag className="h-3 w-3 inline mx-1" /> no canto superior direito da análise
            para deixar seu feedback. Isso nos ajuda a melhorar continuamente.
          </p>
        </div>
      </div>
    </DialogContent>
  </Dialog>
  );
};

interface QuestionAICommentaryProps {
  question: Question;
  onCommentaryGenerated: (commentary: AICommentaryResponse) => void;
  existingCommentary: AICommentaryResponse | null;
  sessionId: string;
  onPauseTimer?: () => void;
}

export const QuestionAICommentary = ({
  question,
  onCommentaryGenerated,
  existingCommentary,
  sessionId,
  onPauseTimer
}: QuestionAICommentaryProps) => {
  // 🚀 HOOKS PRIMEIRO - SEMPRE na mesma ordem
  const { generateCommentary, isLoading, error, resetCommentary } = useAICommentary();
  const { isDarkMode } = useDarkMode();
  const [savedCommentary, setSavedCommentary] = useState<AICommentaryResponse | null>(null);
  const currentQuestionRef = useRef<string>(question.id);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [showCommentary, setShowCommentary] = useState(false);
  const [currentLoadingMessage, setCurrentLoadingMessage] = useState(0);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [localError, setLocalError] = useState<string | null>(null);

  // 🛡️ DEBOUNCE: Evitar múltiplas chamadas de onCommentaryGenerated
  const lastCallbackCallRef = useRef<string>('');
  const callbackTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const hasValidAICommentary = useCallback((commentary: any): boolean => {
    if (!commentary) return false;
    if (typeof commentary !== 'object') return false;

    if (!commentary.alternativas || !Array.isArray(commentary.alternativas)) return false;
    if (commentary.alternativas.length === 0) return false;

    const firstAlt = commentary.alternativas[0];
    return typeof firstAlt === 'object' && 'texto' in firstAlt && 'correta' in firstAlt;
  }, []);

  // 🛡️ FUNÇÃO DEBOUNCED para evitar múltiplas chamadas
  const debouncedOnCommentaryGenerated = useCallback((commentary: AICommentaryResponse) => {
    const commentaryHash = JSON.stringify(commentary);
    const callKey = `${question.id}-${commentaryHash}`;

    // Se já chamamos com os mesmos dados, ignorar
    if (lastCallbackCallRef.current === callKey) {
      return;
    }

    // Cancelar timeout anterior se existir
    if (callbackTimeoutRef.current) {
      clearTimeout(callbackTimeoutRef.current);
    }

    // Agendar chamada com debounce de 50ms
    callbackTimeoutRef.current = setTimeout(() => {
      lastCallbackCallRef.current = callKey;
      onCommentaryGenerated(commentary);
    }, 50);
  }, [question.id, onCommentaryGenerated]);

  const loadingMessages = [
    "Analisando questão médica...",
    "Consultando literatura científica...",
    "Identificando conceitos-chave...",
    "Elaborando explicação detalhada...",
    "Verificando evidências clínicas...",
  ];

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isLoading) {
      interval = setInterval(() => {
        setCurrentLoadingMessage((prev) => (prev + 1) % loadingMessages.length);
      }, 3000);
    }
    return () => clearInterval(interval);
  }, [isLoading, loadingMessages.length]);

  // 🧹 CLEANUP: Limpar timeout ao desmontar componente
  useEffect(() => {
    return () => {
      if (callbackTimeoutRef.current) {
        clearTimeout(callbackTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (currentQuestionRef.current !== question.id) {
      setIsTransitioning(true);
      setIsInitialLoading(true);
      // 🧹 LIMPAR savedCommentary ao mudar questão para evitar vazamento
      setSavedCommentary(null);
      setShowCommentary(false);
      currentQuestionRef.current = question.id;

      setTimeout(() => {
        setIsTransitioning(false);
      }, 50);
    }
  }, [question.id]);

  useEffect(() => {
    const checkQuestionCommentary = async () => {
      if (isTransitioning) return;

      const questionAiCommentary = question.ai_commentary || null;
      const hasQuestionCommentary = hasValidAICommentary(questionAiCommentary);
      const hasExistingCommentary = hasValidAICommentary(existingCommentary);

      // 🔍 NOVA VERIFICAÇÃO: Buscar ai_commentary da função get_question_feedback_info
      let feedbackCommentary = null;
      let hasFeedbackCommentary = false;

      try {
        const { data, error } = await supabase.rpc('get_question_feedback_info', {
          question_id: question.id
        });

        if (!error && data?.ai_commentary) {
          feedbackCommentary = data.ai_commentary;
          hasFeedbackCommentary = hasValidAICommentary(feedbackCommentary);
        }
      } catch (error) {
        // Silenciar erro
      }

      // PRIORIDADE: feedback commentary > question commentary > existing commentary
      if (hasFeedbackCommentary) {
        const typedCommentary = feedbackCommentary as AICommentaryResponse;

        // 🛡️ EVITAR LOOP: só chamar onCommentaryGenerated se savedCommentary mudou
        if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(typedCommentary)) {
          setSavedCommentary(typedCommentary);
          debouncedOnCommentaryGenerated(typedCommentary);
        }
      } else if (hasQuestionCommentary) {
        const typedCommentary = questionAiCommentary as AICommentaryResponse;

        // 🛡️ EVITAR LOOP: só chamar onCommentaryGenerated se savedCommentary mudou
        if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(typedCommentary)) {
          setSavedCommentary(typedCommentary);
          debouncedOnCommentaryGenerated(typedCommentary);
        }
      } else if (hasExistingCommentary) {
        const typedCommentary = existingCommentary as AICommentaryResponse;

        // 🛡️ EVITAR LOOP: só chamar onCommentaryGenerated se savedCommentary mudou
        if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(typedCommentary)) {
          setSavedCommentary(typedCommentary);
          debouncedOnCommentaryGenerated(typedCommentary);
        }
      } else {
        // Se não há comentário salvo, limpar o estado
        if (savedCommentary !== null) {
          setSavedCommentary(null);
        }
      }

      // Finalizar loading inicial após verificar os dados
      setIsInitialLoading(false);
    };

    checkQuestionCommentary();
  }, [
    question.id, // Apenas question.id para recarregar ao mudar questão
    question.ai_commentary,
    existingCommentary,
    isTransitioning
    // Removidas dependências que causam loops: hasValidAICommentary, debouncedOnCommentaryGenerated
  ]);

  const saveCommentaryToQuestion = async (commentary: AICommentaryResponse) => {
    try {
      const { error } = await supabase
        .from('questions')
        .update({ ai_commentary: commentary })
        .eq('id', question.id);

      if (error) {
        // Erro silencioso - análise ainda funciona, só não será salva para uso futuro
      }
    } catch (error) {
      // Erro silencioso - análise ainda funciona, só não será salva para uso futuro
    }
  };

  const handleGenerateCommentary = useCallback(async () => {
    // Evitar múltiplas chamadas simultâneas
    if (isLoading) return;

    const alternatives = question.response_choices || question.alternatives;
    if (!alternatives || alternatives.length === 0) return;

    // ⏸️ Pausar cronômetro durante geração de análise
    onPauseTimer?.();

    // 🔒 SEGURANÇA: Buscar resposta correta do backend de forma segura
    try {
      const { data, error } = await supabase.rpc('get_question_feedback_info', {
        question_id: question.id
      });

      if (error || !data) {
        setLocalError('Não foi possível obter informações necessárias para gerar a análise.');
        return;
      }

      const correctAnswerIndex = data.correct_choice ? parseInt(data.correct_choice) : null;
      if (correctAnswerIndex === null) {
        setLocalError('Informações da questão não estão disponíveis para análise.');
        return;
      }



      const generatedCommentary = await generateCommentary(
        question.id,
        question.question_content || question.statement,
        alternatives,
        correctAnswerIndex,
        sessionId,
        question.specialty?.name || 'Medicina Geral',
        question.media_attachments || []
      );



      if (generatedCommentary && currentQuestionRef.current === question.id) {
        // 🛡️ EVITAR LOOP: só atualizar se realmente mudou
        if (!savedCommentary || JSON.stringify(savedCommentary) !== JSON.stringify(generatedCommentary)) {
          setSavedCommentary(generatedCommentary);
          debouncedOnCommentaryGenerated(generatedCommentary);
        }
        setShowCommentary(true);

        await saveCommentaryToQuestion(generatedCommentary);
      }
    } catch (error) {
      setLocalError('Erro ao gerar análise. Tente novamente.');
    }
  }, [
    isLoading,
    question.id,
    question.response_choices,
    question.alternatives,
    question.question_content,
    question.statement,
    question.specialty?.name,
    question.media_attachments,
    sessionId,
    generateCommentary,
    savedCommentary,
    debouncedOnCommentaryGenerated,
    saveCommentaryToQuestion
    // Removidas dependências que causam loops
  ]);

  const handleShowSavedCommentary = () => {
    // ⏸️ Pausar cronômetro durante visualização de análise
    onPauseTimer?.();
    setShowCommentary(true);
  };

  const handleHideCommentary = () => {
    setShowCommentary(false);
  };

  // 🚀 OTIMIZAÇÃO: Usar useMemo para evitar recálculos desnecessários
  const { activeCommentary, hasExistingCommentary, activeCommentarySource } = useMemo(() => {
    const active = savedCommentary || existingCommentary;
    const hasExisting = hasValidAICommentary(active);
    const source = savedCommentary ? 'saved' : existingCommentary ? 'existing' : 'none';

    return {
      activeCommentary: active,
      hasExistingCommentary: hasExisting,
      activeCommentarySource: source
    };
  }, [savedCommentary, existingCommentary, hasValidAICommentary]);

  // 🚫 NÃO MOSTRAR para questões dissertativas (elas têm seu próprio botão de análise)
  const isDiscursiveQuestion = (question.question_format || question.answer_type) === 'DISSERTATIVA';
  if (isDiscursiveQuestion) {
    return null;
  }

  // 🛡️ RENDERIZAÇÃO CONDICIONAL: Evitar early return após hooks
  if (isTransitioning) {
    return (
      <div className="mt-8 relative">
        <div className="mb-6">
          <div className="mx-auto flex items-center justify-center gap-2 max-w-[240px] h-12 bg-gray-100 rounded-md animate-pulse">
            <div className="w-4 h-4 bg-gray-300 rounded"></div>
            <div className="w-32 h-4 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 relative">

      {isInitialLoading && (
        <div className="mb-6">
          <div className="mx-auto flex items-center justify-center gap-2 max-w-[240px] h-12 bg-gray-100 rounded-md animate-pulse">
            <div className="w-4 h-4 bg-gray-300 rounded"></div>
            <div className="w-32 h-4 bg-gray-300 rounded"></div>
          </div>
        </div>
      )}

      {!isInitialLoading && !hasExistingCommentary && (
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleGenerateCommentary}
              disabled={isLoading}
              variant="hackYellow"
              size="hack"
              className="flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px]"
            >
              <Brain className="h-4 w-4" />
              {isLoading ? "Gerando análise..." : "Gerar análise"}
            </Button>
            <HelpDialog />
          </div>

          {isLoading && (
            <Card className={`border-2 shadow-card-light max-w-md mx-auto transition-colors duration-200 ${
              isDarkMode
                ? 'border-gray-600 bg-gray-700'
                : 'border-black bg-hackathon-lightBg'
            }`}>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <div className="animate-pulse">
                      <Brain className={`h-6 w-6 ${
                        isDarkMode ? 'text-blue-400' : 'text-hackathon-black'
                      }`} />
                    </div>
                    <div className={`text-lg font-semibold ${
                      isDarkMode ? 'text-gray-100' : 'text-hackathon-black'
                    }`}>
                      Análise Inteligente
                    </div>
                  </div>

                  <div className="space-y-3">
                    <p className={`font-medium text-base ${
                      isDarkMode ? 'text-gray-200' : 'text-hackathon-black'
                    }`}>
                      {loadingMessages[currentLoadingMessage]}
                    </p>
                    <p className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Aguarde aproximadamente 15 segundos para uma análise precisa
                    </p>
                  </div>

                  {/* Barra de progresso animada */}
                  <div className={`w-full rounded-full h-2 overflow-hidden ${
                    isDarkMode ? 'bg-gray-600' : 'bg-gray-200'
                  }`}>
                    <div className={`h-2 rounded-full animate-pulse relative ${
                      isDarkMode
                        ? 'bg-gradient-to-r from-blue-500 to-blue-400'
                        : 'bg-gradient-to-r from-hackathon-black to-gray-600'
                    }`}>
                      <div className={`absolute inset-0 bg-gradient-to-r from-transparent to-transparent opacity-30 animate-ping ${
                        isDarkMode ? 'via-blue-200' : 'via-white'
                      }`}></div>
                    </div>
                  </div>

                  {/* Indicador de pontos animados */}
                  <div className="flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full animate-bounce ${
                      isDarkMode ? 'bg-blue-400' : 'bg-hackathon-black'
                    }`}></div>
                    <div className={`w-2 h-2 rounded-full animate-bounce ${
                      isDarkMode ? 'bg-blue-400' : 'bg-hackathon-black'
                    }`} style={{ animationDelay: '0.1s' }}></div>
                    <div className={`w-2 h-2 rounded-full animate-bounce ${
                      isDarkMode ? 'bg-blue-400' : 'bg-hackathon-black'
                    }`} style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {!isInitialLoading && hasExistingCommentary && !showCommentary && (
        <div className="mb-6">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleShowSavedCommentary}
              variant="hackGreen"
              size="hack"
              className="flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px]"
            >
              <Eye className="h-4 w-4" />
              Ver Análise da IA
            </Button>
            <HelpDialog />
          </div>
        </div>
      )}

      {!isInitialLoading && hasExistingCommentary && showCommentary && (
        <div className="space-y-4">
          <div className="flex items-center justify-center gap-2">
            <Button
              onClick={handleHideCommentary}
              variant="outline"
              size="hack"
              className={`flex items-center justify-center gap-2 border-2 max-w-[240px] ${
                isDarkMode
                  ? 'border-gray-600 bg-gray-700 text-gray-200 hover:bg-gray-600 hover:text-white'
                  : 'border-black hover:bg-gray-50'
              }`}
            >
              <Eye className="h-4 w-4" />
              Ocultar Análise da IA
            </Button>

            <HelpDialog />
          </div>

          <Card className={`animate-fade-in border-2 shadow-card transition-colors duration-200 rounded-none sm:rounded-lg ${
            isDarkMode ? 'border-gray-600 bg-gray-800' : 'border-black bg-white'
          }`}>
            <CardHeader className={`border-b-2 py-3 px-4 transition-colors duration-200 ${
              isDarkMode
                ? 'border-gray-600 bg-gray-700'
                : 'border-black bg-hackathon-lightBg'
            }`}>
              {/* Layout otimizado - título à esquerda, botão à direita */}
              <div className="flex items-center justify-between">
                <CardTitle className={`flex items-center gap-2 text-lg transition-colors duration-200 ${
                  isDarkMode ? 'text-gray-200' : 'text-gray-900'
                }`}>
                  <Brain className={`h-5 w-5 transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-300' : 'text-hackathon-black'
                  }`} />
                  Análise da Questão
                </CardTitle>
                <QuestionAICommentaryFeedback
                  questionId={question.id}
                  userId={question.owner || ''}
                />
              </div>
            </CardHeader>
            <CardContent className={`space-y-3 p-0 sm:p-4 transition-colors duration-200 ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            }`}>
              {activeCommentary?.alternativas?.map((alt, index) => (
                <div
                  key={index}
                  className={`p-3 sm:p-4 border-2 rounded-none sm:rounded-lg transition-all duration-200 transform hover:translate-y-[-2px] ${
                    alt.correta
                      ? isDarkMode
                        ? 'border-green-600 bg-green-900/20 hover:bg-green-900/30'
                        : 'border-hackathon-green bg-green-50/50 hover:bg-green-50'
                      : isDarkMode
                        ? 'border-red-600 bg-red-900/20 hover:bg-red-900/30'
                        : 'border-hackathon-red bg-red-50/50 hover:bg-red-50'
                  }`}
                >
                  {/* Layout simplificado - apenas título e comentário */}
                  <div className="block">
                    {/* Título da alternativa */}
                    <h4 className={`font-bold mb-3 transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>
                      Alternativa {String.fromCharCode(65 + index)}
                      {alt.correta && " (Correta)"}
                    </h4>

                    {/* Conteúdo com largura total */}
                    <div
                      className={`leading-relaxed transition-colors duration-200 ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}
                      dangerouslySetInnerHTML={{ __html: formatFinalCommentary(alt.comentario) }}
                    />
                  </div>
                </div>
              ))}

              {activeCommentary?.comentario_final && (
                <div className={`rounded-lg p-4 border-2 transition-all duration-200 transform hover:translate-y-[-2px] ${
                  isDarkMode
                    ? 'border-gray-600 bg-gray-700 hover:bg-gray-600/80'
                    : 'border-black bg-hackathon-lightBg hover:bg-hackathon-lightBg/80'
                }`}>
                  <h4 className={`font-bold mb-2 flex items-center gap-2 transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-900'
                  }`}>
                    <Brain className={`h-4 w-4 transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-300' : 'text-hackathon-black'
                    }`} />
                    Comentário Final
                  </h4>
                  <div
                    className={`leading-relaxed transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}
                    dangerouslySetInnerHTML={{ __html: formatFinalCommentary(activeCommentary.comentario_final) }}
                  />
                </div>
              )}

              {activeCommentary?.possivel_erro_no_gabarito && activeCommentary?.justificativa_erro_gabarito && (
                <Alert variant="destructive" className={`border-2 transition-colors duration-200 ${
                  isDarkMode
                    ? 'border-orange-600 bg-orange-900/20'
                    : 'border-orange-500 bg-orange-50'
                }`}>
                  <AlertDescription className="flex items-start gap-2">
                    <span className={`font-semibold transition-colors duration-200 ${
                      isDarkMode ? 'text-orange-400' : 'text-orange-700'
                    }`}>⚠️ Possível erro no gabarito:</span>
                    <span
                      className={`transition-colors duration-200 ${
                        isDarkMode ? 'text-orange-300' : 'text-orange-700'
                      }`}
                      dangerouslySetInnerHTML={{ __html: activeCommentary.justificativa_erro_gabarito }}
                    />
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {(error || localError) && (
        <Card className={`border-2 max-w-md mx-auto transition-colors duration-200 ${
          isDarkMode
            ? 'border-orange-600 bg-orange-900/20'
            : 'border-orange-400 bg-orange-50'
        }`}>
          <CardContent className="p-6 text-center">
            <div className="flex flex-col items-center gap-4">
              <div className={`flex items-center justify-center w-12 h-12 rounded-full transition-colors duration-200 ${
                isDarkMode ? 'bg-orange-900/40' : 'bg-orange-100'
              }`}>
                <Brain className={`h-6 w-6 transition-colors duration-200 ${
                  isDarkMode ? 'text-orange-400' : 'text-orange-600'
                }`} />
              </div>
              <div className="space-y-2">
                <h3 className={`font-semibold transition-colors duration-200 ${
                  isDarkMode ? 'text-orange-300' : 'text-orange-800'
                }`}>
                  Análise temporariamente indisponível
                </h3>
                <p className={`text-sm leading-relaxed transition-colors duration-200 ${
                  isDarkMode ? 'text-orange-400' : 'text-orange-700'
                }`}>
                  {error || localError}
                </p>
                <p className={`text-xs transition-colors duration-200 ${
                  isDarkMode ? 'text-orange-500' : 'text-orange-600'
                }`}>
                  Você pode tentar novamente em alguns minutos ou continuar estudando normalmente.
                </p>
              </div>
              <Button
                onClick={() => {
                  resetCommentary();
                  setLocalError(null);
                  handleGenerateCommentary();
                }}
                variant="outline"
                size="sm"
                className="border-orange-300 text-orange-700 hover:bg-orange-100"
              >
                Tentar novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default QuestionAICommentary;
