
import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { BookOpenCheck, Timer, Trash2, Eye, Clock, CheckCircle2, ChevronLeft, ChevronRight } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { FlashcardSession } from "./types/FlashcardSession";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { motion } from "framer-motion";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";

// ✅ Constantes para paginação
const SESSIONS_PER_PAGE = 6;

export const FlashcardSessionsList = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const { user } = useAuth(); // ✅ Usar hook de auth em vez de supabase.auth.getUser()

  // Função para buscar sessões de flashcards (otimizada)
  const fetchFlashcardSessions = async (): Promise<FlashcardSession[]> => {
    if (!user?.id) throw new Error('User not authenticated');

    const { data: sessionsData, error } = await supabase
      .from("flashcards_sessions")
      .select(`
        *,
        flashcards_session_cards (
          card_id,
          review_status
        )
      `)
      .eq("user_id", user.id)
      .order("start_time", { ascending: false });

    if (error) throw error;

    // Convert the raw data to properly typed FlashcardSession objects
    const typedSessions: FlashcardSession[] = (sessionsData || []).map(session => ({
      ...session,
      status: session.status as 'in_progress' | 'completed',
      filters: session.filters
    }));

    return typedSessions;
  };

  // React Query para cache e otimização
  const { data: sessions = [], isLoading, error } = useQuery({
    queryKey: ['flashcard-sessions', user?.id],
    queryFn: fetchFlashcardSessions,
    enabled: !!user?.id, // ✅ Só executar quando user estiver disponível
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 10 * 60 * 1000, // 10 minutos
    retry: 1,
    onError: (error: any) => {
      console.error("❌ [FlashcardSessionsList] Error fetching sessions:", error);
      toast({
        title: "Erro ao carregar sessões",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // ✅ Cálculos de paginação otimizados
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(sessions.length / SESSIONS_PER_PAGE);
    const startIndex = (currentPage - 1) * SESSIONS_PER_PAGE;
    const endIndex = startIndex + SESSIONS_PER_PAGE;
    const currentPageSessions = sessions.slice(startIndex, endIndex);

    return {
      totalPages,
      currentPageSessions,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
      totalSessions: sessions.length
    };
  }, [sessions, currentPage]);

  const handleDelete = async (sessionId: string) => {
    try {
      // First, delete all related session cards
      const { error: cardsError } = await supabase
        .from("flashcards_session_cards")
        .delete()
        .eq("session_id", sessionId);

      if (cardsError) throw cardsError;

      // Then, delete the session itself
      const { error } = await supabase
        .from("flashcards_sessions")
        .delete()
        .eq("id", sessionId);

      if (error) throw error;

      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({ queryKey: ['flashcard-sessions'] });

      toast({
        title: "Sessão deletada",
        description: "A sessão foi removida com sucesso"
      });
    } catch (error: any) {
      console.error('❌ [FlashcardSessionsList] Error deleting session:', error);
      toast({
        title: "Erro ao deletar sessão",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleSessionClick = (sessionId: string) => {
    navigate(`/flashcards/session/${sessionId}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-pulse text-primary">Carregando...</div>
      </div>
    );
  }

  if (sessions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-6 sm:py-8 text-center">
        <div className="p-3 sm:p-4 rounded-full bg-gray-50 mb-3 sm:mb-4 border-2 border-black">
          <BookOpenCheck className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400" />
        </div>
        <h3 className="text-lg sm:text-xl font-medium text-gray-700 mb-2">
          Nenhum flashcard estudado ainda
        </h3>
        <p className="text-sm sm:text-base text-gray-500 mb-4 sm:mb-6 max-w-md">
          Comece sua jornada de estudos com flashcards para reforçar seu aprendizado.
        </p>
        <Button
          onClick={() => navigate("/flashcards/study")}
          className="bg-purple-600 hover:bg-purple-700 text-white font-bold border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
        >
          <BookOpenCheck className="h-4 w-4 mr-2" />
          <span className="mr-2">Iniciar Sessão de Estudos</span>
          <span className="text-xs bg-green-500 text-white px-2 py-1 rounded-full font-semibold">
            NOVO
          </span>
        </Button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <div className="space-y-3 sm:space-y-4">
        {/* ✅ Renderizar apenas sessões da página atual */}
        {paginationData.currentPageSessions.map((session, index) => {
          const totalCards = session.cards?.length || 0;
          const reviewedCards = session.flashcards_session_cards?.filter(
            card => card.review_status === 'reviewed'
          ).length || 0;
          const progress = totalCards > 0 ? Math.round((reviewedCards / totalCards) * 100) : 0;
          const allCardsReviewed = reviewedCards === totalCards;

          return (
            <motion.div
              key={session.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
              className="bg-white rounded-lg border-2 border-black shadow-card-sm hover:shadow-card-md transition-all duration-200"
            >
              <div className="p-4 sm:p-6">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Timer className="h-4 w-4" />
                    <span>
                      {formatDistanceToNow(new Date(session.start_time), {
                        addSuffix: true,
                        locale: ptBR,
                      })}
                    </span>
                  </div>
                </div>

                {session.filters?.specialty_name && (
                  <p className="text-sm text-gray-600 mb-3 truncate">
                    {session.filters.specialty_name}
                  </p>
                )}

                <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden mb-4">
                  <div
                    className="bg-hackathon-yellow transition-all duration-300 h-full"
                    style={{
                      width: `${progress}%`,
                    }}
                  />
                </div>

                <div className="grid grid-cols-2 gap-2 sm:gap-3 mt-1.5 sm:mt-2 mb-4">
                  <div className="flex items-center gap-1 rounded-lg py-1 sm:py-1.5 px-2 sm:px-2.5 bg-white shadow-sm border border-gray-200">
                    <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-hackathon-yellow flex-shrink-0" />
                    <span className="text-xs sm:text-sm font-medium text-gray-700 truncate ml-1">
                      {progress}% concluído
                    </span>
                  </div>

                  <div className="flex items-center gap-1 rounded-lg py-1 sm:py-1.5 px-2 sm:px-2.5 bg-white shadow-sm border border-gray-200">
                    <div className="flex gap-1 items-center">
                      {allCardsReviewed ? (
                        <CheckCircle2 className="h-3 w-3 sm:h-4 sm:w-4 text-green-600 flex-shrink-0" />
                      ) : (
                        <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500 flex-shrink-0" />
                      )}
                      <span className="text-xs font-medium truncate text-gray-700">
                        {reviewedCards}/{totalCards}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    onClick={() => handleSessionClick(session.id)}
                    className="border-2 border-black text-black bg-hackathon-yellow hover:bg-hackathon-yellow/90 h-8 px-2 sm:px-3 text-xs sm:text-sm font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                  >
                    <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                    <span className="truncate">
                      {session.status === 'completed' ? 'Ver' : 'Continuar'}
                    </span>
                  </Button>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-2 border-black text-black bg-hackathon-red/10 hover:bg-hackathon-red/20 h-8 px-2 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                      >
                        <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 text-hackathon-red" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="border-2 border-black">
                      <AlertDialogHeader>
                        <AlertDialogTitle>Remover sessão</AlertDialogTitle>
                        <AlertDialogDescription>
                          Tem certeza que deseja remover esta sessão de flashcards? Esta ação não pode ser desfeita.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel className="border-2 border-black">Cancelar</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(session.id)}
                          className="bg-hackathon-red hover:bg-hackathon-red/90 text-white border-2 border-black"
                        >
                          Remover
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* ✅ Controles de paginação */}
      {paginationData.totalPages > 1 && (
        <div className="flex items-center justify-center gap-4 mt-6 pt-4 border-t border-gray-200">
          <Button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={!paginationData.hasPrevPage}
            variant="outline"
            size="sm"
            className="border-2 border-black"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Anterior
          </Button>

          <span className="text-sm text-gray-600 px-4">
            Página {currentPage} de {paginationData.totalPages}
            <span className="text-gray-400 ml-2">
              ({paginationData.totalSessions} sessões)
            </span>
          </span>

          <Button
            onClick={() => setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))}
            disabled={!paginationData.hasNextPage}
            variant="outline"
            size="sm"
            className="border-2 border-black"
          >
            Próxima
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      )}
    </motion.div>
  );
};
