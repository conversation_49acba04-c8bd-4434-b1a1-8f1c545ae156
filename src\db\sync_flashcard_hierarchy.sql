-- =====================================================
-- SINCRONIZAÇÃO DE HIERARQUIA FLASHCARDS
-- =====================================================
-- Garante que todas as categorias de study_categories 
-- existam nas tabelas de flashcards correspondentes

-- Função para sincronizar hierarquia de flashcards
CREATE OR REPLACE FUNCTION sync_flashcard_hierarchy()
RETURNS TEXT AS $$
DECLARE
    sync_user_id UUID := '8a6edf3c-d011-4780-875d-e1b2892c2d44'; -- User ID padrão para categorias do sistema
    specialty_count INTEGER := 0;
    theme_count INTEGER := 0;
    focus_count INTEGER := 0;
    result_text TEXT := '';
BEGIN
    -- 1. Sincronizar especialidades
    INSERT INTO flashcards_specialty (id, name, user_id, created_at)
    SELECT 
        sc.id,
        sc.name,
        sync_user_id,
        NOW()
    FROM study_categories sc
    LEFT JOIN flashcards_specialty fs ON sc.id = fs.id
    WHERE sc.type = 'specialty' 
    AND fs.id IS NULL;
    
    GET DIAGNOSTICS specialty_count = ROW_COUNT;
    
    -- 2. Sincronizar temas
    INSERT INTO flashcards_theme (id, name, specialty_id, user_id, created_at)
    SELECT 
        sc.id,
        sc.name,
        sc.parent_id,
        sync_user_id,
        NOW()
    FROM study_categories sc
    LEFT JOIN flashcards_theme ft ON sc.id = ft.id
    WHERE sc.type = 'theme' 
    AND ft.id IS NULL
    AND sc.parent_id IS NOT NULL;
    
    GET DIAGNOSTICS theme_count = ROW_COUNT;
    
    -- 3. Sincronizar focos
    INSERT INTO flashcards_focus (id, name, theme_id, user_id, created_at)
    SELECT 
        sc.id,
        sc.name,
        sc.parent_id,
        sync_user_id,
        NOW()
    FROM study_categories sc
    LEFT JOIN flashcards_focus ff ON sc.id = ff.id
    WHERE sc.type = 'focus' 
    AND ff.id IS NULL
    AND sc.parent_id IS NOT NULL;
    
    GET DIAGNOSTICS focus_count = ROW_COUNT;
    
    -- Preparar resultado
    result_text := format('Sincronização concluída: %s especialidades, %s temas, %s focos adicionados', 
                         specialty_count, theme_count, focus_count);
    
    RAISE NOTICE '%', result_text;
    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS PARA SINCRONIZAÇÃO AUTOMÁTICA
-- =====================================================

-- Função trigger para sincronizar automaticamente
CREATE OR REPLACE FUNCTION auto_sync_flashcard_category()
RETURNS TRIGGER AS $$
DECLARE
    sync_user_id UUID := '8a6edf3c-d011-4780-875d-e1b2892c2d44';
BEGIN
    -- Só processa INSERTs
    IF TG_OP = 'INSERT' THEN
        -- Sincronizar especialidade
        IF NEW.type = 'specialty' THEN
            INSERT INTO flashcards_specialty (id, name, user_id, created_at)
            VALUES (NEW.id, NEW.name, sync_user_id, NOW())
            ON CONFLICT (id) DO NOTHING;
            
        -- Sincronizar tema
        ELSIF NEW.type = 'theme' AND NEW.parent_id IS NOT NULL THEN
            INSERT INTO flashcards_theme (id, name, specialty_id, user_id, created_at)
            VALUES (NEW.id, NEW.name, NEW.parent_id, sync_user_id, NOW())
            ON CONFLICT (id) DO NOTHING;
            
        -- Sincronizar foco
        ELSIF NEW.type = 'focus' AND NEW.parent_id IS NOT NULL THEN
            INSERT INTO flashcards_focus (id, name, theme_id, user_id, created_at)
            VALUES (NEW.id, NEW.name, NEW.parent_id, sync_user_id, NOW())
            ON CONFLICT (id) DO NOTHING;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger
DROP TRIGGER IF EXISTS trigger_auto_sync_flashcard_category ON study_categories;
CREATE TRIGGER trigger_auto_sync_flashcard_category
    AFTER INSERT ON study_categories
    FOR EACH ROW
    EXECUTE FUNCTION auto_sync_flashcard_category();

-- =====================================================
-- EXECUTAR SINCRONIZAÇÃO INICIAL
-- =====================================================

-- Executar sincronização inicial
SELECT sync_flashcard_hierarchy();
