import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

export const useWelcomeDialog = () => {
  const [showWelcomeDialog, setShowWelcomeDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    const checkWelcomeDialogStatus = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('welcome_dialog_shown')
          .eq('user_id', user.id)
          .maybeSingle();

        if (error) {
          console.error("Erro ao verificar status do welcome dialog:", error);
          // Se houver erro, mostra o dialog por segurança
          setShowWelcomeDialog(true);
          setIsLoading(false);
          return;
        }

        // Se não há dados, cria entrada e mostra dialog
        if (!data) {
          // Cria entrada na tabela user_preferences
          await supabase
            .from('user_preferences')
            .insert({
              user_id: user.id,
              welcome_dialog_shown: false
            });
          setShowWelcomeDialog(true);
        } else if (!data.welcome_dialog_shown) {
          setShowWelcomeDialog(true);
        }
      } catch (err) {
        console.error("Erro ao verificar welcome dialog:", err);
        setShowWelcomeDialog(true);
      } finally {
        setIsLoading(false);
      }
    };

    checkWelcomeDialogStatus();
  }, [user]);

  const handleContinue = async () => {
    if (!user) return;

    try {
      // Marca como mostrado no banco de dados
      const { error } = await supabase
        .from('user_preferences')
        .update({ welcome_dialog_shown: true })
        .eq('user_id', user.id);

      if (error) {
        console.error("Erro ao marcar welcome dialog como mostrado:", error);
      }
    } catch (err) {
      console.error("Erro ao atualizar welcome dialog:", err);
    }

    setShowWelcomeDialog(false);
  };

  const resetWelcomeDialog = async () => {
    if (!user) return;

    try {
      // Função para resetar (útil para desenvolvimento/testes)
      const { error } = await supabase
        .from('user_preferences')
        .update({ welcome_dialog_shown: false })
        .eq('user_id', user.id);

      if (error) {
        console.error("Erro ao resetar welcome dialog:", error);
      } else {
        setShowWelcomeDialog(true);
      }
    } catch (err) {
      console.error("Erro ao resetar welcome dialog:", err);
    }
  };

  return {
    showWelcomeDialog,
    handleContinue,
    resetWelcomeDialog,
    isLoading
  };
};
