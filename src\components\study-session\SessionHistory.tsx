
import { useState, lazy, Suspense } from "react";
import { useDomain } from "@/hooks/useDomain";
import { motion } from "framer-motion";
import { History, BookOpen, Zap } from "lucide-react";
import { lazyWithRetry } from "@/utils/dynamicImportHelper";

// Lazy imports para componentes pesados com retry automático
const StudySessionsList = lazyWithRetry(() => import("./StudySessionsList"));
const FlashcardSessionsList = lazyWithRetry(() => import("@/components/flashcards/FlashcardSessionsList").then(module => ({ default: module.FlashcardSessionsList })));

export const SessionHistory = () => {
  const [tab, setTab] = useState<"questions" | "flashcards">("questions");
  const { domain } = useDomain();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      data-tutorial="history"
      className="w-full"
    >
      <div className="bg-white rounded-lg border-2 border-black shadow-card-sm">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b-2 border-black">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="p-2 sm:p-3 bg-hackathon-red rounded-full border-2 border-black">
              <History className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-gray-800">
              Histórico de Sessões
            </h2>
          </div>
        </div>

        {/* Abas (botões de navegação) */}
        <div className="flex w-full gap-2 px-4 sm:px-6 py-4 sm:py-6 border-b border-gray-200">
          <button
            className={`flex-1 py-2 px-2 sm:px-4 rounded-lg font-medium border-2 border-black transition-all duration-200 flex items-center justify-center gap-2
              ${tab === "questions"
              ? "bg-hackathon-yellow text-black shadow-button"
              : "bg-gray-100 text-gray-600 hover:bg-hackathon-yellow/70"
            }`}
            onClick={() => setTab("questions")}
          >
            <BookOpen className="h-4 w-4" />
            <span>Questões</span>
          </button>
          <button
            className={`flex-1 py-2 px-2 sm:px-4 rounded-lg font-medium border-2 border-black transition-all duration-200 flex items-center justify-center gap-2
              ${tab === "flashcards"
              ? "bg-hackathon-yellow text-black shadow-button"
              : "bg-gray-100 text-gray-600 hover:bg-hackathon-yellow/70"
            }`}
            onClick={() => setTab("flashcards")}
          >
            <Zap className="h-4 w-4" />
            <span>Flashcards</span>
          </button>
        </div>

        {/* Conteúdo da Tab */}
        <div className="p-4 sm:p-6">
          {tab === "questions" ? (
            <Suspense fallback={
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="h-24 bg-gray-100 rounded-lg animate-pulse border-2 border-gray-200"></div>
                ))}
              </div>
            }>
              <StudySessionsList />
            </Suspense>
          ) : (
            <Suspense fallback={
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="h-24 bg-gray-100 rounded-lg animate-pulse border-2 border-gray-200"></div>
                ))}
              </div>
            }>
              <FlashcardSessionsList />
            </Suspense>
          )}
        </div>
      </div>
    </motion.div>
  );
};


