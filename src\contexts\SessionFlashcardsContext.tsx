import React, { createContext, useContext, useState, useCallback } from 'react';

// 🎯 Interface para flashcard criado durante a sessão
export interface SessionFlashcard {
  id: string;
  front: string;
  back: string;
  type?: string;
  questionId?: string;
  createdAt: Date;
}

// 🎯 Interface para o contexto
export interface SessionFlashcardsContextType {
  // Estado dos flashcards da sessão
  sessionFlashcards: SessionFlashcard[];
  
  // Funções de controle
  addSessionFlashcard: (flashcard: Omit<SessionFlashcard, 'id' | 'createdAt'>) => void;
  removeSessionFlashcard: (id: string) => void;
  clearSessionFlashcards: () => void;
  
  // Informações úteis
  hasSessionFlashcards: boolean;
  sessionFlashcardsCount: number;
}

// 🎯 Criar o contexto
const SessionFlashcardsContext = createContext<SessionFlashcardsContextType | undefined>(undefined);

// 🎯 Provider do contexto
export const SessionFlashcardsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sessionFlashcards, setSessionFlashcards] = useState<SessionFlashcard[]>([]);

  // 🎯 Função para adicionar flashcard à sessão
  const addSessionFlashcard = useCallback((flashcard: Omit<SessionFlashcard, 'id' | 'createdAt'>) => {
    const newFlashcard: SessionFlashcard = {
      ...flashcard,
      id: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date()
    };

    setSessionFlashcards(prev => [...prev, newFlashcard]);
  }, []);

  // 🎯 Função para remover flashcard da sessão
  const removeSessionFlashcard = useCallback((id: string) => {
    setSessionFlashcards(prev => prev.filter(card => card.id !== id));
    
    console.log('🗑️ [SessionFlashcardsContext] Flashcard removido da sessão:', id);
  }, []);

  // 🎯 Função para limpar todos os flashcards da sessão
  const clearSessionFlashcards = useCallback(() => {
    setSessionFlashcards([]);
    
    console.log('🧹 [SessionFlashcardsContext] Flashcards da sessão limpos');
  }, []);

  // 🎯 Estados derivados
  const hasSessionFlashcards = sessionFlashcards.length > 0;
  const sessionFlashcardsCount = sessionFlashcards.length;

  // 🎯 Valor do contexto
  const contextValue: SessionFlashcardsContextType = {
    sessionFlashcards,
    addSessionFlashcard,
    removeSessionFlashcard,
    clearSessionFlashcards,
    hasSessionFlashcards,
    sessionFlashcardsCount
  };

  return (
    <SessionFlashcardsContext.Provider value={contextValue}>
      {children}
    </SessionFlashcardsContext.Provider>
  );
};

// 🎯 Hook para usar o contexto
export const useSessionFlashcards = (): SessionFlashcardsContextType => {
  const context = useContext(SessionFlashcardsContext);
  
  if (context === undefined) {
    throw new Error('useSessionFlashcards deve ser usado dentro de um SessionFlashcardsProvider');
  }
  
  return context;
};

// 🎯 Hook otimizado para componentes que só precisam saber se há flashcards
export const useHasSessionFlashcards = (): boolean => {
  const { hasSessionFlashcards } = useSessionFlashcards();
  return hasSessionFlashcards;
};

// 🎯 Hook otimizado para obter informações básicas
export const useSessionFlashcardsInfo = () => {
  const { 
    sessionFlashcardsCount, 
    hasSessionFlashcards,
    sessionFlashcards 
  } = useSessionFlashcards();
  
  return {
    count: sessionFlashcardsCount,
    hasFlashcards: hasSessionFlashcards,
    flashcards: sessionFlashcards,
    latestFlashcard: sessionFlashcards[sessionFlashcards.length - 1] || null
  };
};

export default SessionFlashcardsContext;
