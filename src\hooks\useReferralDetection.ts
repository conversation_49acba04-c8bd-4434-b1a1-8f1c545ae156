import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useReferralDetection = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [hasProcessedReferral, setHasProcessedReferral] = useState(false);
  const [initialCheck, setInitialCheck] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState('');

  // Mutation para processar referral usando inserção direta
  const processReferral = useMutation({
    mutationFn: async (referralCode: string): Promise<boolean> => {
      if (!user?.id) throw new Error('User not authenticated');

      // Processing referral

      // Aguardar um pouco para garantir que a sessão está completamente estabelecida
      await new Promise(resolve => setTimeout(resolve, 200));

      // Verificar se ainda temos usuário autenticado
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      if (!currentUser) {
        throw new Error('User session lost during processing');
      }

      // 1. Verificar se o código de referral existe
      const { data: referralData, error: referralError } = await supabase
        .from('user_referrals')
        .select('referrer_id')
        .eq('referral_code', referralCode)
        .single();

      if (referralError || !referralData) {
        return false;
      }

      // 2. Verificar se o usuário já foi referenciado
      const { data: existingReferral, error: checkError } = await supabase
        .from('user_referred_by')
        .select('id')
        .eq('user_id', currentUser.id)
        .maybeSingle();

      if (checkError) {
        throw checkError;
      }

      if (existingReferral) {
        return false;
      }

      // 3. Inserir referência
      const { error: insertError } = await supabase
        .from('user_referred_by')
        .insert({
          user_id: currentUser.id,
          referrer_id: referralData.referrer_id,
          referral_code: referralCode
        });

      if (insertError) {
        throw insertError;
      }

      // 4. Atualizar contador
      const { error: updateError } = await supabase
        .rpc('increment_referral_count', {
          referrer_user_id: referralData.referrer_id
        });

      if (updateError) {
        // Não falhar aqui, pois o referral já foi inserido
      }

      return true;
    },
    onSuccess: (success) => {
      if (success) {
        setDialogMessage('🎉 Referência processada com sucesso! Bem-vindo à comunidade MedEvo!');
        setShowSuccessDialog(true);
        // Invalidar queries de referral para atualizar status
        queryClient.invalidateQueries({ queryKey: ['user-referral'] });
        queryClient.invalidateQueries({ queryKey: ['user-referral-status'] });
        queryClient.invalidateQueries({ queryKey: ['referred-users'] });
      } else {
        setDialogMessage('Código de referência inválido ou já utilizado.');
        setShowErrorDialog(true);
      }
    },
    onError: (error: any) => {
      setDialogMessage('Erro ao processar referência: ' + error.message);
      setShowErrorDialog(true);
    }
  });

  useEffect(() => {
    // ✅ Executar apenas uma vez quando componente montar
    if (initialCheck) return;
    setInitialCheck(true);

    // Verificar se há código de referência na URL
    const urlParams = new URLSearchParams(location.search);
    const referralCode = urlParams.get('ref');



    if (referralCode && user && !hasProcessedReferral) {

      // Processar referência apenas uma vez
      setHasProcessedReferral(true);

      // Aguardar um pouco para garantir que o usuário está autenticado
      setTimeout(async () => {
        try {
          await processReferral.mutateAsync(referralCode);
        } catch (error) {
          // Error handling is done in onError callback
        }

        // Limpar URL removendo o parâmetro ref
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('ref');
        navigate(newUrl.pathname + newUrl.search, { replace: true });
      }, 500); // Reduzido para 500ms
    }

    // Se há código mas usuário não está logado, salvar no localStorage
    if (referralCode && !user) {
      localStorage.setItem('pendingReferralCode', referralCode);

      // Mostrar dialog informativo
      setDialogMessage('Link de convite detectado! Faça login para ativar a referência.');
      setShowSuccessDialog(true);
    }
  }, []); // ✅ Array vazio para executar apenas uma vez

  // Processar referência pendente após login (apenas no welcome/onboarding)
  useEffect(() => {
    if (user?.id && !hasProcessedReferral && initialCheck) {
      const pendingCode = localStorage.getItem('pendingReferralCode');
      const currentPath = window.location.pathname;

      // Só processar se estiver no welcome, onboarding ou página inicial
      const allowedPaths = ['/', '/onboarding', '/welcome'];
      const shouldProcess = allowedPaths.includes(currentPath) || currentPath === '/';

      if (pendingCode && shouldProcess) {

        // Verificar primeiro se o usuário já foi referenciado
        const checkExistingReferral = async () => {
          const { data: existingReferral } = await supabase
            .from('user_referred_by')
            .select('id')
            .eq('user_id', user.id)
            .maybeSingle();

          if (existingReferral) {
            localStorage.removeItem('pendingReferralCode');
            return;
          }

          // Se não tem referral, processar o código pendente
          setHasProcessedReferral(true);
          setTimeout(async () => {
            try {
              await processReferral.mutateAsync(pendingCode);
            } catch (error) {
              // Error handling is done in onError callback
            }
            localStorage.removeItem('pendingReferralCode');
          }, 1000);
        };

        checkExistingReferral();
      }
    }
  }, [user?.id, hasProcessedReferral, initialCheck]); // ✅ Dependências específicas

  return {
    hasProcessedReferral,
    showSuccessDialog,
    showErrorDialog,
    dialogMessage,
    setShowSuccessDialog,
    setShowErrorDialog
  };
};
