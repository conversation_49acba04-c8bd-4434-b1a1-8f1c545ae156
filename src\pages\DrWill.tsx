import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, Send, Spark<PERSON>, Brain, ArrowLeft, Stethoscope, X, History, Search, Trash2, Clock, Plus, MessageSquare, ChevronLeft, ChevronRight, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import { useAuth } from '@/contexts/AuthContext';
import { useUserData } from '@/hooks/useUserData';
import { useDrWillChat } from '@/hooks/useDrWillChat';
import { useDrWillHistory } from '@/hooks/useDrWillHistory';
import { DrWillHistoryPanel } from '@/components/DrWillSidebar';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useIsMobile } from '@/hooks/useIsMobile';
import { formatDrWillMessage, formatThinkingContent } from '@/utils/messageFormatter';
import { drWillLogger } from '@/utils/logger';
import { ThinkingModeComponent } from '@/components/ThinkingModeComponent';
import { DrWillDiagnostics } from '@/components/DrWillDiagnostics';
import MermaidModal from '@/components/MermaidModal';
import { useCurrentQuestion } from '@/contexts/CurrentQuestionContext';
import { SessionInactiveDialog } from '@/components/SessionInactiveDialog';
import { supabase } from '@/integrations/supabase/client';
import { useDarkMode } from '@/contexts/DarkModeContext';

// 🎯 REMOVIDO: ThinkingModeComponent agora é compartilhado

const DrWill = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useUserData();
  const isMobile = useIsMobile();
  const { isDarkMode } = useDarkMode();

  // 🎯 ESTADO LOCAL para garantir sincronização (DEVE VIR ANTES DOS OUTROS HOOKS)
  const [activeThreadId, setActiveThreadId] = useState<string | null>(null);

  // 🎯 REFATORAÇÃO ESPECÍFICA: Estado para forçar nova thread (DEVE VIR ANTES DO useDrWillChat)
  const [forceNewThread, setForceNewThread] = useState(false);

  // 🎯 CORREÇÃO: Usar hooks separados para evitar conflito de estado
  const {
    currentThreadId,
    threads,
    loadMessages: loadHistoryMessages,
    deleteThread,
    clearCurrentConversation,
    loadThreads,
    saveMessage: saveToHistory,
    createNewThread,
    getConversationHistory
  } = useDrWillHistory();

  const {
    messages,
    isLoading,
    isStreaming,
    error,
    sendMessage,
    clearMessages,
    setMessages,
    cancelRequest
  } = useDrWillChat({
    currentThreadId: activeThreadId || currentThreadId, // 🎯 Priorizar activeThreadId
    saveToHistory,
    createNewThread,
    getConversationHistory
  });

  // Função para obter iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messageRefs = useRef<{[messageId: string]: HTMLElement | null}>({});
  const [inputMessage, setInputMessage] = useState('');
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [tableDialogOpen, setTableDialogOpen] = useState(false);
  const [currentTableData, setCurrentTableData] = useState<string[][]>([]);
  const [showDiagnostics, setShowDiagnostics] = useState(false);

  // State for Mermaid modal
  const [mermaidModalOpen, setMermaidModalOpen] = useState(false);
  const [currentMermaidCode, setCurrentMermaidCode] = useState('');

  // 🎯 Hook para contexto da questão (copiado do FloatingChatButton)
  const { currentQuestion, sessionId, sessionTitle, isInQuestionSession } = useCurrentQuestion();

  // 🎯 State para dialog de sessão inativa (copiado do FloatingChatButton)
  const [sessionDialogOpen, setSessionDialogOpen] = useState(false);
  const [selectedSessionTitle, setSelectedSessionTitle] = useState('');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);



  // 🎯 SCROLL OTIMIZADO - Controle inteligente de scroll
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);

  // 📊 LOADING STATES
  const [isHistoryLoading, setIsHistoryLoading] = useState(true);

  // Detectar quando usuário faz scroll manual
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

      // Se usuário scrollou para longe do final, desabilitar auto-scroll
      if (!isNearBottom) {
        setAutoScrollEnabled(false);
      } else {
        // Se usuário voltou para perto do final, reabilitar auto-scroll
        setAutoScrollEnabled(true);
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  // 📊 CONTROLE DE LOADING STATES
  useEffect(() => {
    // Controlar loading do histórico
    if (threads !== undefined) {
      setIsHistoryLoading(false);
    }
  }, [threads]);



  // Scroll inteligente com detecção de topo (COPIADO EXATO DO FLOATINGCHATBUTTON)
  const lastScrolledMessageId = useRef<string>('');
  const hasReachedTop = useRef<boolean>(false);
  const scrollAttempts = useRef<number>(0);

  useEffect(() => {
    if (messages.length === 0) return;

    const lastMessage = messages[messages.length - 1];

    // SEMPRE scroll para mensagem do usuário (aparecer no topo do chat)
    if (lastMessage.isUser) {
      lastScrolledMessageId.current = lastMessage.id;
      hasReachedTop.current = false; // Reset para próxima resposta
      scrollAttempts.current = 0; // Reset tentativas

      setTimeout(() => {
        const messageElement = messageRefs.current[lastMessage.id];
        const container = messagesContainerRef.current;
        if (messageElement && container) {
          const elementTop = messageElement.offsetTop;
          // Offset menor para Dr. Will (sem header flutuante)
          container.scrollTo({
            top: elementTop - 20, // 20px em vez de 80px
            behavior: 'smooth'
          });
        }
      }, 100);
    }
    // Scroll CONTÍNUO para Dr. Will durante streaming (apenas se autoScroll habilitado)
    else if (lastMessage.isStreaming && !lastMessage.isUser && !hasReachedTop.current && autoScrollEnabled) {
      setTimeout(() => {
        const messageElement = messageRefs.current[lastMessage.id];
        const container = messagesContainerRef.current;
        if (messageElement && container && !hasReachedTop.current) {
          const elementTop = messageElement.offsetTop;
          const targetScrollTop = elementTop - 20; // Mesmo offset de 20px
          const currentScrollTop = container.scrollTop;

          // Verificar se já chegou no topo (diferença menor que 30px)
          if (Math.abs(currentScrollTop - targetScrollTop) <= 30) {
            hasReachedTop.current = true;
            return;
          }

          // Continuar scrolling
          container.scrollTo({
            top: targetScrollTop,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
    // Quando Dr. Will terminar, garantir que parou
    else if (!lastMessage.isStreaming && !lastMessage.isUser && lastMessage.content) {
      hasReachedTop.current = true;
    }
  }, [messages, autoScrollEnabled]);

  // Note: Scroll behavior is now handled by the messages useEffect above
  // This ensures we scroll when user message is added, regardless of loading/streaming states

  // No welcome message - always start fresh
  // User will start conversation when they send first message

  const handleNewConversation = useCallback(() => {
    if (isLoading) return;

    clearMessages();
    clearCurrentConversation();
    setActiveThreadId(null);
    setInputMessage('');
    setShowHistory(false);
    setForceNewThread(true);

    // Scroll para o topo
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = 0;
    }
  }, [isLoading, clearMessages, currentThreadId, activeThreadId, messages.length]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const messageToSend = inputMessage;
    setInputMessage('');

    try {
      const shouldForceNewThread = forceNewThread;

      if (shouldForceNewThread) {
        setForceNewThread(false);
        await sendMessage(messageToSend, user?.id, null, true);
      } else {
        const threadToUse = activeThreadId || currentThreadId;
        await sendMessage(messageToSend, user?.id, threadToUse);
      }
    } catch (error) {
      // Erro silencioso
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // History filter state - usando os mesmos nomes do FloatingChatButton
  const [historyFilter, setHistoryFilter] = useState<'all' | 'general' | 'contextual'>('general');
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 10;

  // Função para detectar threads contextuais (copiada do FloatingChatButton)
  const isContextualThread = useCallback((title: string) => {
    return title.includes('📚') && (
      title.includes('Sessão Contextual') ||
      title.includes('(') && title.includes('q)')
    );
  }, []);

  // 🎯 Função para extrair nome da sessão do título da thread
  const extractSessionNameFromTitle = useCallback((threadTitle: string): string => {
    if (!threadTitle) return 'Sessão';

    // Remover emoji e limpar
    let sessionName = threadTitle.replace('📚 ', '');

    // Remover sufixos antigos
    sessionName = sessionName.replace(' - Sessão Contextual', '');

    // Remover informação de número de questões se presente
    sessionName = sessionName.replace(/\s*\(\d+q\)$/, '');

    return sessionName || 'Sessão';
  }, []);

  // 🎯 Função para buscar sessionId por título (copiada do FloatingChatButton)
  const findSessionIdByTitle = useCallback(async (sessionName: string): Promise<string | null> => {
    try {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('study_sessions')
        .select('id')
        .eq('user_id', user.id)
        .eq('title', sessionName)
        .single();

      if (error) {
        return null;
      }

      return data?.id || null;
    } catch (error) {
      return null;
    }
  }, [user?.id]);

  // 🎯 Função para mostrar dialog de sessão inativa (copiada do FloatingChatButton)
  const showSessionInactiveDialog = async (sessionName: string, threadId?: string) => {
    setSelectedSessionTitle(sessionName);

    // Primeiro tentar extrair sessionId do metadata da thread
    let sessionIdToUse: string | null = null;

    if (threadId) {
      const thread = threads?.find(t => t.id === threadId);

      if (thread && (thread as any).metadata?.sessionId) {
        sessionIdToUse = (thread as any).metadata.sessionId;
      }
    }

    // Se não encontrou no metadata, buscar por título
    if (!sessionIdToUse) {
      sessionIdToUse = await findSessionIdByTitle(sessionName);
    }

    setSelectedSessionId(sessionIdToUse);
    setSessionDialogOpen(true);
  };

  // History management functions - com lógica de sessão (copiada do FloatingChatButton)
  const handleSelectThread = async (threadId: string) => {
    try {
      if (isLoading) return;

      const thread = threads?.find(t => t.id === threadId);
      if (!thread) return;

      const isContextual = isContextualThread(thread.title || '');

      if (isContextual) {
        const sessionName = extractSessionNameFromTitle(thread.title || '');
        showSessionInactiveDialog(sessionName, thread.id);
        return;
      }

      const historyMessages = await loadHistoryMessages(threadId);
      setActiveThreadId(threadId);

      if (historyMessages && historyMessages.length > 0) {
        const chatMessages = historyMessages.map(msg => ({
          id: msg.id,
          content: msg.content,
          isUser: msg.isUser,
          timestamp: msg.timestamp,
          isStreaming: false
        }));

        setMessages(chatMessages);
      } else {
        clearMessages();
      }

      setIsHistoryOpen(false);
      setShowHistory(false);

    } catch (error) {
      drWillLogger.error('selecting thread', error);
    }
  };

  const handleNewThread = () => {
    clearCurrentConversation();
    clearMessages();
    setActiveThreadId(null);
    setIsHistoryOpen(false);
    setShowHistory(false);
    setForceNewThread(true);
  };

  const handleDeleteThread = async (threadId: string) => {
    try {
      await deleteThread(threadId);

      // If we deleted the current thread, start a new conversation
      if (threadId === activeThreadId || threadId === currentThreadId) {
        clearMessages();
        setActiveThreadId(null); // 🎯 Limpar estado local

      }

    } catch (error) {
      drWillLogger.error('deleting thread', error);
    }
  };

  // Filter threads based on search query and filter type (copiado do FloatingChatButton)
  const filteredThreads = useMemo(() => {
    if (!threads) return [];

    let filtered = threads.filter((thread) => {
      if (historyFilter === 'all') return true;
      if (historyFilter === 'contextual') return isContextualThread(thread.title || '');
      if (historyFilter === 'general') return !isContextualThread(thread.title || '');
      return true;
    });

    // Apply search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(thread =>
        thread.title.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [threads, searchQuery, historyFilter, isContextualThread]);

  // Reset page when filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [historyFilter, searchQuery]);

  // Format relative time
  const formatRelativeTime = (date: Date) => {
    try {
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: ptBR
      });
    } catch {
      return 'há alguns momentos';
    }
  };

  // Auto-create new thread on page load (sempre nova conversa)
  useEffect(() => {
    // Page loaded - ready for user interaction
  }, [user?.id]);

  // Global functions for table and mermaid
  useEffect(() => {
    // Função para abrir dialog de tabela
    (window as any).openTableDialog = (tableId: string) => {
      const dataElement = document.getElementById(`table-data-${tableId}`);
      if (dataElement) {
        try {
          const tableData = JSON.parse(dataElement.textContent || '[]');
          setCurrentTableData(tableData);
          setTableDialogOpen(true);
        } catch (error) {
          // Erro silencioso
        }
      }
    };

    // 🎯 ESCUTAR EVENTO CUSTOMIZADO PARA MERMAID
    const handleMermaidModalEvent = (event: any) => {
      if (event.detail && event.detail.mermaidCode) {
        setCurrentMermaidCode(event.detail.mermaidCode);
        setMermaidModalOpen(true);
      }
    };

    document.addEventListener('openMermaidModal', handleMermaidModalEvent);

    // Função global para expandir Mermaid (fallback)
    (window as any).expandMermaidDiagram = (id: string) => {
      // Tentar encontrar o código no script JSON
      const codeElement = document.getElementById(id + '-code');
      if (codeElement) {
        try {
          const mermaidCode = JSON.parse(codeElement.textContent || '');
          setCurrentMermaidCode(mermaidCode);
          setMermaidModalOpen(true);
          return;
        } catch (error) {
          // Erro silencioso
        }
      }

      // Fallback: procurar em todas as mensagens por código Mermaid
      const allMessages = document.querySelectorAll('[id^="mermaid-"]');
      if (allMessages.length > 0) {
        // Usar a última mensagem com Mermaid
        const lastMermaidId = Array.from(allMessages).pop()?.id;
        if (lastMermaidId) {
          const lastCodeElement = document.getElementById(lastMermaidId + '-code');
          if (lastCodeElement) {
            try {
              const mermaidCode = JSON.parse(lastCodeElement.textContent || '');
              setCurrentMermaidCode(mermaidCode);
              setMermaidModalOpen(true);
              return;
            } catch (error) {
              // Erro silencioso
            }
          }
        }
      }


    };

    // Inicializar Mermaid globalmente
    if (typeof (window as any).mermaid !== 'undefined') {
      (window as any).mermaid.initialize({
        startOnLoad: true,
        theme: isDarkMode ? 'dark' : 'default',
        themeVariables: isDarkMode ? {
          primaryColor: '#8b5cf6',
          primaryTextColor: '#e5e7eb',
          primaryBorderColor: '#6366f1',
          lineColor: '#9ca3af',
          background: '#1f2937',
          mainBkg: '#374151',
          secondBkg: '#4b5563',
          tertiaryColor: '#6b7280',
          cScale0: '#1f2937',
          cScale1: '#374151',
          cScale2: '#4b5563'
        } : {
          primaryColor: '#8b5cf6',
          primaryTextColor: '#1f2937',
          primaryBorderColor: '#6366f1',
          lineColor: '#6b7280'
        }
      });
    }

    return () => {
      delete (window as any).openTableDialog;
      delete (window as any).expandMermaidDiagram;
      document.removeEventListener('openMermaidModal', handleMermaidModalEvent);
    };
  }, []);

  // 🎯 FORMATAÇÃO ÚNICA - REMOVIDO getFormattedContent DUPLICADO
  // Usar diretamente formatDrWillMessage do messageFormatter.ts

  // 🎯 CACHE GLOBAL PARA EVITAR FORMATAÇÃO MÚLTIPLA
  const formatCache = useRef(new Map<string, string>());

  // 🎯 COMPONENTE MEMOIZADO COM CACHE INTELIGENTE
  const MemoizedMessageContent = React.memo(({
    content,
    isStreaming,
    messageId,
    isDarkMode
  }: {
    content: string;
    isStreaming: boolean;
    messageId: string;
    isDarkMode: boolean;
  }) => {
    // 🎯 FORMATAÇÃO COM CACHE INTELIGENTE
    const formattedContent = useMemo(() => {
      // Criar chave única para cache
      const cacheKey = `${messageId}_${content.length}_${isStreaming}_${isDarkMode}`;

      // Verificar cache primeiro
      if (formatCache.current.has(cacheKey)) {
        // Cache hit - usar conteúdo existente
        return formatCache.current.get(cacheKey)!;
      }

      // Limpar cache antigo para esta mensagem (diferentes modos)
      const oldKeys = Array.from(formatCache.current.keys()).filter(key =>
        key.startsWith(`${messageId}_${content.length}_${isStreaming}_`)
      );
      oldKeys.forEach(key => formatCache.current.delete(key));

      // Formatar novo conteúdo
      const formatted = formatDrWillMessage(content, isStreaming || false, isDarkMode);
      formatCache.current.set(cacheKey, formatted);

      return formatted;
    }, [content, messageId, isStreaming, isDarkMode]); // Incluir isStreaming para cache correto

    return (
      <div
        dangerouslySetInnerHTML={{
          __html: formattedContent
        }}
        className="leading-relaxed text-sm"
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        }}
      />
    );
  });

  // 🎯 LIMPEZA DE CACHE QUANDO NECESSÁRIO
  useEffect(() => {
    // Limpar cache quando há muitas entradas (evitar memory leak)
    if (formatCache.current.size > 100) {
      // Limpeza de cache
      formatCache.current.clear();
    }
  }, [messages.length]);

  // Limpar cache quando sair do modo de estudo
  useEffect(() => {
    const handleClearCache = () => {
      formatCache.current.clear();
    };

    window.addEventListener('clearDarkModeCache', handleClearCache);
    return () => window.removeEventListener('clearDarkModeCache', handleClearCache);
  }, []);

  // Limpar histórico de mensagens quando sair da sessão de estudos
  useEffect(() => {
    const handleExitStudySession = () => {
      // Limpar todas as mensagens para começar conversa nova
      clearMessages();

      // Resetar thread ativa para forçar criação de nova thread
      setActiveThreadId(null);

      // Fechar histórico se estiver aberto
      if (showHistory) {
        setShowHistory(false);
      }

      // Limpar input se houver texto
      setInputMessage('');

      // Forçar nova thread na próxima mensagem
      setForceNewThread(true);
    };

    window.addEventListener('forceDeactivateStudyMode', handleExitStudySession);
    return () => window.removeEventListener('forceDeactivateStudyMode', handleExitStudySession);
  }, [clearMessages, showHistory]);








  // 🎯 FUNÇÃO PRINCIPAL DO COMPONENTE
  return (
    <div className="h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 flex flex-col overflow-hidden">
      <Header />

      {/* History Panel */}
      <DrWillHistoryPanel
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
        threads={threads || []}
        currentThreadId={currentThreadId}
        onSelectThread={handleSelectThread}
        onNewThread={handleNewThread}
        onDeleteThread={handleDeleteThread}
        isLoading={isLoading}
      />

      <div className={`flex-1 container max-w-4xl mx-auto ${isMobile ? 'px-2 py-2' : 'px-4 py-4'} flex flex-col overflow-hidden`}>
        {/* Header - Todos na mesma linha no Mobile */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`${isMobile ? 'flex items-center justify-between gap-2 mb-2' : 'flex items-center gap-4 mb-4'} flex-shrink-0`}
        >
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/plataformadeestudos')}
              className="border-2 border-black hover:bg-gray-100"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {isMobile ? '' : 'Voltar'}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                if (!showHistory) {
                  // Carregar threads apenas quando abrir o histórico pela primeira vez

                  await loadThreads();
                }
                setShowHistory(!showHistory);
              }}
              className={`border-2 border-black hover:bg-gray-100 ${showHistory ? 'bg-purple-50 border-purple-500' : ''}`}
            >
              <History className="h-4 w-4" />
              {!isMobile && <span className="ml-2">{showHistory ? 'Chat' : 'Histórico'}</span>}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleNewConversation}
              disabled={isLoading}
              className="border-2 border-black hover:bg-gray-100"
            >
              <Plus className="h-4 w-4" />
              {!isMobile && <span className="ml-2">Nova</span>}
            </Button>

          </div>

          {isMobile ? (
            // Layout compacto para mobile - na mesma linha
            <div className="flex items-center gap-2">
              <div className="relative">
                <div className="p-1.5 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg border-2 border-black">
                  <Bot className="h-4 w-4 text-white" />
                  <Sparkles className="h-2 w-2 text-yellow-300 absolute -top-0.5 -right-0.5 animate-pulse" />
                </div>
              </div>
              <h1 className="text-sm font-bold text-gray-800">Dr. Will</h1>
            </div>
          ) : (
            // Layout desktop original
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl border-2 border-black">
                  <Bot className="h-8 w-8 text-white" />
                  <Sparkles className="h-4 w-4 text-yellow-300 absolute -top-1 -right-1 animate-pulse" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Dr. Will</h1>
                <p className="text-sm text-gray-600">
                  MedEvo
                </p>
              </div>
            </div>
          )}
        </motion.div>

        {/* Chat Container */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex-1 bg-white rounded-2xl border-2 border-black shadow-xl overflow-hidden relative flex flex-col"
        >
          {/* Messages Area */}
          <div
            ref={messagesContainerRef}
            className={`flex-1 overflow-y-auto ${isMobile ? 'p-4' : 'p-6'} space-y-4 relative`}
          >
            <AnimatePresence mode="wait">
              {showHistory ? (
                // 📚 HISTÓRICO INTEGRADO
                <motion.div
                  key="history"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="h-full flex flex-col"
                >
                  {/* Header do Histórico - Copiado do FloatingChatButton */}
                  <div className="p-3 border-b border-gray-200">
                    <h4 className="font-semibold text-gray-900 mb-3">Conversas Anteriores</h4>

                    {/* Filtros - Exatamente como no FloatingChatButton */}
                    <div className="flex gap-1">
                      <button
                        onClick={() => {
                          setHistoryFilter('all');
                          setCurrentPage(1);
                        }}
                        disabled={isLoading}
                        className={`px-3 py-1 text-xs rounded-full transition-colors ${
                          historyFilter === 'all'
                            ? 'bg-blue-500 text-white'
                            : isLoading
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        Todas
                      </button>
                      <button
                        onClick={() => {
                          setHistoryFilter('general');
                          setCurrentPage(1);
                        }}
                        disabled={isLoading}
                        className={`px-3 py-1 text-xs rounded-full transition-colors ${
                          historyFilter === 'general'
                            ? 'bg-purple-500 text-white'
                            : isLoading
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        🧠 Gerais
                      </button>
                      <button
                        onClick={() => {
                          setHistoryFilter('contextual');
                          setCurrentPage(1);
                        }}
                        disabled={isLoading}
                        className={`px-3 py-1 text-xs rounded-full transition-colors ${
                          historyFilter === 'contextual'
                            ? 'bg-emerald-500 text-white'
                            : isLoading
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        🎯 Sessões
                      </button>
                    </div>
                  </div>

                  {/* Lista de Threads - Copiado do FloatingChatButton */}
                  {isHistoryLoading ? (
                    <div className="space-y-3 p-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex items-center gap-3 animate-pulse">
                          <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : threads && threads.length > 0 ? (
                    (() => {
                      // Calcular paginação
                      const totalPages = Math.ceil(filteredThreads.length / ITEMS_PER_PAGE);
                      const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
                      const endIndex = startIndex + ITEMS_PER_PAGE;
                      const paginatedThreads = filteredThreads.slice(startIndex, endIndex);

                      return (
                        <>
                          {/* Lista de threads paginada */}
                          {paginatedThreads.map((thread) => (
                            <motion.div
                              key={thread.id}
                              whileHover={{ backgroundColor: '#f8fafc' }}
                              onClick={() => {
                                // Bloquear se estiver carregando
                                if (isLoading) {
                                  return;
                                }
                                handleSelectThread(thread.id);
                              }}
                              className={`p-3 border-b border-gray-100 transition-colors ${
                                isLoading
                                  ? 'opacity-50 cursor-not-allowed'
                                  : 'cursor-pointer hover:bg-gray-50'
                              }`}
                            >
                              <div className="flex items-start gap-3">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm ${
                                  isContextualThread(thread.title || '')
                                    ? 'bg-gradient-to-br from-emerald-500 to-teal-600'
                                    : 'bg-gradient-to-br from-blue-500 to-purple-600'
                                }`}>
                                  {isContextualThread(thread.title || '') ? '🎯' : '🧠'}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2">
                                    <h5 className="font-medium text-gray-900 text-sm truncate">
                                      {thread.title || 'Conversa com Dr. Will'}
                                    </h5>
                                    {isContextualThread(thread.title || '') && (
                                      <span className="px-2 py-0.5 bg-emerald-100 text-emerald-700 text-xs rounded-full">
                                        Contextual
                                      </span>
                                    )}
                                  </div>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {(() => {
                                      try {
                                        if (thread.lastMessageAt) {
                                          const date = new Date(thread.lastMessageAt);
                                          if (!isNaN(date.getTime())) {
                                            return formatDistanceToNow(date, {
                                              addSuffix: true,
                                              locale: ptBR
                                            });
                                          }
                                        }
                                        return 'Recente';
                                      } catch {
                                        return 'Recente';
                                      }
                                    })()}
                                    {isContextualThread(thread.title || '') && (
                                      !isInQuestionSession ? (
                                        <span className="ml-2 text-orange-500">• Sem contexto ativo</span>
                                      ) : (
                                        (() => {
                                          // Usar sessionId do metadata da thread para comparação precisa
                                          const threadSessionId = (thread as any).metadata?.sessionId;
                                          const currentSessionId = sessionId;

                                          return threadSessionId && currentSessionId && threadSessionId !== currentSessionId ? (
                                            <span className="ml-2 text-red-500">• Sessão diferente</span>
                                          ) : threadSessionId && currentSessionId && threadSessionId === currentSessionId ? (
                                            <span className="ml-2 text-green-500">• Sessão atual</span>
                                          ) : (
                                            <span className="ml-2 text-gray-500">• Sessão</span>
                                          );
                                        })()
                                      )
                                    )}
                                  </p>
                                </div>
                              </div>
                            </motion.div>
                          ))}

                          {/* Controles de Paginação - Copiado do FloatingChatButton */}
                          {totalPages > 1 && (
                            <div className="p-3 border-t border-gray-100 bg-gray-50">
                              <div className="flex items-center justify-between">
                                <div className="text-xs text-gray-500">
                                  Página {currentPage} de {totalPages} • {filteredThreads.length} conversas
                                </div>
                                <div className="flex items-center gap-1">
                                  <button
                                    onClick={() => setCurrentPage(currentPage - 1)}
                                    disabled={currentPage === 1 || isLoading}
                                    className={`px-2 py-1 text-xs rounded transition-colors ${
                                      currentPage === 1 || isLoading
                                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                        : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
                                    }`}
                                  >
                                    ←
                                  </button>
                                  <span className="px-2 py-1 text-xs text-gray-600">
                                    {currentPage}
                                  </span>
                                  <button
                                    onClick={() => setCurrentPage(currentPage + 1)}
                                    disabled={currentPage === totalPages || isLoading}
                                    className={`px-2 py-1 text-xs rounded transition-colors ${
                                      currentPage === totalPages || isLoading
                                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                        : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
                                    }`}
                                  >
                                    →
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Botão Voltar à Thread Atual - Copiado do FloatingChatButton */}
                          {currentThreadId && (
                            <div className="p-3 border-t border-gray-100 bg-blue-50">
                              <button
                                onClick={() => {
                                  if (!isLoading) {
                                    setShowHistory(false);
                                  }
                                }}
                                disabled={isLoading}
                                className={`w-full px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                                  isLoading
                                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                    : 'bg-blue-500 text-white hover:bg-blue-600 shadow-sm'
                                }`}
                              >
                                <div className="flex items-center justify-center gap-2">
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                  </svg>
                                  Voltar à Conversa Atual
                                </div>
                              </button>
                            </div>
                          )}
                        </>
                      );
                    })()
                  ) : threads && threads.length > 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm">
                        {historyFilter === 'general' && 'Nenhuma conversa geral'}
                        {historyFilter === 'contextual' && 'Nenhuma sessão contextual'}
                        {historyFilter === 'all' && 'Nenhuma conversa anterior'}
                      </p>
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm">Nenhuma conversa anterior</p>
                    </div>
                  )}
                </motion.div>
              ) : (
                // 💬 ÁREA DE CHAT
                <motion.div
                  key="chat"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="h-full"
                >
                  {messages.length === 0 ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex flex-col items-center justify-center h-full text-center py-12"
                  >
                    <div className="p-4 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl border-2 border-black mb-6">
                      <Bot className="h-12 w-12 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">
                      Olá! Eu sou o Dr. Will 🩺
                    </h3>
                    <p className="text-gray-600 mb-4 max-w-md">
                      Pronto para ajudá-lo na preparação para residência médica.
                    </p>
                    <p className="text-sm text-gray-500">
                      Digite sua primeira pergunta para começarmos!
                    </p>
                  </motion.div>
                ) : (
                  <>
                    {/* Mensagens */}
                    {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      ref={(el) => (messageRefs.current[message.id] = el)}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="flex flex-col"
                      data-message-id={message.id}
                      data-is-user={message.isUser}
                      style={{ scrollMarginTop: '16px' }}
                    >
                  {/* Avatar acima da mensagem */}
                  <div className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} mb-2`}>
                    {message.isUser ? (
                      <Avatar className={`${isMobile ? 'h-8 w-8' : 'h-10 w-10'} border-2 border-blue-500`}>
                        <AvatarImage
                          src={profile?.avatar_url}
                          alt={profile?.full_name || user?.email || "Usuário"}
                        />
                        <AvatarFallback className="bg-blue-500 text-white font-bold">
                          {profile?.full_name ? getInitials(profile.full_name) :
                           user?.email ? getInitials(user.email) : "U"}
                        </AvatarFallback>
                      </Avatar>
                    ) : (
                      <div className={`p-2 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg ${isMobile ? 'h-8 w-8' : 'h-10 w-10'} flex items-center justify-center`}>
                        <Stethoscope className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'} text-white`} />
                      </div>
                    )}
                  </div>

                  {/* Mensagem com largura total */}
                  <div className={`${message.isUser ? 'flex justify-end' : 'flex justify-start'}`}>
                    <div className={`${isMobile ? 'max-w-[95%]' : 'max-w-[90%]'}`}>
                    <div
                      className={`p-4 rounded-2xl ${
                        message.isUser
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-auto shadow-lg'
                          : 'bg-gray-50 text-gray-800 border border-gray-200'
                      }`}
                    >
                      {/* Conteúdo da mensagem com design especial para thinking */}
                      {message.isThinking ? (
                        // 🧠 DESIGN COMPACTO COM TOGGLE PARA THINKING MODE
                        <ThinkingModeComponent
                          content={message.content}
                          isStreaming={message.isStreaming}
                          formatThinkingContent={formatThinkingContent}
                        />
                      ) : (
                        // Conteúdo normal da mensagem
                        <>
                          <MemoizedMessageContent
                            content={message.content}
                            isStreaming={message.isStreaming}
                            messageId={message.id}
                            isDarkMode={isDarkMode}
                          />
                          {message.isStreaming && message.content && !message.isThinking && (
                            <div className="flex items-center gap-2 mt-3 pt-2 border-t border-gray-200">
                              <div className="flex gap-1">
                                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                              <span className="text-xs text-gray-500">Dr. Will está respondendo...</span>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 mt-1 px-2">
                      {message.timestamp.toLocaleTimeString('pt-BR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                    </div>
                  </div>
                  </motion.div>
                    ))}
                  </>
                )}
              </motion.div>
            )}
            </AnimatePresence>
            <div ref={messagesEndRef} />
          </div>



          {/* Input Area */}
          <div className={`flex-shrink-0 border-t-2 border-black ${isMobile ? 'p-3' : 'p-4'} bg-gray-50`}>
            <div className={`flex ${isMobile ? 'gap-2' : 'gap-3'}`}>
              <Textarea
                ref={textareaRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Digite sua pergunta médica..."
                className={`flex-1 ${isMobile ? 'min-h-[50px] max-h-[100px]' : 'min-h-[60px] max-h-[120px]'} resize-none border-2 border-gray-300 rounded-xl focus:border-purple-500 focus:ring-0`}
                disabled={isLoading}
              />
              {isLoading ? (
                <Button
                  onClick={cancelRequest}
                  className={`bg-red-500 hover:bg-red-600 text-white border-2 border-black rounded-xl ${isMobile ? 'px-3 py-2' : 'px-6 py-3'} font-semibold shadow-lg hover:shadow-xl transition-all duration-200`}
                >
                  <X className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                </Button>
              ) : (
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim()}
                  className={`bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white border-2 border-black rounded-xl ${isMobile ? 'px-3 py-2' : 'px-6 py-3'} font-semibold shadow-lg hover:shadow-xl transition-all duration-200`}
                >
                  <Send className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                </Button>
              )}
            </div>

            {isLoading && !isStreaming && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="mt-4"
              >
                <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl border-2 border-purple-200 p-6 shadow-lg">
                  <div className="flex items-center gap-4">
                    {/* Animated Brain Icon */}
                    <div className="relative">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center">
                        <Brain className="h-6 w-6 text-white animate-pulse" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-400 rounded-full animate-ping"></div>
                      <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-indigo-400 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-lg font-semibold text-purple-800">Dr. Will está analisando</h4>
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                        </div>
                      </div>
                      <p className="text-purple-600 text-sm">
                        Processando sua pergunta médica com raciocínio clínico estruturado
                      </p>

                      {/* Progress bar animation */}
                      <div className="mt-3 w-full bg-purple-200 rounded-full h-1.5">
                        <div className="bg-gradient-to-r from-purple-500 to-indigo-500 h-1.5 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}



            {error && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{error}</p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  size="sm"
                  className="mt-2 text-red-600 border-red-300 hover:bg-red-50"
                >
                  Tentar Novamente
                </Button>
              </div>
            )}
          </div>
        </motion.div>






      </div>

      {/* Table Dialog */}
      {tableDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className={`rounded-2xl shadow-2xl w-full max-w-7xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden border transition-colors duration-200 ${
            isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
          }`}>
            {/* Dialog Header */}
            <div className="bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-600 text-white p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                    <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-lg sm:text-xl font-bold">Tabela Diagnóstica</h2>
                    <p className="text-white text-opacity-90 text-xs sm:text-sm">Dr. Will • MedEvo • Diagnósticos Diferenciais</p>
                  </div>
                </div>
                <button
                  onClick={() => setTableDialogOpen(false)}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-xl transition-all duration-200 hover:scale-105"
                >
                  <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>

            {/* Dialog Content */}
            <div className="overflow-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)]">
              {currentTableData.length > 0 && (
                <>
                  {/* Mobile View - Card Layout */}
                  <div className="block sm:hidden p-4 space-y-4">
                    {currentTableData.slice(1).map((row, rowIndex) => (
                      <div
                        key={rowIndex}
                        className={`border rounded-2xl p-4 shadow-sm transition-colors duration-200 ${
                          isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <span className="text-white text-sm font-bold">{rowIndex + 1}</span>
                          </div>
                          <div className={`text-sm font-semibold transition-colors duration-200 ${
                            isDarkMode ? 'text-gray-200' : 'text-gray-900'
                          }`}>
                            <div dangerouslySetInnerHTML={{ __html: row[0] || 'Diagnóstico' }} />
                          </div>
                        </div>

                        {row.slice(1).map((cell, cellIndex) => (
                          <div key={cellIndex} className="mb-3 last:mb-0">
                            <div className={`text-xs font-medium uppercase tracking-wide mb-1 transition-colors duration-200 ${
                              isDarkMode ? 'text-gray-400' : 'text-gray-500'
                            }`}>
                              <div dangerouslySetInnerHTML={{ __html: currentTableData[0]?.[cellIndex + 1] || `Campo ${cellIndex + 1}` }} />
                            </div>
                            <div className={`text-sm leading-relaxed transition-colors duration-200 ${
                              isDarkMode ? 'text-gray-300' : 'text-gray-700'
                            }`}>
                              <div dangerouslySetInnerHTML={{ __html: cell }} />
                            </div>
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>

                  {/* Desktop View - Table Layout */}
                  <div className="hidden sm:block p-6">
                    <div className="overflow-x-auto">
                      <table className={`min-w-full rounded-xl overflow-hidden shadow-sm border transition-colors duration-200 ${
                        isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
                      }`}>
                        <thead className={`transition-colors duration-200 ${
                          isDarkMode
                            ? 'bg-gradient-to-r from-gray-800 via-blue-900/30 to-purple-900/30'
                            : 'bg-gradient-to-r from-gray-50 via-blue-50 to-purple-50'
                        }`}>
                          <tr>
                            {currentTableData[0]?.map((header, index) => (
                              <th
                                key={index}
                                className={`px-6 py-4 text-left text-sm font-bold border-b-2 transition-colors duration-200 ${
                                  isDarkMode
                                    ? 'text-gray-200 border-gray-600'
                                    : 'text-gray-900 border-gray-300'
                                }`}
                              >
                                <div dangerouslySetInnerHTML={{ __html: header }} />
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {currentTableData.slice(1).map((row, rowIndex) => (
                            <tr
                              key={rowIndex}
                              className={`transition-colors duration-200 ${
                                isDarkMode
                                  ? `${rowIndex % 2 === 0 ? 'bg-gray-700' : 'bg-gray-800'} hover:bg-blue-900/30`
                                  : `${rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50`
                              }`}
                            >
                              {row.map((cell, cellIndex) => (
                                <td
                                  key={cellIndex}
                                  className={`px-6 py-4 text-sm border-b align-top transition-colors duration-200 ${
                                    isDarkMode
                                      ? 'text-gray-300 border-gray-600'
                                      : 'text-gray-700 border-gray-200'
                                  }`}
                                >
                                  <div dangerouslySetInnerHTML={{ __html: cell }} />
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Dialog Footer */}
            <div className={`px-4 sm:px-6 py-4 border-t transition-colors duration-200 ${
              isDarkMode
                ? 'bg-gradient-to-r from-gray-800 to-gray-700 border-gray-600'
                : 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200'
            }`}>
              <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span className="hidden sm:inline">
                    {currentTableData.length - 1} diagnósticos diferenciais • Dr. Will MedEvo
                  </span>
                  <span className="sm:hidden">
                    {currentTableData.length - 1} diagnósticos
                  </span>
                </div>
                <button
                  onClick={() => setTableDialogOpen(false)}
                  className="w-full sm:w-auto bg-gradient-to-r from-emerald-500 to-blue-600 hover:from-emerald-600 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 hover:scale-105 shadow-lg"
                >
                  <span className="flex items-center justify-center gap-2">
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Fechar
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Diagnostics Modal */}
      <DrWillDiagnostics
        isOpen={showDiagnostics}
        onClose={() => setShowDiagnostics(false)}
      />

      {/* Mermaid Modal */}
      <MermaidModal
        isOpen={mermaidModalOpen}
        onClose={() => setMermaidModalOpen(false)}
        mermaidCode={currentMermaidCode}
      />

      {/* Dialog de Sessão Inativa - Copiado do FloatingChatButton */}
      <SessionInactiveDialog
        open={sessionDialogOpen}
        onOpenChange={setSessionDialogOpen}
        sessionTitle={selectedSessionTitle}
        sessionId={selectedSessionId}
      />
    </div>
  );
}; // Fechamento da função DrWill

export default DrWill;
