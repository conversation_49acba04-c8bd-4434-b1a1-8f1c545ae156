
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Flame, CheckCircle, Trophy, RefreshCw, HelpCircle, Calendar, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useStreakSystem, isDayActive } from '@/hooks/useOptimizedStreakStats';
import { format, startOfWeek, addDays, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useAuth } from '@/contexts/AuthContext';

interface StudyStreakProps {
  enabled?: boolean;
}

type DayActivityProps = {
  day: string;
  shortDay: string;
  active: boolean;
  index: number;
}

const DayActivity = ({ day, shortDay, active, index }: DayActivityProps) => {
  // Verificar se é hoje
  const today = new Date();
  const dayDate = addDays(startOfWeek(today, { weekStartsOn: 0 }), index);
  const isCurrentDay = isToday(dayDate);

  return (
    <motion.div
      className="flex flex-col items-center flex-1 min-w-0"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 * index }}
    >
      <span className={`text-xs font-bold mb-2 truncate w-full text-center transition-colors ${
        isCurrentDay
          ? 'text-orange-600'
          : active
            ? 'text-orange-500'
            : 'text-gray-500'
      }`}>
        {shortDay}
      </span>
      <div className={`
        w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center flex-shrink-0 border-2
        ${active
          ? 'bg-gradient-to-br from-orange-400 to-orange-600 text-white shadow-lg shadow-orange-200 border-orange-300 ring-2 ring-orange-200'
          : isCurrentDay
            ? 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600 border-blue-300 ring-2 ring-blue-200'
            : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-400 border-gray-300 hover:from-gray-200 hover:to-gray-300'
        }
        transition-all duration-300 ease-in-out transform hover:scale-105
      `}>
        {active ? (
          <CheckCircle className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
        ) : isCurrentDay ? (
          <Star className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
        ) : null}
      </div>
    </motion.div>
  );
};

// Frases motivacionais para cada dia da semana
const MOTIVATIONAL_QUOTES = [
  "A educação vem da experiência, não do conforto.", // Domingo
  "Trabalho em equipe salva vidas.", // Segunda
  "Veja além do óbvio, questione sempre.", // Terça
  "Cada paciente é único e especial.", // Quarta
  "Sua história pode inspirar outros.", // Quinta
  "A ciência revela os mistérios da vida.", // Sexta
  "Um sorriso é o melhor remédio." // Sábado
];

const StudyStreak: React.FC<StudyStreakProps> = ({ enabled = true }) => {
  const { user } = useAuth();
  const {
    currentStreak,
    maxStreak,
    weekActivities,
    isLoading,
    hasError,
    refreshStats
  } = useStreakSystem(enabled);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Obter o horário para a saudação
  const currentHour = new Date().getHours();
  const greeting = currentHour < 12
    ? "Bom dia"
    : currentHour < 18
      ? "Boa tarde"
      : "Boa noite";

  // Obter o dia da semana (0-6, onde 0 é domingo)
  const today = new Date();
  const dayOfWeek = today.getDay();
  const quoteOfTheDay = MOTIVATIONAL_QUOTES[dayOfWeek];

  // Função para atualizar estatísticas
  const handleRefreshStats = async () => {
    setIsRefreshing(true);
    try {
      await refreshStats();
    } catch (error) {
      // Error handled silently
    } finally {
      setIsRefreshing(false);
    }
  };

  // Componente de diálogo de ajuda para sequências
  const StreakHelpDialog = () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 rounded-full hover:bg-gray-100 transition-colors"
          title="Como funciona a sequência?"
        >
          <HelpCircle className="h-3.5 w-3.5 text-gray-400" />
        </Button>
      </DialogTrigger>
      <DialogContent className="w-[80dvw] max-w-md max-h-[80dvh] overflow-y-auto rounded-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Flame className="h-5 w-5 text-orange-500" />
            Sistema de Sequência de Estudos
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-semibold text-gray-800 mb-2">🔥 O que conta como "Dia de Estudo"?</h4>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Responder 1+ questão</strong>
                  <p className="text-gray-600">Qualquer questão respondida na plataforma</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Completar 1+ sessão</strong>
                  <p className="text-gray-600">Simulados, revisões ou estudos dirigidos</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Marcar cronograma como estudado</strong>
                  <p className="text-gray-600">Itens do seu cronograma pessoal</p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-gray-800 mb-2">📊 Como funciona o cálculo?</h4>
            <div className="space-y-2">
              <div>
                <strong className="text-orange-600">Sequência Atual:</strong>
                <p className="text-gray-600">Dias consecutivos de estudo (incluindo hoje ou ontem)</p>
              </div>
              <div>
                <strong className="text-amber-600">Recorde:</strong>
                <p className="text-gray-600">Sua maior sequência histórica (nunca diminui)</p>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-gray-800 mb-2">⏰ Regras de Tempo</h4>
            <div className="space-y-1 text-gray-600">
              <p>• Baseado no seu fuso horário local</p>
              <p>• Dia = 00:00 às 23:59 (horário local)</p>
              <p>• Múltiplas atividades no mesmo dia = 1 dia</p>
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <p className="text-blue-800 text-sm">
              <strong>💡 Dica:</strong> Responder apenas 1 questão por dia já mantém sua sequência ativa!
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  // Gerar os 7 dias da semana, começando pelo domingo (padrão brasileiro)
  const weekDays = Array.from({ length: 7 }, (_, i) => {
    const date = addDays(startOfWeek(new Date(), { weekStartsOn: 0 }), i); // 0 = domingo
    return {
      date,
      fullName: format(date, 'EEEE', { locale: ptBR }),
      shortName: format(date, 'EEEEE', { locale: ptBR }).toUpperCase(),
      active: isDayActive(date, weekActivities),
      key: `day-${i}` // Chave única para cada dia
    };
  });

  // Obter o nome do usuário dos metadados ou usar um nome padrão
  const userName = user?.user_metadata?.name || user?.user_metadata?.full_name || "Estudante";

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-br from-white to-orange-50/30 rounded-xl p-4 sm:p-6 shadow-lg border-2 border-orange-100 w-full max-w-md mx-auto lg:mx-0 hover:shadow-xl transition-all duration-300"
    >
      {/* Header with streak info */}
      <div className="flex items-center gap-4 mb-6">
        <div className="bg-gradient-to-br from-orange-400 to-orange-600 p-3 rounded-full flex-shrink-0 shadow-lg border-2 border-orange-300">
          <Flame className="h-6 w-6 text-white" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between gap-2">
            <div className="flex flex-col">
              <span className="text-lg font-bold text-orange-600 truncate">
                {isLoading ? 'Carregando...' : `${currentStreak} ${currentStreak === 1 ? 'dia' : 'dias'}`}
              </span>
              <span className="text-xs text-orange-500 font-medium">
                de sequência
              </span>
            </div>

            <div className="flex items-center gap-2 flex-shrink-0">
              {maxStreak > 0 && (
                <div className="flex items-center bg-amber-50 px-2 py-1 rounded-full border border-amber-200">
                  <Trophy className="h-3.5 w-3.5 mr-1 text-amber-600" />
                  <span className="text-xs font-bold text-amber-700">Recorde: {maxStreak}</span>
                </div>
              )}

              {/* Botão de ajuda */}
              <StreakHelpDialog />

              {/* Botão de refresh */}
              <button
                onClick={handleRefreshStats}
                disabled={isRefreshing || isLoading}
                className="p-1.5 rounded-full hover:bg-orange-100 transition-colors disabled:opacity-50 border border-orange-200"
                title="Atualizar estatísticas"
              >
                <RefreshCw className={`h-3.5 w-3.5 text-orange-500 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>

          {/* Indicador de erro */}
          {hasError && (
            <div className="text-xs text-red-500 mt-2 bg-red-50 px-2 py-1 rounded border border-red-200">
              Erro ao carregar dados. Clique em ↻ para tentar novamente.
            </div>
          )}
        </div>
      </div>

      {/* Greeting */}
      <div className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
        <p className="text-base font-bold text-gray-800 mb-3 flex items-center gap-2">
          <Calendar className="h-4 w-4 text-blue-500" />
          {greeting}, {userName}!
        </p>

        {/* Quote of the day */}
        <blockquote className="text-sm italic text-gray-700 border-l-4 border-gradient-to-b from-blue-400 to-purple-400 pl-4 leading-relaxed bg-white/50 p-3 rounded-r-lg">
          "{quoteOfTheDay}"
        </blockquote>
      </div>

      {/* Week activity tracker */}
      <div className="bg-gradient-to-r from-gray-50 to-orange-50/50 p-4 rounded-lg border border-gray-200">
        <div className="flex items-center gap-2 mb-3">
          <Calendar className="h-4 w-4 text-gray-600" />
          <span className="text-sm font-bold text-gray-700">Atividade da Semana</span>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            Dom → Sáb
          </span>
        </div>
        <div className="flex justify-between items-center gap-2 sm:gap-3">
          {weekDays.map((day, index) => (
            <DayActivity
              key={day.key}
              day={day.fullName}
              shortDay={day.shortName}
              active={day.active}
              index={index}
            />
          ))}
        </div>
      </div>


    </motion.div>
  );
};

export default StudyStreak;
