import React, { useEffect, useState } from 'react';
import { CheckCircle } from 'lucide-react';

interface ImportFeedbackProps {
  count: number;
  onComplete: () => void;
}

export const ImportFeedback: React.FC<ImportFeedbackProps> = ({ count, onComplete }) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsExiting(true);
      setTimeout(onComplete, 300); // Aguarda a animação de saída
    }, 3000);

    return () => clearTimeout(timer);
  }, [onComplete]);

  return (
    <div className={`fixed top-4 right-4 z-50 transform transition-all duration-300 ease-out ${
      isExiting ? 'translate-x-full opacity-0' : 'translate-x-0 opacity-100'
    }`}>
      <div className="bg-green-500 text-white px-4 py-3 rounded-lg shadow-lg flex items-center gap-2">
        <CheckCircle className="w-5 h-5" />
        <span className="font-medium">
          {count} card{count > 1 ? 's' : ''} importado{count > 1 ? 's' : ''}!
        </span>
      </div>
    </div>
  );
};
