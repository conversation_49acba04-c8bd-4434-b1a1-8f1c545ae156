import React, { useState, useEffect } from 'react';
import { <PERSON>, Clock, Calendar, BookOpen, Info, Sun, Moon } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import Header from '@/components/Header';
import StudyNavBar from '@/components/study/StudyNavBar';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card } from '@/components/ui/card';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"

interface RankingUser {
  id: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  rank: number;
  score: number;
  questions_answered?: number;
  is_current_user?: boolean;
}

const timePeriods = [
  { id: 'day', name: 'Hoje', icon: <Sun className="h-5 w-5" /> },
  { id: 'yesterday', name: 'Ontem', icon: <Moon className="h-5 w-5" /> },
  { id: 'week', name: 'Semanal', icon: <Calendar className="h-5 w-5" /> },
  { id: 'all', name: 'Todo Período', icon: <Clock className="h-5 w-5" /> }
];

const QuestionRanking = () => {
  const [activeTab, setActiveTab] = useState('day');
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [rankingData, setRankingData] = useState<RankingUser[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isRequestInProgress, setIsRequestInProgress] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  // Garantir que a página sempre comece no topo
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleTabChange = async (newTab: string) => {
    if (newTab === activeTab || isRequestInProgress) return;

    console.log(`🔄 [QuestionRanking] Iniciando mudança de aba: ${activeTab} → ${newTab}`);

    setIsRequestInProgress(true);
    setIsTransitioning(true);
    setError(null);

    try {
      // Pequeno delay para suavizar a transição
      await new Promise(resolve => setTimeout(resolve, 150));

      console.log(`📡 [QuestionRanking] Fazendo request para: ${newTab}`);

      // Usar função RPC segura para buscar ranking
      const { data: rankingData, error: rankingError } = await supabase.rpc('get_questions_ranking', {
        p_period: newTab,
        p_limit: 100
      });

      if (rankingError) throw rankingError;

      // Transformar dados para o formato esperado
      const transformedRanking: RankingUser[] = rankingData?.map((user: any) => ({
        id: user.user_id || `anonymous-${user.rank_position}`, // ID anônimo para usuários não-atuais
        username: user.first_name || 'Usuário...',
        full_name: user.first_name || 'Usuário...',
        avatar_url: null, // Não expor avatar URLs
        rank: user.rank_position,
        score: user.questions_answered,
        questions_answered: user.questions_answered,
        is_current_user: user.is_current_user || false
      })) || [];

      setRankingData(transformedRanking);
      setActiveTab(newTab);

      console.log(`✅ [QuestionRanking] Mudança concluída para: ${newTab}`);

    } catch (error: any) {
      console.error('❌ [QuestionRanking] Error in fetchRankingData:', error);
      setError(error.message || 'Failed to load ranking data');
      toast({
        variant: "destructive",
        title: "Erro ao carregar ranking",
        description: "Não foi possível buscar os dados do ranking.",
      });
    } finally {
      setIsTransitioning(false);
      setIsRequestInProgress(false);
    }
  };

  // Carregamento inicial apenas
  useEffect(() => {
    const fetchInitialData = async () => {
      console.log(`🚀 [QuestionRanking] Carregamento inicial para: ${activeTab}`);

      setIsLoading(true);
      setError(null);

      try {
        // Usar função RPC segura para buscar ranking
        const { data: rankingData, error: rankingError } = await supabase.rpc('get_questions_ranking', {
          p_period: activeTab,
          p_limit: 100
        });

        if (rankingError) throw rankingError;

        // Transformar dados para o formato esperado
        const transformedRanking: RankingUser[] = rankingData?.map((user: any) => ({
          id: user.user_id || `anonymous-${user.rank_position}`, // ID anônimo para usuários não-atuais
          username: user.first_name || 'Usuário...',
          full_name: user.first_name || 'Usuário...',
          avatar_url: null, // Não expor avatar URLs
          rank: user.rank_position,
          score: user.questions_answered,
          questions_answered: user.questions_answered,
          is_current_user: user.is_current_user || false
        })) || [];

        setRankingData(transformedRanking);
        console.log(`✅ [QuestionRanking] Carregamento inicial concluído`);

      } catch (error: any) {
        console.error('❌ [QuestionRanking] Error in fetchInitialData:', error);
        setError(error.message || 'Failed to load ranking data');
        toast({
          variant: "destructive",
          title: "Erro ao carregar ranking",
          description: "Não foi possível buscar os dados do ranking.",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, [toast]); // Apenas no mount inicial

  const currentUserRanking = user ? rankingData.find(u => u.is_current_user) : undefined;
  const topThree = rankingData.slice(0, 3);
  const topTen = rankingData.slice(0, 10);
  const remainingTopTen = rankingData.slice(3, 10);

  return (
    <div className="min-h-screen bg-[#FEF7CD]">
      <Header />
      <StudyNavBar className="mb-8" />

      <div className="container max-w-6xl mx-auto px-4 pt-8 space-y-8 animate-fade-in">
        <div className="relative mb-8">
          <h1 className="text-4xl sm:text-5xl font-black leading-none mb-4">
            <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1">
              Ranking de Questões
            </span>
          </h1>

          {currentUserRanking && (
            <Card className="p-6 border-2 border-black bg-white shadow-card-sm my-8">
              <div className="flex items-center gap-4">
                <div className="h-16 w-16 rounded-full bg-hackathon-yellow border-2 border-black flex items-center justify-center text-2xl font-bold">
                  #{currentUserRanking.rank}
                </div>
                <div>
                  <h2 className="text-xl font-bold">Seu Ranking</h2>
                  <p className="text-gray-600">
                    Você está na posição <span className="font-bold">{currentUserRanking.rank}º</span>
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    Total de questões respondidas: <span className="font-bold">{currentUserRanking.questions_answered}</span>
                  </p>
                </div>
              </div>
            </Card>
          )}

          <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid grid-cols-2 sm:grid-cols-4 gap-1 sm:gap-2 bg-transparent">
              {timePeriods.map(period => (
                <TabsTrigger
                  key={period.id}
                  value={period.id}
                  className="flex items-center gap-1 sm:gap-2 border-2 border-black data-[state=active]:bg-hackathon-yellow data-[state=active]:text-black px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm"
                >
                  <span className="hidden sm:inline">{period.icon}</span>
                  <span className="sm:hidden text-xs">{period.icon}</span>
                  <span className="truncate">{period.name}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {timePeriods.map(period => (
              <TabsContent key={period.id} value={period.id} className="mt-6">
                {(isLoading || isTransitioning) ? (
                  <div className="space-y-6">
                    {/* Loading skeleton para o pódio */}
                    <div className="mb-10">
                      <div className="h-8 w-48 bg-gray-200 rounded animate-pulse mb-6"></div>
                      <div className="flex flex-row justify-center items-end gap-2 sm:gap-4 md:gap-8 px-2">
                        <div className="flex flex-col items-center flex-1 max-w-[80px] sm:max-w-none">
                          <div className="w-8 h-8 sm:w-16 sm:h-16 md:w-20 md:h-20 bg-gray-200 rounded-full animate-pulse mb-1 sm:mb-2"></div>
                          <div className="h-16 sm:h-32 md:h-40 w-full bg-gray-200 rounded-t-lg animate-pulse"></div>
                        </div>
                        <div className="flex flex-col items-center flex-1 max-w-[90px] sm:max-w-none">
                          <div className="w-10 h-10 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-gray-200 rounded-full animate-pulse mb-1 sm:mb-2"></div>
                          <div className="h-20 sm:h-40 md:h-52 w-full bg-gray-200 rounded-t-lg animate-pulse"></div>
                        </div>
                        <div className="flex flex-col items-center flex-1 max-w-[80px] sm:max-w-none">
                          <div className="w-8 h-8 sm:w-16 sm:h-16 md:w-20 md:h-20 bg-gray-200 rounded-full animate-pulse mb-1 sm:mb-2"></div>
                          <div className="h-14 sm:h-28 md:h-36 w-full bg-gray-200 rounded-t-lg animate-pulse"></div>
                        </div>
                      </div>
                    </div>

                    {/* Loading skeleton para a tabela */}
                    <div className="bg-white border-2 border-black rounded-lg shadow-card-sm overflow-hidden">
                      <div className="p-4 bg-gray-50">
                        <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                      <div className="space-y-3 p-4">
                        {[...Array(7)].map((_, i) => (
                          <div key={i} className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
                            <div className="flex-1 h-4 bg-gray-200 rounded animate-pulse"></div>
                            <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="animate-fade-in">
                {topThree.length > 0 && (
                  <div className="mb-10">
                    <div className="flex items-center gap-2 mb-6">
                      <h2 className="text-2xl font-bold border-b-2 border-black pb-2">Top 3 Estudantes</h2>
                      <HoverCard>
                        <HoverCardTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Info className="h-5 w-5" />
                          </Button>
                        </HoverCardTrigger>
                        <HoverCardContent className="w-80 bg-white border-2 border-black shadow-lg">
                          <div className="space-y-2">
                            <h4 className="text-sm font-semibold text-black">Como chegar ao Top 3?</h4>
                            <p className="text-sm text-gray-700">
                              Os três primeiros lugares são conquistados pelos estudantes que mais responderam
                              questões no período selecionado. Mantenha uma rotina constante de estudos para
                              alcançar as primeiras posições!
                            </p>
                          </div>
                        </HoverCardContent>
                      </HoverCard>
                    </div>
                    <div className="flex flex-row justify-center items-end gap-2 sm:gap-4 md:gap-8 px-2">
                      {topThree[1] && (
                        <div className="flex flex-col items-center flex-1 max-w-[80px] sm:max-w-none">
                          <div className="w-8 h-8 sm:w-16 sm:h-16 md:w-20 md:h-20 border-2 border-black mb-1 sm:mb-2 rounded-full bg-hackathon-red flex items-center justify-center">
                            <span className="text-white text-xs sm:text-base font-bold">
                              {topThree[1].full_name?.charAt(0) || '2'}
                            </span>
                          </div>
                          <div className="h-16 sm:h-32 md:h-40 w-full bg-hackathon-red border-2 border-black rounded-t-lg flex flex-col items-center justify-end p-1 sm:p-3 relative shadow-card-sm">
                            <Trophy className="w-3 h-3 sm:w-8 sm:h-8 text-white absolute top-0.5 right-0.5 sm:top-2 sm:right-2" />
                            <div className="text-white text-xs sm:text-lg font-bold">{topThree[1].questions_answered}</div>
                            <div className="text-white text-[8px] sm:text-xs truncate w-full text-center leading-tight">
                              {topThree[1].full_name}
                            </div>
                            <div className="bg-white text-black font-bold rounded-full w-4 h-4 sm:w-8 sm:h-8 flex items-center justify-center absolute -top-2 sm:-top-4 text-[8px] sm:text-sm">2</div>
                          </div>
                        </div>
                      )}

                      {topThree[0] && (
                        <div className="flex flex-col items-center flex-1 max-w-[90px] sm:max-w-none">
                          <div className="w-10 h-10 sm:w-20 sm:h-20 md:w-24 md:h-24 border-2 border-black mb-1 sm:mb-2 rounded-full bg-hackathon-yellow flex items-center justify-center">
                            <span className="text-black text-sm sm:text-base font-bold">
                              {topThree[0].full_name?.charAt(0) || '1'}
                            </span>
                          </div>
                          <div className="h-20 sm:h-40 md:h-52 w-full bg-hackathon-yellow border-2 border-black rounded-t-lg flex flex-col items-center justify-end p-1 sm:p-3 relative shadow-card-sm">
                            <Trophy className="w-4 h-4 sm:w-10 sm:h-10 text-black absolute top-0.5 right-0.5 sm:top-2 sm:right-2" />
                            <div className="text-black text-sm sm:text-2xl font-bold">{topThree[0].questions_answered}</div>
                            <div className="text-black text-[8px] sm:text-sm truncate w-full text-center font-bold leading-tight">
                              {topThree[0].full_name}
                            </div>
                            <div className="bg-black text-white font-bold rounded-full w-5 h-5 sm:w-10 sm:h-10 flex items-center justify-center absolute -top-2 sm:-top-5 text-[10px] sm:text-base">1</div>
                          </div>
                        </div>
                      )}

                      {topThree[2] && (
                        <div className="flex flex-col items-center flex-1 max-w-[80px] sm:max-w-none">
                          <div className="w-8 h-8 sm:w-16 sm:h-16 md:w-20 md:h-20 border-2 border-black mb-1 sm:mb-2 rounded-full bg-hackathon-green flex items-center justify-center">
                            <span className="text-black text-xs sm:text-base font-bold">
                              {topThree[2].full_name?.charAt(0) || '3'}
                            </span>
                          </div>
                          <div className="h-14 sm:h-28 md:h-36 w-full bg-hackathon-green border-2 border-black rounded-t-lg flex flex-col items-center justify-end p-1 sm:p-3 relative shadow-card-sm">
                            <Trophy className="w-3 h-3 sm:w-8 sm:h-8 text-black absolute top-0.5 right-0.5 sm:top-2 sm:right-2" />
                            <div className="text-black text-xs sm:text-lg font-bold">{topThree[2].questions_answered}</div>
                            <div className="text-black text-[8px] sm:text-xs truncate w-full text-center leading-tight">
                              {topThree[2].full_name}
                            </div>
                            <div className="bg-white text-black font-bold rounded-full w-4 h-4 sm:w-8 sm:h-8 flex items-center justify-center absolute -top-2 sm:-top-4 text-[8px] sm:text-sm">3</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="bg-white border-2 border-black rounded-lg shadow-card-sm overflow-hidden">
                  <div className="overflow-x-auto">
                    <Table>
                    <TableHeader>
                      <TableRow className="bg-black text-white border-b-0">
                        <TableHead className="w-12 sm:w-16 text-white text-xs sm:text-sm p-2 sm:p-4">Pos.</TableHead>
                        <TableHead className="text-white text-xs sm:text-sm p-2 sm:p-4">Estudante</TableHead>
                        <TableHead className="text-right text-white text-xs sm:text-sm p-2 sm:p-4">
                          <div className="flex items-center justify-end gap-2">
                            <span className="hidden sm:inline">Questões Respondidas</span>
                            <span className="sm:hidden">Questões</span>
                            <HoverCard>
                              <HoverCardTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-6 w-6">
                                  <Info className="h-4 w-4 text-white" />
                                </Button>
                              </HoverCardTrigger>
                              <HoverCardContent className="w-80 bg-white border-2 border-black shadow-lg">
                                <div className="space-y-2">
                                  <h4 className="text-sm font-semibold text-black">Contagem de Questões</h4>
                                  <p className="text-sm text-gray-700">
                                    Este número representa o total de questões únicas respondidas por cada estudante
                                    no período selecionado. Cada questão é contada apenas uma vez, mesmo que tenha
                                    sido respondida múltiplas vezes.
                                  </p>
                                </div>
                              </HoverCardContent>
                            </HoverCard>
                          </div>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-6">
                            Carregando ranking...
                          </TableCell>
                        </TableRow>
                      ) : rankingData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-6">
                            Nenhum usuário com pontuação disponível.
                          </TableCell>
                        </TableRow>
                      ) : (
                        remainingTopTen.map((user) => (
                          <TableRow 
                            key={user.id}
                            className={user.id === currentUserRanking?.id 
                              ? "bg-hackathon-yellow/20 hover:bg-hackathon-yellow/30" 
                              : ""
                            }
                          >
                            <TableCell className="font-medium text-xs sm:text-sm p-2 sm:p-4">{user.rank}</TableCell>
                            <TableCell className="p-2 sm:p-4">
                              <div className="flex items-center gap-2 sm:gap-3">
                                <div className={`w-5 h-5 sm:w-8 sm:h-8 border border-gray-200 rounded-full flex items-center justify-center text-xs font-bold ${
                                  user.is_current_user ? 'bg-hackathon-yellow text-black' : 'bg-gray-100 text-gray-600'
                                }`}>
                                  {user.full_name?.charAt(0) || '?'}
                                </div>
                                <span className="font-medium text-xs sm:text-sm truncate max-w-[80px] sm:max-w-none">{user.full_name}</span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right font-bold text-xs sm:text-sm p-2 sm:p-4">
                              {user.questions_answered}
                            </TableCell>
                          </TableRow>
                        ))
                      )}

                      {/* Mostrar posição do usuário atual se não estiver no top 10 */}
                      {currentUserRanking && currentUserRanking.rank > 10 && (
                        <>
                          <TableRow>
                            <TableCell colSpan={3} className="text-center py-2 text-gray-500 text-sm">
                              ...
                            </TableCell>
                          </TableRow>
                          <TableRow className="bg-hackathon-yellow/20 hover:bg-hackathon-yellow/30 border-t-2 border-hackathon-yellow">
                            <TableCell className="font-medium text-xs sm:text-sm">{currentUserRanking.rank}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2 sm:gap-3">
                                <div className="w-5 h-5 sm:w-8 sm:h-8 border border-gray-200 rounded-full flex items-center justify-center text-xs font-bold bg-hackathon-yellow text-black">
                                  {currentUserRanking.full_name?.charAt(0) || '?'}
                                </div>
                                <span className="font-medium text-xs sm:text-sm truncate">{currentUserRanking.full_name} <span className="hidden sm:inline">(Você)</span></span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right font-bold text-xs sm:text-sm">
                              {currentUserRanking.questions_answered}
                            </TableCell>
                          </TableRow>
                        </>
                      )}
                    </TableBody>
                  </Table>
                  </div>
                </div>

                {/* Informação sobre o Top 10 */}
                <div className="mt-6 p-4 bg-blue-50 border-2 border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="h-5 w-5 text-blue-600" />
                    <h3 className="font-bold text-blue-800">Ranking Top 10</h3>
                  </div>
                  <p className="text-blue-700 text-sm">
                    Mostramos apenas os <strong>10 melhores estudantes</strong> para manter a competitividade.
                    {currentUserRanking && currentUserRanking.rank > 10 && (
                      <span> Sua posição atual é <strong>{currentUserRanking.rank}º lugar</strong> com <strong>{currentUserRanking.questions_answered} questões</strong> respondidas.</span>
                    )}
                    {currentUserRanking && currentUserRanking.rank <= 10 && (
                      <span> Parabéns! Você está no <strong>Top 10</strong> em <strong>{currentUserRanking.rank}º lugar</strong>! 🏆</span>
                    )}
                  </p>
                </div>
                  </div>
                )}
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default QuestionRanking;
