
import { DialogHeader as Header, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { BrainCircuit } from "lucide-react";
import { useDomain } from "@/hooks/useDomain";

export const DialogHeader = () => {
  const { domain } = useDomain();

  return (
    <Header className="mb-6">
      <div className="flex items-center gap-3">
        <div className="p-3 rounded-full bg-[#1CB0F6]/10">
          <BrainCircuit className="w-6 h-6 text-[#1CB0F6]" />
        </div>
        <div>
          <DialogTitle className="text-2xl font-bold text-slate-800">
            Personalizar com IA
          </DialogTitle>
          <DialogDescription className="text-base text-slate-600 mt-1">
            Configure suas preferências e a IA criará um cronograma personalizado
          </DialogDescription>
          {domain && (
            <div className="mt-1 text-sm font-medium text-blue-600">
              Utilizando tópicos do domínio: {domain}
            </div>
          )}
        </div>
      </div>
    </Header>
  );
};
