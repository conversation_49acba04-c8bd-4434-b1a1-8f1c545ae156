import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X, Maximize2, Clock, Send, History, Plus } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useDrWillHistory } from '@/hooks/useDrWillHistory';
import { useDrWillChat } from '@/hooks/useDrWillChat';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { formatDrWillMessage, formatThinkingContent } from '@/utils/messageFormatter';
import MermaidModal from '@/components/MermaidModal';
import { ThinkingModeComponent } from '@/components/ThinkingModeComponent';
import { useCurrentQuestionInfo, useCurrentQuestion } from '@/contexts/CurrentQuestionContext';
import { useDrWillContextualChat, type QuestionContext } from '@/hooks/useDrWillContextualChat';
import { supabase } from '@/integrations/supabase/client';
import { SessionInactiveDialog } from '@/components/SessionInactiveDialog';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface FloatingChatButtonProps {
  className?: string;
}

// 🎯 REMOVIDO: ThinkingModeComponent agora é compartilhado

// 🎯 CACHE GLOBAL SIMPLES (SEM HOOKS) - COMPARTILHADO ENTRE INSTÂNCIAS
const globalFormatCache = new Map<string, string>();

// 🎯 COMPONENTE MEMOIZADO COM CACHE INTELIGENTE (100% IGUAL AO DRWILL)
const MemoizedMessageContent = React.memo(({
  content,
  isStreaming,
  messageId,
  isDarkMode
}: {
  content: string;
  isStreaming: boolean;
  messageId: string;
  isDarkMode: boolean;
}) => {
  // 🎯 FORMATAÇÃO COM CACHE INTELIGENTE (IDÊNTICO AO DRWILL)
  const formattedContent = useMemo(() => {
    // Criar chave única para cache
    const cacheKey = `${messageId}_${content.length}_${isStreaming}_${isDarkMode}`;

    // Verificar cache primeiro
    if (globalFormatCache.has(cacheKey)) {
      // Cache hit - usar conteúdo existente
      return globalFormatCache.get(cacheKey)!;
    }

    // Limpar cache antigo para esta mensagem (diferentes modos)
    const oldKeys = Array.from(globalFormatCache.keys()).filter(key =>
      key.startsWith(`${messageId}_${content.length}_${isStreaming}_`)
    );
    oldKeys.forEach(key => globalFormatCache.delete(key));

    // Formatar novo conteúdo
    const formatted = formatDrWillMessage(content, isStreaming || false, isDarkMode);
    globalFormatCache.set(cacheKey, formatted);

    return formatted;
  }, [content, messageId, isStreaming, isDarkMode]); // Incluir isStreaming para cache correto

  return (
    <div
      className="text-sm break-words leading-relaxed"
      style={{
        wordBreak: 'break-word',
        overflowWrap: 'break-word'
      }}
      dangerouslySetInnerHTML={{
        __html: formattedContent
      }}
    />
  );
});

export const FloatingChatButton: React.FC<FloatingChatButtonProps> = ({ className = '' }) => {
  const location = useLocation();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const { isDarkMode } = useDarkMode();

  // Verificar se está na sessão de flashcards para ocultar no mobile
  const isFlashcardSession = location.pathname.includes('/flashcards/session');


  const [historyFilter, setHistoryFilter] = useState<'all' | 'general' | 'contextual'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 10;
  const [showContextWarning, setShowContextWarning] = useState(true);
  const [inputMessage, setInputMessage] = useState('');
  const [isContextualMode, setIsContextualMode] = useState(false);
  const [userDisabledContextual, setUserDisabledContextual] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // 🎯 Hook para informações da questão atual
  const {
    questionNumber,
    totalQuestions,
    sessionTitle,
    isInQuestionSession,
    questionId,
    specialty,
    theme,
    focus
  } = useCurrentQuestionInfo();

  // 🎯 Hook para contexto completo da questão
  const { currentQuestion, sessionId } = useCurrentQuestion();

  // State for Mermaid modal
  const [mermaidModalOpen, setMermaidModalOpen] = useState(false);
  const [currentMermaidCode, setCurrentMermaidCode] = useState('');

  // 🎯 State para dialog de sessão inativa
  const [sessionDialogOpen, setSessionDialogOpen] = useState(false);
  const [selectedSessionTitle, setSelectedSessionTitle] = useState('');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);



  // 🎯 Estado para "congelar" o contexto atual e evitar mudanças dinâmicas
  const [frozenContext, setFrozenContext] = useState<{
    sessionId: string | null;
    sessionTitle: string | null;
    isInQuestionSession: boolean;
  } | null>(null);

  // 🎯 MESMA ARQUITETURA do Dr. Will principal
  const [activeThreadId, setActiveThreadId] = useState<string | null>(null);

  // 🎯 HARMONIZAÇÃO: Mesma flag do DrWill para forçar nova thread
  const [forceNewThread, setForceNewThread] = useState(false);

  const {
    currentThreadId,
    threads,
    loadMessages: loadHistoryMessages,
    saveMessage: saveToHistory,
    createNewThread,
    findOrCreateContextualThread,
    getConversationHistory,
    loadThreads,
    historyIsLoading: threadsLoading
  } = useDrWillHistory();

  // 🎯 Congelar contexto quando uma sessão está ativa para evitar mudanças dinâmicas
  useEffect(() => {


    if (isInQuestionSession && sessionId && sessionTitle) {
      // Só atualizar se realmente mudou
      if (!frozenContext ||
          frozenContext.sessionId !== sessionId ||
          frozenContext.sessionTitle !== sessionTitle ||
          frozenContext.isInQuestionSession !== isInQuestionSession) {

        const newFrozenContext = {
          sessionId,
          sessionTitle,
          isInQuestionSession
        };


        setFrozenContext(newFrozenContext);
      }
    } else if (!isInQuestionSession) {
      // Limpar contexto congelado quando sair da sessão
      if (frozenContext) {

        setFrozenContext(null);
      }
    }
  }, [isInQuestionSession, sessionId, sessionTitle, frozenContext, currentThreadId, activeThreadId]);

  // 🎯 Criar objeto questionContext baseado nos dados disponíveis
  const questionContext = useMemo(() => {
    if (!isInQuestionSession || !sessionId || !sessionTitle) {
      return null;
    }

    return {
      sessionId,
      sessionTitle,
      questionIndex: questionNumber - 1,
      totalQuestions,
      questionId: questionId || '',
      specialty: specialty || '',
      theme: theme || '',
      focus: focus || ''
    };
  }, [isInQuestionSession, sessionId, sessionTitle, questionNumber, totalQuestions, questionId, specialty, theme, focus]);

  const {
    messages,
    sendMessage,
    isLoading,
    isStreaming,
    clearMessages,
    setMessages
  } = useDrWillChat({
    currentThreadId: activeThreadId || currentThreadId,
    saveToHistory,
    createNewThread,
    getConversationHistory
  });

  // 🎯 Hook para chat contextual
  const {
    messages: contextualMessages,
    sendContextualMessage,
    isLoading: isContextualLoading,
    isStreaming: isContextualStreaming,
    clearMessages: clearContextualMessages,
    setMessages: setContextualMessages
  } = useDrWillContextualChat({
    currentThreadId: activeThreadId || currentThreadId,
    saveToHistory,
    createNewThread,
    findOrCreateContextualThread,
    getConversationHistory,
    questionContext
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // 🎯 Funções auxiliares para gerenciar modo contextual vs normal
  const getCurrentMessages = () => {
    return isContextualMode ? contextualMessages : messages;
  };
  const getCurrentIsLoading = () => isContextualMode ? isContextualLoading : isLoading;
  const getCurrentIsStreaming = () => isContextualMode ? isContextualStreaming : isStreaming;
  const getCurrentClearMessages = () => isContextualMode ? clearContextualMessages : clearMessages;
  const getCurrentSetMessages = () => isContextualMode ? setContextualMessages : setMessages;

  // 🎯 Função para identificar se uma thread é contextual
  const isContextualThread = (threadTitle: string) => {
    return threadTitle.includes('📚') && (
      threadTitle.includes('Sessão Contextual') ||
      threadTitle.includes('(') && threadTitle.includes('q)')
    );
  };

  // 🎯 Função para extrair nome da sessão do título da thread
  const extractSessionNameFromTitle = (threadTitle: string): string => {
    if (!threadTitle) return 'Sessão';

    // Remover emoji e limpar
    let sessionName = threadTitle.replace('📚 ', '');

    // Remover sufixos antigos
    sessionName = sessionName.replace(' - Sessão Contextual', '');

    // Remover informação de número de questões se presente
    sessionName = sessionName.replace(/\s*\(\d+q\)$/, '');

    return sessionName || 'Sessão';
  };

  // 🎯 Função para limpar threads contextuais antigas (mais de 7 dias)
  const cleanupOldContextualThreads = useCallback(async () => {
    if (!user?.id) return;

    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const { data: oldThreads, error } = await supabase
        .from('medevo_chat_threads')
        .select('id, title, created_at')
        .eq('user_id', user.id)
        .lt('created_at', sevenDaysAgo.toISOString());

      if (error) {

        return;
      }

      if (oldThreads && oldThreads.length > 0) {
        const contextualThreadsToDelete = oldThreads.filter(thread =>
          isContextualThread(thread.title || '')
        );

        if (contextualThreadsToDelete.length > 0) {
          const threadIds = contextualThreadsToDelete.map(t => t.id);

          // Deletar mensagens das threads
          await supabase
            .from('medevo_chat_history')
            .delete()
            .in('thread_id', threadIds);

          // Deletar as threads
          await supabase
            .from('medevo_chat_threads')
            .delete()
            .in('id', threadIds);


        }
      }
    } catch (error) {

    }
  }, [user?.id]);

  // 🎯 Executar limpeza uma vez quando o componente monta
  useEffect(() => {
    if (user?.id) {
      // Executar limpeza após 5 segundos para não impactar o carregamento inicial
      const timeoutId = setTimeout(() => {
        cleanupOldContextualThreads();
      }, 5000);

      return () => clearTimeout(timeoutId);
    }
  }, [user?.id, cleanupOldContextualThreads]);

  // 🎯 CORREÇÃO: Carregar threads automaticamente quando o chat abre
  useEffect(() => {
    if (isExpanded && showHistory && user?.id) {
      loadThreads();
    }
  }, [isExpanded, showHistory, user?.id, loadThreads]);

  // 🎯 LIMPEZA DE CACHE QUANDO NECESSÁRIO (IDÊNTICO AO DRWILL)
  useEffect(() => {
    const currentMessages = getCurrentMessages();
    // Limpar cache quando há muitas entradas (evitar memory leak)
    if (globalFormatCache.size > 100) {
      // Limpeza de cache
      globalFormatCache.clear();
    }
  }, [getCurrentMessages().length]);

  // 🎯 SEPARAÇÃO: Limpar mensagens quando modo contextual muda para evitar interferência
  useEffect(() => {
    // Quando muda para modo contextual, limpar mensagens normais
    if (isContextualMode) {
      clearMessages();
    } else {
      // Quando sai do modo contextual, limpar mensagens contextuais
      clearContextualMessages();
    }
  }, [isContextualMode, clearMessages, clearContextualMessages]);

  // 🎯 AUTO-ATIVAR: Ativar modo contextual quando entrar em sessão de questões
  useEffect(() => {
    // Só auto-ativar se o usuário não desativou manualmente
    if (isInQuestionSession && !isContextualMode && !userDisabledContextual) {
      setIsContextualMode(true);
    }
  }, [isInQuestionSession, isContextualMode, userDisabledContextual, questionContext]);

  // 🎯 AUTO-DESATIVAR: Desativar modo contextual quando sair da sessão de questões
  useEffect(() => {


    if (!isInQuestionSession && isContextualMode) {

      setIsContextualMode(false);
      // Resetar flag quando sair da sessão
      setUserDisabledContextual(false);
    }
  }, [isInQuestionSession, isContextualMode]);

  // Setup Mermaid for floating chat
  useEffect(() => {
    // 🎯 ESCUTAR EVENTO CUSTOMIZADO PARA MERMAID
    const handleMermaidModalEvent = (event: any) => {
      if (event.detail && event.detail.mermaidCode) {
        setCurrentMermaidCode(event.detail.mermaidCode);
        setMermaidModalOpen(true);
      }
    };

    document.addEventListener('openMermaidModal', handleMermaidModalEvent);

    // Função global para expandir Mermaid no floating chat (fallback)
    (window as any).expandMermaidDiagram = (id: string) => {

      // Tentar encontrar o código no script JSON
      const codeElement = document.getElementById(id + '-code');
      if (codeElement) {
        try {
          const mermaidCode = JSON.parse(codeElement.textContent || '');
          setCurrentMermaidCode(mermaidCode);
          setMermaidModalOpen(true);
          return;
        } catch (error) {
          // Erro silencioso
        }
      }

      // Fallback: procurar em todas as mensagens por código Mermaid
      const allMessages = document.querySelectorAll('[id^="mermaid-"]');
      if (allMessages.length > 0) {
        const lastMermaidId = Array.from(allMessages).pop()?.id;
        if (lastMermaidId) {
          const lastCodeElement = document.getElementById(lastMermaidId + '-code');
          if (lastCodeElement) {
            try {
              const mermaidCode = JSON.parse(lastCodeElement.textContent || '');
              setCurrentMermaidCode(mermaidCode);
              setMermaidModalOpen(true);
              return;
            } catch (error) {
              // Erro silencioso
            }
          }
        }
      }
    };

    // Inicializar Mermaid se disponível
    if (typeof (window as any).mermaid !== 'undefined') {
      (window as any).mermaid.initialize({
        startOnLoad: true,
        theme: 'default',
        themeVariables: {
          primaryColor: '#8b5cf6',
          primaryTextColor: '#1f2937',
          primaryBorderColor: '#6366f1',
          lineColor: '#6b7280'
        }
      });
    }

    return () => {
      document.removeEventListener('openMermaidModal', handleMermaidModalEvent);
    };
  }, []);

  // 🎯 AUTO-CARREGAR: Carregar thread contextual quando entrar na sessão (OTIMIZADO)
  useEffect(() => {
    let isExecuting = false; // Flag para evitar execuções múltiplas

    const loadContextualThreadOnSessionStart = async () => {


      if (isExecuting || !isInQuestionSession || !isContextualMode || !questionContext) {

        return;
      }

      isExecuting = true;

      try {


        // Buscar thread contextual existente para esta sessão
        const existingThreadId = await findOrCreateContextualThread(
          questionContext.sessionTitle,
          questionContext.sessionId
        );



        if (existingThreadId) {
          // Se não é a thread ativa atual, carregar mensagens
          if (activeThreadId !== existingThreadId) {

            await handleThreadSelect(existingThreadId);
          } else {

            // Se é a thread ativa mas não há mensagens contextuais, recarregar
            if (contextualMessages.length === 0) {

              await handleThreadSelect(existingThreadId);
            }
          }
        }
      } catch (error) {
        console.error('❌ [FloatingChat] Erro ao carregar thread contextual:', error);
      } finally {
        isExecuting = false;
      }
    };

    // Aguardar um pouco para garantir que o contexto está estável
    const timeoutId = setTimeout(loadContextualThreadOnSessionStart, 500);
    return () => {
      clearTimeout(timeoutId);
      isExecuting = false;
    };
  }, [isInQuestionSession, isContextualMode, questionContext?.sessionId, activeThreadId]);

  // 🎯 REMOVIDO: formatMessage duplicado - usar diretamente formatDrWillMessage

  // 🎯 REMOVIDO: formatThinkingContent agora é importado do messageFormatter



  // Scroll inteligente com detecção de topo
  const lastScrolledMessageId = useRef<string>('');
  const hasReachedTop = useRef<boolean>(false);
  const scrollAttempts = useRef<number>(0);

  useEffect(() => {
    const currentMessages = getCurrentMessages();
    if (!isExpanded || showHistory || currentMessages.length === 0) return;

    const lastMessage = currentMessages[currentMessages.length - 1];

    // APENAS scroll para mensagem do usuário (aparecer no topo do chat)
    if (lastMessage.isUser) {
      lastScrolledMessageId.current = lastMessage.id;
      hasReachedTop.current = false; // Reset para próxima resposta
      scrollAttempts.current = 0; // Reset tentativas

      setTimeout(() => {
        const messageElement = messageRefs.current[lastMessage.id];
        const container = messagesContainerRef.current;
        if (messageElement && container) {
          const elementTop = messageElement.offsetTop;
          // Offset maior para não ficar atrás do header
          container.scrollTo({
            top: elementTop - 80, // Aumentado de 20 para 80px
            behavior: 'smooth'
          });
        }
      }, 100);
    }
    // Scroll CONTÍNUO para Dr. Will durante streaming
    else if (lastMessage.isStreaming && !lastMessage.isUser && !hasReachedTop.current) {
      setTimeout(() => {
        const messageElement = messageRefs.current[lastMessage.id];
        const container = messagesContainerRef.current;
        if (messageElement && container && !hasReachedTop.current) {
          const elementTop = messageElement.offsetTop;
          const targetScrollTop = elementTop - 80; // Mesmo offset de 80px
          const currentScrollTop = container.scrollTop;

          // Verificar se já chegou no topo (diferença menor que 30px)
          if (Math.abs(currentScrollTop - targetScrollTop) <= 30) {
            hasReachedTop.current = true;
            return;
          }

          // Continuar scrolling
          container.scrollTo({
            top: targetScrollTop,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
    // Quando Dr. Will terminar, garantir que parou
    else if (!lastMessage.isStreaming && !lastMessage.isUser && lastMessage.content) {
      hasReachedTop.current = true;
    }
  }, [getCurrentMessages(), isExpanded, showHistory, isContextualMode]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || getCurrentIsLoading()) return;

    const messageToSend = inputMessage;
    setInputMessage('');



    try {
      // 🎯 Verificar se está em thread contextual sem contexto ativo
      const isContextualThreadWithoutContext = activeThreadId &&
        threads?.find(t => t.id === activeThreadId && isContextualThread(t.title || '')) &&
        !isInQuestionSession;



      if (isContextualThreadWithoutContext) {

        // Limpar thread ativa para forçar criação de nova thread geral
        setActiveThreadId(null);
        // 🎯 HARMONIZAÇÃO: Usar parâmetro direto para forçar nova thread
        await sendMessage(messageToSend, user?.id, null, true);
      } else if (isContextualMode && isInQuestionSession && currentQuestion) {



        // 🎯 Usar modo contextual se ativo e há questão disponível
        const questionContext: QuestionContext = {
          questionNumber,
          totalQuestions,
          sessionTitle,
          sessionId: sessionId || '',
          questionId: currentQuestion.id,
          specialty,
          theme,
          focus,
          statement: currentQuestion.statement || currentQuestion.question_content,
          alternatives: currentQuestion.alternatives || currentQuestion.response_choices?.map((choice: any, index: number) => ({
            text: choice,
            letter: String.fromCharCode(65 + index)
          })),
          correctAnswer: currentQuestion.correct_answer ?
            String.fromCharCode(65 + (typeof currentQuestion.correct_answer === 'number' ? currentQuestion.correct_answer : parseInt(currentQuestion.correct_answer))) :
            (currentQuestion.correct_choice ? String.fromCharCode(65 + (typeof currentQuestion.correct_choice === 'number' ? currentQuestion.correct_choice : parseInt(currentQuestion.correct_choice))) : undefined),
          explanation: currentQuestion.explanation,
          // 🎯 NOVOS CAMPOS PARA ENRIQUECER O CONTEXTO
          examYear: currentQuestion.exam_year,
          examLocation: currentQuestion.exam_location,
          assessmentType: currentQuestion.assessment_type,
          knowledgeDomain: currentQuestion.knowledge_domain,
          questionFormat: currentQuestion.question_format,
          contentTags: currentQuestion.content_tags,
          aiCommentary: currentQuestion.ai_commentary
        };



        // Para contexto, PASSAR O CONTEXTO CRIADO
        await sendContextualMessage(messageToSend, questionContext, user?.id, null);
      } else {
        // 🎯 HARMONIZAÇÃO: Mesma lógica do DrWill
        const shouldForceNewThread = forceNewThread;

        if (shouldForceNewThread) {
          setForceNewThread(false);
          await sendMessage(messageToSend, user?.id, null, true);
        } else {
          // Modo normal - usar thread atual ou criar novo
          const threadToUse = activeThreadId || currentThreadId;
          await sendMessage(messageToSend, user?.id, threadToUse);
        }
      }
    } catch (error) {
      console.error('❌ [FloatingChat] Erro ao enviar mensagem:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleThreadSelect = async (threadId: string) => {


    try {
      // 🎯 MESMA LÓGICA do Dr. Will principal
      const historyMessages = await loadHistoryMessages(threadId);


      setActiveThreadId(threadId);

      if (historyMessages && historyMessages.length > 0) {
        const chatMessages = historyMessages.map(msg => ({
          id: msg.id,
          content: msg.content,
          isUser: msg.isUser,
          timestamp: msg.timestamp,
          isStreaming: false
        }));



        // 🎯 SEMPRE carregar mensagens em AMBOS os contextos para manter consistência
        setMessages(chatMessages);
        setContextualMessages(chatMessages);
      } else {

        clearMessages();
        clearContextualMessages();
      }

      setShowHistory(false);

      // Force scroll to top after loading (com offset para header)
      setTimeout(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }
      }, 200);
    } catch (error) {
      console.error('❌ [FloatingChat] Erro em handleThreadSelect:', error);
    }
  };

  const handleNewChat = () => {
    // 🎯 HARMONIZAÇÃO: Mesma lógica do DrWill
    clearMessages();
    clearContextualMessages();
    setActiveThreadId(null); // 🎯 Limpar estado local
    setShowHistory(false);
    // 🎯 Resetar modo contextual ao iniciar nova conversa
    setIsContextualMode(false);

    // 🎯 HARMONIZAÇÃO: Forçar criação de nova thread na próxima mensagem
    setForceNewThread(true);
  };

  // Limpar cache quando sair do modo de estudo
  useEffect(() => {
    const handleClearCache = () => {
      globalFormatCache.clear();
    };

    window.addEventListener('clearDarkModeCache', handleClearCache);
    return () => window.removeEventListener('clearDarkModeCache', handleClearCache);
  }, []);

  // Limpar histórico de mensagens quando sair da sessão de estudos
  useEffect(() => {
    const handleExitStudySession = () => {
      // Limpar todas as mensagens para começar conversa nova
      clearMessages();
      clearContextualMessages();

      // Resetar thread ativa para forçar criação de nova thread
      setActiveThreadId(null);

      // Sair do modo contextual se estiver ativo
      if (isContextualMode) {
        setIsContextualMode(false);
      }

      // Fechar histórico se estiver aberto
      if (showHistory) {
        setShowHistory(false);
      }
    };

    window.addEventListener('forceDeactivateStudyMode', handleExitStudySession);
    return () => window.removeEventListener('forceDeactivateStudyMode', handleExitStudySession);
  }, [clearMessages, clearContextualMessages, isContextualMode, showHistory]);

  // 🎯 FUNÇÃO GLOBAL PARA TABELAS - COMPATIBILIDADE COM MESSAGEFORMATTER
  useEffect(() => {
    (window as any).openTableDialog = (tableId: string) => {
      const dataElement = document.getElementById(`table-data-${tableId}`);
      if (dataElement) {
        try {
          const tableData = JSON.parse(dataElement.textContent || '[]');

          // Criar dialog HTML simples para o FloatingChat
          const dialogHtml = `
            <div id="table-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
              <div class="rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden transition-colors duration-200 ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              }">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
                  <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold">Tabela Completa</h2>
                    <button onclick="closeTableDialog()" class="bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-lg transition-colors">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </button>
                  </div>
                </div>
                <div class="p-6 overflow-auto max-h-[70vh]">
                  <div class="overflow-x-auto">
                    <table class="w-full border-collapse border ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}">
                      <tbody>
                        ${tableData.map((row: string[], index: number) => `
                          <tr class="${index === 0
                            ? (isDarkMode ? 'bg-gray-700 font-semibold' : 'bg-gray-50 font-semibold')
                            : (isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50')
                          }">
                            ${row.map(cell => `
                              <td class="border px-4 py-3 text-sm ${
                                isDarkMode ? 'border-gray-600' : 'border-gray-300'
                              } ${index === 0
                                ? (isDarkMode ? 'font-bold text-gray-200' : 'font-bold text-gray-900')
                                : (isDarkMode ? 'text-gray-300' : 'text-gray-700')
                              }">${cell}</td>
                            `).join('')}
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          `;

          document.body.insertAdjacentHTML('beforeend', dialogHtml);

          (window as any).closeTableDialog = () => {
            const dialog = document.getElementById('table-dialog');
            if (dialog) dialog.remove();
          };

          const handleEsc = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
              (window as any).closeTableDialog();
              document.removeEventListener('keydown', handleEsc);
            }
          };
          document.addEventListener('keydown', handleEsc);

        } catch (error) {
          console.error('Erro ao abrir tabela:', error);
        }
      }
    };

    return () => {
      delete (window as any).openTableDialog;
      delete (window as any).closeTableDialog;
    };
  }, []);

  const handleOpenFullChat = () => {
    if (currentThreadId) {
      navigate(`/dr-will?thread=${currentThreadId}`);
    } else {
      navigate('/dr-will');
    }
    setIsExpanded(false);
  };



  // 🎯 Função para buscar sessionId pelo título da sessão
  const findSessionIdByTitle = async (sessionTitle: string): Promise<string | null> => {
    if (!user?.id) return null;

    try {
      // Buscar sessões do usuário que contenham o título
      const { data: sessions, error } = await supabase
        .from('study_sessions')
        .select('id, title')
        .eq('user_id', user.id)
        .ilike('title', `%${sessionTitle}%`)
        .order('started_at', { ascending: false })
        .limit(1);

      if (error) {
        return null;
      }

      return sessions && sessions.length > 0 ? sessions[0].id : null;
    } catch (error) {
      return null;
    }
  };

  // 🎯 Função para mostrar dialog de sessão inativa
  const showSessionInactiveDialog = async (sessionName: string, threadId?: string) => {
    setSelectedSessionTitle(sessionName);

    // Primeiro tentar extrair sessionId do metadata da thread
    let sessionIdToUse: string | null = null;

    if (threadId) {
      const thread = threads?.find(t => t.id === threadId);
      if (thread && (thread as any).metadata?.sessionId) {
        sessionIdToUse = (thread as any).metadata.sessionId;
      }
    }

    // Se não encontrou no metadata, buscar por título
    if (!sessionIdToUse) {
      sessionIdToUse = await findSessionIdByTitle(sessionName);
    }

    setSelectedSessionId(sessionIdToUse);
    setSessionDialogOpen(true);
  };



  // 🎯 Função para mostrar dialog com informações da questão
  const showQuestionInfoDialog = () => {
    if (!currentQuestion) return;

    const dialogHtml = `
      <div id="question-info-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
        <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
          <div class="bg-gradient-to-r from-emerald-500 to-teal-600 p-6 text-white">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  📝
                </div>
                <div>
                  <h3 class="text-xl font-bold">Questão ${questionNumber} de ${totalQuestions}</h3>
                  <p class="text-emerald-100 text-sm">${sessionTitle}</p>
                </div>
              </div>
              <button onclick="document.getElementById('question-info-dialog').remove()" class="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>

          <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            <div class="space-y-6">
              ${specialty || theme || focus ? `
                <div>
                  <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <span class="w-2 h-2 bg-emerald-500 rounded-full"></span>
                    Contexto da Questão
                  </h4>
                  <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                    ${specialty ? `<div class="flex items-center gap-2"><span class="font-medium text-gray-700">Especialidade:</span> <span class="text-gray-600">${specialty}</span></div>` : ''}
                    ${theme ? `<div class="flex items-center gap-2"><span class="font-medium text-gray-700">Tema:</span> <span class="text-gray-600">${theme}</span></div>` : ''}
                    ${focus ? `<div class="flex items-center gap-2"><span class="font-medium text-gray-700">Foco:</span> <span class="text-gray-600">${focus}</span></div>` : ''}
                  </div>
                </div>
              ` : ''}

              <div class="bg-blue-50 rounded-lg p-4">
                <div class="text-sm text-blue-800">
                  💡 <strong>Dica:</strong> Use o modo contextual para fazer perguntas específicas sobre esta questão.
                  O Dr. Will terá acesso a todas essas informações para dar respostas mais precisas.
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-end">
              <button onclick="document.getElementById('question-info-dialog').remove()" class="px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors">
                Fechar
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Adicionar dialog ao DOM
    document.body.insertAdjacentHTML('beforeend', dialogHtml);

    // Adicionar evento para fechar ao clicar fora
    const dialog = document.getElementById('question-info-dialog');
    if (dialog) {
      dialog.addEventListener('click', (e) => {
        if (e.target === dialog) {
          dialog.remove();
        }
      });
    }
  };

  return (
    <>
      {/* Mobile Overlay - APENAS para mobile */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-[60] md:hidden"
            onClick={() => setIsExpanded(false)}
          />
        )}
      </AnimatePresence>

      {/* Floating Button - Z-index alto para ficar na frente de tudo */}
      <div className={`fixed bottom-6 right-6 z-[70] ${
        isFlashcardSession ? 'hidden sm:block' : ''
      } ${className}`}>
        <AnimatePresence>
          {isExpanded ? (
            // Expanded Chat Panel - Mobile: Centralizado, Desktop: Posicionamento absoluto sem overlay
            <div className="fixed inset-0 flex items-center justify-center p-4 md:relative md:inset-auto md:flex-none md:p-0">
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 20 }}
                className={`rounded-2xl shadow-2xl border overflow-hidden flex flex-col transition-colors duration-200
                           w-full max-w-[360px] h-full max-h-[85vh]
                           md:w-[420px] md:h-[600px] md:max-h-none md:absolute md:bottom-0 md:right-0 ${
                  isDarkMode
                    ? 'bg-gray-800 border-gray-600'
                    : 'bg-white border-gray-200'
                }`}
              >
              {/* Header */}
              <div className={`p-4 text-white flex-shrink-0 ${
                isContextualMode && isInQuestionSession
                  ? 'bg-gradient-to-r from-emerald-500 to-teal-600'
                  : 'bg-gradient-to-r from-blue-500 to-purple-600'
              }`}>
                <div className="flex items-center justify-between">
                  <div
                    className="flex items-center gap-3 cursor-pointer hover:bg-white hover:bg-opacity-10 rounded-lg p-2 transition-colors"
                    onClick={() => setIsExpanded(false)}
                    title="Clique para minimizar"
                  >
                    <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-base">
                      {isContextualMode && isInQuestionSession ? '🎯' : '🧠'}
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">
                        Dr. Will
                      </h3>
                      {isContextualMode && isInQuestionSession && (
                        <div className="text-xs text-white text-opacity-80">
                          Modo Contextual
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (!getCurrentIsLoading()) {
                          setShowHistory(!showHistory);
                          if (!showHistory) {
                            setCurrentPage(1); // Reset para primeira página ao abrir
                          }
                        }
                      }}
                      disabled={getCurrentIsLoading()}
                      className={`p-2 rounded-lg transition-colors ${
                        getCurrentIsLoading()
                          ? 'opacity-50 cursor-not-allowed'
                          : 'hover:bg-white hover:bg-opacity-20'
                      }`}
                      title={getCurrentIsLoading() ? "Aguarde a resposta..." : "Histórico"}
                    >
                      <History className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleNewChat();
                      }}
                      className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                      title="Nova conversa"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleOpenFullChat();
                      }}
                      className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                      title="Abrir chat completo"
                    >
                      <Maximize2 className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsExpanded(false);
                      }}
                      className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* 🎯 Questão Atual Info - Mostrar apenas quando há questão ativa */}
              {isInQuestionSession && (
                <div className="bg-gradient-to-r from-emerald-500 to-teal-600 px-4 py-3 border-b border-white border-opacity-20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-7 h-7 bg-white bg-opacity-20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                        📝
                      </div>
                      <div>
                        <div className="text-white font-semibold text-sm">
                          Questão {questionNumber} de {totalQuestions}
                        </div>
                        <div className="text-white text-opacity-80 text-xs">
                          {sessionTitle}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {/* Botão de informações da questão */}
                      <button
                        onClick={() => showQuestionInfoDialog()}
                        className="p-2 bg-white bg-opacity-20 text-white hover:bg-opacity-30 rounded-lg transition-all duration-200"
                        title="Ver detalhes da questão"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>

                      {/* Toggle para modo contextual */}
                      <button
                        onClick={async () => {
                          if (!isInQuestionSession && !isContextualMode) {
                            return;
                          }
                          if (getCurrentIsLoading()) {
                            return;
                          }
                          const newMode = !isContextualMode;

                          setIsContextualMode(newMode);

                          // Se o usuário está desativando o modo contextual em uma sessão, marcar como desativado manualmente
                          if (isInQuestionSession && !newMode) {
                            setUserDisabledContextual(true);
                          }

                          // 🔄 GARANTIR QUE AS MENSAGENS SEJAM CARREGADAS NO NOVO MODO
                          if (activeThreadId || currentThreadId) {
                            const threadToLoad = activeThreadId || currentThreadId;

                            try {
                              // Aguardar um pouco para o modo ser atualizado
                              setTimeout(async () => {
                                await handleThreadSelect(threadToLoad);
                              }, 100);
                            } catch (error) {
                              console.error('❌ [FloatingChat] Erro ao recarregar mensagens:', error);
                            }
                          }
                        }}
                        disabled={(!isInQuestionSession && !isContextualMode) || getCurrentIsLoading()}
                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                          getCurrentIsLoading()
                            ? 'bg-white bg-opacity-10 text-white text-opacity-50 cursor-not-allowed'
                            : isContextualMode
                              ? 'bg-white text-emerald-600 shadow-md'
                              : !isInQuestionSession
                                ? 'bg-white bg-opacity-10 text-white text-opacity-50 cursor-not-allowed'
                                : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'
                        }`}
                        title={
                          getCurrentIsLoading()
                            ? 'Aguarde a resposta...'
                            : !isInQuestionSession && !isContextualMode
                              ? 'Modo contextual disponível apenas durante questões'
                              : isContextualMode
                                ? 'Desativar modo contextual'
                                : 'Ativar modo contextual'
                        }
                      >
                        {isContextualMode ? '🎯 Contextual' : '💬 Geral'}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Content Area */}
              <div className="flex-1 flex flex-col overflow-hidden">
                {showHistory ? (
                  // History View
                  <div className="flex-1 overflow-y-auto">
                    <div className={`p-3 border-b transition-colors duration-200 ${
                      isDarkMode ? 'border-gray-600' : 'border-gray-200'
                    }`}>
                      <h4 className={`font-semibold mb-3 transition-colors duration-200 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-900'
                      }`}>Conversas Anteriores</h4>

                      {/* Filtros */}
                      <div className="flex gap-1">
                        <button
                          onClick={() => {
                            setHistoryFilter('all');
                            setCurrentPage(1);
                          }}
                          disabled={getCurrentIsLoading()}
                          className={`px-3 py-1 text-xs rounded-full transition-colors ${
                            historyFilter === 'all'
                              ? 'bg-blue-500 text-white'
                              : getCurrentIsLoading()
                                ? isDarkMode
                                  ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : isDarkMode
                                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }`}
                        >
                          Todas
                        </button>
                        <button
                          onClick={() => {
                            setHistoryFilter('general');
                            setCurrentPage(1);
                          }}
                          disabled={getCurrentIsLoading()}
                          className={`px-3 py-1 text-xs rounded-full transition-colors ${
                            historyFilter === 'general'
                              ? 'bg-purple-500 text-white'
                              : getCurrentIsLoading()
                                ? isDarkMode
                                  ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : isDarkMode
                                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }`}
                        >
                          🧠 Gerais
                        </button>
                        <button
                          onClick={() => {
                            setHistoryFilter('contextual');
                            setCurrentPage(1);
                          }}
                          disabled={getCurrentIsLoading()}
                          className={`px-3 py-1 text-xs rounded-full transition-colors ${
                            historyFilter === 'contextual'
                              ? 'bg-emerald-500 text-white'
                              : getCurrentIsLoading()
                                ? isDarkMode
                                  ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : isDarkMode
                                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }`}
                        >
                          🎯 Sessões
                        </button>
                      </div>
                    </div>
                    {threadsLoading ? (
                      <div className="space-y-3 p-4">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="flex items-center gap-3 animate-pulse">
                            <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                            <div className="flex-1">
                              <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : threads && threads.length > 0 ? (
                      (() => {
                        // Filtrar threads
                        const filteredThreads = threads.filter((thread) => {
                          if (historyFilter === 'all') return true;
                          if (historyFilter === 'contextual') return isContextualThread(thread.title || '');
                          if (historyFilter === 'general') return !isContextualThread(thread.title || '');
                          return true;
                        });

                        // Calcular paginação
                        const totalPages = Math.ceil(filteredThreads.length / ITEMS_PER_PAGE);
                        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
                        const endIndex = startIndex + ITEMS_PER_PAGE;
                        const paginatedThreads = filteredThreads.slice(startIndex, endIndex);

                        return (
                          <>
                            {/* Lista de threads paginada */}
                            {paginatedThreads.map((thread) => (
                        <motion.div
                          key={thread.id}
                          whileHover={{ backgroundColor: '#f8fafc' }}
                          onClick={() => {
                            // 🎯 Usar contexto congelado se disponível, senão usar contexto atual
                            const contextToUse = frozenContext || {
                              isInQuestionSession,
                              sessionTitle,
                              sessionId
                            };

                            // Bloquear se estiver carregando
                            if (getCurrentIsLoading()) {
                              return;
                            }

                            // 🎯 Verificar se é thread contextual
                            const isContextual = isContextualThread(thread.title || '');

                            if (isContextual) {
                              // 🎯 Usar contexto estável
                              if (!contextToUse.isInQuestionSession) {
                                // Extrair nome da sessão do título
                                const sessionName = extractSessionNameFromTitle(thread.title || '') || 'Sessão';
                                showSessionInactiveDialog(sessionName, thread.id);
                                return;
                              }

                              // 🎯 Verificar se é uma sessão diferente da atual usando sessionId (mais confiável)
                              const threadSessionId = (thread as any).metadata?.sessionId;
                              const stableSessionId = contextToUse.sessionId;

                              if (threadSessionId && stableSessionId && threadSessionId !== stableSessionId) {
                                const sessionName = extractSessionNameFromTitle(thread.title || '') || 'Sessão';
                                showSessionInactiveDialog(sessionName, thread.id);
                                return;
                              }

                              // 🎯 Ativar modo contextual automaticamente ao abrir thread contextual
                              setIsContextualMode(true);
                              // Aguardar um pouco para o modo contextual ser ativado antes de carregar mensagens
                              setTimeout(() => handleThreadSelect(thread.id), 100);
                            } else {
                              // 🎯 Desativar modo contextual ao abrir thread normal
                              setIsContextualMode(false);
                              handleThreadSelect(thread.id);
                            }
                          }}
                          className={`p-3 border-b border-gray-100 transition-colors ${
                            getCurrentIsLoading()
                              ? 'opacity-50 cursor-not-allowed'
                              : isContextualThread(thread.title || '') && !isInQuestionSession
                                ? 'opacity-50 cursor-pointer'
                                : 'cursor-pointer hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm ${
                              isContextualThread(thread.title || '')
                                ? 'bg-gradient-to-br from-emerald-500 to-teal-600'
                                : 'bg-gradient-to-br from-blue-500 to-purple-600'
                            }`}>
                              {isContextualThread(thread.title || '') ? '🎯' : '🧠'}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <h5 className="font-medium text-gray-900 text-sm truncate">
                                  {thread.title || 'Conversa com Dr. Will'}
                                </h5>
                                {isContextualThread(thread.title || '') && (
                                  <span className="px-2 py-0.5 bg-emerald-100 text-emerald-700 text-xs rounded-full">
                                    Contextual
                                  </span>
                                )}
                              </div>
                              <p className="text-xs text-gray-500 mt-1">
                                {(() => {
                                  try {
                                    if (thread.createdAt) {
                                      const date = new Date(thread.createdAt);
                                      if (!isNaN(date.getTime())) {
                                        return formatDistanceToNow(date, {
                                          addSuffix: true,
                                          locale: ptBR
                                        });
                                      }
                                    }
                                    return 'Recente';
                                  } catch {
                                    return 'Recente';
                                  }
                                })()}
                                {isContextualThread(thread.title || '') && (
                                  !isInQuestionSession ? (
                                    <span className="ml-2 text-orange-500">• Sem contexto ativo</span>
                                  ) : (
                                    (() => {
                                      // Usar sessionId do metadata da thread para comparação precisa
                                      const threadSessionId = (thread as any).metadata?.sessionId;
                                      const currentSessionId = questionContext?.sessionId;

                                      return threadSessionId && currentSessionId && threadSessionId !== currentSessionId ? (
                                        <span className="ml-2 text-red-500">• Sessão diferente</span>
                                      ) : threadSessionId && currentSessionId && threadSessionId === currentSessionId ? (
                                        <span className="ml-2 text-green-500">• Sessão atual</span>
                                      ) : (
                                        <span className="ml-2 text-gray-500">• Sessão</span>
                                      );
                                    })()
                                  )
                                )}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      ))}

                            {/* Controles de Paginação */}
                            {totalPages > 1 && (
                              <div className="p-3 border-t border-gray-100 bg-gray-50">
                                <div className="flex items-center justify-between">
                                  <div className="text-xs text-gray-500">
                                    Página {currentPage} de {totalPages} • {filteredThreads.length} conversas
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <button
                                      onClick={() => setCurrentPage(currentPage - 1)}
                                      disabled={currentPage === 1 || getCurrentIsLoading()}
                                      className={`px-2 py-1 text-xs rounded transition-colors ${
                                        currentPage === 1 || getCurrentIsLoading()
                                          ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                          : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
                                      }`}
                                    >
                                      ←
                                    </button>
                                    <span className="px-2 py-1 text-xs text-gray-600">
                                      {currentPage}
                                    </span>
                                    <button
                                      onClick={() => setCurrentPage(currentPage + 1)}
                                      disabled={currentPage === totalPages || getCurrentIsLoading()}
                                      className={`px-2 py-1 text-xs rounded transition-colors ${
                                        currentPage === totalPages || getCurrentIsLoading()
                                          ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                          : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
                                      }`}
                                    >
                                      →
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Botão Voltar à Thread Atual */}
                            {(activeThreadId || currentThreadId) && (
                              <div className="p-3 border-t border-gray-100 bg-blue-50">
                                <button
                                  onClick={() => {
                                    if (!getCurrentIsLoading()) {
                                      setShowHistory(false);
                                    }
                                  }}
                                  disabled={getCurrentIsLoading()}
                                  className={`w-full px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                                    getCurrentIsLoading()
                                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                                      : 'bg-blue-500 text-white hover:bg-blue-600 shadow-sm'
                                  }`}
                                >
                                  <div className="flex items-center justify-center gap-2">
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                    </svg>
                                    Voltar à Conversa Atual
                                  </div>
                                </button>
                              </div>
                            )}
                          </>
                        );
                      })()
                    ) : threads && threads.length > 0 ? (
                      <div className="p-4 text-center text-gray-500">
                        <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">
                          {historyFilter === 'general' && 'Nenhuma conversa geral'}
                          {historyFilter === 'contextual' && 'Nenhuma sessão contextual'}
                          {historyFilter === 'all' && 'Nenhuma conversa anterior'}
                        </p>
                      </div>
                    ) : (
                      <div className="p-4 text-center text-gray-500">
                        <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">Nenhuma conversa anterior</p>
                      </div>
                    )}
                  </div>
                ) : (
                  // Chat View
                  <>
                    {/* Messages Area */}
                    <div
                      ref={messagesContainerRef}
                      className="flex-1 overflow-y-auto p-4 space-y-4"
                    >


                      {getCurrentMessages().length === 0 ? (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl mx-auto mb-4">
                            🧠
                          </div>
                          <h4 className={`font-semibold mb-2 transition-colors duration-200 ${
                            isDarkMode ? 'text-gray-200' : 'text-gray-900'
                          }`}>
                            Olá! Sou o Dr. Will {isContextualMode && isInQuestionSession && '🎯'}
                          </h4>
                          <p className={`text-sm transition-colors duration-200 ${
                            isDarkMode ? 'text-gray-400' : 'text-gray-600'
                          }`}>
                            {isContextualMode && isInQuestionSession
                              ? `Estou acompanhando sua sessão "${sessionTitle}" - questão ${questionNumber} de ${totalQuestions}!`
                              : "Como posso ajudar com seus estudos hoje?"
                            }
                          </p>
                          {isContextualMode && isInQuestionSession && (
                            <div className={`mt-3 p-3 rounded-lg border transition-colors duration-200 ${
                              isDarkMode
                                ? 'bg-emerald-900/20 border-emerald-600'
                                : 'bg-emerald-50 border-emerald-200'
                            }`}>
                              <div className={`text-xs transition-colors duration-200 ${
                                isDarkMode ? 'text-emerald-300' : 'text-emerald-700'
                              }`}>
                                <div className="font-medium mb-1">📚 Sessão Contextual Ativa</div>
                                <div>Todas as suas perguntas serão respondidas com base na questão atual. Posso ajudar com:</div>
                                <ul className={`mt-1 ml-3 list-disc transition-colors duration-200 ${
                                  isDarkMode ? 'text-emerald-400' : 'text-emerald-600'
                                }`}>
                                  <li>Explicação do enunciado atual</li>
                                  <li>Análise das alternativas</li>
                                  <li>Conceitos relacionados</li>
                                  <li>Comparação com questões anteriores da sessão</li>
                                </ul>
                                <div className={`mt-2 font-medium transition-colors duration-200 ${
                                  isDarkMode ? 'text-emerald-400' : 'text-emerald-600'
                                }`}>
                                  💡 Histórico mantido para toda a sessão!
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        getCurrentMessages().map((message) => (
                          <motion.div
                            key={message.id}
                            ref={(el) => (messageRefs.current[message.id] = el)}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
                          >
                            {message.isThinking ? (
                              // 🧠 THINKING MODE PROGRESSIVO (COMPARTILHADO)
                              <div className="w-full">
                                <ThinkingModeComponent
                                  content={message.content}
                                  isStreaming={message.isStreaming}
                                  formatThinkingContent={formatThinkingContent}
                                  compact={true}
                                />
                              </div>
                            ) : (
                              // Design normal para mensagens
                              <div className={`${
                                message.isUser
                                  ? 'max-w-[85%] bg-blue-500 text-white rounded-2xl rounded-br-md'
                                  : isDarkMode
                                    ? 'w-full bg-gray-700 text-gray-200 rounded-2xl rounded-bl-md border border-gray-600'
                                    : 'w-full bg-gray-50 text-gray-900 rounded-2xl rounded-bl-md border border-gray-200'
                              } px-3 py-2 shadow-sm`}>
                                {message.isUser ? (
                                  <div className="text-sm whitespace-pre-wrap break-words">
                                    {message.content}
                                  </div>
                                ) : (
                                  <MemoizedMessageContent
                                    content={message.content}
                                    isStreaming={message.isStreaming}
                                    messageId={message.id}
                                    isDarkMode={isDarkMode}
                                  />
                                )}
                                {message.isStreaming && !message.isThinking && !message.isUser && (
                                  <div className="flex items-center gap-1 mt-2">
                                    <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                                    <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                                  </div>
                                )}
                              </div>
                            )}
                          </motion.div>
                        ))
                      )}
                      <div ref={messagesEndRef} />
                    </div>

                    {/* Aviso para threads contextuais sem contexto ativo */}
                    {showContextWarning && activeThreadId && threads?.find(t => t.id === activeThreadId && isContextualThread(t.title || '')) && !isInQuestionSession && (
                      <div className="border-t border-gray-200 p-3 bg-orange-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-orange-700 text-sm">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <span className="font-medium">Fora do contexto de estudos</span>
                          </div>
                          <button
                            onClick={() => setShowContextWarning(false)}
                            className="text-orange-500 hover:text-orange-700 p-1"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                        <p className="text-xs text-orange-600 mt-1">
                          Ao enviar uma mensagem, será iniciada uma nova conversa com Dr. Will.
                        </p>
                      </div>
                    )}

                    {/* Input Area */}
                    <div className={`border-t p-4 flex-shrink-0 transition-colors duration-200 ${
                      isDarkMode ? 'border-gray-600' : 'border-gray-200'
                    }`}>
                      <div className="flex items-end gap-3">
                        <Textarea
                          value={inputMessage}
                          onChange={(e) => setInputMessage(e.target.value)}
                          onKeyDown={handleKeyDown}
                          placeholder={
                            isContextualMode && isInQuestionSession
                              ? `Pergunte sobre a questão ${questionNumber}...`
                              : "Digite sua pergunta médica..."
                          }
                          disabled={getCurrentIsLoading()}
                          className="flex-1 text-sm min-h-[44px] max-h-[120px] resize-none"
                        />
                        <Button
                          onClick={handleSendMessage}
                          disabled={!inputMessage.trim() || getCurrentIsLoading()}
                          size="sm"
                          className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>

                    </div>
                  </>
                )}

              </div>
            </motion.div>
            </div>
          ) : (
            // Collapsed Button - Mobile: Mais compacto, Desktop: Tamanho normal
            <motion.button
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsExpanded(true)}
              className={`text-white p-2 md:p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 relative ${
                isContextualMode && isInQuestionSession
                  ? 'bg-gradient-to-r from-emerald-500 to-teal-600'
                  : 'bg-gradient-to-r from-blue-500 to-purple-600'
              }`}
            >
              <div className="text-base md:text-2xl">
                {isContextualMode && isInQuestionSession ? '🎯' : '🧠'}
              </div>

              {/* Notification Badge - mostrar número da questão em modo contextual ou threads em modo geral */}
              {isContextualMode && isInQuestionSession ? (
                <div className="absolute -top-1 -right-1 w-4 h-4 md:w-5 md:h-5 bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                  {questionNumber || '?'}
                </div>
              ) : (threads && threads.length > 0) || getCurrentMessages().length > 0 ? (
                <div className="absolute -top-1 -right-1 w-4 h-4 md:w-5 md:h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                  {threads ? Math.min(threads.length, 9) : '!'}
                </div>
              ) : null}

              {/* Status indicator */}
              <div className={`absolute -bottom-0.5 -right-0.5 md:-bottom-1 md:-right-1 w-3 h-3 md:w-4 md:h-4 border-2 border-white rounded-full ${
                isContextualMode && isInQuestionSession
                  ? 'bg-orange-500'
                  : 'bg-green-500'
              }`}></div>



              {/* Pulse animation when active */}
              {getCurrentIsLoading() && (
                <div className="absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-75"></div>
              )}
            </motion.button>
          )}
        </AnimatePresence>
      </div>

      {/* Mermaid Modal */}
      <MermaidModal
        isOpen={mermaidModalOpen}
        onClose={() => setMermaidModalOpen(false)}
        mermaidCode={currentMermaidCode}
      />

      {/* Dialog de Sessão Inativa */}
      <SessionInactiveDialog
        open={sessionDialogOpen}
        onOpenChange={setSessionDialogOpen}
        sessionTitle={selectedSessionTitle}
        sessionId={selectedSessionId}
      />
    </>
  );
};

export default FloatingChatButton;
