import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface ThinkingModeComponentProps {
  content: string;
  isStreaming: boolean;
  formatThinkingContent: (content: string) => string;
  compact?: boolean; // Para versão compacta do floating chat
}

// 🧠 Componente do Thinking Mode Progressivo (COMPARTILHADO)
export const ThinkingModeComponent: React.FC<ThinkingModeComponentProps> = ({
  content,
  isStreaming,
  formatThinkingContent,
  compact = false
}) => {
  const { isDarkMode } = useDarkMode();
  const [visibleSteps, setVisibleSteps] = useState<string[]>([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false); // Sempre oculto por padrão
  const [lastProcessedContent, setLastProcessedContent] = useState('');

  // Reset apenas quando conteúdo fica vazio (nova conversa)
  useEffect(() => {
    if (!content && lastProcessedContent) {
      // Log removido
      setVisibleSteps([]);
      setCurrentStepIndex(0);
      setLastProcessedContent('');
    }
  }, [content, lastProcessedContent]);

  // Dividir o conteúdo em etapas baseado nos títulos **
  const parseThinkingSteps = useCallback((content: string): string[] => {
    if (!content) return [];

    // 🎯 ESTRATÉGIA MELHORADA: Dividir por títulos ** com melhor regex
    // Primeiro, encontrar todas as posições dos títulos
    const titleMatches = [...content.matchAll(/\*\*([^*\n]+)\*\*/g)];

    if (titleMatches.length === 0) {
      return [content]; // Se não há títulos, retorna o conteúdo todo
    }

    const steps: string[] = [];
    let lastIndex = 0;

    titleMatches.forEach((match, index) => {
      const startIndex = match.index!;
      const nextMatch = titleMatches[index + 1];
      const endIndex = nextMatch ? nextMatch.index! : content.length;

      const stepContent = content.substring(startIndex, endIndex).trim();
      if (stepContent.length > 10) {
        steps.push(stepContent);
      }
    });

    // Log removido para limpeza

    return steps.length > 0 ? steps : [content];
  }, [lastProcessedContent]);

  // Efeito para mostrar etapas progressivamente
  useEffect(() => {
    if (!content) return;

    const steps = parseThinkingSteps(content);
    // Log removido para limpeza

    if (steps.length === 0) return;

    // 🎯 ESTRATÉGIA: ACUMULAR em vez de resetar
    // Se temos mais etapas do que as visíveis, mostrar as novas
    if (steps.length > visibleSteps.length) {
      const nextStepIndex = visibleSteps.length;
      // Log removido para limpeza

      const timer = setTimeout(() => {
        setVisibleSteps(prev => {
          // Verificar se a etapa já existe para evitar duplicatas
          if (prev.length < steps.length) {
            const newSteps = [...prev, steps[nextStepIndex]];
            // Log removido para limpeza
            return newSteps;
          }
          return prev;
        });
        setCurrentStepIndex(nextStepIndex + 1);
      }, nextStepIndex === 0 ? 200 : 800); // Primeiro step mais rápido

      return () => clearTimeout(timer);
    }

    // Atualizar lastProcessedContent apenas no final
    if (content !== lastProcessedContent) {
      setLastProcessedContent(content);
    }
  }, [content, visibleSteps.length, parseThinkingSteps]);

  // Debug render removido para limpeza

  if (compact) {
    // Versão compacta para floating chat
    return (
      <div className={`relative border-2 rounded-xl p-3 -m-3 overflow-hidden transition-colors duration-200 ${
        isDarkMode
          ? 'bg-gradient-to-br from-indigo-900/30 via-purple-900/30 to-pink-900/30 border-indigo-600'
          : 'bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 border-indigo-200'
      }`}>
        {/* Efeito de fundo animado */}
        <div className={`absolute inset-0 animate-pulse transition-colors duration-200 ${
          isDarkMode
            ? 'bg-gradient-to-r from-indigo-800/30 to-purple-800/30'
            : 'bg-gradient-to-r from-indigo-100/30 to-purple-100/30'
        }`}></div>

        {/* Header compacto com botão */}
        <div className="relative flex items-center gap-3 mb-3">
          <div className="relative">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-white text-sm shadow-lg">
              🧠
            </div>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
          </div>
          <div className="flex-1">
            <h4 className={`font-bold text-sm flex items-center gap-2 transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-800'
            }`}>
              Dr. Will está pensando
              <div className="flex gap-1">
                <div className="w-1 h-1 bg-indigo-500 rounded-full animate-bounce"></div>
                <div className="w-1 h-1 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1 h-1 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </h4>
            <p className={`text-xs transition-colors duration-200 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Etapa {currentStepIndex} de {visibleSteps.length + (isStreaming ? 1 : 0)}
            </p>
          </div>
          <button
            onClick={() => setIsVisible(!isVisible)}
            className={`px-2 py-1 border rounded-lg text-xs font-medium transition-all duration-200 hover:shadow-sm ${
              isDarkMode
                ? 'bg-gray-700/70 hover:bg-gray-700/90 border-indigo-600 text-indigo-400'
                : 'bg-white/70 hover:bg-white/90 border-indigo-200 text-indigo-600'
            }`}
          >
            {isVisible ? 'Ocultar' : 'Ver'}
          </button>
        </div>

        {/* Etapas do thinking - só mostra se isVisible */}
        {isVisible && (
          <div className="relative space-y-2">
            {/* Debug removido */}
            {visibleSteps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className={`relative backdrop-blur-sm rounded-lg p-2 border shadow-sm transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-gray-700/70 border-gray-600/50'
                  : 'bg-white/70 border-white/50'
              }`}
            >
              <div className="flex items-start gap-2">
                <div className="w-4 h-4 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0 mt-0.5">
                  {index + 1}
                </div>
                <div className={`flex-1 text-xs leading-relaxed transition-colors duration-200 ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: formatThinkingContent(step, isDarkMode)
                    }}
                  />
                </div>
              </div>
            </motion.div>
          ))}
          
          {/* Indicador de carregamento para próxima etapa */}
          {isStreaming && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className={`relative backdrop-blur-sm rounded-lg p-2 border shadow-sm transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-gray-700/50 border-gray-600/30'
                  : 'bg-white/50 border-white/30'
              }`}
            >
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full flex items-center justify-center animate-pulse ${
                  isDarkMode ? 'bg-gray-600' : 'bg-gray-300'
                }`}>
                  <div className={`w-1 h-1 rounded-full ${
                    isDarkMode ? 'bg-gray-400' : 'bg-gray-500'
                  }`}></div>
                </div>
                <div className={`flex items-center gap-1 text-xs transition-colors duration-200 ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  <div className="flex gap-1">
                    <div className={`w-1 h-1 rounded-full animate-bounce ${
                      isDarkMode ? 'bg-gray-500' : 'bg-gray-400'
                    }`}></div>
                    <div className={`w-1 h-1 rounded-full animate-bounce ${
                      isDarkMode ? 'bg-gray-500' : 'bg-gray-400'
                    }`} style={{ animationDelay: '0.1s' }}></div>
                    <div className={`w-1 h-1 rounded-full animate-bounce ${
                      isDarkMode ? 'bg-gray-500' : 'bg-gray-400'
                    }`} style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span>Processando...</span>
                </div>
              </div>
            </motion.div>
          )}
          </div>
        )}
        
        {/* Barra de progresso compacta */}
        <div className={`relative mt-3 h-1 rounded-full overflow-hidden transition-colors duration-200 ${
          isDarkMode ? 'bg-gray-600' : 'bg-gray-200'
        }`}>
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full"
            initial={{ width: '0%' }}
            animate={{
              width: `${(currentStepIndex / Math.max(visibleSteps.length + (isStreaming ? 1 : 0), 1)) * 100}%`
            }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>
    );
  }

  // Versão completa para Dr. Will principal
  return (
    <div className={`relative border-2 rounded-2xl p-4 -m-4 overflow-hidden transition-colors duration-200 ${
      isDarkMode
        ? 'bg-gradient-to-br from-indigo-900/30 via-purple-900/30 to-pink-900/30 border-indigo-600'
        : 'bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 border-indigo-200'
    }`}>
      {/* Efeito de fundo animado */}
      <div className={`absolute inset-0 animate-pulse transition-colors duration-200 ${
        isDarkMode
          ? 'bg-gradient-to-r from-indigo-800/30 to-purple-800/30'
          : 'bg-gradient-to-r from-indigo-100/30 to-purple-100/30'
      }`}></div>
      
      {/* Header com botão */}
      <div className="relative flex items-center gap-3 mb-4">
        <div className="relative">
          <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-white text-xl shadow-lg">
            🧠
          </div>
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-ping"></div>
        </div>
        <div className="flex-1">
          <h4 className={`font-bold text-lg flex items-center gap-2 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-800'
          }`}>
            Dr. Will está pensando
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </h4>
          <p className={`text-sm font-medium transition-colors duration-200 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>
            Etapa {currentStepIndex} de {visibleSteps.length + (isStreaming ? 1 : 0)}
          </p>
        </div>
        <button
          onClick={() => setIsVisible(!isVisible)}
          className={`px-3 py-2 border rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-md ${
            isDarkMode
              ? 'bg-gray-700/70 hover:bg-gray-700/90 border-indigo-600 text-indigo-400'
              : 'bg-white/70 hover:bg-white/90 border-indigo-200 text-indigo-600'
          }`}
        >
          {isVisible ? 'Ocultar Pensamento' : 'Ver Pensamento'}
        </button>
      </div>

      {/* Etapas do thinking - só mostra se isVisible */}
      {isVisible && (
        <div className="relative space-y-3">
          {visibleSteps.map((step, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className={`relative backdrop-blur-sm rounded-xl p-3 border shadow-sm transition-colors duration-200 ${
              isDarkMode
                ? 'bg-gray-700/70 border-gray-600/50'
                : 'bg-white/70 border-white/50'
            }`}
          >
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0 mt-0.5">
                {index + 1}
              </div>
              <div className={`flex-1 text-sm leading-relaxed transition-colors duration-200 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <div
                  dangerouslySetInnerHTML={{
                    __html: formatThinkingContent(step, isDarkMode)
                  }}
                />
              </div>
            </div>
          </motion.div>
        ))}
        
        {/* Indicador de carregamento para próxima etapa */}
        {isStreaming && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={`relative backdrop-blur-sm rounded-xl p-3 border shadow-sm transition-colors duration-200 ${
              isDarkMode
                ? 'bg-gray-700/50 border-gray-600/30'
                : 'bg-white/50 border-white/30'
            }`}
          >
            <div className="flex items-center gap-3">
              <div className={`w-6 h-6 rounded-full flex items-center justify-center animate-pulse ${
                isDarkMode ? 'bg-gray-600' : 'bg-gray-300'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  isDarkMode ? 'bg-gray-400' : 'bg-gray-500'
                }`}></div>
              </div>
              <div className={`flex items-center gap-2 text-sm transition-colors duration-200 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                <div className="flex gap-1">
                  <div className={`w-1.5 h-1.5 rounded-full animate-bounce ${
                    isDarkMode ? 'bg-gray-500' : 'bg-gray-400'
                  }`}></div>
                  <div className={`w-1.5 h-1.5 rounded-full animate-bounce ${
                    isDarkMode ? 'bg-gray-500' : 'bg-gray-400'
                  }`} style={{ animationDelay: '0.1s' }}></div>
                  <div className={`w-1.5 h-1.5 rounded-full animate-bounce ${
                    isDarkMode ? 'bg-gray-500' : 'bg-gray-400'
                  }`} style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span>Processando próxima etapa...</span>
              </div>
            </div>
          </motion.div>
        )}
        </div>
      )}
      
      {/* Barra de progresso */}
      <div className={`relative mt-4 h-2 rounded-full overflow-hidden transition-colors duration-200 ${
        isDarkMode ? 'bg-gray-600' : 'bg-gray-200'
      }`}>
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full"
          initial={{ width: '0%' }}
          animate={{
            width: `${(currentStepIndex / Math.max(visibleSteps.length + (isStreaming ? 1 : 0), 1)) * 100}%`
          }}
          transition={{ duration: 0.5 }}
        />
      </div>
    </div>
  );
};
