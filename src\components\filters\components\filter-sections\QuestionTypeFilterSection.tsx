
import React from "react";
import { FilterItem } from "../../FilterItem";
import type { SelectedFilters } from "@/components/filters/types";
import { useSingletonTypeCounts } from "@/hooks/useSingletonHierarchicalCounts";

interface QuestionTypeFilterSectionProps {
  selectedTypes: string[];
  onToggleType: (type: string) => void;
  searchTerm?: string;
  selectedFilters: SelectedFilters;
  questionCounts?: {
    totalCounts: {[key: string]: number};
    filteredCounts: {[key: string]: number};
  };
}

export const QuestionTypeFilterSection = ({
  selectedTypes,
  onToggleType,
  searchTerm = "",
  selectedFilters,
  questionCounts
}: QuestionTypeFilterSectionProps) => {
  // ✅ SINGLETON: Usar cache singleton para contagens dinâmicas de tipos de prova
  const { data: hierarchicalCounts, isLoading: isLoadingHierarchical } = useSingletonTypeCounts(selectedFilters);

  // Verificar se há filtros hierárquicos ativos (excluindo question_types e question_formats)
  const hasHierarchicalFilters = (
    (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
    (selectedFilters.themes && selectedFilters.themes.length > 0) ||
    (selectedFilters.focuses && selectedFilters.focuses.length > 0) ||
    (selectedFilters.locations && selectedFilters.locations.length > 0) ||
    (selectedFilters.years && selectedFilters.years.length > 0)
  );

  const getTypeCount = (typeId: string) => {
    if (hasHierarchicalFilters && hierarchicalCounts) {
      // Se há filtros hierárquicos, usar contagens filtradas
      return hierarchicalCounts[typeId] || 0;
    }

    // Se não há filtros hierárquicos, usar contagem padrão
    return questionCounts?.totalCounts?.[typeId] || 0;
  };
  // ✅ OTIMIZADO: Define question types com contagens dinâmicas
  const questionTypes = [
    {
      id: 'teorica-1',
      name: 'Teórica I',
      count: getTypeCount('teorica-1')
    },
    {
      id: 'teorica-2',
      name: 'Teórica II',
      count: getTypeCount('teorica-2')
    },
    {
      id: 'teorico-pratica',
      name: 'Teórico-Prática',
      count: getTypeCount('teorico-pratica')
    }
  ];

  // Se há filtros hierárquicos, mostrar apenas tipos com questões. Se não há, mostrar todos
  const filteredQuestionTypes = hasHierarchicalFilters
    ? questionTypes.filter(type => type.count > 0)
    : questionTypes;

  // Filter question types based on search term
  const filteredTypes = filteredQuestionTypes.filter(type =>
    type.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Check if any filters are active
  const hasActiveFilters = Object.values(selectedFilters).some(filters => filters.length > 0);

  return (
    <div className="space-y-2">
      {filteredTypes.length === 0 ? (
        <div className="text-center py-4 text-gray-500">
          Nenhum tipo de prova encontrado
        </div>
      ) : (
        <div className="space-y-1">
          {filteredTypes.map(type => {
            const typeItem = {
              id: type.id,
              name: type.name,
              type: "question_type"
            };

            return (
              <FilterItem
                key={type.id}
                item={typeItem}
                level={0}
                isExpanded={false}
                isSelected={selectedTypes.includes(type.id)}
                questionCount={{
                  total: type.count,
                  filtered: type.count
                }}
                hasChildren={false}
                onToggleExpand={() => {}}
                onToggleSelect={() => onToggleType(type.id)}
                className={hasHierarchicalFilters ? "ring-2 ring-purple-200" : undefined} // Indicador visual
              />
            );
          })}
        </div>
      )}
    </div>
  );
};
