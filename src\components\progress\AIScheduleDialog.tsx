import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, DialogHeader, DialogTitle, DialogDescription, DialogPortal, DialogOverlay } from "@/components/ui/dialog";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { useForm, Controller, useFieldArray } from "react-hook-form";
import { toast } from "sonner";
import {
  Sparkles, Clock, CalendarDays, ListChecks, Timer,
  Plus, Trash2, Loader2, CheckCircle2, Info,
  AlertTriangle, AlertCircle, BookOpen, Lightbulb, BrainCircuit
} from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { useDomain } from '@/hooks/useDomain';
import { cn } from "@/lib/utils";

// Custom DialogContent without automatic close button
const CustomDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPrimitive.Content
    ref={ref}
    className={cn(
      "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border-2 border-black bg-white p-6 shadow-card-sm duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-xl max-h-[90vh] overflow-y-auto",
      className
    )}
    {...props}
  >
    {children}
  </DialogPrimitive.Content>
))
CustomDialogContent.displayName = DialogPrimitive.Content.displayName;

interface AIScheduleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: AIScheduleFormData) => void;
  isLoading: boolean;
  existingWeeks?: number[];
}

export interface StudyPeriod {
  startTime: string;
  endTime: string;
}

export interface DayConfig {
  enabled: boolean;
  periods: StudyPeriod[];
}

export interface AIScheduleFormData {
  availableDays: {
    [key: string]: DayConfig;
  };
  weeksCount: number;
  topicDuration: "15" | "30" | "60";
  scheduleOption: "new" | "existing";
  targetWeek?: number;
}

const WEEK_DAYS = [
  { id: "Domingo", label: "Domingo" },
  { id: "Segunda-feira", label: "Segunda-feira" },
  { id: "Terça-feira", label: "Terça-feira" },
  { id: "Quarta-feira", label: "Quarta-feira" },
  { id: "Quinta-feira", label: "Quinta-feira" },
  { id: "Sexta-feira", label: "Sexta-feira" },
  { id: "Sábado", label: "Sábado" },
];

// Helper function to check if time is valid and has minimum interval
const isValidTimeRange = (startTime: string, endTime: string): boolean => {
  if (!startTime || !endTime) return false;

  const start = new Date(`2000-01-01T${startTime}`);
  const end = new Date(`2000-01-01T${endTime}`);

  // Check if end time is after start time
  if (end <= start) return false;

  // Check if the difference is at least 15 minutes
  const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
  return diffMinutes >= 15;
};

const calculateTotalHours = (availableDays: { [key: string]: DayConfig }): number => {
  let totalMinutes = 0;

  Object.entries(availableDays).forEach(([day, config]) => {
    if (config.enabled && config.periods.length > 0) {
      const validPeriods = config.periods.filter(p => p.startTime && p.endTime);
      if (validPeriods.length > 0) {
        validPeriods.forEach(period => {
          if (period.startTime && period.endTime) {
            const start = new Date(`2000-01-01T${period.startTime}`);
            const end = new Date(`2000-01-01T${period.endTime}`);
            const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
            totalMinutes += diffMinutes;
          }
        });
      }
    }
  });

  return Math.round(totalMinutes / 60 * 10) / 10; // Round to 1 decimal place
};

// Animated messages to show during generation
const progressMessages = [
  "Analisando suas preferências...",
  "Selecionando tópicos relevantes...",
  "Organizando seu cronograma...",
  "Calculando distribuição de temas...",
  "Equilibrando carga de estudos...",
  "Aplicando personalização...",
  "Ajustando tempos de estudo...",
  "Verificando compatibilidade de horários...",
  "Priorizando conteúdos essenciais...",
  "Alocando recursos de estudo...",
  "Finalizando seu plano personalizado...",
];

// Generation phases
enum GenerationPhase {
  NOT_STARTED = 'not_started',
  CREATING_WEEKS = 'creating_weeks',
  ANALYZING_SPECIALTIES = 'analyzing_specialties',
  GENERATING_TOPICS = 'generating_topics',
  COMPLETING = 'completing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

export const AIScheduleDialog = ({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
  existingWeeks = []
}: AIScheduleDialogProps) => {
  // Get user domain
  const { domain, isReady } = useDomain();
  // Estados simples de progresso
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("");
  const [currentStep, setCurrentStep] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [generationPhase, setGenerationPhase] = useState<GenerationPhase>(GenerationPhase.NOT_STARTED);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [customWeeks, setCustomWeeks] = useState(false);
  const lastLogRef = useRef<string>("");
  const topicsCreatedRef = useRef(0);
  const lastCompletionCheckRef = useRef(0);
  const [formValid, setFormValid] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Initialize default periods for each day
  const defaultDays = WEEK_DAYS.reduce((acc, day) => {
    acc[day.id] = {
      enabled: ["Segunda-feira", "Quarta-feira", "Sexta-feira"].includes(day.id),
      periods: [{ startTime: "09:00", endTime: "11:00" }]
    };
    return acc;
  }, {} as { [key: string]: DayConfig });

  const { register, handleSubmit, control, watch, setValue, formState: { errors, isValid, isDirty } } = useForm<AIScheduleFormData>({
    defaultValues: {
      availableDays: defaultDays,
      weeksCount: 4,
      topicDuration: "30",
      scheduleOption: "new"
    },
    mode: "onChange"
  });

  const availableDays = watch("availableDays");
  const weeksCount = watch("weeksCount");
  const topicDuration = watch("topicDuration");
  const scheduleOption = watch("scheduleOption");

  // Calculate total study hours
  const totalHours = calculateTotalHours(availableDays);
  const enabledDaysCount = Object.values(availableDays).filter(day => day.enabled).length;

  // Form validation
  useEffect(() => {
    const newErrors: string[] = [];
    let valid = true;

    // Check if at least one day is selected
    if (enabledDaysCount === 0) {
      newErrors.push("Selecione pelo menos um dia da semana");
      valid = false;
    }

    // Validate time ranges
    let hasInvalidTimeRange = false;
    Object.entries(availableDays).forEach(([day, config]) => {
      if (config.enabled) {
        config.periods.forEach(period => {
          if (!isValidTimeRange(period.startTime, period.endTime)) {
            hasInvalidTimeRange = true;
          }
        });
      }
    });

    if (hasInvalidTimeRange) {
      newErrors.push("Verifique os horários. Cada período deve ter no mínimo 15 minutos");
      valid = false;
    }

    // Validate schedule option
    if (scheduleOption === "existing" && !watch("targetWeek")) {
      newErrors.push("Selecione uma semana existente");
      valid = false;
    }

    // Validate week count
    if (scheduleOption === "new") {
      const weekCountNumber = typeof weeksCount === 'number' ? weeksCount : parseInt(weeksCount as unknown as string);
      if (isNaN(weekCountNumber) || weekCountNumber < 1 || weekCountNumber > 50) {
        newErrors.push("O número de semanas deve estar entre 1 e 50");
        valid = false;
      }
    }

    setValidationErrors(newErrors);
    setFormValid(valid);
  }, [availableDays, enabledDaysCount, scheduleOption, weeksCount, watch]);

  // ✅ REMOVIDO: Auto-close que estava fechando o dialog
  // Agora usa o sistema de isComplete do hook para mostrar tela de sucesso

    // Se começou a carregar, simular progresso
    console.log('📊 [AIScheduleDialog] isLoading=true, iniciando progresso');
    setShowSuccessScreen(false);

    let timeouts: NodeJS.Timeout[] = [];

    const simulateProgress = () => {
      // Phase 1: Creating weeks (0-25%)
      timeouts.push(setTimeout(() => {
        if (isLoading) {
          setGenerationPhase(GenerationPhase.CREATING_WEEKS);
          setProgress(25);
        }
      }, 500));

      // Phase 2: Analyzing specialties (25-50%)
      timeouts.push(setTimeout(() => {
        if (isLoading) {
          setGenerationPhase(GenerationPhase.ANALYZING_SPECIALTIES);
          setProgress(50);
        }
      }, 2000));

      // Phase 3: Generating topics (50-75%)
      timeouts.push(setTimeout(() => {
        if (isLoading) {
          setGenerationPhase(GenerationPhase.GENERATING_TOPICS);
          setProgress(75);
        }
      }, 4000));

      // Phase 4: Completing (75-90%)
      timeouts.push(setTimeout(() => {
        if (isLoading) {
          setGenerationPhase(GenerationPhase.COMPLETING);
          setProgress(90);
        }
      }, 6000));
    };

    simulateProgress();

    // Clean up timeouts when component unmounts or loading stops
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [isLoading, onOpenChange]);

  // Helper function to add a new period to a day
  const addPeriod = (dayId: string) => {
    const currentDayConfig = availableDays[dayId];
    const updatedPeriods = [...currentDayConfig.periods, { startTime: "", endTime: "" }];

    setValue(`availableDays.${dayId}.periods`, updatedPeriods);
  };

  // Helper function to remove a period from a day
  const removePeriod = (dayId: string, periodIndex: number) => {
    const currentDayConfig = availableDays[dayId];

    // Don't remove if it's the last period
    if (currentDayConfig.periods.length <= 1) return;

    const updatedPeriods = currentDayConfig.periods.filter((_, index) => index !== periodIndex);
    setValue(`availableDays.${dayId}.periods`, updatedPeriods);
  };

  // Manage progress bar animation
  useEffect(() => {
    if (isLoading) {
      setIsComplete(false);
      topicsCreatedRef.current = 0;
      lastCompletionCheckRef.current = Date.now();

      // Set up periodic message update
      const messageInterval = setInterval(() => {
        // Update progress message every few seconds
        setCurrentStep((prev) => {
          const next = (prev + 1) % progressMessages.length;
          setProgressMessage(progressMessages[next]);
          return next;
        });
      }, 2000);

      // Fallback progress simulation if logs are not detected
      const progressInterval = setInterval(() => {
        setProgress((currentProgress) => {
          // If progress is still at initial 5% after 3 seconds, start simulating
          if (currentProgress <= 5) {
            return 15; // Jump to 15%
          }
          // If progress is stuck below 25% for too long, gradually increase
          if (currentProgress < 25) {
            return Math.min(currentProgress + 5, 25);
          }
          // If progress is stuck below 60% for too long, gradually increase
          if (currentProgress < 60) {
            return Math.min(currentProgress + 3, 60);
          }
          // If progress is stuck below 90% for too long, gradually increase
          if (currentProgress < 90) {
            return Math.min(currentProgress + 2, 90);
          }
          return currentProgress; // Don't go above 90% without completion signal
        });
      }, 3000);

      return () => {
        clearInterval(messageInterval);
        clearInterval(progressInterval);
      };
    }
  }, [isLoading]);

  // Reset quando dialog abre/fecha
  useEffect(() => {
    if (!open) {
      setTimeout(() => {
        setProgress(0);
        setProgressMessage("");
        setCurrentStep(0);
        setIsComplete(false);
        setGenerationPhase(GenerationPhase.NOT_STARTED);
        setShowSuccessScreen(false);
        topicsCreatedRef.current = 0;
        lastCompletionCheckRef.current = 0;
      }, 300);
    } else if (open) {
      setProgress(0);
      setProgressMessage("");
      setCurrentStep(0);
      setIsComplete(false);
      setGenerationPhase(GenerationPhase.NOT_STARTED);
      setShowSuccessScreen(false);
      topicsCreatedRef.current = 0;
      lastCompletionCheckRef.current = 0;

      if (!isLoading) {
        setProgressMessage(progressMessages[0]);
      }
    }
  }, [open, isLoading]);

  useEffect(() => {
    if (open && domain) {
      // Dialog opened with user domain
    }
  }, [open, domain]);

  const onSubmitForm = (data: AIScheduleFormData) => {
    // Força a abertura do dialog com progresso
    if (!open) onOpenChange(true);



    // Check if at least one day is selected
    const enabledDays = Object.values(data.availableDays).filter(day => day.enabled);
    if (enabledDays.length === 0) {
      toast.error("Selecione pelo menos um dia da semana");
      return;
    }

    // Validate time ranges
    let hasInvalidTimeRange = false;
    enabledDays.forEach(dayConfig => {
      dayConfig.periods.forEach(period => {
        if (!isValidTimeRange(period.startTime, period.endTime)) {
          hasInvalidTimeRange = true;
        }
      });
    });

    if (hasInvalidTimeRange) {
      toast.error("Verifique os horários. Cada período deve ter no mínimo 15 minutos");
      return;
    }

    // Validate schedule option
    if (data.scheduleOption === "existing" && !data.targetWeek) {
      toast.error("Selecione uma semana existente para adicionar o cronograma");
      return;
    }

    // Start progress bar
    setProgress(5);
    setProgressMessage(progressMessages[0]);
    setIsComplete(false);
    setGenerationPhase(GenerationPhase.NOT_STARTED);
    topicsCreatedRef.current = 0;

    onSubmit(data);
  };

  const handleClose = () => {
    if (!isLoading) {
      onOpenChange(false);
    } else {
      // Warning if trying to close during loading
      toast.warning("Aguarde a conclusão da geração do cronograma antes de fechar.");
    }
  };

  const getPhaseDescription = (phase: GenerationPhase): string => {
    switch (phase) {
      case GenerationPhase.CREATING_WEEKS:
        return "Criando estrutura de semanas";
      case GenerationPhase.ANALYZING_SPECIALTIES:
        return "Analisando especialidades disponíveis";
      case GenerationPhase.GENERATING_TOPICS:
        return "Gerando tópicos de estudo";
      case GenerationPhase.COMPLETING:
        return "Finalizando cronograma";
      case GenerationPhase.COMPLETED:
        return "Cronograma concluído";
      case GenerationPhase.ERROR:
        return "Erro na geração";
      default:
        return "Preparando...";
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <CustomDialogContent className="w-[95vw] max-w-[650px] max-h-[85dvh] overflow-hidden p-0 border-2 border-black shadow-2xl rounded-xl">
        {/* Modern colorful background with MedEvo style */}
        <div className="absolute inset-0 bg-amber-50 rounded-xl"></div>
        <div className="absolute top-0 right-0 w-40 h-40 bg-yellow-300 rounded-full blur-3xl opacity-10 -mr-10 -mt-10"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-blue-400 rounded-full blur-3xl opacity-15 -ml-10 -mb-10"></div>

        {/* Content overlay */}
        <div className="relative z-10 p-3 sm:p-4 lg:p-6">
          <DialogHeader className="mb-3 sm:mb-4">
            <div className="flex items-center justify-between gap-2 sm:gap-3">
              <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                <div className="p-1.5 sm:p-2 rounded-full bg-[#1CB0F6]/10 flex-shrink-0">
                  <BrainCircuit className="w-4 h-4 sm:w-5 sm:h-5 text-[#1CB0F6]" />
                </div>
                <div className="min-w-0 flex-1">
                  <DialogTitle className="text-base sm:text-lg font-bold text-slate-800">
                    Personalizar com IA
                  </DialogTitle>
                  <DialogDescription className="text-xs sm:text-sm text-slate-600 mt-0.5">
                    Configure suas preferências e a IA criará um cronograma personalizado
                  </DialogDescription>
                  {domain && (
                    <div className="mt-1 text-xs font-medium text-blue-600">
                      Domínio: {domain}
                    </div>
                  )}
                </div>
              </div>

              {/* Close button */}
              <button
                onClick={handleClose}
                className="flex-shrink-0 p-1.5 sm:p-2 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Fechar"
              >
                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </DialogHeader>

          {isLoading || showSuccessScreen ? (
            <div className="py-2 sm:py-3 space-y-4 sm:space-y-6 animate-in fade-in">
              {/* Warning not to close during generation */}
              {isLoading && !showSuccessScreen && (
                <div className="flex p-3 sm:p-4 space-x-2 sm:space-x-3 border-2 rounded-lg shadow-md bg-amber-50 border-amber-200">
                  <AlertTriangle className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 text-amber-500 mt-0.5" />
                  <div className="space-y-1">
                    <h4 className="font-bold text-amber-800 text-sm sm:text-base">Atenção! Não feche esta janela</h4>
                    <p className="text-xs sm:text-sm text-amber-700">
                      A geração do cronograma está em andamento. Fechar agora interromperá o processo.
                    </p>
                  </div>
                </div>
              )}

              {/* Progress bar and status */}
              <div className="p-4 sm:p-5 rounded-xl bg-white shadow-lg border-2 border-white">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <p className="text-base font-bold text-blue-700">{progressMessage}</p>
                    <span className="px-3 py-1 text-sm font-bold text-blue-700 rounded-full bg-blue-100">{Math.round(progress)}%</span>
                  </div>

                  <Progress
                    value={progress}
                    className="h-3 w-full bg-blue-100"
                  />

                  <div className="pt-2 space-y-2 text-sm text-slate-600">
                    <p className="flex items-center gap-2">
                      <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
                      Analisando {Object.values(availableDays).filter(d => d.enabled).length} dias da semana
                    </p>
                    <p className="flex items-center gap-2">
                      <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
                      Preparando {scheduleOption === "new" ? weeksCount : 1} semana(s) de estudos
                    </p>
                    <p className="flex items-center gap-2">
                      <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
                      Tópicos com duração de {topicDuration} minutos
                    </p>
                    <p className="flex items-center gap-2">
                      <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
                      Total de horas: {totalHours.toFixed(1)} horas por semana
                    </p>
                    {domain && (
                      <p className="flex items-center gap-2">
                        <span className="inline-block w-2 h-2 rounded-full bg-blue-500"></span>
                        Domínio de estudos: {domain}
                      </p>
                    )}
                  </div>
                </div>

                {/* Generation phases tracker */}
                <div className="mt-8 pt-6 border-t-2 border-slate-100">
                  <h4 className="mb-4 text-base font-bold text-blue-700">Progresso da geração:</h4>

                  <div className="space-y-4">
                    {/* Creation phase */}
                    <div className="flex items-center">
                      <div className={cn(
                        "w-8 h-8 flex items-center justify-center rounded-full mr-3",
                        generationPhase === GenerationPhase.CREATING_WEEKS
                          ? "bg-blue-100"
                          : generationPhase > GenerationPhase.CREATING_WEEKS
                            ? "bg-green-100"
                            : "bg-slate-100"
                      )}>
                        {generationPhase === GenerationPhase.CREATING_WEEKS ? (
                          <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                        ) : generationPhase > GenerationPhase.CREATING_WEEKS ? (
                          <CheckCircle2 className="w-4 h-4 text-green-600" />
                        ) : (
                          <div className="w-2.5 h-2.5 bg-slate-300 rounded-full" />
                        )}
                      </div>
                      <span className={cn(
                        "text-sm font-medium",
                        generationPhase === GenerationPhase.CREATING_WEEKS
                          ? "text-blue-700"
                          : generationPhase > GenerationPhase.CREATING_WEEKS
                            ? "text-green-700"
                            : "text-slate-500"
                      )}>
                        Criando estrutura de semanas
                      </span>
                    </div>

                    {/* Analysis phase */}
                    <div className="flex items-center">
                      <div className={cn(
                        "w-8 h-8 flex items-center justify-center rounded-full mr-3",
                        generationPhase === GenerationPhase.ANALYZING_SPECIALTIES
                          ? "bg-blue-100"
                          : generationPhase > GenerationPhase.ANALYZING_SPECIALTIES
                            ? "bg-green-100"
                            : "bg-slate-100"
                      )}>
                        {generationPhase === GenerationPhase.ANALYZING_SPECIALTIES ? (
                          <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                        ) : generationPhase > GenerationPhase.ANALYZING_SPECIALTIES ? (
                          <CheckCircle2 className="w-4 h-4 text-green-600" />
                        ) : (
                          <div className="w-2.5 h-2.5 bg-slate-300 rounded-full" />
                        )}
                      </div>
                      <span className={cn(
                        "text-sm font-medium",
                        generationPhase === GenerationPhase.ANALYZING_SPECIALTIES
                          ? "text-blue-700"
                          : generationPhase > GenerationPhase.ANALYZING_SPECIALTIES
                            ? "text-green-700"
                            : "text-slate-500"
                      )}>
                        Analisando especialidades disponíveis
                      </span>
                    </div>

                    {/* Topic generation phase */}
                    <div className="flex items-center">
                      <div className={cn(
                        "w-8 h-8 flex items-center justify-center rounded-full mr-3",
                        generationPhase === GenerationPhase.GENERATING_TOPICS
                          ? "bg-blue-100"
                          : generationPhase > GenerationPhase.GENERATING_TOPICS
                            ? "bg-green-100"
                            : "bg-slate-100"
                      )}>
                        {generationPhase === GenerationPhase.GENERATING_TOPICS ? (
                          <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                        ) : generationPhase > GenerationPhase.GENERATING_TOPICS ? (
                          <CheckCircle2 className="w-4 h-4 text-green-600" />
                        ) : (
                          <div className="w-2.5 h-2.5 bg-slate-300 rounded-full" />
                        )}
                      </div>
                      <span className={cn(
                        "text-sm font-medium",
                        generationPhase === GenerationPhase.GENERATING_TOPICS
                          ? "text-blue-700"
                          : generationPhase > GenerationPhase.GENERATING_TOPICS
                            ? "text-green-700"
                            : "text-slate-500"
                      )}>
                        Gerando tópicos de estudo
                      </span>
                    </div>

                    {/* Completion phase */}
                    <div className="flex items-center">
                      <div className={cn(
                        "w-8 h-8 flex items-center justify-center rounded-full mr-3",
                        generationPhase === GenerationPhase.COMPLETING
                          ? "bg-blue-100"
                          : generationPhase > GenerationPhase.COMPLETING
                            ? "bg-green-100"
                            : "bg-slate-100"
                      )}>
                        {generationPhase === GenerationPhase.COMPLETING ? (
                          <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                        ) : generationPhase > GenerationPhase.COMPLETING ? (
                          <CheckCircle2 className="w-4 h-4 text-green-600" />
                        ) : (
                          <div className="w-2.5 h-2.5 bg-slate-300 rounded-full" />
                        )}
                      </div>
                      <span className={cn(
                        "text-sm font-medium",
                        generationPhase === GenerationPhase.COMPLETING
                          ? "text-blue-700"
                          : generationPhase > GenerationPhase.COMPLETING
                            ? "text-green-700"
                            : "text-slate-500"
                      )}>
                        Finalizando cronograma
                      </span>
                    </div>
                  </div>

                  {topicsCreatedRef.current > 0 && (
                    <div className="flex items-center gap-2 px-4 py-3 mt-6 bg-blue-50 border-2 border-blue-100 rounded-lg">
                      <Lightbulb className="w-5 h-5 text-blue-600" />
                      <p className="text-sm font-bold text-blue-700">
                        {topicsCreatedRef.current} tópicos gerados
                        {generationPhase !== GenerationPhase.COMPLETED && " até o momento"}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Enhanced Success message */}
              {showSuccessScreen ? (
                <div className="p-4 sm:p-6 rounded-xl border-2 border-green-200 bg-gradient-to-br from-green-50 to-green-100 shadow-lg">
                  <div className="text-center space-y-4">
                    {/* Success Icon with Animation */}
                    <div className="flex justify-center">
                      <div className="relative">
                        <div className="p-3 sm:p-4 bg-white rounded-full shadow-lg border-2 border-green-200">
                          <CheckCircle2 className="w-8 h-8 sm:w-10 sm:h-10 text-green-600 animate-pulse" />
                        </div>
                        {/* Celebration particles */}
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-bounce"></div>
                        <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-100"></div>
                        <div className="absolute top-1 -left-2 w-2 h-2 bg-pink-400 rounded-full animate-bounce delay-200"></div>
                      </div>
                    </div>

                    {/* Success Message */}
                    <div className="space-y-2">
                      <h3 className="text-lg sm:text-xl font-bold text-green-800">
                        🎉 Cronograma Criado com Sucesso!
                      </h3>
                      <p className="text-sm sm:text-base text-green-700">
                        Seu cronograma personalizado foi gerado com <span className="font-semibold">IA avançada</span> e está pronto para uso.
                      </p>
                    </div>

                    {/* Stats Summary */}
                    <div className="grid grid-cols-2 gap-3 sm:gap-4 py-3 px-2 bg-white/50 rounded-lg border border-green-200">
                      <div className="text-center">
                        <div className="text-lg sm:text-xl font-bold text-green-800">{topicsCreatedRef.current}</div>
                        <div className="text-xs sm:text-sm text-green-600">Tópicos Criados</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg sm:text-xl font-bold text-green-800">{scheduleOption === "new" ? weeksCount : 1}</div>
                        <div className="text-xs sm:text-sm text-green-600">Semana(s)</div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                      <Button
                        onClick={handleClose}
                        className="flex-1 h-10 sm:h-12 text-sm sm:text-base font-bold text-white bg-green-600 hover:bg-green-700 shadow-lg border-2 border-green-800 rounded-lg transition-all hover:scale-105"
                      >
                        <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        Visualizar Cronograma
                      </Button>

                      <Button
                        onClick={handleClose}
                        variant="outline"
                        className="h-10 sm:h-12 text-sm sm:text-base font-bold border-2 border-green-600 text-green-700 hover:bg-green-50 rounded-lg px-4"
                      >
                        Fechar
                      </Button>
                    </div>

                    {/* Tip */}
                    <div className="text-xs sm:text-sm text-green-600 bg-green-50 p-2 sm:p-3 rounded-lg border border-green-200">
                      💡 <span className="font-medium">Dica:</span> Você pode editar e personalizar seu cronograma a qualquer momento!
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center gap-3 p-4 border-2 rounded-lg bg-white border-slate-100 shadow-md">
                  <Loader2 className="w-6 h-6 text-blue-600 animate-spin" />
                  <span className="font-bold text-slate-700">
                    {getPhaseDescription(generationPhase)}...
                  </span>
                </div>
              )}
            </div>
          ) : (
            <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-3 sm:space-y-4">
              <ScrollArea className="h-[45dvh] sm:h-[50dvh] lg:h-[55dvh] pr-2 sm:pr-3">
                <div className="pr-1 sm:pr-2 space-y-3 sm:space-y-4">
                  {/* Duration selection */}
                  <div className="p-4 sm:p-6 space-y-3 sm:space-y-4 border-2 border-slate-200 rounded-xl bg-white shadow-md">
                    <div className="flex items-center gap-2 text-slate-800">
                      <div className="p-1.5 sm:p-2 rounded-full bg-amber-100">
                        <Timer className="w-4 h-4 sm:w-5 sm:h-5 text-amber-600" />
                      </div>
                      <h3 className="text-base sm:text-lg font-bold">Duração de cada tópico</h3>
                    </div>

                    <RadioGroup
                      value={topicDuration}
                      onValueChange={(value) => setValue("topicDuration", value as "15" | "30" | "60")}
                      className="grid grid-cols-3 gap-2 sm:gap-3 mt-3"
                    >
                      <div className={cn(
                        "flex items-center justify-center space-x-1 sm:space-x-2 border-2 rounded-lg p-2 sm:p-3 cursor-pointer transition-colors",
                        topicDuration === "15"
                          ? "border-blue-500 bg-blue-50"
                          : "border-slate-200 hover:bg-slate-50"
                      )}>
                        <RadioGroupItem value="15" id="duration-15" className="text-blue-600 w-3 h-3 sm:w-4 sm:h-4" />
                        <Label htmlFor="duration-15" className="cursor-pointer font-bold text-xs sm:text-sm">15 min</Label>
                      </div>

                      <div className={cn(
                        "flex items-center justify-center space-x-1 sm:space-x-2 border-2 rounded-lg p-2 sm:p-3 cursor-pointer transition-colors",
                        topicDuration === "30"
                          ? "border-blue-500 bg-blue-50"
                          : "border-slate-200 hover:bg-slate-50"
                      )}>
                        <RadioGroupItem value="30" id="duration-30" className="text-blue-600 w-3 h-3 sm:w-4 sm:h-4" />
                        <Label htmlFor="duration-30" className="cursor-pointer font-bold text-xs sm:text-sm">30 min</Label>
                      </div>

                      <div className={cn(
                        "flex items-center justify-center space-x-1 sm:space-x-2 border-2 rounded-lg p-2 sm:p-3 cursor-pointer transition-colors",
                        topicDuration === "60"
                          ? "border-blue-500 bg-blue-50"
                          : "border-slate-200 hover:bg-slate-50"
                      )}>
                        <RadioGroupItem value="60" id="duration-60" className="text-blue-600 w-3 h-3 sm:w-4 sm:h-4" />
                        <Label htmlFor="duration-60" className="cursor-pointer font-bold text-xs sm:text-sm">1 hora</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Days and periods selection */}
                  <div className="p-4 sm:p-6 space-y-3 sm:space-y-4 border-2 border-slate-200 rounded-xl bg-white shadow-md">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-slate-800">
                        <div className="p-1.5 sm:p-2 rounded-full bg-emerald-100">
                          <CalendarDays className="w-4 h-4 sm:w-5 sm:h-5 text-emerald-600" />
                        </div>
                        <h3 className="text-base sm:text-lg font-bold">Dias e períodos disponíveis</h3>
                      </div>
                      <span className="px-2 py-1 sm:px-3 text-xs sm:text-sm font-bold rounded-full bg-emerald-100 text-emerald-700">
                        {enabledDaysCount} dias
                      </span>
                    </div>

                    <div className="overflow-hidden divide-y border-2 border-slate-200 rounded-xl shadow-md">
                      {WEEK_DAYS.map((day, index) => (
                        <div key={day.id} className={cn(
                          "py-3 px-3 sm:py-4 sm:px-5",
                          availableDays[day.id]?.enabled ? "bg-white" : "bg-slate-50"
                        )}>
                          <div className="flex items-center justify-between mb-2 sm:mb-3">
                            <div className="flex items-center space-x-2">
                              <Controller
                                name={`availableDays.${day.id}.enabled`}
                                control={control}
                                render={({ field }) => (
                                  <Checkbox
                                    id={`day-${day.id}`}
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    className={cn(
                                      "border-slate-300 w-4 h-4 sm:w-5 sm:h-5",
                                      field.value ? "text-blue-600 border-blue-600" : ""
                                    )}
                                  />
                                )}
                              />
                              <Label htmlFor={`day-${day.id}`} className="text-sm sm:text-base font-bold cursor-pointer">
                                {day.label}
                              </Label>
                            </div>

                            {availableDays[day.id]?.enabled && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                className="h-7 sm:h-8 text-xs border-blue-600 text-blue-600 hover:bg-blue-50 font-bold px-2 sm:px-3"
                                onClick={() => addPeriod(day.id)}
                              >
                                <Plus className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1" />
                                <span className="hidden sm:inline">Adicionar período</span>
                                <span className="sm:hidden">Adicionar</span>
                              </Button>
                            )}
                          </div>

                          {availableDays[day.id]?.enabled && (
                            <div className="pl-4 sm:pl-6 mt-3 sm:mt-4 space-y-3 sm:space-y-4">
                              {availableDays[day.id]?.periods.map((period, periodIndex) => (
                                <div key={periodIndex} className="flex items-end gap-2 sm:gap-3">
                                  <div className="grid flex-1 grid-cols-2 gap-2 sm:gap-3">
                                    <div>
                                      <div className="mb-1 text-xs font-medium text-slate-500">Início</div>
                                      <Controller
                                        name={`availableDays.${day.id}.periods.${periodIndex}.startTime`}
                                        control={control}
                                        render={({ field }) => (
                                          <Input
                                            type="time"
                                            value={field.value}
                                            onChange={field.onChange}
                                            className="h-8 sm:h-10 border-2 border-slate-300 focus:border-blue-600 focus:ring-blue-600/20 font-medium text-sm"
                                          />
                                        )}
                                      />
                                    </div>
                                    <div>
                                      <div className="mb-1 text-xs font-medium text-slate-500">Término</div>
                                      <Controller
                                        name={`availableDays.${day.id}.periods.${periodIndex}.endTime`}
                                        control={control}
                                        render={({ field }) => (
                                          <Input
                                            type="time"
                                            value={field.value}
                                            onChange={field.onChange}
                                            className="h-8 sm:h-10 border-2 border-slate-300 focus:border-blue-600 focus:ring-blue-600/20 font-medium text-sm"
                                          />
                                        )}
                                      />
                                    </div>
                                  </div>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    className="w-8 h-8 sm:w-9 sm:h-9 text-red-500 hover:text-red-600 hover:bg-red-50 flex-shrink-0"
                                    onClick={() => removePeriod(day.id, periodIndex)}
                                    disabled={availableDays[day.id]?.periods.length <= 1}
                                  >
                                    <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Schedule options */}
                  <div className="p-4 sm:p-6 space-y-3 sm:space-y-4 border-2 border-slate-200 rounded-xl bg-white shadow-md">
                    <div className="flex items-center gap-2 text-slate-800">
                      <div className="p-1.5 sm:p-2 rounded-full bg-purple-100">
                        <ListChecks className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600" />
                      </div>
                      <h3 className="text-base sm:text-lg font-bold">Opções de cronograma</h3>
                    </div>

                    <RadioGroup
                      value={scheduleOption}
                      onValueChange={(value) => {
                        setValue("scheduleOption", value as "new" | "existing");
                        if (value === "existing") {
                          setCustomWeeks(false);
                        }
                      }}
                      className="space-y-3 mt-3"
                    >
                      <div className={cn(
                        "flex items-center space-x-2 sm:space-x-3 border-2 rounded-lg p-3 sm:p-4 cursor-pointer transition-colors",
                        scheduleOption === "new"
                          ? "border-blue-500 bg-blue-50"
                          : "border-slate-200 hover:bg-slate-50"
                      )}>
                        <RadioGroupItem value="new" id="option-new" className="text-blue-600 w-4 h-4" />
                        <div className="flex-1 min-w-0">
                          <Label htmlFor="option-new" className="text-sm sm:text-base font-bold cursor-pointer">Criar novas semanas</Label>
                          <p className="mt-1 text-xs sm:text-sm text-slate-600">
                            Adiciona novas semanas ao seu cronograma, a partir da última semana existente.
                          </p>
                        </div>
                      </div>

                      <div className={cn(
                        "flex items-center space-x-2 sm:space-x-3 border-2 rounded-lg p-3 sm:p-4 cursor-pointer transition-colors",
                        existingWeeks.length === 0 && "opacity-60",
                        scheduleOption === "existing"
                          ? "border-blue-500 bg-blue-50"
                          : "border-slate-200 hover:bg-slate-50"
                      )}>
                        <RadioGroupItem
                          value="existing"
                          id="option-existing"
                          disabled={existingWeeks.length === 0}
                          className="text-blue-600 w-4 h-4"
                        />
                        <div className="flex-1 min-w-0">
                          <Label htmlFor="option-existing" className="text-sm sm:text-base font-bold cursor-pointer">
                            Adicionar à semana existente
                          </Label>
                          <p className="mt-1 text-xs sm:text-sm text-slate-600">
                            {existingWeeks.length > 0
                              ? "Adiciona tópicos de estudo a uma semana que já existe no seu cronograma."
                              : "Não há semanas existentes no seu cronograma."}
                          </p>

                          {scheduleOption === "existing" && existingWeeks.length > 0 && (
                            <div className="mt-3 sm:mt-4">
                              <Label htmlFor="targetWeek" className="text-xs sm:text-sm font-bold">Selecione a semana</Label>
                              <Controller
                                name="targetWeek"
                                control={control}
                                render={({ field }) => (
                                  <Select
                                    value={field.value?.toString() || ""}
                                    onValueChange={(value) => field.onChange(parseInt(value))}
                                  >
                                    <SelectTrigger className="mt-2 border-2 border-slate-300 focus-visible:ring-blue-600/20 bg-white font-medium h-9 sm:h-10 text-sm">
                                      <SelectValue placeholder="Selecione uma semana" />
                                    </SelectTrigger>
                                    <SelectContent className="bg-white shadow-lg border-slate-200">
                                      {existingWeeks.map(week => (
                                        <SelectItem key={week} value={week.toString()} className="font-medium text-sm">
                                          Semana {week}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                )}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </RadioGroup>

                    {scheduleOption === "new" && (
                      <div className="p-3 sm:p-4 mt-3 sm:mt-4 border-2 rounded-lg bg-white shadow-md border-slate-100">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 gap-2 sm:gap-0">
                          <div className="space-y-1">
                            <Label htmlFor="weeksCount" className="flex items-center gap-2 text-slate-800 font-bold text-sm sm:text-base">
                              Número de semanas a criar
                            </Label>
                            <div className="flex items-center">
                              <Checkbox
                                id="customWeeks"
                                checked={customWeeks}
                                onCheckedChange={(checked) => setCustomWeeks(!!checked)}
                                className="mr-2 text-blue-600 border-blue-600 w-4 h-4"
                              />
                              <Label
                                htmlFor="customWeeks"
                                className="text-xs text-slate-600 cursor-pointer font-medium"
                              >
                                Personalizar quantidade
                              </Label>
                            </div>
                          </div>
                          <span className="px-2 py-1 sm:px-3 text-xs sm:text-sm font-bold text-blue-700 bg-blue-100 rounded-full self-start sm:self-auto">
                            {weeksCount} semanas
                          </span>
                        </div>

                        {customWeeks ? (
                          <div className="space-y-2">
                            <Input
                              id="weeksCount"
                              type="number"
                              min={1}
                              max={50}
                              {...register('weeksCount', {
                                valueAsNumber: true,
                                min: 1,
                                max: 50
                              })}
                              className="max-w-[120px] sm:max-w-[150px] border-2 border-slate-300 focus:border-blue-600 focus:ring-blue-600/20 h-8 sm:h-10 font-medium text-sm"
                            />
                            <p className="text-xs text-slate-500 font-medium">
                              Escolha entre 1 e 50 semanas
                            </p>
                          </div>
                        ) : (
                          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 mt-3">
                            {[1, 4, 8, 12].map(count => (
                              <Button
                                key={count}
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => setValue('weeksCount', count)}
                                className={cn(
                                  "h-8 sm:h-10 border-2 font-bold text-xs sm:text-sm px-2 sm:px-3",
                                  weeksCount === count
                                    ? "border-blue-500 bg-blue-50 text-blue-700"
                                    : "border-slate-200"
                                )}
                              >
                                <span className="hidden sm:inline">{count} {count === 1 ? 'semana' : 'semanas'}</span>
                                <span className="sm:hidden">{count}{count === 1 ? 's' : 'sem'}</span>
                              </Button>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Schedule summary */}
                  <div className="p-3 sm:p-4 space-y-2 sm:space-y-3 border-2 border-slate-200 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 shadow-md">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="p-1.5 sm:p-2 rounded-full bg-blue-100">
                        <Info className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
                      </div>
                      <h3 className="text-base sm:text-lg font-bold text-blue-800">Resumo do cronograma</h3>
                    </div>

                    <div className="flex items-center justify-between text-xs sm:text-sm">
                      <span className="text-slate-600 font-medium">Total de estudo:</span>
                      <span className="font-bold text-blue-700">{totalHours.toFixed(1)} h/sem</span>
                    </div>

                    <div className="flex items-center justify-between text-xs sm:text-sm">
                      <span className="text-slate-600 font-medium">Horas em {scheduleOption === "new" ? weeksCount : 1} semana(s):</span>
                      <span className="font-bold text-blue-700">
                        {(totalHours * (scheduleOption === "new" ? weeksCount : 1)).toFixed(1)} h
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-xs sm:text-sm">
                      <span className="text-slate-600 font-medium">Duração dos temas:</span>
                      <span className="font-bold text-blue-700">{topicDuration} min</span>
                    </div>

                    <div className="flex items-center justify-between text-xs sm:text-sm">
                      <span className="text-slate-600 font-medium">Temas por semana:</span>
                      <span className="font-bold text-blue-700">
                        ~{Math.round(totalHours * 60 / parseInt(topicDuration))}
                      </span>
                    </div>

                    {domain && (
                      <div className="flex items-center justify-between pt-2 sm:pt-3 mt-2 sm:mt-3 text-xs sm:text-sm border-t border-blue-100">
                        <span className="text-slate-600 font-medium">Domínio:</span>
                        <span className="font-bold text-blue-700 truncate ml-2">{domain}</span>
                      </div>
                    )}
                  </div>

                  {/* Validation errors */}
                  {validationErrors.length > 0 && (
                    <div className="p-3 sm:p-4 border-2 rounded-lg bg-red-50 border-red-200 shadow-md">
                      <div className="flex items-start gap-2 sm:gap-3">
                        <AlertCircle className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 text-red-500 mt-0.5" />
                        <div className="min-w-0 flex-1">
                          <p className="font-bold text-red-800 text-sm sm:text-base">Por favor, corrija os seguintes erros:</p>
                          <ul className="mt-2 ml-3 sm:ml-5 space-y-1 text-xs sm:text-sm list-disc text-red-700">
                            {validationErrors.map((error, i) => (
                              <li key={i}>{error}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>

              {/* Submit button */}
              <Button
                type="submit"
                className="w-full h-10 sm:h-12 lg:h-14 text-sm sm:text-base lg:text-lg font-bold text-white bg-blue-600 hover:bg-blue-700 shadow-lg border-b-2 border-blue-800 rounded-xl"
                disabled={isLoading || !formValid}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 animate-spin" />
                    <span className="hidden sm:inline">Gerando cronograma...</span>
                    <span className="sm:hidden">Gerando...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2" />
                    <span className="hidden sm:inline">Gerar cronograma com IA</span>
                    <span className="sm:hidden">Gerar com IA</span>
                  </>
                )}
              </Button>
            </form>
          )}
        </div>
      </CustomDialogContent>
    </Dialog>
  );
};
