import React from 'react';
import { ReviewButtons } from './ReviewButtons';
import { Card } from "@/components/ui/card";
import type { FlashcardResponse } from "@/types/flashcard";

export const FlashcardTest = () => {
  const mockCard = {
    id: 'test-card-001',
    front: 'Pergunta Teste',
    back: 'Resposta Teste',
    stability: 1.0,
    difficulty: 5.0,
    retrievability: 0.9,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    user_id: 'test-user-001',
    specialty_id: 'test-specialty-001',
    current_state: 'available' as const
  };

  const mockResponse = (feedback: FlashcardResponse) => {
    // Feedback recebido
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-4"><PERSON>e de Flashcard</h2>
        <div className="space-y-4">
          <div>
            <p className="font-semibold">Frente:</p>
            <p>{mockCard.front}</p>
          </div>
          <div>
            <p className="font-semibold">Verso:</p>
            <p>{mockCard.back}</p>
          </div>
        </div>
      </Card>

      <ReviewButtons
        currentCard={mockCard}
        onResponse={mockResponse}
        isDisabled={false}
      />
    </div>
  );
};