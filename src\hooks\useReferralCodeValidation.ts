import { useState, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface ValidationResult {
  isValid: boolean;
  error?: string;
  message?: string;
  isChecking: boolean;
  canUpdate?: boolean;
  nextUpdateTime?: string;
}

export const useReferralCodeValidation = () => {
  const { user } = useAuth();
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: false,
    isChecking: false
  });

  // Cache de validações para evitar requests desnecessários
  const validationCache = useRef<Map<string, ValidationResult>>(new Map());
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  const validateCodeImmediate = useCallback(async (code: string): Promise<ValidationResult> => {
    const normalizedCode = code.toUpperCase().trim();

    if (!normalizedCode) {
      return {
        isValid: false,
        error: 'Código não pode estar vazio',
        isChecking: false
      };
    }

    // Validações básicas no frontend
    if (normalizedCode.length < 3) {
      return {
        isValid: false,
        error: 'Código deve ter pelo menos 3 caracteres',
        isChecking: false
      };
    }

    if (normalizedCode.length > 20) {
      return {
        isValid: false,
        error: 'Código deve ter no máximo 20 caracteres',
        isChecking: false
      };
    }

    // Verificar se contém apenas letras e números
    if (!/^[A-Z0-9]+$/.test(normalizedCode)) {
      return {
        isValid: false,
        error: 'Código deve conter apenas letras e números',
        isChecking: false
      };
    }

    // Verificar cache primeiro
    const cacheKey = `${normalizedCode}_${user?.id}`;
    if (validationCache.current.has(cacheKey)) {
      const cachedResult = validationCache.current.get(cacheKey)!;
      setValidationResult(cachedResult);
      return cachedResult;
    }

    try {
      // Verificar se o código já existe no banco
      const { data, error } = await supabase
        .from('user_referrals')
        .select('referrer_id')
        .ilike('referral_code', normalizedCode)
        .neq('referrer_id', user?.id || '') // Excluir o próprio usuário
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      const result: ValidationResult = data ? {
        isValid: false,
        error: 'Este código já está em uso',
        isChecking: false
      } : {
        isValid: true,
        message: 'Código disponível! ✅',
        isChecking: false
      };

      // Salvar no cache
      validationCache.current.set(cacheKey, result);
      setValidationResult(result);
      return result;

    } catch (error) {
      const result = {
        isValid: false,
        error: 'Erro ao verificar disponibilidade',
        isChecking: false
      };
      setValidationResult(result);
      return result;
    }
  }, [user?.id]);

  const validateCode = useCallback((code: string): Promise<ValidationResult> => {
    return new Promise((resolve) => {
      // Limpar timer anterior
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }

      const normalizedCode = code.toUpperCase().trim();

      // Validações básicas imediatas (sem debounce)
      if (!normalizedCode || normalizedCode.length < 3 || normalizedCode.length > 20 || !/^[A-Z0-9]+$/.test(normalizedCode)) {
        const result = validateCodeImmediate(code);
        resolve(result);
        return;
      }

      // Mostrar estado de carregamento
      setValidationResult({ isValid: false, isChecking: true });

      // Debounce para validação no servidor
      debounceTimer.current = setTimeout(async () => {
        const result = await validateCodeImmediate(code);
        resolve(result);
      }, 800); // 800ms de debounce
    });
  }, [validateCodeImmediate]);

  const generateRandomCode = useCallback((): string => {
    // Gerar código aleatório de 8 caracteres (letras maiúsculas e números)
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }, []);

  const generateUniqueCode = useCallback(async (): Promise<string> => {
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      const code = generateRandomCode();
      const validation = await validateCode(code);
      
      if (validation.isValid) {
        return code;
      }
      
      attempts++;
    }

    // Se não conseguir gerar um código único, usar timestamp
    const timestamp = Date.now().toString(36).toUpperCase();
    return `MED${timestamp}`;
  }, [generateRandomCode, validateCode]);

  const checkUpdatePermission = useCallback(async (): Promise<{ canUpdate: boolean; nextUpdateTime?: string; error?: string }> => {
    if (!user?.id) {
      return { canUpdate: false, error: 'Usuário não autenticado' };
    }

    try {
      const { data, error } = await supabase
        .from('user_referrals')
        .select('updated_at')
        .eq('referrer_id', user.id)
        .single();

      if (error) {
        return { canUpdate: false, error: 'Erro ao verificar permissões' };
      }

      if (!data?.updated_at) {
        return { canUpdate: true };
      }

      const lastUpdate = new Date(data.updated_at);
      const now = new Date();
      const timeDiff = now.getTime() - lastUpdate.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      if (hoursDiff < 24) {
        const nextUpdate = new Date(lastUpdate.getTime() + 24 * 60 * 60 * 1000);
        const timeLeft = Math.ceil((24 - hoursDiff) * 60); // minutos restantes

        let timeMessage = '';
        if (timeLeft > 60) {
          const hoursLeft = Math.floor(timeLeft / 60);
          const minutesLeft = timeLeft % 60;
          timeMessage = `${hoursLeft}h ${minutesLeft}m`;
        } else {
          timeMessage = `${timeLeft}m`;
        }

        return {
          canUpdate: false,
          nextUpdateTime: nextUpdate.toISOString(),
          error: `Você pode alterar seu código novamente em ${timeMessage}`
        };
      }

      return { canUpdate: true };
    } catch (error) {
      return { canUpdate: false, error: 'Erro ao verificar permissões' };
    }
  }, [user?.id]);

  return {
    validationResult,
    validateCode,
    generateRandomCode,
    generateUniqueCode,
    checkUpdatePermission
  };
};
