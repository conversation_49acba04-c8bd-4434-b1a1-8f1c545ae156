
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { StatCategory } from '@/types/studySession';
import { useAuth } from '@/contexts/AuthContext';
import { useStaticStudyCategories } from '@/hooks/useStaticDataCache';

interface CategoryData {
  name: string;
  correct: number;
  total: number;
}

interface SkillTreeData {
  bySpecialty: Record<string, CategoryData>;
  byTheme: Record<string, CategoryData>;
  byFocus: Record<string, CategoryData>;
  totalQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
}

export const useSkillTreeData = () => {
  const { user } = useAuth();
  const { data: staticCategories } = useStaticStudyCategories();

  return useQuery({
    queryKey: ['skill-tree-data', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');

      // ✅ Aguardar dados estáticos em cache
      if (!staticCategories) {
        throw new Error("Static categories not loaded");
      }



      // ✅ Criar mapeamento usando dados estáticos em cache (1 operação em memória)
      const allCategories = [
        ...staticCategories.specialties,
        ...staticCategories.themes,
        ...staticCategories.focuses
      ];

      const categoryMap = allCategories.reduce((acc, cat) => {
        acc[cat.id] = cat;
        return acc;
      }, {} as Record<string, any>);



      // Tentar usar função RPC otimizada, com fallback para query tradicional
      let stats: any = null;
      let userAnswers: any[] = [];

      try {
        const { data: progressStats, error: rpcError } = await supabase
          .rpc('get_user_progress_stats', {
            user_id_param: user.id,
            days_limit: 365 // Um ano de dados para skill tree completa
          });

        if (rpcError) {
          console.warn('⚠️ RPC não disponível, usando fallback:', rpcError.message);
          throw rpcError;
        }

        stats = progressStats?.[0];
      } catch (rpcError) {
        // Fallback para query tradicional
        console.log('📊 Usando fallback para dados de progresso');
      }

      // Fetch user answers para dados específicos necessários para skill tree
      const { data: answersData, error: detailError } = await supabase
        .from('user_answers')
        .select('specialty_id, theme_id, focus_id, is_correct, question_id')
        .eq('user_id', user.id)
        .limit(5000); // Limite para performance

      userAnswers = answersData || [];

      if (detailError) {
        throw detailError;
      }

      // Processar dados: usar RPC se disponível, senão calcular localmente
      let totalQuestions: number;
      let correctAnswers: number;
      let incorrectAnswers: number;

      if (stats) {
        // Usar dados da RPC
        totalQuestions = Number(stats.total_answers) || 0;
        correctAnswers = Number(stats.correct_answers) || 0;
        incorrectAnswers = totalQuestions - correctAnswers;
      } else {
        // Fallback: calcular localmente
        const uniqueAnswers = userAnswers.filter((answer, index, self) =>
          index === self.findIndex(a => a.question_id === answer.question_id)
        );
        totalQuestions = uniqueAnswers.length;
        correctAnswers = uniqueAnswers.filter(a => a.is_correct).length;
        incorrectAnswers = totalQuestions - correctAnswers;
      }

      // Para skill tree, ainda precisamos dos dados detalhados
      const uniqueQuestionIds = new Set(userAnswers?.map(a => a.question_id) || []);

      // Usar dados otimizados da RPC para totais (já calculados no banco)
      // totalQuestions, correctAnswers e incorrectAnswers já definidos acima

      //console.log(`📊 Processing ${totalQuestions} answers with ${correctAnswers} correct`);

      // Get statistics by category
      const bySpecialty: Record<string, CategoryData> = {};
      const byTheme: Record<string, CategoryData> = {};
      const byFocus: Record<string, CategoryData> = {};

      // Organize statistics by category using unique answers only
      for (const questionId of uniqueQuestionIds) {
        // Find the latest answer for this question
        const answer = userAnswers
          ?.filter(a => a.question_id === questionId)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];

        if (!answer) continue;

        // Process specialty stats
        if (answer.specialty_id) {
          const id = answer.specialty_id;
          if (!bySpecialty[id]) {
            // ✅ Usar cache em vez de query SQL
            const specialty = categoryMap[id];

            bySpecialty[id] = {
              name: specialty?.name || 'Unknown',
              correct: 0,
              total: 0
            };
          }
          bySpecialty[id].total++;
          if (answer.is_correct) {
            bySpecialty[id].correct++;
          }
        }

        // Process theme stats
        if (answer.theme_id) {
          const id = answer.theme_id;
          if (!byTheme[id]) {
            // ✅ Usar cache em vez de query SQL
            const theme = categoryMap[id];

            byTheme[id] = {
              name: theme?.name || 'Unknown',
              correct: 0,
              total: 0
            };
          }
          byTheme[id].total++;
          if (answer.is_correct) {
            byTheme[id].correct++;
          }
        }

        // Process focus stats
        if (answer.focus_id) {
          const id = answer.focus_id;
          if (!byFocus[id]) {
            // ✅ Usar cache em vez de query SQL
            const focus = categoryMap[id];

            byFocus[id] = {
              name: focus?.name || 'Unknown',
              correct: 0,
              total: 0
            };
          }
          byFocus[id].total++;
          if (answer.is_correct) {
            byFocus[id].correct++;
          }
        }
      }

      // Create the final data structure
      const result: SkillTreeData = {
        bySpecialty,
        byTheme,
        byFocus,
        totalQuestions,
        correctAnswers,
        incorrectAnswers
      };



      return result as SkillTreeData;
    },
    enabled: !!user?.id && !!staticCategories, // ✅ Aguardar dados estáticos
    retry: 1,
    staleTime: 0, // ✅ Sempre buscar dados frescos para debug
    cacheTime: 10 * 60 * 1000, // 10 minutos
  });
};
