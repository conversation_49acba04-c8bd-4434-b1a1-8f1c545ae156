import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface ReferralStatus {
  isReferred: boolean;
  referrerName?: string;
  referralCode?: string;
  referredAt?: string;
  showNotification: boolean;
}

export const useReferralStatus = () => {
  const { user } = useAuth();
  const [hasShownNotification, setHasShownNotification] = useState(false);

  // Verificar se o usuário foi referenciado
  const { data: referralStatus, isLoading } = useQuery({
    queryKey: ['user-referral-status', user?.id],
    queryFn: async (): Promise<ReferralStatus> => {
      if (!user?.id) {
        return { isReferred: false, showNotification: false };
      }

      // Buscar se o usuário foi referenciado
      const { data, error } = await supabase
        .from('user_referred_by')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
        return { isReferred: false, showNotification: false };
      }

      if (!data) {
        return { isReferred: false, showNotification: false };
      }

      // Buscar nome do referenciador
      let referrerName = 'Um amigo';
      if (data.referrer_id) {
        const { data: referrerProfile, error: profileError } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', data.referrer_id)
          .single();

        if (!profileError && referrerProfile?.full_name) {
          referrerName = referrerProfile.full_name;
        }
      }

      // Verificar se já mostrou a notificação
      const notificationKey = `referral_notification_shown_${user.id}`;
      const hasShown = localStorage.getItem(notificationKey) === 'true';

      return {
        isReferred: true,
        referrerName,
        referralCode: data.referral_code,
        referredAt: data.referred_at,
        showNotification: !hasShown && !hasShownNotification
      };
    },
    enabled: !!user?.id,
    staleTime: 0, // Sempre considerar dados como stale para refetch rápido
    cacheTime: 30 * 1000, // Cache por 30 segundos apenas
    retry: false, // Não tentar novamente em caso de erro
    refetchOnWindowFocus: false // Não refazer query ao focar janela
  });

  // Marcar notificação como mostrada
  const markNotificationAsShown = () => {
    if (user?.id) {
      const notificationKey = `referral_notification_shown_${user.id}`;
      localStorage.setItem(notificationKey, 'true');
      setHasShownNotification(true);
    }
  };

  return {
    ...referralStatus,
    isLoading,
    markNotificationAsShown
  };
};
