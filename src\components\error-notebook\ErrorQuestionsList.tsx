import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { QuestionImages } from '../question/QuestionImages';
import {
  Eye,
  RotateCcw,
  CheckCircle,
  Clock,
  MessageSquare,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Edit3,
  Save,
  X,
  Square,
  CheckSquare,
  Play,
  BookOpen
} from 'lucide-react';
import { ErrorQuestion } from '@/hooks/useErrorNotebook';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ErrorQuestionsListProps {
  questions: ErrorQuestion[];
  currentPage: number;
  totalPages: number;
  questionsPerPage: number;
  isLoading: boolean;
  selectedQuestions: string[];
  onPageChange: (page: number) => void;
  onViewQuestion: (question: ErrorQuestion) => void;
  onRetryQuestion: (question: ErrorQuestion) => void;
  onMarkAsReviewed: (questionId: string, userAnswerId: string, mode: 'visualizacao' | 'refazer', annotation?: string, correctOnReview?: boolean, timeSpent?: number, discursiveAnswer?: string) => void;
  onUpdateAnnotation: (questionId: string, userAnswerId: string, annotation: string) => void;
  onToggleSelection: (questionId: string) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onReviewSelected: () => void;
  onRetrySelected: () => void;
}

export const ErrorQuestionsList: React.FC<ErrorQuestionsListProps> = ({
  questions,
  currentPage,
  totalPages,
  questionsPerPage,
  isLoading,
  selectedQuestions,
  onPageChange,
  onViewQuestion,
  onRetryQuestion,
  onMarkAsReviewed,
  onUpdateAnnotation,
  onToggleSelection,
  onSelectAll,
  onClearSelection,
  onReviewSelected,
  onRetrySelected
}) => {
  const [editingAnnotation, setEditingAnnotation] = useState<string | null>(null);
  const [annotationText, setAnnotationText] = useState('');
  const [localAnnotations, setLocalAnnotations] = useState<Record<string, string>>({});

  const handleEditAnnotation = (questionId: string, currentAnnotation: string) => {
    setEditingAnnotation(questionId);
    setAnnotationText(currentAnnotation || '');
  };

  const handleSaveAnnotation = async (questionId: string, userAnswerId: string) => {
    try {
      await onUpdateAnnotation(questionId, userAnswerId, annotationText);

      // Atualizar estado local imediatamente
      setLocalAnnotations(prev => ({
        ...prev,
        [userAnswerId]: annotationText
      }));

      setEditingAnnotation(null);
      setAnnotationText('');
    } catch (error) {
      console.error('Erro ao salvar anotação:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingAnnotation(null);
    setAnnotationText('');
  };

  // Função para obter a anotação (prioriza estado local)
  const getAnnotation = (question: ErrorQuestion) => {
    return localAnnotations[question.id] ?? question.anotacao ?? '';
  };

  const isDiscursive = (question: ErrorQuestion) => {
    return question.question_format === 'DISSERTATIVA';
  };

  const getCorrectAlternative = (question: ErrorQuestion) => {
    if (isDiscursive(question)) {
      if (question.analise_ia_discursiva?.ai_answer) {
        return question.analise_ia_discursiva.ai_answer;
      }
      if (question.analise_ia_discursiva?.aiAnswer) {
        return question.analise_ia_discursiva.aiAnswer;
      }
      if (question.analise_ia_discursiva?.feedback) {
        return question.analise_ia_discursiva.feedback;
      }
      return 'Análise não disponível';
    }
    const correctIndex = parseInt(question.correct_choice);
    return question.response_choices[correctIndex] || 'Não identificada';
  };

  const getUserAlternative = (question: ErrorQuestion) => {
    if (isDiscursive(question)) {
      return question.resposta_discursiva || 'Resposta não fornecida';
    }
    return question.response_choices[question.selected_answer] || 'Não identificada';
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ptBR 
      });
    } catch {
      return 'Data inválida';
    }
  };

  if (isLoading) {
    return (
      <Card className="p-8 bg-white border-2 border-black shadow-card-sm">
        <div className="flex items-center justify-center">
          <div className="flex items-center gap-3">
            <RotateCcw className="h-6 w-6 animate-spin text-red-500" />
            <span className="text-lg font-medium">Carregando questões...</span>
          </div>
        </div>
      </Card>
    );
  }

  if (questions.length === 0) {
    return (
      <Card className="p-8 bg-white border-2 border-black shadow-card-sm text-center">
        <div className="max-w-md mx-auto">
          <div className="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
            <CheckCircle className="h-10 w-10 text-gray-400" />
          </div>
          <h3 className="text-xl font-bold mb-2">Nenhuma questão encontrada</h3>
          <p className="text-gray-600">
            Não há questões erradas que correspondam aos filtros selecionados.
          </p>
        </div>
      </Card>
    );
  }

  const allCurrentPageSelected = questions.every(q => selectedQuestions.includes(q.id));
  const someCurrentPageSelected = questions.some(q => selectedQuestions.includes(q.id));

  return (
    <div className="space-y-6">
      {/* Header com informações */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Lista de Questões Erradas</h2>
          <p className="text-gray-600">
            Mostrando {((currentPage - 1) * questionsPerPage) + 1} a {Math.min(currentPage * questionsPerPage, questions.length)} de {questions.length} questões
          </p>
        </div>
      </div>

      {/* Barra de Seleção e Ações em Lote */}
      <Card className="p-3 sm:p-4 bg-gray-50 border-2 border-gray-200">
        <div className="space-y-3">
          {/* Linha 1: Controles de Seleção */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={allCurrentPageSelected ? onClearSelection : onSelectAll}
              className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-3"
            >
              {allCurrentPageSelected ? <Square className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" /> : <CheckSquare className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />}
              <span className="hidden sm:inline">{allCurrentPageSelected ? 'Desmarcar Todas' : 'Selecionar Todas'}</span>
              <span className="sm:hidden">{allCurrentPageSelected ? 'Desmarcar' : 'Selecionar'}</span>
            </Button>

            {selectedQuestions.length > 0 && (
              <span className="text-xs sm:text-sm text-gray-600 font-medium">
                {selectedQuestions.length} selecionada{selectedQuestions.length !== 1 ? 's' : ''}
              </span>
            )}
          </div>

          {/* Linha 2: Botões de Ação */}
          {selectedQuestions.length > 0 && (
            <div className="flex gap-2 justify-center sm:justify-start">
              <Button
                onClick={onReviewSelected}
                size="sm"
                className="bg-blue-500 hover:bg-blue-600 text-white border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-3 sm:px-4"
              >
                <BookOpen className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Revisar Selecionadas</span>
                <span className="sm:hidden">Revisar</span>
              </Button>
              <Button
                onClick={onRetrySelected}
                size="sm"
                variant="outline"
                className="border-2 border-purple-500 text-purple-600 hover:bg-purple-50 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-3 sm:px-4"
              >
                <Play className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Refazer Selecionadas</span>
                <span className="sm:hidden">Refazer</span>
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Lista de Questões */}
      <div className="space-y-4">
        {questions.map((question, index) => (
          <Card key={question.id} className="p-6 bg-white border-2 border-black shadow-card-sm hover:shadow-lg transition-all">
            <div className="space-y-4">
              {/* Header da Questão */}
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3 flex-1">
                  {/* Checkbox de Seleção */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onToggleSelection(question.id)}
                    className="p-1 h-auto"
                  >
                    {selectedQuestions.includes(question.id) ? (
                      <CheckSquare className="h-5 w-5 text-blue-500" />
                    ) : (
                      <Square className="h-5 w-5 text-gray-400" />
                    )}
                  </Button>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline" className="text-xs">
                      #{(currentPage - 1) * questionsPerPage + index + 1}
                    </Badge>
                    <Badge variant={question.revisado ? "default" : "destructive"} className="text-xs">
                      {question.revisado ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Revisada
                        </>
                      ) : (
                        <>
                          <Clock className="h-3 w-3 mr-1" />
                          Pendente
                        </>
                      )}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {question.exam_year}
                    </Badge>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">{question.specialty_name}</span>
                    {question.theme_name && (
                      <>
                        <span className="mx-2">•</span>
                        <span>{question.theme_name}</span>
                      </>
                    )}
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-500">
                  Errou {formatDate(question.created_at)}
                </div>
              </div>

              {/* Conteúdo da Questão */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm font-medium mb-3">
                  {question.question_content}
                </p>

                {/* Question Images */}
                {question.media_attachments && question.media_attachments.length > 0 && (
                  <div className="mb-3">
                    <QuestionImages images={question.media_attachments} />
                  </div>
                )}

                {isDiscursive(question) ? (
                  // Layout para questões discursivas
                  <div className="space-y-4 text-xs">
                    <div className="space-y-1">
                      <div className="font-medium text-blue-600">Sua resposta:</div>
                      <div className="p-3 bg-blue-50 rounded border-l-4 border-blue-500 max-h-32 overflow-y-auto">
                        {getUserAlternative(question)}
                      </div>
                    </div>

                    <div className="space-y-1">
                      <div className="font-medium text-green-600">Análise da IA:</div>
                      <div className="p-3 bg-green-50 rounded border-l-4 border-green-500 max-h-32 overflow-y-auto">
                        {getCorrectAlternative(question)}
                      </div>
                    </div>
                  </div>
                ) : (
                  // Layout para questões de múltipla escolha
                  <div className="grid md:grid-cols-2 gap-4 text-xs">
                    <div className="space-y-1">
                      <div className="font-medium text-red-600">Sua resposta:</div>
                      <div className="p-2 bg-red-50 rounded border-l-4 border-red-500">
                        {getUserAlternative(question)}
                      </div>
                    </div>

                    <div className="space-y-1">
                      <div className="font-medium text-green-600">Resposta correta:</div>
                      <div className="p-2 bg-green-50 rounded border-l-4 border-green-500">
                        {getCorrectAlternative(question)}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Anotações */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Anotações</span>
                  </div>
                  {!editingAnnotation && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditAnnotation(question.question_id, getAnnotation(question))}
                      className="text-xs"
                    >
                      <Edit3 className="h-3 w-3 mr-1" />
                      {getAnnotation(question) ? 'Editar' : 'Adicionar'}
                    </Button>
                  )}
                </div>
                
                {editingAnnotation === question.question_id ? (
                  <div className="space-y-2">
                    <Textarea
                      value={annotationText}
                      onChange={(e) => setAnnotationText(e.target.value)}
                      placeholder="Adicione suas anotações sobre esta questão..."
                      className="text-sm"
                      rows={3}
                    />
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleSaveAnnotation(question.question_id, question.id)}
                        className="text-xs"
                      >
                        <Save className="h-3 w-3 mr-1" />
                        Salvar
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancelEdit}
                        className="text-xs"
                      >
                        <X className="h-3 w-3 mr-1" />
                        Cancelar
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded min-h-[60px]">
                    {getAnnotation(question) || 'Nenhuma anotação adicionada.'}
                  </div>
                )}
              </div>

              {/* Ações */}
              <div className="flex gap-1 sm:gap-2 pt-2 border-t">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onViewQuestion(question)}
                  className="text-xs flex-1 sm:flex-none px-2 sm:px-3"
                >
                  <Eye className="h-3 w-3 mr-1" />
                  <span className="hidden sm:inline">Visualizar</span>
                  <span className="sm:hidden">Ver</span>
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onRetryQuestion(question)}
                  className="text-xs flex-1 sm:flex-none px-2 sm:px-3"
                >
                  <RotateCcw className="h-3 w-3 mr-1" />
                  <span className="hidden sm:inline">Refazer</span>
                  <span className="sm:hidden">Refaz</span>
                </Button>

                {!question.revisado && (
                  <Button
                    size="sm"
                    onClick={() => onMarkAsReviewed(question.question_id, question.id, 'visualizacao')}
                    className="text-xs bg-green-500 hover:bg-green-600 text-white flex-1 sm:flex-none px-2 sm:px-3"
                  >
                    <CheckCircle className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Marcar como Revisada</span>
                    <span className="sm:hidden">Marcar</span>
                  </Button>
                )}
                
                {question.revisado && question.ultima_revisao && (
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    Revisada {formatDate(question.ultima_revisao)}
                  </div>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Paginação */}
      {totalPages > 1 && (
        <Card className="p-4 bg-white border-2 border-black shadow-card-sm">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Página {currentPage} de {totalPages}
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage <= 1}
                className="text-xs"
              >
                <ChevronLeft className="h-3 w-3 mr-1" />
                Anterior
              </Button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      size="sm"
                      variant={currentPage === page ? "default" : "outline"}
                      onClick={() => onPageChange(page)}
                      className="text-xs w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>
              
              <Button
                size="sm"
                variant="outline"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage >= totalPages}
                className="text-xs"
              >
                Próxima
                <ChevronRight className="h-3 w-3 ml-1" />
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
