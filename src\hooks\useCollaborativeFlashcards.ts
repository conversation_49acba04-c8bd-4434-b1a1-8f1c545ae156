
import { useState, useCallback, useRef, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useStaticFlashcardHierarchy } from '@/hooks/useStaticDataCache';

interface FlashcardFilter {
  specialty?: string;
  theme?: string;
  focus?: string;
  showImported?: boolean;
}

export const useCollaborativeFlashcards = () => {
  const [flashcards, setFlashcards] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [importedCardIds, setImportedCardIds] = useState<string[]>([]);
  const requestInProgressRef = useRef(false);
  const lastFilterRef = useRef<{specialty?: string; showImported?: boolean}>({});
  const initialImportCheckDoneRef = useRef(false);

  // Hook para carregar dados da hierarquia
  const { data: hierarchyData, isLoading: hierarchyLoading } = useStaticFlashcardHierarchy();

  // Função para resolver nomes da hierarquia
  const resolveHierarchyNames = (cards: any[]) => {
    // ✅ Verificação mais robusta: dados devem existir E ter conteúdo
    if (!hierarchyData || hierarchyLoading ||
        !hierarchyData.specialties || hierarchyData.specialties.length === 0) {

      return [];
    }

    return cards.map(card => ({
      ...card,
      hierarchy: {
        specialty: card.specialty_id ? {
          id: card.specialty_id,
          name: hierarchyData.specialties.find(s => s.id === card.specialty_id)?.name || 'Especialidade não encontrada'
        } : undefined,
        theme: card.theme_id ? {
          id: card.theme_id,
          name: hierarchyData.themes.find(t => t.id === card.theme_id)?.name || 'Tema não encontrado'
        } : undefined,
        focus: card.focus_id ? {
          id: card.focus_id,
          name: hierarchyData.focuses.find(f => f.id === card.focus_id)?.name || 'Foco não encontrado'
        } : undefined,
        extraFocus: card.extrafocus_id ? {
          id: card.extrafocus_id,
          name: hierarchyData.extrafocuses.find(e => e.id === card.extrafocus_id)?.name || 'Extra foco não encontrado'
        } : undefined
      }
    }));
  };

  const refreshImportedCards = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      // Sempre buscar os cards importados mais recentes
      const { data: userCards, error } = await supabase
        .from('flashcards_cards')
        .select('origin_id')
        .eq('user_id', user.id)
        .not('origin_id', 'is', null);

      if (error) throw error;

      if (userCards && userCards.length > 0) {
        const importedIds = userCards.map(card => card.origin_id).filter(Boolean);
        setImportedCardIds(importedIds);
        initialImportCheckDoneRef.current = true;
        return importedIds;
      }

      return [];
    } catch (error: any) {
      console.error('❌ [useCollaborativeFlashcards] Error refreshing imported cards:', error);
      return [];
    }
  }, []);

  // Run initial refresh
  useEffect(() => {
    if (!initialImportCheckDoneRef.current) {
      refreshImportedCards();
    }
  }, [refreshImportedCards]);

  // ✅ Detectar e corrigir nomes "Carregando..." quando a hierarquia estiver disponível
  useEffect(() => {
    if (hierarchyData && hierarchyData.specialties && hierarchyData.specialties.length > 0 && flashcards.length > 0) {
      // Verificar se algum flashcard ainda tem nomes desconhecidos
      const hasLoadingNames = flashcards.some(card =>
        card.hierarchy?.specialty?.name?.includes('Desconhecida') ||
        card.hierarchy?.theme?.name?.includes('Desconhecido') ||
        card.hierarchy?.focus?.name?.includes('Desconhecido') ||
        card.hierarchy?.extraFocus?.name?.includes('Desconhecido')
      );

      if (hasLoadingNames) {


        // Resolver nomes da hierarquia para os flashcards existentes
        const updatedFlashcards = resolveHierarchyNames(flashcards);
        if (updatedFlashcards.length > 0) {
          setFlashcards(updatedFlashcards);
        }
      }
    }
  }, [hierarchyData, flashcards, resolveHierarchyNames]);

  const refreshImportedCounts = useCallback(async (cardIds: string[]) => {
    if (!cardIds.length) return;

    try {
      // A função add_cards_to_user_deck já incrementa o import_count automaticamente
      // Atualizar estado local para refletir a importação
      setImportedCardIds(prev => [...new Set([...prev, ...cardIds])]);

      // Atualizar os flashcards locais para marcar como importados
      setFlashcards(prevCards =>
        prevCards.map(card =>
          cardIds.includes(card.id)
            ? { ...card, isImported: true, import_count: (card.import_count || 0) + 1 }
            : card
        )
      );
    } catch (error: any) {
      console.error('❌ [useCollaborativeFlashcards] Error updating import counts:', error);
    }
  }, []);

  const loadFlashcards = useCallback(async (
    page: number = 1,
    filters: FlashcardFilter = {},
    sortBy: string = 'recent',
    limit: number = 200
  ) => {
    // ✅ Aguardar carregamento da hierarquia antes de processar flashcards
    if (hierarchyLoading || !hierarchyData ||
        !hierarchyData.specialties || hierarchyData.specialties.length === 0) {

      return;
    }

    // Check if we're already loading data with same filters
    const currentFilter = { specialty: filters.specialty, showImported: filters.showImported };
    const isSameFilter = JSON.stringify(currentFilter) === JSON.stringify(lastFilterRef.current);

    // Skip if already loading data with same filters and we have data
    if (requestInProgressRef.current) {
      return;
    }

    if (isSameFilter && flashcards.length > 0 && page === 1) {

      return;
    }

    try {
      lastFilterRef.current = currentFilter;
      requestInProgressRef.current = true;
      setIsLoading(true);



      // ✅ Otimizado: Buscar apenas dados dos flashcards sem joins
      let query = supabase
        .from('flashcards_cards')
        .select(`
          id,
          user_id,
          front,
          back,
          front_image,
          back_image,
          specialty_id,
          theme_id,
          focus_id,
          extrafocus_id,
          is_shared,
          current_state,
          created_at,
          import_count,
          likes,
          dislikes,
          liked_by,
          disliked_by,
          flashcard_type
        `, { count: 'exact' })
        .eq('is_shared', true);





      // Apply filters
      if (filters.specialty) {

        query = query.eq('specialty_id', filters.specialty);
      }

      if (filters.theme) {

        query = query.eq('theme_id', filters.theme);
      }

      if (filters.focus) {

        query = query.eq('focus_id', filters.focus);
      }

      if (filters.flashcardType && filters.flashcardType !== 'all') {

        if (filters.flashcardType === 'null') {
          // Buscar cards sem tipo definido
          query = query.is('flashcard_type', null);
        } else {
          // Para tipos específicos, buscar exatamente o tipo
          query = query.eq('flashcard_type', filters.flashcardType);
        }
      }

      // Sort
      if (sortBy === 'recent') {
        query = query.order('created_at', { ascending: false });
      } else if (sortBy === 'oldest') {
        query = query.order('created_at', { ascending: true });
      } else if (sortBy === 'likes') {
        query = query.order('likes', { ascending: false });
      } else if (sortBy === 'most_imported') {
        query = query.order('import_count', { ascending: false });
      } else if (sortBy === 'dislikes_desc') {
        query = query.order('dislikes', { ascending: false });
      }

      // Pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data, error: fetchError, count } = await query;



      if (fetchError) {
        console.error(`❌ [useCollaborativeFlashcards] Error fetching cards:`, fetchError);
        throw fetchError;
      }

      if (count !== null) {
        setTotalCount(count);
        setTotalPages(Math.ceil(count / limit));
      }

      // Get current user ID to handle imported cards
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;
      // Process the data
      if (data && data.length > 0) {


        // Filter cards to ensure they have is_shared=true (just a double-check)
        const sharedCards = data.filter(card => card.is_shared === true);

        // Check which cards the user has already imported
        if (userId) {
          // Use the cached importedCardIds if available
          if (importedCardIds.length === 0) {
            const cardIds = sharedCards.map(card => card.id);

            if (cardIds.length > 0) {
              const { data: importedCards, error: importedError } = await supabase
                .from('flashcards_cards')
                .select('origin_id')
                .eq('user_id', userId)
                .in('origin_id', cardIds);

              if (importedError) {
                console.error(`❌ [useCollaborativeFlashcards] Error checking imported cards:`, importedError);
              } else if (importedCards && importedCards.length > 0) {


                // Mark cards that user has already imported
                const importedIds = new Set(importedCards.map(card => card.origin_id));
                sharedCards.forEach(card => {
                  card.isImported = importedIds.has(card.id);
                });

                // Update the imported card IDs state
                setImportedCardIds(prev => [...new Set([...prev, ...Array.from(importedIds)])]);
              }
            }
          } else {


            // Mark cards that user has already imported using cached importedCardIds
            sharedCards.forEach(card => {
              card.isImported = importedCardIds.includes(card.id);
            });
          }
        }

        // Filter out cards that the user has already imported if showImported is false
        let filteredData = sharedCards || [];
        if (!filters.showImported && userId) {
          const beforeCount = filteredData.length;
          filteredData = filteredData.filter(card => !card.isImported);

        }

        // ✅ Resolver nomes da hierarquia usando dados estáticos
        const transformedCards = resolveHierarchyNames(filteredData);

        if (page === 1) {
          setFlashcards(transformedCards);
        } else {
          setFlashcards(prevCards => {
            // Combine with existing cards, avoiding duplicates
            const existingIds = new Set(prevCards.map(c => c.id));
            const newCards = transformedCards.filter(c => !existingIds.has(c.id));
            const combined = [...prevCards, ...newCards];

            return combined;
          });
        }
      } else {
        if (page === 1) {
          setFlashcards([]);
        }
      }

      return data;
    } catch (err: any) {
      setError(err);
      toast.error('Erro ao carregar flashcards', {
        description: err.message
      });
      throw err;
    } finally {
      setIsLoading(false);
      // Reset request flag after a short delay to prevent immediate retriggering
      setTimeout(() => {
        requestInProgressRef.current = false;
      }, 300);
    }
  }, [importedCardIds, hierarchyData, hierarchyLoading]);

  // ✅ Recarregar flashcards quando a hierarquia terminar de carregar
  useEffect(() => {
    if (!hierarchyLoading && hierarchyData &&
        hierarchyData.specialties && hierarchyData.specialties.length > 0 &&
        flashcards.length === 0) {

      loadFlashcards();
    }
  }, [hierarchyLoading, hierarchyData, loadFlashcards, flashcards.length]);

  /**
   * Function to like or dislike a flashcard
   *
   * @param cardId - ID of the card to like/dislike
   * @param action - Action to perform (like or dislike)
   */
  const likeDislikeCard = useCallback(async (cardId: string, action: 'like' | 'dislike') => {

    try {
      // First get the user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('❌ [likeDislikeCard] User not authenticated:', userError);
        toast.error("Você precisa estar logado para votar");
        return { error: 'User not authenticated' };
      }

      // Get the current card data
      const { data: card, error: cardError } = await supabase
        .from('flashcards_cards')
        .select('likes, dislikes, liked_by, disliked_by, origin_id')
        .eq('id', cardId)
        .single();

      if (cardError) {
        console.error('❌ [likeDislikeCard] Error fetching card:', cardError);
        return { error: cardError };
      }

      // Initialize arrays if they don't exist
      const likedBy = card.liked_by || [];
      const dislikedBy = card.disliked_by || [];

      // Check if user already liked or disliked
      const hasLiked = likedBy.includes(user.id);
      const hasDisliked = dislikedBy.includes(user.id);



      // Handle like/dislike logic
      let newLikedBy = [...likedBy];
      let newDislikedBy = [...dislikedBy];
      let newLikes = card.likes || 0;
      let newDislikes = card.dislikes || 0;

      // Remove existing votes first
      if (action === 'like') {
        if (hasLiked) {

          return {
            success: true,
            likes: newLikes,
            dislikes: newDislikes,
            liked_by: newLikedBy,
            disliked_by: newDislikedBy
          };
        }

        // Remove from disliked if present
        if (hasDisliked) {
          newDislikedBy = newDislikedBy.filter(id => id !== user.id);
          newDislikes--;
        }

        // Add new like
        newLikedBy.push(user.id);
        newLikes++;
      } else if (action === 'dislike') {
        if (hasDisliked) {

          return {
            success: true,
            likes: newLikes,
            dislikes: newDislikes,
            liked_by: newLikedBy,
            disliked_by: newDislikedBy
          };
        }

        // Remove from liked if present
        if (hasLiked) {
          newLikedBy = newLikedBy.filter(id => id !== user.id);
          newLikes--;
        }

        // Add new dislike
        newDislikedBy.push(user.id);
        newDislikes++;
      }



      // Update the card in the database
      const { error: updateError } = await supabase
        .from('flashcards_cards')
        .update({
          likes: newLikes,
          dislikes: newDislikes,
          liked_by: newLikedBy,
          disliked_by: newDislikedBy
        })
        .eq('id', cardId);

      if (updateError) {
        console.error('❌ [likeDislikeCard] Error updating card:', updateError);
        return { error: updateError };
      }

      // Update the local state of the card
      setFlashcards(cards =>
        cards.map(c => {
          if (c.id === cardId) {
            return {
              ...c,
              likes: newLikes,
              dislikes: newDislikes,
              liked_by: newLikedBy,
              disliked_by: newDislikedBy
            };
          }
          return c;
        })
      );



      // If this is referring to an original card, also update the original card
      if (card.origin_id) {
        await updateOriginalCard(card.origin_id, action, user.id);
      }

      return {
        success: true,
        likes: newLikes,
        dislikes: newDislikes,
        liked_by: newLikedBy,
        disliked_by: newDislikedBy
      };
    } catch (error: any) {
      console.error(`❌ [likeDislikeCard] Error ${action}ing card:`, error);
      return { error };
    }
  }, []);

  // Helper function to update original card
  const updateOriginalCard = async (originId: string, action: 'like' | 'dislike', userId: string) => {
    try {


      // Get the original card data
      const { data: originalCard, error: fetchError } = await supabase
        .from('flashcards_cards')
        .select('liked_by, disliked_by, likes, dislikes')
        .eq('id', originId)
        .single();

      if (fetchError) {
        console.error('❌ [updateOriginalCard] Error fetching original card:', fetchError);
        return;
      }

      // Initialize arrays if they don't exist
      const likedBy = originalCard.liked_by || [];
      const dislikedBy = originalCard.disliked_by || [];

      // Check if user already liked or disliked
      const hasLiked = likedBy.includes(userId);
      const hasDisliked = dislikedBy.includes(userId);

      // Handle like/dislike logic
      let newLikedBy = [...likedBy];
      let newDislikedBy = [...dislikedBy];
      let newLikes = originalCard.likes || 0;
      let newDislikes = originalCard.dislikes || 0;

      // Remove existing votes first
      if (hasLiked) {
        newLikedBy = newLikedBy.filter(id => id !== userId);
        newLikes--;
      }

      if (hasDisliked) {
        newDislikedBy = newDislikedBy.filter(id => id !== userId);
        newDislikes--;
      }

      // Add new vote based on action
      if (action === 'like') {
        newLikedBy.push(userId);
        newLikes++;
      } else if (action === 'dislike') {
        newDislikedBy.push(userId);
        newDislikes++;
      }

      // Update the original card in the database
      const { error: updateError } = await supabase
        .from('flashcards_cards')
        .update({
          likes: newLikes,
          dislikes: newDislikes,
          liked_by: newLikedBy,
          disliked_by: newDislikedBy
        })
        .eq('id', originId);

      if (updateError) {
        console.error('❌ [updateOriginalCard] Error updating original card:', updateError);
      } else {

      }

    } catch (error) {
      console.error('❌ [updateOriginalCard] Error:', error);
    }
  };

  return {
    flashcards,
    isLoading,
    error,
    totalCount,
    totalPages,
    importedCardIds,
    loadFlashcards,
    refreshImportedCards,
    refreshImportedCounts,
    likeDislikeCard
  };
};
