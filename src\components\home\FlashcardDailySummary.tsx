
import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Play, Plus } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { usePageVisibility } from '@/hooks/usePageVisibility';

const getTodayISOString = () => {
  const today = new Date();
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'America/Sao_Paulo',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
  return formatter.format(today);
};

export const FlashcardDailySummary: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { user } = useAuth();

  // Invalidar cache quando o componente é montado (usuário navega de volta)
  useEffect(() => {
    const handleFocus = () => {
      queryClient.invalidateQueries({ queryKey: ['daily-review-cards-mini'] });
    };

    // Invalidar quando a janela recebe foco
    window.addEventListener('focus', handleFocus);

    // Invalidar imediatamente quando o componente é montado
    queryClient.invalidateQueries({ queryKey: ['daily-review-cards-mini'] });

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [queryClient]);

  // Invalidar quando o usuário navega de volta para a home
  useEffect(() => {
    if (location.pathname === '/') {
      queryClient.invalidateQueries({ queryKey: ['daily-review-cards-mini'] });
    }
  }, [location.pathname, queryClient]);

  // Auto-refresh quando o usuário volta para a aba
  usePageVisibility({
    queryKeys: ['daily-review-cards-mini'],
    delay: 500
  });

  const { data: cardsCount = 0, isLoading } = useQuery({
    queryKey: ['daily-review-cards-mini', user?.id],
    queryFn: async () => {
      if (!user) return 0;
      const today = getTodayISOString();
      const { data: reviews, error } = await supabase
        .from('flashcards_reviews')
        .select('card_id')
        .eq('user_id', user.id)
        .lte('next_review_date', today);

      if (error) {
        return 0;
      }
      return reviews?.length || 0;
    },
    enabled: !!user,
    refetchOnWindowFocus: true,
    refetchInterval: 30000, // Atualiza a cada 30 segundos
    staleTime: 0, // Considera os dados sempre stale para forçar refetch
  });

  const handleReview = async () => {
    if (cardsCount > 0) {
      // Redireciona para os flashcards revisão, mesmo fluxo do botão "Revisar Agora"
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error("User not authenticated");
        const today = getTodayISOString();
        const { data: cardsToReview, error: cardsError } = await supabase
          .from('flashcards_reviews')
          .select('card_id')
          .eq('user_id', user.id)
          .lte('next_review_date', today);

        if (cardsError) throw cardsError;

        const cardIds = cardsToReview?.map(review => review.card_id) || [];

        const { data: session, error: sessionError } = await supabase
          .from('flashcards_sessions')
          .insert({
            user_id: user.id,
            status: 'in_progress',
            total_cards: cardIds.length,
            cards: cardIds,
            correct_cards: 0
          })
          .select()
          .single();

        if (sessionError) throw sessionError;

        if (cardIds.length > 0) {
          await supabase
            .from('flashcards_cards')
            .update({ current_state: 'reviewing' })
            .in('id', cardIds);
        }

        navigate(`/flashcards/session/${session.id}`);
      } catch (error) {
        toast({
          title: "Erro ao iniciar revisão",
          description: "Não foi possível iniciar a sessão de revisão.",
          variant: "destructive"
        });
      }
    }
  };

  const handleStudyRedirect = () => {
    navigate("/flashcards/study");
  };

  const startReviewSession = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const today = new Date().toISOString().split('T')[0];

      const { data: cardsToReview, error: cardsError } = await supabase
        .from('flashcards_reviews')
        .select('card_id')
        .eq('user_id', user.id)
        .lte('next_review_date', today);

      if (cardsError) {
        console.error('❌ [FlashcardDailySummary] Erro ao buscar cards:', cardsError);
        throw cardsError;
      }

      const cardIds = cardsToReview?.map(review => review.card_id) || [];

      const { data: session, error: sessionError } = await supabase
        .from('flashcards_sessions')
        .insert({
          user_id: user.id,
          status: 'in_progress',
          total_cards: cardIds.length,
          cards: cardIds,
          correct_cards: 0
        })
        .select()
        .single();

      if (sessionError) {
        console.error('❌ [FlashcardDailySummary] Erro ao criar sessão:', sessionError);
        throw sessionError;
      }

      if (cardIds.length > 0) {
        const { error: updateError } = await supabase
          .from('flashcards_cards')
          .update({ current_state: 'reviewing' })
          .in('id', cardIds);

        if (updateError) {
          console.error('❌ [FlashcardDailySummary] Erro ao atualizar estado dos cards:', updateError);
          throw updateError;
        }
      }

      // Invalidar a query antes de navegar para garantir atualização quando voltar
      queryClient.invalidateQueries({ queryKey: ['daily-review-cards-mini'] });

      navigate(`/flashcards/session/${session.id}`);
    } catch (error) {
      console.error('❌ [FlashcardDailySummary] Erro ao iniciar sessão de revisão:', error);
      toast({
        variant: "destructive",
        title: "Erro ao iniciar revisão",
        description: "Não foi possível iniciar a sessão de revisão. Tente novamente."
      });
    }
  };

  if (isLoading) return null;

  return (
    <>
      <div className="relative flex flex-row items-center justify-between bg-white border-2 border-black rounded-lg px-4 py-2 mb-4 shadow-card-sm">
        <div className="flex items-center gap-3">
          <span className="font-bold text-purple-600 text-xl">{cardsCount}</span>
          <span className="text-base text-gray-700">
            cards para revisar
          </span>
        </div>
        <div className="flex items-center gap-2">
          {cardsCount > 0 ? (
            <Button
              onClick={startReviewSession}
              variant="hackRed"
              size="hack"
              className="shadow-card-sm"
            >
              <Play className="w-5 h-5 mr-2" />
              Revisar Agora
            </Button>
          ) : (
            <Button
              variant="hackYellow"
              size="hack"
              className="shadow-card-sm"
              onClick={handleStudyRedirect}
            >
              <Plus className="w-5 h-5 mr-2" />
              Nova Sessão
            </Button>
          )}
        </div>
      </div>
    </>
  );
};
