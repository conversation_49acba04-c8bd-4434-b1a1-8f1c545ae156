import { supabase } from "@/integrations/supabase/client";

/**
 * Wrapper para queries de flashcards que trata erros de cards deletados
 * Evita erros 406 (Not Acceptable) quando tentamos acessar cards que já foram deletados
 */
export const safeFlashcardQuery = {
  /**
   * Busca um flashcard por ID de forma segura
   * Retorna null se o card não existir ou foi deletado
   */
  async getById(cardId: string, select: string = '*') {
    try {
      // Usar maybeSingle() em vez de single() para evitar erro 406
      const { data, error } = await supabase
        .from('flashcards_cards')
        .select(select)
        .eq('id', cardId)
        .maybeSingle();

      if (error) {
        // Se o card não existe (foi deletado), retorna null silenciosamente
        if (error.code === 'PGRST116') {
          console.log(`ℹ️ [safeFlashcardQuery] Card ${cardId} não encontrado (provavelmente deletado)`);
          return null;
        }
        // Para outros erros, relança a exceção
        throw error;
      }

      // maybeSingle() retorna null se não encontrar, não erro
      return data;
    } catch (error) {
      console.error(`❌ [safeFlashcardQuery] Erro ao buscar card ${cardId}:`, error);
      return null;
    }
  },

  /**
   * Verifica se um usuário é dono de um flashcard de forma segura
   * Retorna false se o card não existir ou não pertencer ao usuário
   */
  async checkOwnership(cardId: string, userId: string): Promise<boolean> {
    try {
      // Usar query direta com maybeSingle para evitar erro 406
      const { data: card, error } = await supabase
        .from('flashcards_cards')
        .select('user_id')
        .eq('id', cardId)
        .maybeSingle();

      if (error) {
        console.log(`ℹ️ [safeFlashcardQuery] Erro ao verificar propriedade do card ${cardId}:`, error);
        return false;
      }

      return card?.user_id === userId;
    } catch (error) {
      console.error(`❌ [safeFlashcardQuery] Erro ao verificar propriedade do card ${cardId}:`, error);
      return false;
    }
  },

  /**
   * Busca múltiplos flashcards por IDs de forma segura
   * Filtra automaticamente cards que não existem
   */
  async getByIds(cardIds: string[], select: string = '*') {
    try {
      const { data, error } = await supabase
        .from('flashcards_cards')
        .select(select)
        .in('id', cardIds);

      if (error) {
        console.error(`❌ [safeFlashcardQuery] Erro ao buscar cards:`, error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error(`❌ [safeFlashcardQuery] Erro ao buscar cards:`, error);
      return [];
    }
  }
};

/**
 * Hook para invalidar queries de flashcards de forma segura
 * Evita invalidações desnecessárias que podem causar queries para cards deletados
 */
export const safeInvalidateFlashcards = (queryClient: any, cardId?: string) => {
  try {
    // Invalidar queries gerais de flashcards
    queryClient.invalidateQueries({ queryKey: ['flashcards'] });
    queryClient.invalidateQueries({ queryKey: ['daily-review-cards'] });
    queryClient.invalidateQueries({ queryKey: ['daily-review-cards-mini'] });
    
    // Se um cardId específico foi fornecido, invalidar queries relacionadas
    if (cardId) {
      queryClient.invalidateQueries({ queryKey: ['flashcard', cardId] });
    }
    
    console.log(`✅ [safeInvalidateFlashcards] Queries invalidadas com sucesso`);
  } catch (error) {
    console.error(`❌ [safeInvalidateFlashcards] Erro ao invalidar queries:`, error);
  }
};
