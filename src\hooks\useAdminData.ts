import { useSharedProfile } from './useSharedProfile';

interface AdminData {
  isAdmin: boolean;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook centralizado para verificações de admin
 * CRÍTICO: Mantém segurança verificando sempre no servidor
 * Cache de apenas 1 minuto para garantir segurança
 */
export const useAdminData = (): AdminData => {
  const { user } = useAuth();

  const { data: isAdmin = false, isLoading, error } = useQuery({
    queryKey: ['admin-status', user?.id],
    queryFn: async (): Promise<boolean> => {
      if (!user?.id) {
        return false;
      }

      // SEGURANÇA: Sempre verificar no servidor
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();

      if (error) {
        throw error;
      }

      return profile?.is_admin || false;
    },
    enabled: !!user?.id,
    staleTime: 1 * 60 * 1000, // SEGURANÇA: Cache de apenas 1 minuto
    cacheTime: 2 * 60 * 1000, // SEGURANÇA: Cache total de 2 minutos
    refetchOnWindowFocus: true, // SEGURANÇA: Revalidar ao focar janela
    retry: 2, // Tentar novamente em caso de erro
  });

  return {
    isAdmin,
    isLoading,
    error: error as Error | null,
  };
};
