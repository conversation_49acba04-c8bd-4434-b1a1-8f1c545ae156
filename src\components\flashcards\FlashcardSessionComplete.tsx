import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Clock, CheckCircle, BookOpen } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { formatDistance } from "date-fns";
import { ptBR } from "date-fns/locale";

interface SessionDetails {
  total_cards: number;
  start_time: string;
  end_time: string;
  specialties: { id: string; name: string }[];
  themes: { id: string; name: string }[];
  focuses: { id: string; name: string }[];
  extrafocuses: { id: string; name: string }[];
}

export const FlashcardSessionComplete = ({ sessionId }: { sessionId: string }) => {
  const navigate = useNavigate();
  const [sessionDetails, setSessionDetails] = useState<SessionDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSessionDetails = async () => {
      try {
        setIsLoading(true);

        
        const { data: session, error } = await supabase
          .from("flashcards_sessions")
          .select(`
            *,
            cards
          `)
          .eq("id", sessionId)
          .single();

        if (error) throw error;



        if (session.cards && session.cards.length > 0) {
          // ✅ Buscar TODAS as categorias únicas estudadas na sessão
          const { data: allCardsData, error: cardsError } = await supabase
            .from("flashcards_cards")
            .select(`
              id,
              specialty:flashcards_specialty(id, name),
              theme:flashcards_theme(id, name),
              focus:flashcards_focus(id, name),
              extrafocus:flashcards_extrafocus(id, name)
            `)
            .in("id", session.cards);

          if (cardsError) {
            console.warn("⚠️ [FlashcardSessionComplete] Could not fetch cards hierarchy:", cardsError);
          } else {

          }

          // ✅ Extrair categorias únicas
          const uniqueSpecialties = new Map();
          const uniqueThemes = new Map();
          const uniqueFocuses = new Map();
          const uniqueExtrafocuses = new Map();

          allCardsData?.forEach(card => {
            if (card.specialty) {
              uniqueSpecialties.set(card.specialty.id, card.specialty);
            }
            if (card.theme) {
              uniqueThemes.set(card.theme.id, card.theme);
            }
            if (card.focus) {
              uniqueFocuses.set(card.focus.id, card.focus);
            }
            if (card.extrafocus) {
              uniqueExtrafocuses.set(card.extrafocus.id, card.extrafocus);
            }
          });

          setSessionDetails({
            total_cards: session.total_cards || session.cards.length,
            start_time: session.start_time,
            end_time: session.end_time,
            specialties: Array.from(uniqueSpecialties.values()),
            themes: Array.from(uniqueThemes.values()),
            focuses: Array.from(uniqueFocuses.values()),
            extrafocuses: Array.from(uniqueExtrafocuses.values()),
          });
        } else {
          setSessionDetails({
            total_cards: session.total_cards || 0,
            start_time: session.start_time,
            end_time: session.end_time,
            specialties: [],
            themes: [],
            focuses: [],
            extrafocuses: [],
          });
        }
      } catch (error) {
        console.error("❌ [FlashcardSessionComplete] Error fetching session details:", error);
        setSessionDetails(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessionDetails();
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600">Carregando detalhes da sessão...</p>
      </div>
    );
  }

  if (!sessionDetails) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <Card className="p-6 text-center">
          <p className="text-gray-600">Não foi possível carregar os detalhes da sessão.</p>
          <Button onClick={() => navigate("/flashcards")} className="mt-4">
            Voltar para Flashcards
          </Button>
        </Card>
      </div>
    );
  }

  const duration = sessionDetails.end_time && sessionDetails.start_time
    ? formatDistance(
        new Date(sessionDetails.end_time),
        new Date(sessionDetails.start_time),
        { locale: ptBR }
      )
    : "Tempo indisponível";

  return (
    <div className="max-w-2xl mx-auto p-6 pt-12">
        <Card className="bg-white border-2 border-black rounded-xl shadow-button p-8 space-y-6 transform hover:translate-y-0.5 hover:shadow-sm transition-all">
        <h1 className="text-2xl font-bold text-center mb-6 slanted-title">
          ✅ Sessão Finalizada com Sucesso!
        </h1>

        {/* ✅ Layout horizontal otimizado - removido "cartões corretos" */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          <div className="filter-card p-4 rounded-xl flex items-center gap-3 sm:flex-col sm:text-center">
            <div className="icon-container bg-orange-50 flex-shrink-0">
              <BookOpen className="text-orange-500" size={20} />
            </div>
            <div className="flex-1 sm:flex-none">
              <div className="font-bold text-lg text-orange-600">{sessionDetails.total_cards}</div>
              <div className="text-sm text-gray-600">Total de cartões estudados</div>
            </div>
          </div>

          <div className="filter-card p-4 rounded-xl flex items-center gap-3 sm:flex-col sm:text-center">
            <div className="icon-container bg-blue-50 flex-shrink-0">
              <Clock className="text-blue-500" size={20} />
            </div>
            <div className="flex-1 sm:flex-none">
              <div className="font-bold text-lg text-blue-600">{duration}</div>
              <div className="text-sm text-gray-600">Duração da sessão</div>
            </div>
          </div>
        </div>

        {/* ✅ Resumo compacto das categorias estudadas */}
        <div className="border-t-2 border-black/10 pt-6 mt-6">
          <h3 className="font-bold mb-4 text-center">Diversidade de estudos:</h3>

          <div className="flex flex-wrap justify-center gap-4">
            {sessionDetails.specialties.length > 0 && (
              <div className="filter-card p-3 rounded-lg text-center w-24">
                <div className="font-bold text-lg text-orange-600">{sessionDetails.specialties.length}</div>
                <div className="text-xs text-gray-600">Especialidade{sessionDetails.specialties.length > 1 ? 's' : ''}</div>
              </div>
            )}

            {sessionDetails.themes.length > 0 && (
              <div className="filter-card p-3 rounded-lg text-center w-24">
                <div className="font-bold text-lg text-purple-600">{sessionDetails.themes.length}</div>
                <div className="text-xs text-gray-600">Tema{sessionDetails.themes.length > 1 ? 's' : ''}</div>
              </div>
            )}

            {sessionDetails.focuses.length > 0 && (
              <div className="filter-card p-3 rounded-lg text-center w-24">
                <div className="font-bold text-lg text-green-600">{sessionDetails.focuses.length}</div>
                <div className="text-xs text-gray-600">Foco{sessionDetails.focuses.length > 1 ? 's' : ''}</div>
              </div>
            )}

            {sessionDetails.extrafocuses.length > 0 && (
              <div className="filter-card p-3 rounded-lg text-center">
                <div className="font-bold text-lg text-blue-600">{sessionDetails.extrafocuses.length}</div>
                <div className="text-xs text-gray-600">Extra Foco{sessionDetails.extrafocuses.length > 1 ? 's' : ''}</div>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-center mt-8">
          <Button
            onClick={() => navigate("/flashcards")}
            size="hack"
            className="min-w-[200px]"
          >
            Voltar para Flashcards
          </Button>
        </div>
      </Card>
    </div>
  );
};
