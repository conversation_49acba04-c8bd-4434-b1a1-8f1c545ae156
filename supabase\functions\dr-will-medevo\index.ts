import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Function to get all available Gemini API keys
const getGeminiApiKeys = (): string[] => {
  const keys: string[] = [];

  // Primeira chave (sem número)
  const primaryKey = Deno.env.get('GEMINI_API_KEY');
  if (primaryKey) {
    keys.push(primaryKey);
  }

  // Chaves numeradas (GEMINI_API_KEY2, GEMINI_API_KEY3, etc.)
  for (let i = 2; i <= 10; i++) {
    const keyName = `GEMINI_API_KEY${i}`;
    const key = Deno.env.get(keyName);
    if (key) {
      keys.push(key);
    }
  }

  console.log(`🔑 [dr-will-medevo] Encontradas ${keys.length} chaves API disponíveis`);
  return keys;
};

// Simple in-memory cache for rate limiting tracking
const keyUsageTracker = new Map<string, number>();

// Function to check if a key was used recently (within last 5 seconds)
const isKeyRecentlyUsed = (keyName: string): boolean => {
  const lastUsed = keyUsageTracker.get(keyName);
  if (!lastUsed) return false;

  const now = Date.now();
  const timeDiff = now - lastUsed;
  return timeDiff < 5000; // 5 seconds cooldown
};

// Function to mark a key as used
const markKeyAsUsed = (keyName: string): void => {
  keyUsageTracker.set(keyName, Date.now());
};

// Function to make Gemini API call with fallback system
const callGeminiWithFallback = async (requestBody: any): Promise<{ response: Response; keyUsed: string }> => {
  const apiKeys = getGeminiApiKeys();

  if (apiKeys.length === 0) {
    throw new Error('No Gemini API keys found');
  }

  let lastError: Error | null = null;

  for (let i = 0; i < apiKeys.length; i++) {
    const apiKey = apiKeys[i];
    const keyName = i === 0 ? 'GEMINI_API_KEY' : `GEMINI_API_KEY${i + 1}`;

    // Check if this key was used recently
    if (isKeyRecentlyUsed(keyName)) {
      continue;
    }

    console.log(`🔑 [dr-will-medevo] Tentando ${keyName} (${i + 1}/${apiKeys.length})`);

    try {
      const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:streamGenerateContent", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": apiKey
        },
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        console.log(`✅ [dr-will-medevo] Sucesso com ${keyName}`);
        markKeyAsUsed(keyName);
        return { response, keyUsed: keyName };
      } else {
        const errorText = await response.text();
        console.warn(`⚠️ [dr-will-medevo] ${keyName} falhou: ${response.status}`);
        lastError = new Error(`API key ${keyName} failed: ${response.status} ${response.statusText}`);
        markKeyAsUsed(keyName);

        // Se for rate limiting (429), aguarda um pouco antes da próxima tentativa
        if (response.status === 429) {
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    } catch (error) {
      console.warn(`⚠️ [dr-will-medevo] Erro de rede com ${keyName}`);
      markKeyAsUsed(keyName); // Mark key as used even on network error
      lastError = error instanceof Error ? error : new Error(String(error));
    }
  }

  // Se chegou aqui, todas as chaves falharam
  console.error(`❌ [dr-will-medevo] Todas as ${apiKeys.length} chaves Gemini falharam`);
  throw lastError || new Error('All Gemini API keys failed');
};
// Função para determinar o origin permitido baseado na requisição
const getAllowedOrigin = (request)=>{
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://medevo.com.br',
    'https://pedb.com.br',
    'https://www.pedb.com.br',
    'https://www.medevo.com.br',
    'http://localhost:5173',
    'http://localhost:800',
    'http://localhost:8080'
  ];
  if (origin && allowedOrigins.includes(origin)) {
    return origin;
  }
  return 'https://medevo.com.br'; // fallback
};
const getCorsHeaders = (request)=>({
    'Access-Control-Allow-Origin': getAllowedOrigin(request),
    'Access-Control-Allow-Headers': 'authorization, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Credentials': 'true'
  });
// FUNÇÃO PARA VALIDAR SE O CONTEÚDO É REALMENTE UMA TABELA
function validateTableContent(content) {
  // Remover prefixos
  const cleanContent = content.replace(/^\[TABLE_RESPONSE\]\s*/, '').replace(/^\[TEXT_RESPONSE\]\s*/, '');
  // Dividir em linhas
  const lines = cleanContent.split('\n');
  // Encontrar linhas que parecem ser de tabela (com |) mas não são separadores
  const tableLines = lines.filter((line)=>{
    const trimmed = line.trim();
    // Deve ter | e pelo menos 3 colunas
    if (!trimmed.includes('|') || trimmed.split('|').length < 3) {
      return false;
    }
    // Não deve ser linha separadora (só hífens, dois pontos e espaços)
    if (trimmed.match(/^\s*\|[\s\-:]+\|\s*$/) || trimmed.match(/^[\s\-:|]+$/)) {
      return false;
    }
    return true;
  });
  // Deve ter pelo menos 3 linhas de tabela (cabeçalho + separador + dados)
  if (tableLines.length < 3) {
    return false;
  }
  // Verificar consistência de colunas
  const columnCounts = tableLines.map((line)=>line.split('|').length);
  const firstCount = columnCounts[0];
  const consistentLines = columnCounts.filter((count)=>Math.abs(count - firstCount) <= 1);
  // Pelo menos 70% das linhas devem ter número similar de colunas
  const consistency = consistentLines.length / columnCounts.length;
  console.log('[VALIDATION] Table validation:', {
    totalLines: lines.length,
    tableLines: tableLines.length,
    consistency: consistency,
    isValid: consistency >= 0.7 && tableLines.length >= 3
  });
  return consistency >= 0.7 && tableLines.length >= 3;
}
// ULTRA-ROBUST RESPONSE TYPE DETECTOR
function detectResponseType(content, userMessage) {
  console.log('[detectResponseType] Analyzing content for type detection');
  // Priority 0: Check user message for Mermaid request
  if (userMessage) {
    const userLower = userMessage.toLowerCase();
    const mermaidUserKeywords = [
      'mapa mental',
      'mindmap',
      'fluxograma',
      'diagrama',
      'esquema visual',
      'organograma'
    ];
    const userRequestedMermaid = mermaidUserKeywords.some((keyword)=>userLower.includes(keyword));
    if (userRequestedMermaid) {
      console.log('[detectResponseType] User requested Mermaid format');
      return "mermaid";
    }
  }
  // Priority 1: Explicit markers
  if (content.includes('[MERMAID_RESPONSE]')) {
    console.log('[detectResponseType] Found MERMAID_RESPONSE marker');
    return "mermaid";
  }
  if (content.includes('[TABLE_RESPONSE]')) {
    console.log('[detectResponseType] Found TABLE_RESPONSE marker');
    return "table";
  }
  if (content.includes('[TEXT_RESPONSE]')) {
    console.log('[detectResponseType] Found TEXT_RESPONSE marker');
    return "text";
  }
  // Priority 2: Content analysis for Mermaid
  const mermaidKeywords = [
    'mindmap',
    'graph',
    'flowchart',
    'mapa mental',
    'fluxograma',
    'diagrama',
    'esquema visual'
  ];
  const hasMermaidKeyword = mermaidKeywords.some((keyword)=>content.toLowerCase().includes(keyword));
  if (hasMermaidKeyword) {
    return "mermaid";
  }
  // Priority 3: Content analysis for Table
  if (validateTableContent(content)) {
    return "table";
  }
  // Default: text
  return "text";
}
// MERMAID CONTENT VALIDATOR
function validateMermaidContent(content) {
  const cleanContent = content.replace(/\[(TABLE|TEXT|MERMAID)_RESPONSE\]/g, '').trim();
  // Check for Mermaid syntax
  const hasMermaidSyntax = cleanContent.includes('mindmap') || cleanContent.includes('graph') || cleanContent.includes('flowchart') || cleanContent.includes('sequenceDiagram') || /root\(\([^)]+\)\)/.test(cleanContent); // mindmap root syntax
  return hasMermaidSyntax;
}
serve(async (req)=>{
  const corsHeaders = getCorsHeaders(req);
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  // Simple health check
  if (req.method === 'GET') {
    return new Response(JSON.stringify({
      status: 'Dr. Will MedEvo is online',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
  try {
    const requestBody = await req.json();
    const { message, userId, conversationHistory } = requestBody;
    console.log('[dr-will-medevo] Extracted data:', {
      messageLength: message?.length || 0,
      userId: userId || 'not provided',
      historyLength: conversationHistory?.length || 0
    });
    if (!message || typeof message !== 'string') {
      return new Response(JSON.stringify({
        error: 'Message is required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // ✅ NOVO: Verificar se há chaves Gemini disponíveis
    const geminiApiKeys = getGeminiApiKeys();
    if (geminiApiKeys.length === 0) {
      return new Response(JSON.stringify({
        error: "Serviço temporariamente indisponível. Tente novamente em alguns minutos."
      }), {
        status: 503,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Get user info for personalization
    let userName = 'estudante';
    let userGreeting = 'Prezado(a) estudante';
    if (userId) {
      try {
        // Get user profile from Supabase
        const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
        const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
        if (supabaseUrl && supabaseServiceKey) {
          const supabase = createClient(supabaseUrl, supabaseServiceKey);
          // Try multiple approaches to get user name
          let profile = null;
          // First try: profiles table
          const { data: profileData, error: profileError } = await supabase.from('profiles').select('full_name, first_name').eq('id', userId).single();
          if (profileData && !profileError) {
            profile = profileData;
          } else {
            // Second try: auth.users table (if accessible)
            const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
            if (userData?.user?.user_metadata) {
              profile = {
                full_name: userData.user.user_metadata.full_name || userData.user.user_metadata.name,
                first_name: userData.user.user_metadata.first_name
              };
            }
          }
          if (profile?.first_name) {
            userName = profile.first_name;
            userGreeting = `Olá, ${profile.first_name}!`;
          } else if (profile?.full_name) {
            const firstName = profile.full_name.split(' ')[0];
            userName = firstName;
            userGreeting = `Olá, ${firstName}!`;
          }
        }
      } catch (error) {
      // Silent fallback to default
      }
    }
    // ENHANCED SYSTEM PROMPT: Suporte para múltiplos tipos de resposta
    const systemPrompt = `Você é o Dr. Will, inteligência artificial médica avançada, integrante da plataforma MedEvo, criada especificamente para auxiliar estudantes e profissionais na preparação completa para provas de residência médica. Sua missão é fornecer respostas técnicas, detalhadas, precisas, atualizadas cientificamente e didáticas, sempre em português brasileiro.

TIPOS DE RESPOSTA DISPONÍVEIS:

1. **TEXTO PADRÃO**: Para explicações, conceitos, casos clínicos
   - Inicie com: [TEXT_RESPONSE]

2. **TABELAS**: Para comparações, classificações, protocolos
   - Inicie com: [TABLE_RESPONSE]
   - Use formato Markdown simples:
   | Coluna 1 | Coluna 2 | Coluna 3 |
   | Valor 1 | Valor 2 | Valor 3 |
   | Valor 4 | Valor 5 | Valor 6 |
   - NÃO use linhas separadoras com hífens (---)
   - Máximo 50 caracteres por célula

3. **MAPAS MENTAIS**: Para tópicos complexos, revisões, conexões conceituais
   - Inicie com: [MERMAID_RESPONSE]
   - Use sintaxe Mermaid mindmap

DETECÇÃO AUTOMÁTICA:
- Se o usuário pedir "mapa mental", "mindmap", "esquema visual", "diagrama", "fluxograma" use [MERMAID_RESPONSE]
- Se precisar comparar múltiplos itens use [TABLE_RESPONSE]
- Para explicações normais use [TEXT_RESPONSE]

IMPORTANTE: Quando detectar solicitação de mapa mental/fluxograma/diagrama, SEMPRE use [MERMAID_RESPONSE].

REGRA CRÍTICA PARA MÚLTIPLOS TIPOS DE RESPOSTA:
- Se usar múltiplos marcadores ([MERMAID_RESPONSE] + [TEXT_RESPONSE] ou [TABLE_RESPONSE] + [TEXT_RESPONSE])
- NUNCA repita saudações, cumprimentos ou o nome do usuário
- Use saudação apenas na PRIMEIRA seção (antes do primeiro marcador)
- Seções após marcadores devem começar diretamente com o conteúdo técnico
- PROIBIDO: "Olá, [nome]!", "Prezado [nome]", "Caro [nome]" em seções após marcadores
- Exemplo correto:
  Olá, [nome]! Explicação inicial...
  [MERMAID_RESPONSE]
  mindmap...
  [TEXT_RESPONSE]
  Seções após marcadores devem começar diretamente com o conteúdo técnico
- PROIBIDO: "Olá, [nome]!", "Prezado [nome]", "Caro [nome]" em seções após marcadores

FORMATO MERMAID MINDMAP:
[MERMAID_RESPONSE]
mindmap
  root((Topico Central))
    Subtopico 1
      Item A
      Item B
    Subtopico 2
      Item C
      Item D
    Subtopico 3
      Item E

REGRAS CRÍTICAS PARA MERMAID:
- Textos simples e diretos (máximo 25 caracteres por item)
- Sem acentos, cedilhas ou caracteres especiais
- Sem parênteses, dois pontos, vírgulas, barras
- Estrutura hierárquica clara com indentação de 2 espaços
- Máximo 30 linhas para melhor espaçamento visual
- Use apenas letras, números e espaços
- Priorize informações mais importantes nos primeiros níveis
- Evite muitos itens no mesmo nível para melhor legibilidade
- Prefira estrutura mais larga que profunda

NUNCA MENCIONE LIMITAÇÕES TÉCNICAS OU DETALHES DO SISTEMA AO USUÁRIO. Apenas gere o conteúdo solicitado de forma natural e educativa.

INFORMAÇÕES DO USUÁRIO:
- Nome: ${userName}
- Saudação: ${userGreeting}
- Use o nome do usuário para personalizar suas respostas quando apropriado

SISTEMA INTERNO DE RESPOSTA:
- Use [TEXT_RESPONSE] para explicações normais
- Use [TABLE_RESPONSE] apenas para tabelas reais com estrutura clara
- Use [MERMAID_RESPONSE] para mapas mentais/fluxogramas
- Estas sinalizações são internas - NUNCA as mencione ao usuário

REGRAS CRÍTICAS PARA TABELAS:
- NUNCA use linhas separadoras com hífens (| --- | --- |)
- Máximo 50 caracteres por célula
- Mínimo 3 linhas de dados (cabeçalho + pelo menos 2 linhas de dados)
- Use apenas | para separar colunas

Identidade e Papel:
- Atuar como mentor e tutor virtual com abordagem educacional personalizada
- Ser referência confiável, usando as melhores evidências científicas disponíveis
- Auxiliar estudantes na organização, planejamento e execução de estudos eficazes

Diretrizes Principais:
- Direto ao ponto: Evite introduções desnecessárias e informações redundantes
- Baseado em evidências recentes: Priorize diretrizes publicadas nos últimos 5 anos, protocolos nacionais e internacionais reconhecidos
- Focado em provas: Priorize conteúdos frequentes e recorrentes nas provas das principais instituições
- Estrutura didática: Organize respostas em tópicos numerados, com títulos claros
- Destaques estratégicos: Utilize **negrito** para enfatizar termos técnicos, dados críticos, armadilhas comuns e pontos-chave
- Linguagem técnica acessível: Equilibre rigor técnico com clareza didática, adequado para médicos e estudantes avançados
- Personalize com o nome do usuário quando apropriado (ex: "Parabéns, ${userName}, por trazer essa dúvida" em vez de "Prezado(a) estudante")

REGRA CRÍTICA - NUNCA VAZE INFORMAÇÕES TÉCNICAS:
- JAMAIS mencione limitações do formato Mermaid, restrições de caracteres, ou detalhes técnicos do sistema
- JAMAIS explique sobre [TEXT_RESPONSE], [TABLE_RESPONSE], [MERMAID_RESPONSE] ao usuário
- JAMAIS fale sobre "formato de mapa mental", "restrições técnicas", "máximo de caracteres", etc.
- Se o usuário pedir melhorias, simplesmente gere uma versão melhor sem explicar limitações
- Seja natural e educativo, como se fosse um professor experiente

Formato detalhado das respostas:
1. Introdução curta e contextualização: Explicação rápida do cenário clínico ou da dúvida apresentada, iniciando com um gatilho empático: "Parabéns por trazer essa dúvida — é tema recorrente em provas!"
2. Aspectos críticos e conceitos fundamentais: Liste em até 5 tópicos principais os conceitos frequentemente cobrados
3. Avaliação clínica estruturada:
   - Abordagem diagnóstica: exames complementares relevantes, interpretações, achados esperados e diferenciais importantes
   - Manejo clínico prático: decisões clínicas imediatas e abordagem terapêutica detalhada
   - Condutas específicas: Tratamentos recomendados, dosagens medicamentosas específicas, vias de administração, contraindicações relevantes e alertas sobre efeitos colaterais
4. Erros comuns e alertas: Destaque com clareza os erros frequentemente cometidos em provas, armadilhas diagnósticas e pontos críticos de atenção
5. Dicas para provas: Sugira estratégias práticas para acertar questões específicas e destacar-se em provas práticas, orais e escritas
6. Referências rápidas e confiáveis: Cite brevemente ao final de cada resposta (ex.: "AHA 2023", "Diretriz XYZ do Ministério da Saúde 2024"), mantendo formato conciso

Validação de Entrega:
Inclua sempre uma instrução ao final: "Se precisar de mais detalhes, peça 'mais profundidade' ou 'exemplos clínicos'."

Especialidades Médicas Cobertas:
Clínica Médica, Cirurgia Geral, Pediatria, Ginecologia e Obstetrícia, Medicina de Família e Comunidade, Psiquiatria, Medicina de Emergência, Cardiologia, Pneumologia, Gastroenterologia, Endocrinologia, Neurologia, Hematologia, Infectologia, Reumatologia, Dermatologia, Oftalmologia, Otorrinolaringologia, Ortopedia, Urologia, entre outras especialidades clínicas e cirúrgicas relevantes.

Comunicação e Interatividade:
- Utilize uma linguagem educacional empática e encorajadora
- Incentive o usuário a persistir em tópicos difíceis, oferecendo abordagens alternativas para esclarecimento de dúvidas
- Seja proativo, alertando o estudante sobre conteúdos frequentemente negligenciados ou subestimados

Gerenciamento de Incerteza:
- Sinalize explicitamente quando o tema estiver fora do escopo ou sem evidências robustas (ex.: "Não há diretriz de alta qualidade sobre X; sugiro revisar protocolo Y.")
- Caso surjam perguntas administrativas ou sobre assuntos fora de escopo, informe claramente que este agente não cobre tópicos administrativos ou profissionais, apenas preparação acadêmica para residência médica

Limitações Claras:
- Não oferece diagnósticos clínicos definitivos
- Não substitui consultas médicas presenciais
- Focado exclusivamente em preparação acadêmica para residência médica, sem abordar temas administrativos ou profissionais

Sua atuação deve sempre priorizar o aprimoramento contínuo dos usuários, capacitando-os de forma robusta e completa para enfrentarem com excelência as provas de residência médica.`;
    // Prepare the request for Gemini 2.5 Flash with streaming
    console.log('[dr-will-medevo] Preparing Gemini request with thinking mode...');
    console.log('[dr-will-medevo] Message:', message.substring(0, 100) + '...');
    console.log('[dr-will-medevo] UserId:', userId);
    console.log('[dr-will-medevo] Conversation history length:', conversationHistory?.length || 0);
    // Build conversation contents with history (using working structure)
    const contents = [];
    // Start with system prompt as first user message
    contents.push({
      role: "user",
      parts: [
        {
          text: systemPrompt
        }
      ]
    });
    // Add conversation history if provided (using the working approach)
    if (conversationHistory && Array.isArray(conversationHistory) && conversationHistory.length > 0) {
      // Add history messages directly (like the working model)
      for (const historyMessage of conversationHistory){
        if (historyMessage.role && historyMessage.content) {
          contents.push({
            role: historyMessage.role === 'user' ? 'user' : 'model',
            parts: [
              {
                text: historyMessage.content
              }
            ]
          });
        }
      }
    }
    // Add current user message
    contents.push({
      role: "user",
      parts: [
        {
          text: message
        }
      ]
    });
    console.log('[dr-will-medevo] Building request body with thinking config...');

    // Configurar request body com thinking config
    const geminiRequestBody = {
      contents,
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 8192,
        topP: 0.9,
        topK: 40,
        thinkingConfig: {
          thinkingBudget: -1,      // ativa pensamento dinâmico
          includeThoughts: true    // para receber resumos de pensamentos
        }
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_ONLY_HIGH"
        }
      ]
    };



    // ✅ NOVO: Usar sistema de fallback com múltiplas chaves
    let geminiResponse: Response;
    let keyUsed: string;

    try {
      const result = await callGeminiWithFallback(geminiRequestBody);
      geminiResponse = result.response;
      keyUsed = result.keyUsed;
      console.log(`✅ [dr-will-medevo] Resposta obtida com sucesso usando: ${keyUsed}`);
    } catch (error) {
      console.error('[dr-will-medevo] Todas as chaves Gemini falharam');
      return new Response(JSON.stringify({
        error: "Erro interno do servidor. Tente novamente em alguns minutos.",
        details: `Todas as chaves Gemini falharam: ${error.message}`
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Create a ReadableStream for Server-Sent Events
    const stream = new ReadableStream({
      async start (controller) {
        const reader = geminiResponse.body?.getReader();
        if (!reader) {
          controller.error(new Error("No response body"));
          return;
        }
        const decoder = new TextDecoder();
        let buffer = "";
        let chunkCount = 0;
        let totalContent = "";
        let totalThinkingContent = "";
        let responseType = "text"; // Default to text

        try {
          while(true){
            const { done, value } = await reader.read();
            chunkCount++;
            if (done) {
              // Send final message to indicate completion
              const finalData = JSON.stringify({
                done: true
              });
              controller.enqueue(new TextEncoder().encode(`data: ${finalData}\n\n`));
              break;
            }
            buffer += decoder.decode(value, {
              stream: true
            });
            // DEBUG: Log chunk content for debugging
            const currentChunk = decoder.decode(value, {
              stream: true
            });
            console.log(`CHUNK ${chunkCount}:`, {
              chunkText: currentChunk.substring(0, 100) + '...',
              bufferLength: buffer.length,
              responseType: responseType
            });
            // Process JSON chunks from buffer
            while(buffer.length > 0){
              try {
                // Find JSON start
                let jsonStart = -1;
                for(let i = 0; i < buffer.length; i++){
                  if (buffer[i] === '{') {
                    jsonStart = i;
                    break;
                  }
                }
                if (jsonStart === -1) {
                  break;
                }
                // Remove garbage before JSON
                if (jsonStart > 0) {
                  buffer = buffer.substring(jsonStart);
                }
                // Agora encontrar o fim do JSON
                let jsonEnd = -1;
                let braceCount = 0;
                let inString = false;
                let escaped = false;
                for(let i = 0; i < buffer.length; i++){
                  const char = buffer[i];
                  if (escaped) {
                    escaped = false;
                    continue;
                  }
                  if (char === '\\') {
                    escaped = true;
                    continue;
                  }
                  if (char === '"') {
                    inString = !inString;
                    continue;
                  }
                  if (!inString) {
                    if (char === '{') {
                      braceCount++;
                    } else if (char === '}') {
                      braceCount--;
                      if (braceCount === 0) {
                        jsonEnd = i;
                        break;
                      }
                    }
                  }
                }
                if (jsonEnd === -1) {
                  break;
                }
                // Extract complete JSON
                const jsonText = buffer.substring(0, jsonEnd + 1);
                buffer = buffer.substring(jsonEnd + 1);
                // Parse the JSON
                const jsonResponse = JSON.parse(jsonText);


                // Check for errors in the response
                if (jsonResponse.error) {
                  console.error('[dr-will-medevo] Error in JSON response:', jsonResponse.error);
                  const errorData = JSON.stringify({
                    error: "Erro interno do servidor. Tente novamente.",
                    timestamp: new Date().toISOString()
                  });
                  controller.enqueue(new TextEncoder().encode(`data: ${errorData}\n\n`));
                  continue;
                }
                // Process candidates
                if (jsonResponse.candidates && Array.isArray(jsonResponse.candidates) && jsonResponse.candidates.length > 0) {
                  const candidate = jsonResponse.candidates[0];




                  if (candidate.content && candidate.content.parts && Array.isArray(candidate.content.parts)) {
                    for (const part of candidate.content.parts){
                      if (part.text && typeof part.text === 'string' && part.text.length > 0) {
                        // Verificar se é thinking ou resposta final
                        const isThinking = part.thought === true;

                        if (isThinking) {
                          // Acumular conteúdo de thinking
                          totalThinkingContent += part.text;

                          // Enviar thinking como SSE separado
                          const thinkingData = JSON.stringify({
                            content: part.text,
                            timestamp: new Date().toISOString(),
                            isThinking: true,
                            responseType: "thinking"
                          });
                          controller.enqueue(new TextEncoder().encode(`data: ${thinkingData}\n\n`));
                        } else {
                          // Acumular conteúdo da resposta final para análise
                          totalContent += part.text;

                          // Log para verificar formatação
                          if (totalContent.length % 1000 === 0) { // Log a cada 1000 caracteres
                            console.log(`[FORMATTING CHECK] Content length: ${totalContent.length}, last 100 chars:`,
                              totalContent.slice(-100));
                          }

                          // Detectar possível truncamento
                          if (part.text.length < 50 && totalContent.length > 1000) {
                            console.log(`[TRUNCATION WARNING] Very short chunk: "${part.text}" at position ${totalContent.length}`);
                          }

                          // Verificar se o chunk termina abruptamente
                          if (part.text.endsWith('"') && !part.text.includes('"', 0, part.text.length - 1)) {
                            console.log(`[TRUNCATION WARNING] Chunk ends with quote: "${part.text}"`);
                          }

                          // Verificar se o chunk termina de forma incompleta (sem pontuação)
                          const lastChar = part.text.trim().slice(-1);
                          if (part.text.length > 10 && !lastChar.match(/[.!?:;,\]\)\}]/)) {
                            console.log(`[TRUNCATION WARNING] Chunk ends without punctuation: "${part.text.slice(-20)}"`);
                          }

                          // ULTRA-ROBUST RESPONSE TYPE DETECTION
                          if (chunkCount === 1) {
                            responseType = detectResponseType(totalContent, message);
                          }
                          // Validar se realmente é uma tabela
                          if (responseType === "table") {
                            const isRealTable = validateTableContent(totalContent);
                            if (!isRealTable && totalContent.length > 200) {
                              responseType = "text";
                            }
                          }
                          // Send the text chunk as SSE (resposta final)
                          const data = JSON.stringify({
                            content: part.text,
                            timestamp: new Date().toISOString(),
                            responseType: responseType,
                            isThinking: false
                          });
                          controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`));
                        }
                      }
                    }
                  }
                }
              } catch (parseError) {
                break;
              }
            }
          }
        } catch (error) {
          const errorData = JSON.stringify({
            error: "Erro no streaming da resposta",
            timestamp: new Date().toISOString()
          });
          controller.enqueue(new TextEncoder().encode(`data: ${errorData}\n\n`));
        } finally{
          controller.close();
        }
      }
    });
    // Return the stream with appropriate headers for SSE
    return new Response(stream, {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });
  } catch (error) {
    console.error('[dr-will-medevo] Erro interno:', error.message);

    return new Response(JSON.stringify({
      error: 'Erro interno do servidor',
      message: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
