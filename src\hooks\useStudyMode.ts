import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useDarkMode } from '@/contexts/DarkModeContext';

export const useStudyMode = () => {
  const location = useLocation();
  const { isStudyModeActive } = useDarkMode();

  // Verificar se está em uma sessão de estudo (resolvendo questões)
  const isInStudySession = () => {
    return location.pathname.startsWith('/questions/') ||
           location.pathname.startsWith('/plataformadeestudos/questions/');
  };

  useEffect(() => {
    const shouldBeActive = isInStudySession();

    // Sempre disparar o evento para garantir sincronização
    window.dispatchEvent(new CustomEvent('studyModeChange', {
      detail: { active: shouldBeActive }
    }));

    // Se não deveria estar ativo, forçar desativação
    if (!shouldBeActive) {
      window.dispatchEvent(new CustomEvent('forceDeactivateStudyMode'));
    }
  }, [location.pathname]); // Remover isStudyModeActive para evitar loops

  return {
    isInStudySession: isInStudySession(),
    isStudyModeActive
  };
};
