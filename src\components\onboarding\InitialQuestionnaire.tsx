
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { supabase } from "@/integrations/supabase/client";
import {
  GraduationCap,
  BookOpen,
  Award,
  Stethoscope,
  CheckCircle,
  ArrowRight,
} from "lucide-react";
import { motion } from "framer-motion";
import { useQueryClient } from "@tanstack/react-query";
import { persistentCache } from "@/utils/persistentCache";

interface InitialQuestionnaireProps {
  userId: string;
  onComplete: () => void;
}

const formSchema = z.object({
  preparation_type: z.enum(["residencia"], {
    required_error: "Por favor, selecione o tipo de preparação",
  }),
});

const InitialQuestionnaire = ({ userId, onComplete }: InitialQuestionnaireProps) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      preparation_type: undefined,
    },
  });

  const watchPreparationType = form.watch("preparation_type");

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);

    console.log("🚀 [InitialQuestionnaire] Starting onboarding process", {
      userId,
      preparationType: values.preparation_type,
      timestamp: new Date().toISOString()
    });

    try {
      // Primeiro, verificar se o perfil existe

      const { data: existingProfile, error: checkError } = await supabase
        .from("profiles")
        .select("id, full_name, preparation_type, created_at")
        .eq("id", userId)
        .single();

      if (checkError) {
        if (checkError.code === 'PGRST116') {
          // Profile not found - this should not happen during onboarding
        }

        throw checkError;
      }

      // ✅ SEGURANÇA: Log removido - não expor dados do usuário

      // Atualizar o perfil com o tipo de preparação
      // ✅ SEGURANÇA: Log removido - não expor dados do usuário

      // Para residência, definir specialty como 'residencia' também
      const updateData = {
        preparation_type: values.preparation_type,
        specialty: values.preparation_type === 'residencia' ? 'residencia' : null
      };

      const { data: updatedProfile, error } = await supabase
        .from("profiles")
        .update(updateData)
        .eq("id", userId)
        .select("id, full_name, preparation_type, specialty, updated_at")
        .single();

      if (error) {
        // ✅ SEGURANÇA: Log de erro sem expor dados sensíveis
        console.error("❌ [InitialQuestionnaire] Profile update error:", error.message);
        throw error;
      }

      // ✅ SEGURANÇA: Log removido - não expor dados do usuário

      // Invalidar TODOS os caches relacionados ao usuário
      // ✅ SEGURANÇA: Log removido - não expor operações internas

      // Invalidar todas as queries do usuário
      await queryClient.invalidateQueries({ queryKey: ['user-profile'] });
      await queryClient.refetchQueries({ queryKey: ['user-profile'] });

      // Limpar cache persistente
      const cacheKey = `user-profile-${userId}`;
      persistentCache.remove(cacheKey);

      // Forçar re-fetch imediato dos dados atualizados
      await queryClient.refetchQueries({
        queryKey: ['user-profile', userId],
        type: 'active'
      });

      toast({
        title: "Perfil atualizado com sucesso!",
        description: "Agora você pode acessar a plataforma de estudos.",
      });

      // Forçar redirecionamento usando window.location para evitar problemas de cache/routing
      setTimeout(() => {
        window.location.href = '/study-preferences';
      }, 1000);
    } catch (error) {

      toast({
        variant: "destructive",
        title: "Erro ao atualizar perfil",
        description: "Por favor, tente novamente mais tarde.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  const cardVariants = {
    unselected: { scale: 1, y: 0 },
    selected: { scale: 1.02, y: -5 }
  };

  // Automatically submit when residencia is selected
  const handleOptionSelect = async (value: "residencia") => {
    // Prevent multiple clicks
    if (isSubmitting) {
      console.log("⏳ [InitialQuestionnaire] Option select ignored - already submitting");
      return;
    }

    console.log("🎯 [InitialQuestionnaire] Option selected:", {
      userId,
      selectedOption: value,
      timestamp: new Date().toISOString()
    });

    // Set submitting state immediately to prevent multiple clicks
    setIsSubmitting(true);
    form.setValue("preparation_type", value);

    // Submit immediately without delay to avoid issues
    console.log("⏰ [InitialQuestionnaire] Submitting immediately");
    await onSubmit({ preparation_type: value });
  };

  return (
    <div className="bg-white rounded-xl border-2 border-black shadow-lg p-6 md:p-8 overflow-hidden">
      <Form {...form}>
        <div className="space-y-8">
          <motion.div
            initial="hidden"
            animate="show"
            variants={container}
            className="space-y-6"
          >
            <motion.div variants={item} className="text-center mb-2">
              <div className="inline-block transform -rotate-2 mb-4">
                <div className="bg-hackathon-red border-2 border-black px-4 py-1 text-white font-bold tracking-wide text-sm shadow-card-sm">
                  PERSONALIZE SUA EXPERIÊNCIA
                </div>
              </div>
              <FormLabel className="text-4xl font-black text-black block mb-2">
                <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1 mb-1">Qual seu</span>
                <span className="block text-hackathon-red">objetivo?</span>
              </FormLabel>
              <FormDescription className="text-gray-700 text-lg max-w-md mx-auto">
                Escolha o tipo de prova que você está estudando para personalizarmos sua experiência.
              </FormDescription>
            </motion.div>

            <FormField
              control={form.control}
              name="preparation_type"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormControl>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <motion.div
                        variants={cardVariants}
                        animate={field.value === "residencia" ? "selected" : "unselected"}
                        className={`border-2 rounded-xl overflow-hidden group transition-all
                          ${isSubmitting
                            ? "cursor-wait opacity-75"
                            : "cursor-pointer"
                          }
                          ${field.value === "residencia"
                            ? "border-black bg-hackathon-yellow/10 shadow-lg"
                            : "border-gray-200 hover:border-gray-300 shadow-md"}`}
                        onClick={() => !isSubmitting && handleOptionSelect("residencia")}
                      >
                        <div className="relative">
                          <div className="bg-gradient-to-r from-black to-gray-800 h-20 flex items-center justify-center">
                            <GraduationCap className="text-white h-12 w-12" />
                          </div>
                          <div className="absolute top-3 right-3">
                            {isSubmitting && field.value === "residencia" ? (
                              <div className="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full border-2 border-black flex items-center gap-1">
                                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                                Processando...
                              </div>
                            ) : field.value === "residencia" ? (
                              <div className="bg-hackathon-green text-black text-xs font-bold px-2 py-1 rounded-full border-2 border-black">
                                Selecionado
                              </div>
                            ) : null}
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-bold">Residência Médica</h3>
                          </div>
                          <p className="text-gray-700 text-sm">
                            Prepare-se para conquistar sua vaga na residência médica dos seus sonhos.
                          </p>
                        </div>
                      </motion.div>

                      {/* Revalida - Coming Soon */}
                      <motion.div
                        variants={item}
                        className="border-2 rounded-xl overflow-hidden opacity-50 cursor-not-allowed"
                      >
                        <div className="relative">
                          <div className="bg-gradient-to-r from-green-600 to-green-800 h-20 flex items-center justify-center">
                            <Stethoscope className="text-white h-12 w-12" />
                          </div>
                          <div className="absolute -right-1 -top-1">
                            <span className="bg-black text-white text-xs px-2 py-1 rounded-bl-lg font-medium">
                              Em breve
                            </span>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-bold">Revalida</h3>
                          </div>
                          <p className="text-gray-700 text-sm">
                            Valide seu diploma obtido no exterior e exerça a medicina no Brasil.
                          </p>
                        </div>
                      </motion.div>

                      {/* Título de Especialista - Coming Soon */}
                      <motion.div
                        variants={item}
                        className="border-2 rounded-xl overflow-hidden opacity-50 cursor-not-allowed col-span-full md:col-span-2"
                      >
                        <div className="relative">
                          <div className="bg-gradient-to-r from-blue-600 to-blue-800 h-20 flex items-center justify-center">
                            <Award className="text-white h-12 w-12" />
                          </div>
                          <div className="absolute -right-1 -top-1">
                            <span className="bg-black text-white text-xs px-2 py-1 rounded-bl-lg font-medium">
                              Em breve
                            </span>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-bold">Título de Especialista</h3>
                          </div>
                          <p className="text-gray-700 text-sm">
                            Conquiste seu título em uma especialidade e alcance o reconhecimento profissional.
                          </p>
                        </div>
                      </motion.div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </motion.div>
        </div>
      </Form>
    </div>
  );
};

export default InitialQuestionnaire;
