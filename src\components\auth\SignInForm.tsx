
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { useFeedbackDialog } from "@/components/ui/feedback-dialog";
import { useNavigate } from "react-router-dom";
import NotificationDialog from "./NotificationDialog";
import ErrorAlert from "./ErrorAlert";
import LoadingBar from "./LoadingBar";
import GoogleLoginButton from "./GoogleLoginButton";

const formSchema = z.object({
  email: z.string()
    .email("Email inválido")
    .min(5, "Email muito curto")
    .refine((val) => {
      const domain = val.split('@')[1];
      return domain && domain.includes('.') && domain.length > 3;
    }, "Domínio de email inválido"),
  password: z.string()
    .min(6, "A senha deve ter pelo menos 6 caracteres")
    .refine((val) => !/^\s|\s$/.test(val), "Senha não pode começar ou terminar com espaços"),
});

interface SignInFormProps {
  onModeChange: () => void;
  onSuccess: () => void;
  onResetPassword?: () => void;
}

const SignInForm = ({ onModeChange, onSuccess, onResetPassword }: SignInFormProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState({ title: "", description: "" });
  const [userName, setUserName] = useState("");
  const { showFeedback } = useFeedbackDialog();
  const navigate = useNavigate();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      setShowError(false);

      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });

      if (error) {
        throw error;
      }

      if (!authData.user) {
        throw new Error('Usuário não encontrado após login');
      }

      // Verificar perfil usando o ID do usuário autenticado

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('preparation_type, specialty, premium, premium_requested, full_name, created_at')
        .eq('id', authData.user.id)
        .single();

      if (profileError) {
        console.error("❌ [SignInForm] Profile query error:", {
          userId: authData.user.id,
          error: profileError.message,
          code: profileError.code,
          details: profileError
        });
      }

      // Buscar nome do usuário para personalizar mensagem
      const fullName = authData.user.user_metadata?.full_name || values.email.split('@')[0];
      setUserName(fullName);

      // Mostrar dialog de sucesso
      setShowSuccessDialog(true);

      // Aguardar um pouco para garantir que o AuthContext seja atualizado
      await new Promise(resolve => setTimeout(resolve, 100));

      // Lógica de redirecionamento baseada no perfil
      if (profileError || !profile) {
        navigate("/onboarding", { replace: true });
        return;
      }

      if (!profile.preparation_type) {
        navigate("/onboarding", { replace: true });
        return;
      }

      // Verificar acesso à plataforma
      if (!profile.premium && !profile.premium_requested) {
        navigate("/acesso-restrito", { replace: true });
        return;
      }

      navigate("/plataformadeestudos", { replace: true });

    } catch (error: any) {
      let title = "Não foi possível fazer login";
      let description = "Verifique seus dados e tente novamente.";

      if (error.message.includes("Invalid login credentials")) {
        title = "Não foi possível fazer login";
        description = "Email ou senha incorretos.";
      } else if (error.message.includes("Email not confirmed")) {
        title = "Email não confirmado";
        description = "Verifique sua caixa de entrada e confirme seu email antes de fazer login.";
      } else if (error.message.includes("Too many requests")) {
        title = "Muitas tentativas";
        description = "Aguarde alguns minutos antes de tentar novamente.";
      }

      setErrorMessage({ title, description });
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccessDialogClose = async () => {
    setShowSuccessDialog(false);
    onSuccess();

    // Aguardar um pouco para garantir que o AuthContext seja atualizado
    await new Promise(resolve => setTimeout(resolve, 100));

    // Verificar se o usuário tem acesso
    const { data: user } = await supabase.auth.getUser();
    if (user.user) {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('preparation_type, premium, premium_requested')
        .eq('id', user.user.id)
        .single();

      // Lógica de redirecionamento baseada no perfil
      if (profileError || !profile) {
        navigate("/onboarding", { replace: true });
        return;
      }

      if (!profile.preparation_type) {
        navigate("/onboarding", { replace: true });
        return;
      }

      // Verificar acesso à plataforma
      if (!profile.premium && !profile.premium_requested) {
        navigate("/acesso-restrito", { replace: true });
        return;
      }

      navigate("/plataformadeestudos", { replace: true });
    }
  };

  return (
    <>
      <div className="space-y-4 py-2 pb-4">
        <ErrorAlert
          title={errorMessage.title}
          description={errorMessage.description}
          show={showError}
        />

        {/* Google Login Button */}
        <div className="space-y-3">
          <GoogleLoginButton
            onSuccess={onSuccess}
            onError={(error) => {
              setErrorMessage({
                title: "Erro no Login com Google",
                description: error
              });
              setShowError(true);
            }}
          />

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Ou continue com email
              </span>
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Senha</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="••••••••"
                    autoComplete="current-password"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex flex-col gap-3">
            <Button
              type="submit"
              className="w-full bg-hackathon-green hover:bg-hackathon-green/90 border-2 border-black text-black font-bold py-3 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              disabled={isLoading}
            >
              {isLoading ? "Entrando..." : "Entrar"}
            </Button>

            <div className="flex flex-col gap-2">
              <Button
                type="button"
                variant="outline"
                className="w-full text-sm border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all"
                onClick={onModeChange}
              >
                Não tem uma conta? <span className="font-bold">Cadastre-se</span>
              </Button>

              {onResetPassword && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full text-sm text-gray-600 border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all"
                  onClick={onResetPassword}
                >
                  Esqueceu sua senha?
                </Button>
              )}
            </div>
          </div>
        </form>
      </Form>

      <LoadingBar show={isLoading} className="mt-4" />
    </div>

    <NotificationDialog
      open={showSuccessDialog}
      onOpenChange={setShowSuccessDialog}
      type="success"
      title="Login realizado com sucesso!"
      description={`Boa ${new Date().getHours() < 12 ? 'dia' : new Date().getHours() < 18 ? 'tarde' : 'noite'}, ${userName}! Que bom ter você de volta.`}
      buttonText="Continuar"
      onButtonClick={handleSuccessDialogClose}
    />
  </>
  );
};

export default SignInForm;
