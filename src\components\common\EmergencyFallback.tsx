import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { AlertCircle, Home, RefreshCw, ExternalLink, Shield } from 'lucide-react';
import { motion } from 'framer-motion';

interface EmergencyFallbackProps {
  title?: string;
  message?: string;
  onForceReload?: () => void;
  onGoHome?: () => void;
  onContactSupport?: () => void;
  showSupportOption?: boolean;
}

export const EmergencyFallback: React.FC<EmergencyFallbackProps> = ({
  title = "Sistema Temporariamente Indisponível",
  message = "Esgotamos todas as tentativas de conexão. Você pode tentar algumas opções abaixo.",
  onForceReload,
  onGoHome,
  onContactSupport,
  showSupportOption = true
}) => {
  const handleForceReload = () => {
    // Limpar todo o cache e storage antes de recarregar
    try {
      localStorage.clear();
      sessionStorage.clear();
      
      // Limpar cache do service worker se existir
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => registration.unregister());
        });
      }
      
      // Recarregar página forçando cache refresh
      window.location.reload();
    } catch (error) {
      // Fallback simples
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    try {
      // Limpar dados problemáticos
      localStorage.removeItem('studywise-auth-storage');
      sessionStorage.clear();
      
      // Ir para home
      window.location.href = '/';
    } catch (error) {
      window.location.href = '/';
    }
  };

  const handleContactSupport = () => {
    const message = encodeURIComponent(
      "Olá! Estou tendo problemas para acessar a plataforma MedEvo. O sistema não consegue carregar mesmo após várias tentativas."
    );
    window.open(`https://api.whatsapp.com/send?phone=5564993198433&text=${message}`, '_blank');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-lg"
      >
        <Card className="p-8 text-center border-2 border-red-200 shadow-xl bg-white">
          {/* Ícone de Alerta */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mb-6 flex justify-center"
          >
            <div className="bg-red-100 rounded-full p-4">
              <AlertCircle className="h-10 w-10 text-red-600" />
            </div>
          </motion.div>

          {/* Título e Mensagem */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {title}
            </h2>
            <p className="text-gray-600 mb-8 leading-relaxed">
              {message}
            </p>
          </motion.div>

          {/* Status Técnico */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="mb-8 p-4 rounded-lg bg-gray-50 border border-gray-200"
          >
            <div className="flex items-center justify-center gap-2 text-sm text-gray-600 mb-2">
              <Shield className="h-4 w-4" />
              <span className="font-medium">Status do Sistema</span>
            </div>
            <div className="text-xs text-gray-500 space-y-1">
              <div>• Tentativas de conexão: Esgotadas</div>
              <div>• Modo de emergência: Ativo</div>
              <div>• Dados locais: Preservados</div>
            </div>
          </motion.div>

          {/* Botões de Ação */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="space-y-4"
          >
            {/* Recarregar Forçado */}
            <Button
              onClick={onForceReload || handleForceReload}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-3"
              size="lg"
            >
              <RefreshCw className="h-5 w-5 mr-2" />
              Recarregar Completamente
            </Button>

            {/* Ir para Home */}
            <Button
              onClick={onGoHome || handleGoHome}
              variant="outline"
              className="w-full border-gray-300 hover:bg-gray-50 py-3"
              size="lg"
            >
              <Home className="h-5 w-5 mr-2" />
              Voltar ao Início
            </Button>

            {/* Contatar Suporte */}
            {showSupportOption && (
              <Button
                onClick={onContactSupport || handleContactSupport}
                variant="secondary"
                className="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-3"
                size="lg"
              >
                <ExternalLink className="h-5 w-5 mr-2" />
                Contatar Suporte
              </Button>
            )}
          </motion.div>

          {/* Informações Adicionais */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200"
          >
            <p className="text-xs text-blue-700 font-medium mb-2">ℹ️ O que fazer:</p>
            <ul className="text-xs text-blue-600 space-y-1 text-left">
              <li>1. Tente "Recarregar Completamente" primeiro</li>
              <li>2. Verifique sua conexão com a internet</li>
              <li>3. Se persistir, contate o suporte</li>
              <li>4. Seus dados de estudo estão seguros</li>
            </ul>
          </motion.div>
        </Card>
      </motion.div>
    </div>
  );
};

export default EmergencyFallback;
