
import { HierarchySelect } from "./HierarchySelect";
import { Button } from "@/components/ui/button";
import React from "react";

interface FlashcardAIWizardFiltersProps {
  specialty: string;
  setSpecialty: (val: string) => void;
  theme: string;
  setTheme: (val: string) => void;
  focus: string;
  setFocus: (val: string) => void;
  extrafocus: string;
  setExtrafocus: (val: string) => void;
  quantity: number;
  setQuantity: (val: number) => void;
  fcType: "cloze"|"vf"|"option"|"qa";
  setFcType: (val: "cloze"|"vf"|"option"|"qa") => void;
  loading: boolean;
  specialties: Array<{ id: string; name: string }>;
  themes: Array<{ id: string; name: string }>;
  focuses: Array<{ id: string; name: string }>;
  extraFocuses: Array<{ id: string; name: string }>;
  onGenerate: () => void;
}

const MAX_FLASHCARDS = 20;
const FLASHCARD_TYPES = [
  { value: "cloze", label: "Cloze Deletion (Lacuna)" },
  { value: "vf", label: "Verdadeiro ou Falso (V/F)" },
  { value: "multipla", label: "Alternativas (Múltipla Escolha)" },
  { value: "qa", label: "Pergunta e Resposta (Q&A/Padrão)" },
];

export function FlashcardAIWizardFilters({
  specialty,
  setSpecialty,
  theme,
  setTheme,
  focus,
  setFocus,
  extrafocus,
  setExtrafocus,
  quantity,
  setQuantity,
  fcType,
  setFcType,
  loading,
  specialties,
  themes,
  focuses,
  extraFocuses,
  onGenerate,
}: FlashcardAIWizardFiltersProps) {
  return (
    <div>
      <h2 className="font-bold text-2xl mb-2">Gerar Flashcards com IA</h2>
      <p className="text-muted-foreground mb-4">
        Selecione os filtros desejados, escolha o tipo de flashcard e quantos quer gerar (máx. {MAX_FLASHCARDS}) e clique em "Gerar com IA".
      </p>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
        <HierarchySelect
          label="Especialidade *"
          value={specialty}
          onChange={setSpecialty}
          options={specialties}
          placeholder="Selecione a especialidade"
          disabled={loading}
        />
        <HierarchySelect
          label="Tema"
          value={theme}
          onChange={setTheme}
          options={themes}
          placeholder="Selecione o tema (opcional)"
          disabled={!specialty || loading}
        />
        <HierarchySelect
          label="Foco"
          value={focus}
          onChange={setFocus}
          options={focuses}
          placeholder="Selecione o foco (opcional)"
          disabled={!theme || loading}
        />

        <div>
          <label className="block font-medium mb-1">Tipo de flashcard</label>
          <select
            className="w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-hackathon-yellow focus:border-transparent"
            value={fcType}
            onChange={(e) => setFcType(e.target.value as "cloze"|"vf"|"multipla"|"qa")}
            disabled={loading}
          >
            {FLASHCARD_TYPES.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block font-medium mb-1">Quantidade de flashcards</label>
          <input
            type="number"
            min={1}
            max={MAX_FLASHCARDS}
            value={quantity}
            onChange={e => setQuantity(Math.max(1, Math.min(MAX_FLASHCARDS, Number(e.target.value))))}
            placeholder="Quantidade"
            disabled={loading}
            className="w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-hackathon-yellow focus:border-transparent"
          />
        </div>
      </div>
      <Button
        onClick={onGenerate}
        disabled={loading || !specialty}
        className="bg-hackathon-yellow border-2 border-black font-bold shadow-button text-black disabled:opacity-50 disabled:bg-gray-200 disabled:text-gray-500 disabled:border-gray-300"
      >
        {loading ? "Gerando..." : "Gerar com IA"}
      </Button>

      {loading && (
        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700 text-center">
            ⚠️ <strong>Não feche esta aba</strong> até a geração terminar
          </p>
        </div>
      )}
    </div>
  );
}
