import React from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, ArrowLeft, Check } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDarkMode } from "@/contexts/DarkModeContext";

interface ErrorQuestionNavigationProps {
  currentIndex: number;
  totalQuestions: number;
  onNext?: () => void;
  onPrevious?: () => void;
  onGoToQuestion?: (index: number) => void;
  onBack: () => void;
  mode: 'view' | 'retry';
  questionStatuses?: Record<number, 'correct' | 'incorrect' | 'pending'>;
  reviewedQuestions?: Set<number>; // Índices das questões já revisadas
  answeredStatuses?: Record<number, boolean | null>; // Status das respostas no modo retry
  onFinishSession?: () => void; // Finalizar sessão no modo retry
}

export const ErrorQuestionNavigation = ({
  currentIndex,
  totalQuestions,
  onNext,
  onPrevious,
  onGoToQuestion,
  onBack,
  mode,
  questionStatuses = {},
  reviewedQuestions = new Set(),
  answeredStatuses = {},
  onFinishSession
}: ErrorQuestionNavigationProps) => {
  const { isDarkMode } = useDarkMode();

  const handleNext = () => {
    if (onNext && currentIndex < totalQuestions - 1) {
      onNext();
    }
  };

  const handlePrevious = () => {
    if (onPrevious && currentIndex > 0) {
      onPrevious();
    }
  };

  const getQuestionButtonStyle = (index: number) => {
    const status = questionStatuses[index];
    const isActive = index === currentIndex;
    const isReviewed = reviewedQuestions.has(index);
    const answerStatus = answeredStatuses[index];

    if (isActive) {
      return isDarkMode
        ? "bg-blue-600 text-white border-blue-500"
        : "bg-blue-600 text-white border-blue-500";
    }

    // No modo retry, priorizar o status da resposta
    if (mode === 'retry' && answerStatus !== undefined) {
      if (answerStatus === true) {
        // Acertou
        return isDarkMode
          ? "bg-green-900/40 border-green-600 text-green-300 hover:bg-green-900/60"
          : "bg-green-50 border-green-200 text-green-700 hover:bg-green-100";
      } else if (answerStatus === false) {
        // Errou
        return isDarkMode
          ? "bg-red-900/40 border-red-600 text-red-300 hover:bg-red-900/60"
          : "bg-red-50 border-red-200 text-red-700 hover:bg-red-100";
      }
    }

    // No modo view, se a questão foi revisada, mostrar em verde
    if (mode === 'view' && isReviewed) {
      return isDarkMode
        ? "bg-green-700 border-green-500 text-green-100 hover:bg-green-600"
        : "bg-green-500 border-green-400 text-white hover:bg-green-600";
    }

    // Fallback para status antigo ou questões não respondidas
    switch (status) {
      case 'correct':
        return isDarkMode
          ? "bg-green-900/40 border-green-600 text-green-300 hover:bg-green-900/60"
          : "bg-green-50 border-green-200 text-green-700 hover:bg-green-100";
      case 'incorrect':
        return isDarkMode
          ? "bg-red-900/40 border-red-600 text-red-300 hover:bg-red-900/60"
          : "bg-red-50 border-red-200 text-red-700 hover:bg-red-100";
      default:
        return isDarkMode
          ? "bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
          : "bg-white border-gray-200 text-gray-700 hover:bg-gray-50";
    }
  };

  const getModeIcon = () => {
    if (mode === 'retry') {
      return "🔄";
    }
    return "👁️";
  };

  const getModeText = () => {
    if (mode === 'retry') {
      return "Modo Refazer";
    }
    return "Modo Visualização";
  };

  return (
    <div className={`sticky top-0 z-50 backdrop-blur-sm shadow-md rounded-xl mb-4 transition-colors duration-200 ${
      isDarkMode ? 'bg-gray-800/95' : 'bg-white/95'
    }`}>
      <div className="flex items-center justify-between gap-2 max-w-full px-2 sm:px-4 py-2 sm:py-3">
        
        {/* Mobile View */}
        <div className="md:hidden flex items-center justify-between w-full">
          <Button
            variant="outline"
            size="sm"
            onClick={onBack}
            className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>

          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">{getModeIcon()}</span>
            {totalQuestions > 1 ? (
              <>
                <Select
                  value={currentIndex.toString()}
                  onValueChange={(value) => {
                    const targetIndex = parseInt(value);
                    if (onGoToQuestion) {
                      onGoToQuestion(targetIndex);
                    } else {
                      // Fallback para navegação sequencial
                      if (targetIndex > currentIndex && onNext) {
                        onNext();
                      } else if (targetIndex < currentIndex && onPrevious) {
                        onPrevious();
                      }
                    }
                  }}
                >
                  <SelectTrigger className={cn(
                    "w-16 h-10 rounded-lg transition-colors duration-200",
                    getQuestionButtonStyle(currentIndex)
                  )}>
                    <SelectValue>
                      {mode === 'retry' && answeredStatuses[currentIndex] === true ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        currentIndex + 1
                      )}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: totalQuestions }, (_, i) => (
                      <SelectItem key={i} value={i.toString()}>
                        {i + 1}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <span className="text-sm text-gray-600">de {totalQuestions}</span>
              </>
            ) : (
              <span className={`text-sm font-medium px-3 py-2 rounded-lg transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-gray-700 text-gray-300'
                  : 'bg-gray-100 text-gray-700'
              }`}>
                Questão Única
              </span>
            )}

            {/* Botão Finalizar - Mobile */}
            {mode === 'retry' && onFinishSession && (
              <Button
                variant="default"
                size="sm"
                onClick={onFinishSession}
                className="rounded-full px-4"
              >
                Finalizar
              </Button>
            )}
          </div>
        </div>

        {/* Desktop View */}
        <div className="hidden md:flex items-center justify-between w-full">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={onBack}
              className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Button>

            <div className="flex items-center gap-2">
              <span className="text-lg">{getModeIcon()}</span>
              <h2 className="text-xl font-bold">{getModeText()}</h2>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              {currentIndex + 1} de {totalQuestions}
            </span>

            {totalQuestions > 1 && (
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handlePrevious}
                  disabled={currentIndex === 0}
                  className="h-8 w-8 rounded-full"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                <div className="flex gap-1 max-w-md overflow-x-auto">
                  {Array.from({ length: Math.min(totalQuestions, 10) }, (_, i) => {
                    // Mostrar questões ao redor da atual
                    const startIndex = Math.max(0, currentIndex - 5);
                    const questionIndex = startIndex + i;

                    if (questionIndex >= totalQuestions) return null;

                    return (
                      <Button
                        key={questionIndex}
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (onGoToQuestion) {
                            onGoToQuestion(questionIndex);
                          } else {
                            // Fallback para navegação sequencial se onGoToQuestion não estiver disponível
                            if (questionIndex > currentIndex && onNext) {
                              onNext();
                            } else if (questionIndex < currentIndex && onPrevious) {
                              onPrevious();
                            }
                          }
                        }}
                        className={cn(
                          "h-8 w-8 p-0 rounded-full transition-all duration-200",
                          getQuestionButtonStyle(questionIndex)
                        )}
                      >
                        {mode === 'retry' && answeredStatuses[questionIndex] === true ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          questionIndex + 1
                        )}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleNext}
                  disabled={currentIndex === totalQuestions - 1}
                  className="h-8 w-8 rounded-full"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Botão Finalizar - Desktop */}
          {mode === 'retry' && onFinishSession && (
            <Button
              variant="default"
              size="sm"
              onClick={onFinishSession}
              className="flex items-center gap-2 rounded-full"
            >
              Finalizar
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
