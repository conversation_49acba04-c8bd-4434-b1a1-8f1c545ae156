import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";


interface Tag {
  id: string;
  name: string;
  user_id: string;
  created_at: string;
}

export const useTags = () => {
  const { user } = useAuth();

  const { data: tags, isLoading } = useQuery({
    queryKey: ['notes-tags', user?.id],
    queryFn: async () => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from('pedbook_notes_tags')
        .select('*')
        .eq('user_id', user.id)
        .order('name');

      if (error) {
        console.error('❌ [useTags] Erro ao carregar tags:', error);
        throw error;
      }

      return data as Tag[];
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 10 * 60 * 1000, // 10 minutos
  });

  return {
    tags,
    isLoading,
  };
};