
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  <PERSON><PERSON>, 
  Dialog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Trash2, AlertTriangle } from "lucide-react";

interface DeleteWeekDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  weekNumber: number;
  isHighestWeek: boolean;
  onDelete: () => void;
  onDeleteAll: () => void;
}

export function DeleteWeekDialog({
  open,
  onOpenChange,
  weekNumber,
  isHighestWeek,
  onDelete,
  onDeleteAll
}: DeleteWeekDialogProps) {
  const [deleteAllMode, setDeleteAllMode] = useState(false);

  const handleDelete = () => {
    if (deleteAllMode) {
      if (typeof onDeleteAll === 'function') {
        onDeleteAll();
      } else {
        console.error("❌ onDeleteAll is not a function:", onDeleteAll);
      }
    } else {
      if (typeof onDelete === 'function') {
        onDelete();
      } else {
        console.error("❌ onDelete is not a function:", onDelete);
      }
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-600" />
            {deleteAllMode ? "Excluir Todas as Semanas" : `Excluir Semana ${weekNumber}`}
          </DialogTitle>
          <div className="text-sm text-muted-foreground mt-2">
            {deleteAllMode ? (
              <div className="space-y-2 mt-2">
                <div className="flex items-start gap-2 bg-amber-50 p-3 rounded-md border border-amber-200">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-amber-700">
                    Isto irá excluir <strong>todas as semanas</strong> permanentemente e seus tópicos de estudo.
                    Esta ação não pode ser desfeita.
                  </span>
                </div>
              </div>
            ) : isHighestWeek ? (
              <div className="space-y-2 mt-2">
                <span>Você tem certeza que deseja excluir a Semana {weekNumber}?</span>
                <div className="text-sm text-gray-500">
                  Isso removerá permanentemente a semana e todos os seus tópicos de estudo.
                </div>
              </div>
            ) : (
              <div className="space-y-2 mt-2">
                <div className="flex items-start gap-2 bg-amber-50 p-3 rounded-md border border-amber-200">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-amber-700">
                    Você só pode excluir a última semana (Semana {weekNumber} não é a última semana).
                    As semanas devem ser excluídas em ordem reversa.
                  </span>
                </div>
              </div>
            )}
          </div>
        </DialogHeader>
        <div className="flex-col sm:flex-row sm:justify-between sm:space-x-0 gap-3 mt-4">
          {!deleteAllMode && (
            <Button
              type="button"
              variant="outline"
              className="border-red-300 text-red-600 hover:bg-red-50 hover:text-red-700"
              onClick={() => setDeleteAllMode(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Excluir Todas as Semanas
            </Button>
          )}
          
          <div className="flex gap-3 w-full sm:w-auto">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setDeleteAllMode(false);
                onOpenChange(false);
              }}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
            <Button
              type="button"
              variant={deleteAllMode ? "destructive" : isHighestWeek ? "destructive" : "outline"}
              onClick={handleDelete}
              disabled={!deleteAllMode && !isHighestWeek}
              className="w-full sm:w-auto"
            >
              {deleteAllMode ? (
                <>Excluir Todas as Semanas</>
              ) : (
                <>Excluir Semana</>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
