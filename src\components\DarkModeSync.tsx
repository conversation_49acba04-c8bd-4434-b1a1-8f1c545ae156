import { useEffect, useRef } from 'react';
import { useDarkMode } from '@/contexts/DarkModeContext';
import { useConsolidatedUserPreferences } from '@/hooks/useConsolidatedUserPreferences';

/**
 * Componente para sincronizar preferências de modo noturno com o banco de dados
 * Deve ser renderizado dentro do QueryClientProvider
 */
export const DarkModeSync = () => {
  const { syncWithDatabase } = useDarkMode();
  const { darkModeEnabled, updateDarkModePreference, isLoading } = useConsolidatedUserPreferences();
  const syncedRef = useRef(false);

  // Configurar função de sincronização apenas uma vez
  useEffect(() => {
    if (!syncedRef.current) {
      syncWithDatabase(updateDarkModePreference);
      syncedRef.current = true;
    }
  }, [syncWithDatabase, updateDarkModePreference]);

  // Sincronizar com banco apenas quando dados carregarem pela primeira vez
  useEffect(() => {
    if (!isLoading && darkModeEnabled !== undefined) {
      const localStorageMode = localStorage.getItem('darkMode') === 'true';

      // Se há diferença entre banco e localStorage, usar a do banco
      if (darkModeEnabled !== localStorageMode) {
        localStorage.setItem('darkMode', darkModeEnabled.toString());

        // Disparar evento para atualizar o contexto
        window.dispatchEvent(new CustomEvent('darkModeFromDatabase', {
          detail: { enabled: darkModeEnabled }
        }));
      }
    }
  }, [darkModeEnabled, isLoading]); // Remover syncWithDatabase das dependências

  return null; // Componente invisível
};
