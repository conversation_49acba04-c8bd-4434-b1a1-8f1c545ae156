
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { FileImage, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface ImageUploadProps {
  label: string;
  currentImage: string;
  onImageSelect: (url: string) => void;
  id: string;
}

export const ImageUpload = ({
  label,
  currentImage,
  onImageSelect,
  id
}: ImageUploadProps) => {
  const [isUploading, setIsUploading] = useState(false);

  // ✅ Função para converter imagem para WebP
  const convertToWebP = (file: File): Promise<File> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // ✅ Redimensionar se necessário (máximo 1200px)
        const maxSize = 1200;
        let { width, height } = img;

        if (width > maxSize || height > maxSize) {
          if (width > height) {
            height = (height * maxSize) / width;
            width = maxSize;
          } else {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // ✅ Desenhar imagem redimensionada
        ctx?.drawImage(img, 0, 0, width, height);

        // ✅ Converter para WebP com qualidade 85%
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const webpFile = new File([blob], `${Math.random()}.webp`, {
                type: 'image/webp'
              });
              resolve(webpFile);
            } else {
              reject(new Error('Falha na conversão para WebP'));
            }
          },
          'image/webp',
          0.85
        );
      };

      img.onerror = () => reject(new Error('Falha ao carregar imagem'));
      img.src = URL.createObjectURL(file);
    });
  };

  const uploadImage = async (file: File) => {
    setIsUploading(true);
    try {
      // ✅ Converter para WebP
      const webpFile = await convertToWebP(file);

      const filePath = webpFile.name;

      const { error: uploadError } = await supabase.storage
        .from('flashcard_images')
        .upload(filePath, webpFile);

      if (uploadError) {
        throw uploadError;
      }

      const { data } = supabase.storage
        .from('flashcard_images')
        .getPublicUrl(filePath);

      onImageSelect(data.publicUrl);
      toast.success('Imagem otimizada e carregada com sucesso!');
    } catch (error) {
      console.error('Erro ao enviar imagem:', error);
      toast.error('Erro ao enviar imagem. Tente novamente.');
    } finally {
      setIsUploading(false);
    }
  };

  // ✅ Upload direto para GIFs (sem conversão)
  const uploadImageDirect = async (file: File) => {
    setIsUploading(true);
    try {


      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = fileName;

      const { error: uploadError } = await supabase.storage
        .from('flashcard_images')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      const { data } = supabase.storage
        .from('flashcard_images')
        .getPublicUrl(filePath);

      onImageSelect(data.publicUrl);

      toast.success('GIF carregado com sucesso!');
    } catch (error) {
      console.error('Erro ao enviar GIF:', error);
      toast.error('Erro ao enviar GIF. Tente novamente.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];



    if (file) {
      // ✅ Verificar se é GIF primeiro para aplicar limite diferente
      if (file.type === 'image/gif') {
        // ✅ Limite maior para GIFs (20MB)
        if (file.size > 20 * 1024 * 1024) {
          toast.error('O GIF deve ter menos de 20MB');
          return;
        }

        uploadImageDirect(file);
      } else {
        // ✅ Limite normal para outras imagens (5MB)
        if (file.size > 5 * 1024 * 1024) {
          toast.error('A imagem deve ter menos de 5MB');
          return;
        }

        uploadImage(file);
      }
    }
  };

  const removeImage = () => {
    onImageSelect('');
  };

  // Debug state tracking removed for production

  return (
    <div className="space-y-2">
      <Label className="text-base font-bold">{label}</Label>
      <div className="flex flex-col gap-2">
        {currentImage ? (
          <div className="flex items-center gap-2">
            <img
              src={currentImage}
              alt="Preview"
              className="w-16 h-16 object-cover rounded-lg border-2 border-black/20"
              onError={(e) => console.error('❌ [ImageUpload] Erro ao carregar imagem:', {
                src: currentImage,
                error: e
              })}
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={removeImage}
              className="text-red-500 hover:text-red-700 hover:bg-red-50"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        ) : (
          <Button
            type="button"
            variant="outline"
            className="border-2 border-black/20 hover:border-black/50 transition-colors"
            disabled={isUploading}
            onClick={() => {
              document.getElementById(id)?.click();
            }}
          >
            <FileImage className="mr-2 h-5 w-5" />
            {isUploading ? 'Enviando...' : 'Escolher imagem'}
          </Button>
        )}
        <input
          id={id}
          type="file"
          accept="image/*,image/gif,.gif"
          onChange={handleImageSelect}
          className="hidden"
        />
      </div>
    </div>
  );
};
