
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { FlashcardForm } from "@/components/collaborate/flashcards/FlashcardForm";
import { FlashcardList } from "@/components/collaborate/flashcards/FlashcardList";
import { FlashcardImport } from "@/components/collaborate/flashcards/FlashcardImport";
import { TopicForm } from "@/components/collaborate/flashcards/TopicForm";
import { TopicHierarchyTree } from "@/components/collaborate/flashcards/TopicHierarchyTree";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import type { FlashcardWithHierarchy } from "@/types/flashcard";
import { toast } from "sonner";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { Brain, Pencil } from "lucide-react";
import { Button } from "@/components/ui/button";
import FlashcardAIWizard from "@/components/collaborate/flashcards/FlashcardAIWizard";
import { useStaticFlashcardHierarchy } from "@/hooks/useStaticDataCache";

const CollaborateFlashcards = () => {
  const [flashcards, setFlashcards] = useState<FlashcardWithHierarchy[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [createMode, setCreateMode] = useState<"manual" | "ia" | null>(null);

  // Hook para carregar dados da hierarquia
  const { data: hierarchyData } = useStaticFlashcardHierarchy();

  // Função para resolver nomes da hierarquia
  const resolveHierarchyNames = (cards: any[]) => {
    if (!cards || cards.length === 0) return cards;

    if (!hierarchyData) {
      // Se não temos dados da hierarquia, retorna com nomes padrão
      return cards.map(card => ({
        ...card,
        current_state: card.current_state as FlashcardWithHierarchy['current_state'],
        hierarchy: {
          specialty: card.specialty_id ? { id: card.specialty_id, name: 'Especialidade Desconhecida' } : undefined,
          theme: card.theme_id ? { id: card.theme_id, name: 'Tema Desconhecido' } : undefined,
          focus: card.focus_id ? { id: card.focus_id, name: 'Foco Desconhecido' } : undefined,
          extraFocus: card.extrafocus_id ? { id: card.extrafocus_id, name: 'Extra Foco Desconhecido' } : undefined
        }
      }));
    }

    return cards.map(card => ({
      ...card,
      current_state: card.current_state as FlashcardWithHierarchy['current_state'],
      hierarchy: {
        specialty: card.specialty_id ? {
          id: card.specialty_id,
          name: hierarchyData.specialties.find(s => s.id === card.specialty_id)?.name || 'Especialidade não encontrada'
        } : undefined,
        theme: card.theme_id ? {
          id: card.theme_id,
          name: hierarchyData.themes.find(t => t.id === card.theme_id)?.name || 'Tema não encontrado'
        } : undefined,
        focus: card.focus_id ? {
          id: card.focus_id,
          name: hierarchyData.focuses.find(f => f.id === card.focus_id)?.name || 'Foco não encontrado'
        } : undefined,
        extraFocus: card.extrafocus_id ? {
          id: card.extrafocus_id,
          name: hierarchyData.extrafocuses.find(e => e.id === card.extrafocus_id)?.name || 'Extra foco não encontrado'
        } : undefined
      }
    }));
  };

  useEffect(() => {
    checkAdminStatus();
    loadFlashcards();
    // eslint-disable-next-line
  }, [isAdmin]);

  // Recarregar flashcards quando os dados da hierarquia estiverem disponíveis
  useEffect(() => {
    if (hierarchyData && flashcards.length > 0) {
      // Verificar se algum flashcard ainda tem nomes desconhecidos nos nomes
      const hasLoadingNames = flashcards.some(card =>
        card.hierarchy?.specialty?.name?.includes('Desconhecida') ||
        card.hierarchy?.theme?.name?.includes('Desconhecido') ||
        card.hierarchy?.focus?.name?.includes('Desconhecido') ||
        card.hierarchy?.extraFocus?.name?.includes('Desconhecido')
      );

      if (hasLoadingNames) {
        loadFlashcards();
      }
    }
  }, [hierarchyData]);

  const checkAdminStatus = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      const { data: profile } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();

      setIsAdmin(profile?.is_admin || false);
    }
  };

  const loadFlashcards = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.warn("⚠️ [CollaborateFlashcards] Usuário não autenticado");
        return;
      }

      // ✅ Otimizado: Buscar apenas dados dos flashcards sem joins
      let query = supabase
        .from('flashcards_cards')
        .select('*');

      if (isAdmin) {
        query = query.or(`user_id.eq.${user.id},current_state.eq.pending`);
      } else {
        query = query.eq('user_id', user.id);
      }

      query = query.order('created_at', { ascending: false });

      const { data: cards, error } = await query;

      if (error) {
        console.error("❌ [CollaborateFlashcards] Erro ao carregar flashcards:", error);
        toast.error("Erro ao carregar flashcards");
        return;
      }

      if (cards) {
        // ✅ Resolver nomes da hierarquia usando dados estáticos
        const formattedCards = resolveHierarchyNames(cards);
        setFlashcards(formattedCards);
      }
    } catch (error) {
      console.error("❌ [CollaborateFlashcards] Erro inesperado:", error);
      toast.error("Erro ao carregar flashcards");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />

      {/* Espaçamento para StudyNavBar flutuante */}
      <div className="h-16 sm:h-20"></div>

      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in relative z-10">
        {/* Header Section */}
        <div className="relative">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Gerenciar Flashcards
          </h1>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="create" className="space-y-6">
          {/* ✅ Navegação Principal - Design System Melhorado */}
          <div className="flex justify-center">
            <TabsList className="grid w-full max-w-lg grid-cols-2 bg-white border-2 border-black rounded-xl p-1 shadow-button h-12">
              <TabsTrigger
                value="create"
                className="flex items-center justify-center w-full h-full rounded-lg font-bold text-sm data-[state=active]:bg-hackathon-green data-[state=active]:text-black data-[state=active]:shadow-button data-[state=active]:border data-[state=active]:border-black transition-all hover:bg-gray-100 data-[state=active]:hover:bg-hackathon-green/90 data-[state=active]:hover:translate-y-0.5 data-[state=active]:hover:shadow-sm"
              >
                <Brain className="h-4 w-4 mr-2 flex-shrink-0" />
                <span className="truncate">Criar Flashcards</span>
              </TabsTrigger>
              <TabsTrigger
                value="my-cards"
                className="flex items-center justify-center w-full h-full rounded-lg font-bold text-sm data-[state=active]:bg-hackathon-yellow data-[state=active]:text-black data-[state=active]:shadow-button data-[state=active]:border data-[state=active]:border-black transition-all hover:bg-gray-100 data-[state=active]:hover:bg-hackathon-yellow/90 data-[state=active]:hover:translate-y-0.5 data-[state=active]:hover:shadow-sm"
              >
                <Pencil className="h-4 w-4 mr-2 flex-shrink-0" />
                <span className="truncate">Meus Flashcards</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Botões Admin - Discretos no Canto */}
          {isAdmin && (
            <div className="flex justify-end gap-2">
              <TabsList className="bg-gray-50 border border-gray-300 rounded-lg p-1">
                <TabsTrigger value="import" className="text-xs px-2 py-1 rounded data-[state=active]:bg-gray-200">
                  Importar
                </TabsTrigger>
                <TabsTrigger value="topics" className="text-xs px-2 py-1 rounded data-[state=active]:bg-gray-200">
                  Tópicos
                </TabsTrigger>
              </TabsList>
            </div>
          )}

          {/* ## CRIAÇÃO FLUXO MANUAL/IA */}
          <TabsContent value="create">
            <Card className="bg-white border-2 border-black rounded-xl shadow-button p-6 transition-transform hover:translate-y-0.5 hover:shadow-sm">
              {!createMode && (
                <div className="flex flex-col gap-6 items-center justify-center min-h-[240px]">
                  <div className="font-bold text-xl text-center mb-2">
                    Como deseja criar seu flashcard?
                  </div>
                  <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md justify-center">
                    <Button
                      className="flex-1 flex gap-2 items-center justify-center bg-hackathon-yellow hover:bg-yellow-400 text-black font-semibold px-6 py-4 rounded-xl text-lg border-2 border-black/20 shadow-sm transition-all"
                      onClick={() => setCreateMode("manual")}
                    >
                      <Pencil className="w-5 h-5" /> Criar manualmente
                    </Button>
                    <Button
                      className="flex-1 flex gap-2 items-center justify-center bg-hackathon-yellow hover:bg-yellow-400 text-black font-semibold px-6 py-4 rounded-xl text-lg border-2 border-black/20 shadow-sm transition-all"
                      onClick={() => {
                        setCreateMode("ia");
                      }}
                    >
                      <Brain className="w-5 h-5" />
                      Criar com IA
                    </Button>
                  </div>
                  <div className="text-sm text-gray-500 text-center max-w-xs mt-3">
                    Escolha se prefere criar do zero ou aguarde a nossa ferramenta de inteligência artificial para gerar flashcards automaticamente!
                  </div>
                </div>
              )}

              {createMode === "manual" && (
                <div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mb-3 text-gray-500"
                    onClick={() => setCreateMode(null)}
                  >
                    ← Voltar para seleção de modo
                  </Button>
                  <FlashcardForm
                    selectedSpecialty=""
                    selectedTheme=""
                    selectedFocus=""
                    onCreateSuccess={loadFlashcards}
                  />
                </div>
              )}

              {createMode === "ia" && (
                <div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mb-3 text-gray-500"
                    onClick={() => setCreateMode(null)}
                  >
                    ← Voltar para seleção de modo
                  </Button>
                  <FlashcardAIWizard />
                </div>
              )}
            </Card>
          </TabsContent>

          <TabsContent value="my-cards">
            <Card className="bg-white border-2 border-black rounded-xl shadow-button p-6">
              <FlashcardList flashcards={flashcards} isAdmin={isAdmin} />
            </Card>
          </TabsContent>

          {isAdmin && (
            <>
              <TabsContent value="import">
                <Card className="p-6">
                  <FlashcardImport />
                </Card>
              </TabsContent>

              <TabsContent value="topics">
                <Card className="p-6">
                  <TopicForm topics={[]} onCreateTopic={() => {}} />
                  <TopicHierarchyTree topics={[]} />
                </Card>
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export default CollaborateFlashcards;
