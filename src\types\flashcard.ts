export interface Topic {
  id: string;
  name: string;
  parent_id?: string;
}

export interface Specialty {
  id: string;
  name: string;
  user_id: string;
}

export interface Theme {
  id: string;
  name: string;
  specialty_id: string;
  user_id: string;
}

export interface Focus {
  id: string;
  name: string;
  theme_id: string;
  user_id: string;
}

export interface Extrafocus {
  id: string;
  name: string;
  focus_id: string;
  user_id: string;
}

export interface SelectedFilters {
  specialties: string[];
  themes: string[];
  focuses: string[];
  extrafocuses: string[];
  locations: string[];
  years: string[];
}

export interface CategoryNode {
  id: string;
  name: string;
  cardsCount?: number;
  nextReview?: Date | null;
  children?: CategoryNode[];
}

export type FlashcardState = 'available' | 'reviewing' | 'archived' | 'pending' | 'rejected';
export type FlashcardResponse = 'again' | 'hard' | 'medium' | 'easy' | 'error';

export interface Flashcard {
  id: string;
  front: string;
  back: string;
  front_image?: string | null;
  back_image?: string | null;
  specialty_id: string;
  theme_id?: string;
  focus_id?: string;
  extrafocus_id?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  current_state: FlashcardState;
  rejection_reason?: string | null;
  stability?: number;
  difficulty?: number;
  retrievability?: number;
  recall_probability?: number;
  intervalindays?: string | number;
  next_review_date?: string | null;
  last_review_date?: string | null;
  total_reviews?: number;
  correct_reviews?: number;
  flashcard_type?: string;
}

export interface FlashcardWithHierarchy extends Flashcard {
  hierarchy: {
    specialty: { id: string; name: string };
    theme?: { id: string; name: string };
    focus?: { id: string; name: string };
    extraFocus?: { id: string; name: string };
  };
  likes?: number;
  dislikes?: number;
  liked_by?: string[];
  disliked_by?: string[];
  next_review_date?: string;
}

export interface FlashcardSession {
  id: string;
  user_id: string;
  start_time: string;
  end_time?: string;
  total_cards: number;
  correct_cards: number;
  status: 'in_progress' | 'completed' | 'cancelled';
  created_at: string;
  cards: string[];
  filters?: Record<string, any>;
  updated_at: string;
}

export interface FlashcardSessionCard {
  id: string;
  session_id: string;
  card_id: string;
  response?: FlashcardResponse;
  review_status: 'pending' | 'reviewed';
  last_review_time?: string;
  response_data?: Record<string, any>;
  created_at: string;
}

export interface FlashcardReview {
  id: string;
  user_id: string;
  card_id: string;
  stability: number;
  difficulty: number;
  retrievability: number;
  intervalindays: number;
  total_reviews: number;
  correct_reviews: number;
  last_review_date: string | null;
  next_review_date: string | null;
  created_at: string;
  updated_at: string;
}

export interface PreCalculatedMetrics {
  stability: number;
  difficulty: number;
  retrievability: number;
  nextReviewDate: Date;
  intervalInDays: number;
}

export interface ResponseMetrics {
  error: PreCalculatedMetrics;
  hard: PreCalculatedMetrics;
  medium: PreCalculatedMetrics;
  easy: PreCalculatedMetrics;
}
