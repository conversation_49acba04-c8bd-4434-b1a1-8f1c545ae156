import { useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useStudySession } from '@/hooks/useStudySession';
import { useFilterQuery } from './useFilterQuery';
import { useUserData } from '@/hooks/useUserData';
import { useDomain } from '@/hooks/useDomain';
import { supabase } from '@/integrations/supabase/client';
import type { SelectedFilters } from '@/types/question';
import type { StudySessionRow } from '@/types/study-session';

export const useSessionCreation = () => {
  const { toast } = useToast();
  const { createSession } = useStudySession();
  const { buildQuery } = useFilterQuery();
  const { user } = useUserData();
  const { domain } = useDomain();

  const handleCreateSession = useCallback(async (
    filters: SelectedFilters,
    title: string,
    questionIds?: string[]
  ): Promise<StudySessionRow | null> => {

    try {
      // If questionIds are provided, use them directly
      if (questionIds && questionIds.length > 0) {
        const userId = await ensureUserId();
        const session = await createSession(userId, questionIds, title);

        if (!session?.id) {
          toast({
            title: "Erro ao criar sessão",
            description: "Não foi possível criar a sessão de estudos",
            variant: "destructive"
          });
          return null;
        }

        return session;
      }

      // Otherwise, fetch questions based on filters

      // Use RPC functions to get questions with proper filtering
      let questions, error;

      if (filters.excludeAnswered && user?.id) {
        console.log('🔍 [useSessionCreation] Using excluding_answered function for session creation');

        // Use function that excludes answered questions
        const rpcParams = {
          specialty_ids: filters.specialties || [],
          theme_ids: filters.themes || [],
          focus_ids: filters.focuses || [],
          location_ids: filters.locations || [],
          years: (filters.years || []).map(Number),
          question_types: filters.question_types || [],
          question_formats: filters.question_formats || [],
          page_number: 1,
          items_per_page: 1000, // Get all questions for session
          domain_filter: domain,
          user_id: user.id
        };

        // 🔒 SEGURANÇA: Usar função segura que exclui questões respondidas
        const { data: rpcData, error: rpcError } = await supabase.rpc('get_filtered_questions_excluding_answered_secure', rpcParams);

        if (rpcError) {
          error = rpcError;
        } else {
          questions = rpcData?.questions || [];
        }
      } else {
        // Use regular function
        const rpcParams = {
          specialty_ids: filters.specialties || [],
          theme_ids: filters.themes || [],
          focus_ids: filters.focuses || [],
          location_ids: filters.locations || [],
          years: (filters.years || []).map(Number),
          question_types: filters.question_types || [],
          question_formats: filters.question_formats || [],
          page_number: 1,
          items_per_page: 1000, // Get all questions for session
          domain_filter: domain
        };

        // 🔒 SEGURANÇA: Usar função segura que não expõe respostas corretas
        const { data: rpcData, error: rpcError } = await supabase.rpc('get_filtered_questions_secure', rpcParams);

        if (rpcError) {
          error = rpcError;
        } else {
          questions = rpcData?.questions || [];
        }
      }

      if (error) {
        throw error;
      }

      console.log(`🔍 [useSessionCreation] Found ${questions?.length || 0} questions for session creation`);

      if (!questions?.length) {
        toast({
          title: "Nenhuma questão encontrada",
          description: "Tente outros filtros",
          variant: "destructive"
        });
        return null;
      }

      if (!user?.id) {
        toast({
          title: "Erro de autenticação",
          description: "Usuário não autenticado",
          variant: "destructive"
        });
        return null;
      }

      const session = await createSession(user.id, questions.map(q => q.id), title);

      if (!session?.id) {
        toast({
          title: "Erro ao criar sessão",
          description: "Não foi possível criar a sessão de estudos",
          variant: "destructive"
        });
        return null;
      }

      return session;

    } catch (error: any) {
      toast({
        title: "Erro ao criar sessão",
        description: error.message,
        variant: "destructive"
      });
      return null;
    }
  }, [toast, createSession, user?.id, domain]);

  return { handleCreateSession };
};
