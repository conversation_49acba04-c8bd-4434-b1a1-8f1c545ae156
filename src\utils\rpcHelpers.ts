/**
 * 🚀 SOLUÇÃO DEFINITIVA: Helpers RPC para resolver URLs longas
 * 
 * Este arquivo contém funções utilitárias que usam RPCs do banco
 * para evitar URLs muito longas com muitos IDs.
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * ✅ CHUNKING: Divide array em chunks menores
 */
const chunkArray = <T>(array: T[], chunkSize: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

/**
 * 🚀 FUNÇÃO UNIVERSAL: Buscar dados por IDs usando RPC
 */
export const fetchByIds = async (
  tableName: string,
  ids: string[],
  selectFields: string = '*',
  idColumn: string = 'id'
): Promise<{ data: any[] | null; error: any }> => {
  try {
    if (!ids || ids.length === 0) {
      return { data: [], error: null };
    }

    // ✅ ESTRATÉGIA HÍBRIDA: Escolher melhor método baseado na quantidade
    if (ids.length <= 30) {
      // Query normal para poucos IDs
      const { data, error } = await supabase
        .from(tableName)
        .select(selectFields)
        .in(idColumn, ids);
      
      return { data, error };
    } 
    else if (ids.length <= 1000) {
      // RPC para muitos IDs
      const { data, error } = await supabase.rpc('fetch_by_ids', {
        table_name: tableName,
        ids: ids,
        select_fields: selectFields,
        id_column: idColumn
      });

      if (error) {
        console.warn(`⚠️ [fetchByIds] RPC falhou, usando chunking:`, error);
        return await fetchByIdsWithChunking(tableName, ids, selectFields, idColumn);
      }

      return { data: data || [], error: null };
    } 
    else {
      // Chunking para quantidades extremas
      return await fetchByIdsWithChunking(tableName, ids, selectFields, idColumn);
    }
  } catch (error) {
    console.error(`❌ [fetchByIds] Erro:`, error);
    return { data: null, error };
  }
};

/**
 * 🔄 FALLBACK: Chunking quando RPC falha ou há muitos IDs
 */
const fetchByIdsWithChunking = async (
  tableName: string,
  ids: string[],
  selectFields: string,
  idColumn: string
): Promise<{ data: any[] | null; error: any }> => {
  try {
    const chunks = chunkArray(ids, 30);
    const allData: any[] = [];

    for (const chunk of chunks) {
      const { data, error } = await supabase
        .from(tableName)
        .select(selectFields)
        .in(idColumn, chunk);

      if (error) {
        return { data: null, error };
      }

      if (data) {
        allData.push(...data);
      }
    }

    return { data: allData, error: null };
  } catch (error) {
    return { data: null, error };
  }
};

/**
 * 📚 FUNÇÃO ESPECÍFICA: Buscar categorias de estudo
 */
export const fetchCategoriesByIds = async (
  ids: string[],
  selectFields: string = 'id, name, type, parent_id'
): Promise<{ data: any[] | null; error: any }> => {
  try {
    if (!ids || ids.length === 0) {
      return { data: [], error: null };
    }

    // ✅ ESTRATÉGIA OTIMIZADA para categorias
    if (ids.length <= 30) {
      // Query normal
      const { data, error } = await supabase
        .from('study_categories')
        .select(selectFields)
        .in('id', ids);
      
      return { data, error };
    } 
    else if (ids.length <= 1000) {
      // RPC otimizada para categorias
      const { data, error } = await supabase.rpc('fetch_categories_by_ids', {
        ids: ids,
        select_fields: selectFields
      });

      if (error) {
        console.warn(`⚠️ [fetchCategoriesByIds] RPC falhou, usando chunking:`, error);
        return await fetchByIdsWithChunking('study_categories', ids, selectFields, 'id');
      }

      return { data: data || [], error: null };
    } 
    else {
      // Chunking para quantidades extremas
      return await fetchByIdsWithChunking('study_categories', ids, selectFields, 'id');
    }
  } catch (error) {
    console.error(`❌ [fetchCategoriesByIds] Erro:`, error);
    return { data: null, error };
  }
};

/**
 * 🏥 FUNÇÃO ESPECÍFICA: Buscar instituições
 */
export const fetchInstitutionsByIds = async (
  ids: string[]
): Promise<{ data: any[] | null; error: any }> => {
  return await fetchByIds('exam_locations', ids, 'id, name');
};

/**
 * ❓ FUNÇÃO ESPECÍFICA: Buscar questões
 */
export const fetchQuestionsByIds = async (
  ids: string[],
  selectFields: string = '*'
): Promise<{ data: any[] | null; error: any }> => {
  return await fetchByIds('questions', ids, selectFields);
};

/**
 * 📋 FUNÇÃO ESPECÍFICA: Buscar questões por instituições
 */
export const fetchQuestionsByInstitutions = async (
  institutionIds: string[],
  filters: {
    startYear?: number;
    endYear?: number;
    domain?: string;
  } = {},
  selectFields: string = 'id, specialty_id, theme_id, focus_id, exam_year, exam_location'
): Promise<{ data: any[] | null; error: any }> => {
  try {
    if (!institutionIds || institutionIds.length === 0) {
      return { data: [], error: null };
    }

    // ✅ ESTRATÉGIA: Sempre usar chunking para questões por instituições
    // porque pode haver muitas questões por instituição
    const chunks = chunkArray(institutionIds, 30);
    const allQuestions: any[] = [];

    for (const chunk of chunks) {
      let query = supabase
        .from('questions')
        .select(selectFields)
        .in('exam_location', chunk);

      // Aplicar filtros
      if (filters.startYear) {
        query = query.gte('exam_year', filters.startYear);
      }
      if (filters.endYear) {
        query = query.lte('exam_year', filters.endYear);
      }
      if (filters.domain) {
        query = query.eq('knowledge_domain', filters.domain);
      }

      const { data, error } = await query;

      if (error) {
        return { data: null, error };
      }

      if (data) {
        allQuestions.push(...data);
      }
    }

    return { data: allQuestions, error: null };
  } catch (error) {
    console.error(`❌ [fetchQuestionsByInstitutions] Erro:`, error);
    return { data: null, error };
  }
};

/**
 * 🎯 FUNÇÃO HELPER: Log de performance
 */
export const logRpcPerformance = (
  functionName: string,
  idsCount: number,
  method: 'query' | 'rpc' | 'chunking',
  duration: number
) => {
  // Performance logging disabled in production
  if (import.meta.env.DEV) {
    console.log(`📊 [RPC Performance] ${functionName}: ${idsCount} IDs via ${method} em ${duration}ms`);
  }
};
