
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Pencil } from "lucide-react";
import React from "react";
import { FlashcardAIWizardEditForm } from "./FlashcardAIWizardEditForm";
import { FlashcardAIWizardImportButton } from "./FlashcardAIWizardImportButton";

interface FlashcardAIWizardCardListProps {
  suggested: { front: string; back: string }[];
  editingIndex: number | null;
  editingFront: string;
  editingBack: string;
  importedIndexes: number[];
  importingIndex: number | null;
  importingAll: boolean;
  onEdit: (i: number) => void;
  onEditFront: (t: string) => void;
  onEditBack: (t: string) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onImport: (i: number) => void;
  onImportAll: () => void;
}

export function FlashcardAIWizardCardList({
  suggested,
  editingIndex,
  editingFront,
  editingBack,
  importedIndexes,
  importingIndex,
  importingAll,
  onEdit,
  onEditFront,
  onEditBack,
  onSaveEdit,
  onCancelEdit,
  onImport,
  onImportAll
}: FlashcardAIWizardCardListProps) {
  return (
    <div>
      <h3 className="font-semibold text-lg mb-2">Sugestão de Flashcards IA</h3>
      <div className="mb-4">
        <Button
          onClick={onImportAll}
          disabled={importingAll || importedIndexes.length === suggested.length}
          className={
            "font-bold px-6 py-2 rounded " +
            (importedIndexes.length === suggested.length
              ? "bg-green-600 text-white border-green-700 pointer-events-none"
              : "bg-blue-600 text-white hover:bg-blue-700 border-blue-700")
          }
        >
          {importedIndexes.length === suggested.length
            ? "Todos importados"
            : importingAll
            ? "Importando todos..."
            : "Importar todos"}
        </Button>
      </div>
      <div className="grid gap-4">
        {suggested.map((fc, i) => (
          <Card key={i} className="p-4 bg-yellow-50 border-yellow-300 rounded-xl relative">
            {editingIndex === i ? (
              <FlashcardAIWizardEditForm
                editingFront={editingFront}
                editingBack={editingBack}
                setEditingFront={onEditFront}
                setEditingBack={onEditBack}
                onSave={onSaveEdit}
                onCancel={onCancelEdit}
              />
            ) : (
              <>
                {/* Header com botão de editar */}
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1 pr-2">
                    <div className="text-xs text-gray-500 mb-1">Flashcard #{i + 1}</div>
                  </div>
                  <button
                    className="flex-shrink-0 p-2 rounded-full hover:bg-yellow-100 transition"
                    onClick={() => onEdit(i)}
                    aria-label={`Editar flashcard ${i + 1}`}
                  >
                    <Pencil className="w-4 h-4 text-gray-500" />
                  </button>
                </div>

                {/* Conteúdo do flashcard */}
                <div className="space-y-3">
                  <div className="whitespace-pre-line font-bold text-gray-900">
                    <span className="text-sm text-gray-600 font-normal">Q: </span>
                    {fc.front}
                  </div>
                  <div className="whitespace-pre-line text-gray-800">
                    <span className="text-sm text-gray-600 font-semibold">A: </span>
                    {fc.back}
                  </div>
                </div>

                {/* Botão de importar */}
                <div className="mt-4">
                  <FlashcardAIWizardImportButton
                    imported={importedIndexes.includes(i)}
                    importing={importingIndex === i}
                    onClick={() => onImport(i)}
                  />
                </div>
              </>
            )}
          </Card>
        ))}
      </div>
    </div>
  );
}
