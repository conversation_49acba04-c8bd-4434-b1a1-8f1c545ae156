import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home, Wifi, WifiOff } from 'lucide-react';
import { motion } from 'framer-motion';

interface LoadingErrorRecoveryProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  onGoHome?: () => void;
  onContinueOffline?: () => void;
  showOfflineOption?: boolean;
  retryCount?: number;
  maxRetries?: number;
}

export const LoadingErrorRecovery: React.FC<LoadingErrorRecoveryProps> = ({
  title = "Problema de Conexão",
  message = "Não foi possível carregar os dados. Verifique sua conexão com a internet.",
  onRetry,
  onGoHome,
  onContinueOffline,
  showOfflineOption = false,
  retryCount = 0,
  maxRetries = 3
}) => {
  const isOnline = navigator.onLine;
  const hasRetriesLeft = retryCount < maxRetries;

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#FEF7CD] p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="p-8 text-center border-2 border-orange-200 shadow-lg">
          {/* Ícone de Status */}
          <motion.div
            initial={{ rotate: 0 }}
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="mb-6 flex justify-center"
          >
            <div className="bg-orange-100 rounded-full p-4">
              {isOnline ? (
                <AlertTriangle className="h-8 w-8 text-orange-600" />
              ) : (
                <WifiOff className="h-8 w-8 text-red-600" />
              )}
            </div>
          </motion.div>

          {/* Título e Mensagem */}
          <h2 className="text-xl font-bold text-gray-900 mb-3">
            {title}
          </h2>
          <p className="text-gray-600 mb-6 leading-relaxed">
            {message}
          </p>

          {/* Status da Conexão */}
          <div className="mb-6 p-3 rounded-lg bg-gray-50 border">
            <div className="flex items-center justify-center gap-2 text-sm">
              {isOnline ? (
                <>
                  <Wifi className="h-4 w-4 text-green-600" />
                  <span className="text-green-600 font-medium">Conectado à Internet</span>
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 text-red-600" />
                  <span className="text-red-600 font-medium">Sem Conexão</span>
                </>
              )}
            </div>
          </div>

          {/* Contador de Tentativas */}
          {retryCount > 0 && (
            <div className="mb-4 text-sm text-gray-500">
              Tentativa {retryCount} de {maxRetries}
            </div>
          )}

          {/* Botões de Ação */}
          <div className="space-y-3">
            {/* Botão Tentar Novamente */}
            {hasRetriesLeft && onRetry && (
              <Button
                onClick={onRetry}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white"
                disabled={!isOnline}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Tentar Novamente
              </Button>
            )}

            {/* Botão Ir para Home */}
            {onGoHome && (
              <Button
                onClick={onGoHome}
                variant="outline"
                className="w-full border-gray-300 hover:bg-gray-50"
              >
                <Home className="h-4 w-4 mr-2" />
                Ir para Página Inicial
              </Button>
            )}

            {/* Botão Continuar Offline */}
            {showOfflineOption && onContinueOffline && (
              <Button
                onClick={onContinueOffline}
                variant="secondary"
                className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700"
              >
                Continuar com Funcionalidades Limitadas
              </Button>
            )}
          </div>

          {/* Dicas */}
          <div className="mt-6 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-xs text-blue-700 font-medium mb-2">💡 Dicas:</p>
            <ul className="text-xs text-blue-600 space-y-1 text-left">
              <li>• Verifique sua conexão Wi-Fi</li>
              <li>• Tente recarregar a página (F5)</li>
              <li>• Aguarde alguns segundos e tente novamente</li>
            </ul>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default LoadingErrorRecovery;
