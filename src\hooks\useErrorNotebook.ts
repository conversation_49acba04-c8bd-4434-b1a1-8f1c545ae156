import { useState, useEffect } from 'react';
import { useUser } from '@supabase/auth-helpers-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface ErrorQuestion {
  id: string;
  question_id: string;
  user_id: string;
  selected_answer: number;
  is_correct: boolean;
  specialty_id: string;
  theme_id: string;
  focus_id: string;
  created_at: string;
  time_spent: number;
  ai_commentary: any;
  specialty_name: string;
  theme_name: string;
  focus_name: string;
  question_content: string;
  response_choices: string[];
  correct_choice: string;
  exam_year: number;
  media_attachments?: string[];
  question_format?: string;
  // Campos de controle de revisão
  revisado?: boolean;
  anotacao?: string;
  tentativas?: number;
  ultima_revisao?: string;
  modo_ultima_revisao?: 'visualizacao' | 'refazer';
  acertou_na_revisao?: boolean;
  erros_meta_id?: string;
  resposta_discursiva?: string;
  analise_ia_discursiva?: any;
}

export interface ErrorStats {
  totalErrors: number;
  reviewedErrors: number;
  pendingErrors: number;
  topErrorThemes: Array<{
    theme_name: string;
    theme_id: string;
    error_count: number;
  }>;
  topErrorSpecialties: Array<{
    specialty_name: string;
    specialty_id: string;
    error_count: number;
  }>;
  topPendingTheme?: {
    theme_name: string;
    theme_id: string;
    pending_count: number;
  };
}

export const useErrorNotebook = (autoLoad: boolean = true) => {
  const user = useUser();
  const { toast } = useToast();
  const [errorStats, setErrorStats] = useState<ErrorStats>({
    totalErrors: 0,
    reviewedErrors: 0,
    pendingErrors: 0,
    topErrorThemes: [],
    topErrorSpecialties: [],
    topPendingTheme: undefined
  });
  const [isLoading, setIsLoading] = useState(autoLoad);
  const [errorQuestions, setErrorQuestions] = useState<ErrorQuestion[]>([]);

  // 🚀 CACHE: Evitar re-consultas desnecessárias
  const [lastStatsUpdate, setLastStatsUpdate] = useState<number>(0);
  const [questionsCache, setQuestionsCache] = useState<Map<string, ErrorQuestion[]>>(new Map());

  // Buscar estatísticas de erros do usuário
  const fetchErrorStats = async (forceRefresh: boolean = false) => {
    if (!user?.id) return;

    // 🚀 CACHE: Evitar re-consultas se dados são recentes (menos de 30 segundos)
    const now = Date.now();
    if (!forceRefresh && (now - lastStatsUpdate) < 30000) {
      return;
    }

    try {
      setIsLoading(true);

      // 🚀 OTIMIZAÇÃO: Paralelizar consultas independentes
      const [
        totalErrorsResult,
        reviewedErrorsResult,
        topThemesResult,
        topSpecialtiesResult
      ] = await Promise.all([
        // Buscar total de questões erradas
        supabase
          .from('user_answers')
          .select('id')
          .eq('user_id', user.id)
          .eq('is_correct', false),

        // Buscar questões revisadas
        supabase
          .from('erros_meta')
          .select('id')
          .eq('user_id', user.id)
          .eq('revisado', true),

        // Buscar top temas com mais erros
        supabase
          .from('user_answers')
          .select(`
            theme_id,
            study_categories!user_answers_theme_id_fkey(name)
          `)
          .eq('user_id', user.id)
          .eq('is_correct', false)
          .not('theme_id', 'is', null),

        // Buscar top especialidades com mais erros
        supabase
          .from('user_answers')
          .select(`
            specialty_id,
            study_categories!user_answers_specialty_id_fkey(name)
          `)
          .eq('user_id', user.id)
          .eq('is_correct', false)
          .not('specialty_id', 'is', null)
      ]);

      // Verificar erros
      if (totalErrorsResult.error) throw totalErrorsResult.error;
      if (reviewedErrorsResult.error) throw reviewedErrorsResult.error;
      if (topThemesResult.error) throw topThemesResult.error;
      if (topSpecialtiesResult.error) throw topSpecialtiesResult.error;

      const totalErrorsData = totalErrorsResult.data;
      const reviewedErrorsData = reviewedErrorsResult.data;
      const topThemesData = topThemesResult.data;
      const topSpecialtiesData = topSpecialtiesResult.data;



      // Processar dados dos temas
      const themeErrorCounts = topThemesData?.reduce((acc: any, item: any) => {
        const themeId = item.theme_id;
        const themeName = item.study_categories?.name || 'Tema não identificado';
        
        if (!acc[themeId]) {
          acc[themeId] = {
            theme_id: themeId,
            theme_name: themeName,
            error_count: 0
          };
        }
        acc[themeId].error_count++;
        return acc;
      }, {});

      // Processar dados das especialidades
      const specialtyErrorCounts = topSpecialtiesData?.reduce((acc: any, item: any) => {
        const specialtyId = item.specialty_id;
        const specialtyName = item.study_categories?.name || 'Especialidade não identificada';
        
        if (!acc[specialtyId]) {
          acc[specialtyId] = {
            specialty_id: specialtyId,
            specialty_name: specialtyName,
            error_count: 0
          };
        }
        acc[specialtyId].error_count++;
        return acc;
      }, {});

      const topErrorThemes = Object.values(themeErrorCounts || {})
        .sort((a: any, b: any) => b.error_count - a.error_count)
        .slice(0, 5);

      const topErrorSpecialties = Object.values(specialtyErrorCounts || {})
        .sort((a: any, b: any) => b.error_count - a.error_count)
        .slice(0, 5);

      const totalErrors = totalErrorsData?.length || 0;
      const reviewedErrors = reviewedErrorsData?.length || 0;

      // 🎯 CALCULAR TEMA COM MAIS QUESTÕES PENDENTES
      let topPendingTheme = undefined;

      // Buscar questões pendentes por tema
      if (topThemesData && topThemesData.length > 0) {
        // Criar set de IDs de questões revisadas
        const reviewedQuestionIds = new Set(
          reviewedErrorsData?.map(meta => meta.user_answer_id) || []
        );

        // Contar questões pendentes por tema
        const pendingThemeCounts: Record<string, any> = {};

        topThemesData.forEach((answer: any) => {
          if (!reviewedQuestionIds.has(answer.id)) {
            const themeId = answer.theme_id;
            const themeName = answer.study_categories?.name;

            if (themeId && themeName) {
              if (!pendingThemeCounts[themeId]) {
                pendingThemeCounts[themeId] = {
                  theme_id: themeId,
                  theme_name: themeName,
                  pending_count: 0
                };
              }
              pendingThemeCounts[themeId].pending_count++;
            }
          }
        });

        // Encontrar tema com mais questões pendentes
        const pendingThemes = Object.values(pendingThemeCounts);
        if (pendingThemes.length > 0) {
          topPendingTheme = pendingThemes
            .sort((a: any, b: any) => b.pending_count - a.pending_count)[0];
        }
      }

      setErrorStats({
        totalErrors,
        reviewedErrors,
        pendingErrors: totalErrors - reviewedErrors,
        topErrorThemes: topErrorThemes as any,
        topErrorSpecialties: topErrorSpecialties as any,
        topPendingTheme: topPendingTheme as any
      });

      // 🚀 CACHE: Atualizar timestamp do último update
      setLastStatsUpdate(now);

    } catch (error) {
      console.error('Erro ao buscar estatísticas de erros:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as estatísticas de erros.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Buscar questões erradas com detalhes
  const fetchErrorQuestions = async (filters?: {
    specialty_id?: string;
    theme_id?: string;
    focus_id?: string;
    limit?: number;
    offset?: number;
  }) => {
    if (!user?.id) return [];

    // 🚀 CACHE: Verificar se já temos os dados em cache
    const cacheKey = JSON.stringify(filters || {});
    const cachedQuestions = questionsCache.get(cacheKey);
    if (cachedQuestions) {
      setErrorQuestions(cachedQuestions);
      return cachedQuestions;
    }

    try {
      // Primeira consulta: buscar user_answers básicos
      let query = supabase
        .from('user_answers')
        .select(`
          id,
          question_id,
          user_id,
          selected_answer,
          is_correct,
          specialty_id,
          theme_id,
          focus_id,
          created_at,
          time_spent,
          ai_commentary
        `)
        .eq('user_id', user.id)
        .eq('is_correct', false)
        .order('created_at', { ascending: false });

      if (filters?.specialty_id) {
        query = query.eq('specialty_id', filters.specialty_id);
      }
      if (filters?.theme_id) {
        query = query.eq('theme_id', filters.theme_id);
      }
      if (filters?.focus_id) {
        query = query.eq('focus_id', filters.focus_id);
      }
      if (filters?.limit) {
        query = query.limit(filters.limit);
      }
      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
      }

      const { data: userAnswers, error } = await query;
      if (error) throw error;

      if (!userAnswers || userAnswers.length === 0) {
        setErrorQuestions([]);
        return [];
      }

      // 🚀 OTIMIZAÇÃO: Paralelizar todas as consultas de dados relacionados
      const questionIds = userAnswers.map(ua => ua.question_id);
      const userAnswerIds = userAnswers.map(ua => ua.id);
      const specialtyIds = [...new Set(userAnswers.map(ua => ua.specialty_id).filter(Boolean))];
      const themeIds = [...new Set(userAnswers.map(ua => ua.theme_id).filter(Boolean))];
      const focusIds = [...new Set(userAnswers.map(ua => ua.focus_id).filter(Boolean))];

      const [
        questionsResult,
        specialtiesResult,
        themesResult,
        focusesResult,
        errorMetasResult
      ] = await Promise.all([
        // Buscar dados das questões
        supabase
          .from('questions')
          .select('id, question_content, response_choices, correct_choice, exam_year, media_attachments, question_format')
          .in('id', questionIds),

        // Buscar especialidades
        specialtyIds.length > 0
          ? supabase.from('study_categories').select('id, name').in('id', specialtyIds)
          : Promise.resolve({ data: [], error: null }),

        // Buscar temas
        themeIds.length > 0
          ? supabase.from('study_categories').select('id, name').in('id', themeIds)
          : Promise.resolve({ data: [], error: null }),

        // Buscar focos
        focusIds.length > 0
          ? supabase.from('study_categories').select('id, name').in('id', focusIds)
          : Promise.resolve({ data: [], error: null }),

        // Buscar metadados de erro
        supabase
          .from('erros_meta')
          .select('*')
          .in('user_answer_id', userAnswerIds)
      ]);

      // Verificar erros
      if (questionsResult.error) throw questionsResult.error;
      if (specialtiesResult.error) throw specialtiesResult.error;
      if (themesResult.error) throw themesResult.error;
      if (focusesResult.error) throw focusesResult.error;
      if (errorMetasResult.error) console.warn('Erro ao buscar metadados:', errorMetasResult.error);

      const questions = questionsResult.data;
      const specialtiesData = { data: specialtiesResult.data };
      const themesData = { data: themesResult.data };
      const focusesData = { data: focusesResult.data };
      const errorMetas = errorMetasResult.data;



      // Criar mapas para lookup rápido
      const questionsMap = new Map(questions?.map(q => [q.id, q]) || []);
      const specialtiesMap = new Map(specialtiesData.data?.map(s => [s.id, s]) || []);
      const themesMap = new Map(themesData.data?.map(t => [t.id, t]) || []);
      const focusesMap = new Map(focusesData.data?.map(f => [f.id, f]) || []);
      const errorMetasMap = new Map(errorMetas?.map(em => [em.user_answer_id, em]) || []);

      const formattedData = userAnswers.map((item: any) => {
        const question = questionsMap.get(item.question_id);
        const specialty = specialtiesMap.get(item.specialty_id);
        const theme = themesMap.get(item.theme_id);
        const focus = focusesMap.get(item.focus_id);
        const errorMeta = errorMetasMap.get(item.id);

        return {
          id: item.id,
          question_id: item.question_id,
          user_id: item.user_id,
          selected_answer: item.selected_answer,
          is_correct: item.is_correct,
          specialty_id: item.specialty_id,
          theme_id: item.theme_id,
          focus_id: item.focus_id,
          created_at: item.created_at,
          time_spent: item.time_spent,
          ai_commentary: item.ai_commentary,
          specialty_name: specialty?.name || 'Especialidade não identificada',
          theme_name: theme?.name || 'Tema não identificado',
          focus_name: focus?.name || 'Foco não identificado',
          question_content: question?.question_content || '',
          response_choices: question?.response_choices || [],
          correct_choice: question?.correct_choice || '0',
          exam_year: question?.exam_year || 0,
          media_attachments: question?.media_attachments || [],
          question_format: question?.question_format || null,
          // Dados de controle de revisão
          revisado: errorMeta?.revisado || false,
          anotacao: errorMeta?.anotacao || '',
          tentativas: errorMeta?.tentativas || 0,
          ultima_revisao: errorMeta?.ultima_revisao || null,
          modo_ultima_revisao: errorMeta?.modo_ultima_revisao || null,
          acertou_na_revisao: errorMeta?.acertou_na_revisao || null,
          erros_meta_id: errorMeta?.id || null,
          resposta_discursiva: errorMeta?.resposta_discursiva || null,
          analise_ia_discursiva: errorMeta?.analise_ia_discursiva || null
        };
      });

      setErrorQuestions(formattedData);

      // 🚀 CACHE: Salvar no cache
      setQuestionsCache(prev => new Map(prev.set(cacheKey, formattedData)));

      return formattedData;

    } catch (error) {
      console.error('Erro ao buscar questões erradas:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as questões erradas.",
        variant: "destructive",
      });
      return [];
    }
  };

  // Marcar questão como revisada
  const markAsReviewed = async (
    userAnswerId: string,
    questionId: string,
    mode: 'visualizacao' | 'refazer',
    annotation?: string,
    correctOnReview?: boolean,
    timeSpent?: number,
    discursiveAnswer?: string
  ) => {
    if (!user?.id) return false;

    try {
      // Verificar se já existe registro na erros_meta
      const { data: existingMetaArray, error: checkError } = await supabase
        .from('erros_meta')
        .select('id')
        .eq('user_id', user.id)
        .eq('question_id', questionId)
        .eq('user_answer_id', userAnswerId);

      const existingMeta = existingMetaArray?.[0];

      if (checkError && checkError.code !== 'PGRST116') throw checkError;

      if (existingMeta) {
        // Buscar o valor atual de tentativas
        const { data: currentMeta, error: fetchError } = await supabase
          .from('erros_meta')
          .select('tentativas')
          .eq('id', existingMeta.id)
          .single();

        if (fetchError) throw fetchError;

        // Atualizar registro existente com tentativas incrementadas
        const { error: updateError } = await supabase
          .from('erros_meta')
          .update({
            revisado: true,
            anotacao: annotation,
            ultima_revisao: new Date().toISOString(),
            modo_ultima_revisao: mode,
            acertou_na_revisao: correctOnReview,
            tempo_gasto_revisao: timeSpent,
            tentativas: (currentMeta?.tentativas || 0) + 1,
            resposta_discursiva: discursiveAnswer
          })
          .eq('id', existingMeta.id);

        if (updateError) throw updateError;
      } else {
        // Criar novo registro
        const { error: insertError } = await supabase
          .from('erros_meta')
          .insert({
            user_id: user.id,
            question_id: questionId,
            user_answer_id: userAnswerId,
            revisado: true,
            anotacao: annotation,
            ultima_revisao: new Date().toISOString(),
            modo_ultima_revisao: mode,
            acertou_na_revisao: correctOnReview,
            tempo_gasto_revisao: timeSpent,
            tentativas: 1,
            resposta_discursiva: discursiveAnswer
          });

        if (insertError) throw insertError;
      }

      // ✅ SOLUÇÃO: Atualizar apenas as estatísticas localmente sem re-fetch
      setErrorStats(prev => ({
        ...prev,
        reviewedErrors: prev.reviewedErrors + 1,
        pendingErrors: Math.max(0, prev.pendingErrors - 1)
      }));

      return true;

    } catch (error) {
      console.error('Erro ao marcar questão como revisada:', error);
      toast({
        title: "Erro",
        description: "Não foi possível marcar a questão como revisada.",
        variant: "destructive",
      });
      return false;
    }
  };

  // Salvar análise da IA para questão discursiva
  const saveDiscursiveAnalysis = async (
    userAnswerId: string,
    questionId: string,
    analysis: any
  ) => {
    if (!user?.id) return false;

    try {
      // Buscar o registro existente na erros_meta
      const { data: existingMetaArray, error: checkError } = await supabase
        .from('erros_meta')
        .select('id')
        .eq('user_id', user.id)
        .eq('question_id', questionId)
        .eq('user_answer_id', userAnswerId);

      const existingMeta = existingMetaArray?.[0];

      if (checkError && checkError.code !== 'PGRST116') throw checkError;

      if (existingMeta) {
        // Atualizar registro existente com a análise da IA
        const { error: updateError } = await supabase
          .from('erros_meta')
          .update({
            analise_ia_discursiva: analysis
          })
          .eq('id', existingMeta.id);

        if (updateError) throw updateError;
        return true;
      }

      return false;
    } catch (error) {
      console.error('Erro ao salvar análise da IA:', error);
      return false;
    }
  };

  // Adicionar/atualizar anotação
  const updateAnnotation = async (userAnswerId: string, questionId: string, annotation: string) => {
    if (!user?.id) return false;

    try {
      const { data: existingMetaArray, error: checkError } = await supabase
        .from('erros_meta')
        .select('id')
        .eq('user_id', user.id)
        .eq('question_id', questionId)
        .eq('user_answer_id', userAnswerId);

      const existingMeta = existingMetaArray?.[0];

      if (checkError && checkError.code !== 'PGRST116') throw checkError;

      if (existingMeta) {
        const { error: updateError } = await supabase
          .from('erros_meta')
          .update({ anotacao: annotation })
          .eq('id', existingMeta.id);

        if (updateError) throw updateError;
      } else {
        const { error: insertError } = await supabase
          .from('erros_meta')
          .insert({
            user_id: user.id,
            question_id: questionId,
            user_answer_id: userAnswerId,
            anotacao: annotation,
            tentativas: 0
          });

        if (insertError) throw insertError;
      }

      return true;
    } catch (error) {
      console.error('Erro ao atualizar anotação:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a anotação.",
        variant: "destructive",
      });
      return false;
    }
  };

  // Buscar histórico de revisões de uma questão
  const fetchReviewHistory = async (questionId: string, userAnswerId: string) => {
    if (!user?.id) return [];

    try {
      const { data, error } = await supabase
        .from('erros_meta')
        .select('*')
        .eq('user_id', user.id)
        .eq('question_id', questionId)
        .eq('user_answer_id', userAnswerId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Erro ao buscar histórico de revisões:', error);
      return [];
    }
  };

  useEffect(() => {
    if (user?.id && autoLoad) {
      fetchErrorStats();
    }
  }, [user?.id, autoLoad]);

  return {
    errorStats,
    errorQuestions,
    isLoading,
    fetchErrorStats,
    fetchErrorQuestions,
    fetchReviewHistory,
    markAsReviewed,
    updateAnnotation,
    saveDiscursiveAnalysis,
    refetch: fetchErrorStats
  };
};
