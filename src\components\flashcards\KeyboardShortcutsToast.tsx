import React from 'react';

export const KeyboardShortcutsToast = () => {
  const shortcuts = [
    { key: '<PERSON>spaç<PERSON>', action: 'V<PERSON>r cartão', icon: '🔄' },
    { key: '1', action: 'De novo', icon: '🔴', color: 'text-red-600' },
    { key: '2', action: '<PERSON><PERSON><PERSON>ci<PERSON>', icon: '🟠', color: 'text-orange-600' },
    { key: '3', action: '<PERSON>é<PERSON>', icon: '🟡', color: 'text-yellow-600' },
    { key: '4', action: 'Fácil', icon: '🟢', color: 'text-green-600' },
  ];

  return (
    <div className="space-y-4 min-w-[280px]">
      <div className="text-center">
        <h3 className="font-semibold text-gray-900 mb-2">Atalhos do Teclado</h3>
        <p className="text-sm text-gray-600">Use estas teclas para navegar rapidamente</p>
      </div>

      <div className="space-y-3">
        {shortcuts.map((shortcut, index) => (
          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center gap-3">
              <kbd className={`inline-flex items-center justify-center ${shortcut.key === 'Espaço' ? 'w-16 h-8' : 'w-8 h-8'} text-sm font-semibold text-gray-900 bg-white border border-gray-300 rounded-lg shadow-sm`}>
                {shortcut.key}
              </kbd>
              <span className="text-lg">{shortcut.icon}</span>
            </div>
            <span className={`font-medium ${shortcut.color || 'text-gray-700'}`}>
              {shortcut.action}
            </span>
          </div>
        ))}
      </div>

      <div className="text-center pt-2 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          💡 Dica: Use os números para avaliar rapidamente
        </p>
      </div>
    </div>
  );
};