import { useState, useEffect } from "react";
import { Avatar } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { ThumbsUp, ThumbsDown, Reply } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { useDarkMode } from "@/contexts/DarkModeContext";

interface UserProfile {
  full_name: string | null;
  avatar_url: string | null;
}

interface CommentProps {
  comment: any;
  onReply: (commentId: number | string, replyText: string) => void;
  onLike: (commentId: number | string, isLike: boolean) => void;
  onReplyLike?: (commentId: number | string, replyId: number | string, isLike: boolean) => void;
  userId?: string | null;
}

export const QuestionComment = ({ comment, onReply, onLike, onReplyLike, userId }: CommentProps) => {
  const { toast } = useToast();
  const { isDarkMode } = useDarkMode();
  const [isReplying, setIsReplying] = useState(false);
  const [replyText, setReplyText] = useState("");
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [replyProfiles, setReplyProfiles] = useState<Record<string, UserProfile>>({});

  useEffect(() => {
    const fetchUserProfile = async (profileId: string) => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('full_name, avatar_url')
          .eq('id', profileId)
          .single();

        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Error fetching user profile:', error);
        return null;
      }
    };

    const loadProfiles = async () => {
      if (comment.user) {
        const profile = await fetchUserProfile(comment.user);
        if (profile) setUserProfile(profile);
      }

      if (comment.replies?.length > 0) {
        const profiles: Record<string, UserProfile> = {};
        for (const reply of comment.replies) {
          if (reply.user) {
            const profile = await fetchUserProfile(reply.user);
            if (profile) profiles[reply.user] = profile;
          }
        }
        setReplyProfiles(profiles);
      }
    };

    loadProfiles();
  }, [comment.user, comment.replies]);

  const formatTimestamp = (timestamp: string) => {
    try {
      if (!timestamp) return 'Data inválida';
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        console.error('Invalid timestamp:', timestamp);
        return 'Data inválida';
      }
      return formatDistanceToNow(date, { addSuffix: true, locale: ptBR });
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Data inválida';
    }
  };

  const handleSubmitReply = async () => {
    if (!replyText.trim()) return;

    try {
      await onReply(comment.id, replyText);
      setReplyText("");
      setIsReplying(false);
      toast({
        title: "Resposta adicionada",
        description: "Sua resposta foi publicada com sucesso!"
      });
    } catch (error) {
      toast({
        title: "Erro ao adicionar resposta",
        description: "Não foi possível publicar sua resposta.",
        variant: "destructive"
      });
    }
  };

  const handleLike = async (isLike: boolean) => {
    if (!userId) {

      return;
    }

    try {
      await onLike(comment.id, isLike);

    } catch (error) {
      console.error('Error handling like:', error);

    }
  };

  const handleReplyLike = async (replyId: number | string, isLike: boolean) => {
    if (!onReplyLike || !userId) {

      return;
    }

    try {
      await onReplyLike(comment.id, replyId, isLike);

    } catch (error) {
      console.error('Error handling reply like:', error);

    }
  };

  const displayName = userProfile?.full_name || 'Anônimo';

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <Avatar className="h-10 w-10">
          <img
            src={userProfile?.avatar_url || '/default-avatar.png'}
            alt={displayName}
          />
        </Avatar>

        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className={`font-medium transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-900'
            }`}>{displayName}</span>
            <span className={`text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-500'
            }`}>
              {formatTimestamp(comment.timestamp)}
            </span>
          </div>

          <p className={`mb-2 transition-colors duration-200 ${
            isDarkMode ? 'text-gray-300' : 'text-gray-700'
          }`}>{comment.text}</p>

          <div className="flex items-center gap-4">
            <button
              onClick={() => handleLike(true)}
              className={`flex items-center gap-1 text-sm transition-colors duration-200 ${
                userId && comment.likedBy?.includes(userId)
                  ? isDarkMode ? 'text-blue-400' : 'text-blue-600'
                  : isDarkMode ? 'text-gray-400 hover:text-blue-400' : 'text-gray-500 hover:text-blue-600'
              }`}
              disabled={!userId}
            >
              <ThumbsUp className="h-4 w-4" />
              <span>{comment.likes || 0}</span>
            </button>

            <button
              onClick={() => handleLike(false)}
              className={`flex items-center gap-1 text-sm transition-colors duration-200 ${
                userId && comment.dislikedBy?.includes(userId)
                  ? isDarkMode ? 'text-red-400' : 'text-red-600'
                  : isDarkMode ? 'text-gray-400 hover:text-red-400' : 'text-gray-500 hover:text-red-600'
              }`}
              disabled={!userId}
            >
              <ThumbsDown className="h-4 w-4" />
              <span>{comment.dislikes || 0}</span>
            </button>

            <button
              onClick={() => setIsReplying(!isReplying)}
              className="flex items-center gap-1 text-sm text-gray-500"
              disabled={!userId}
            >
              <Reply className="h-4 w-4" />
              <span>Responder</span>
            </button>
          </div>

          {isReplying && (
            <div className="mt-4 space-y-2">
              <Textarea
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder="Escreva sua resposta..."
                className="min-h-[100px]"
              />
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => {
                  setIsReplying(false);
                  setReplyText("");
                }}>
                  Cancelar
                </Button>
                <Button onClick={handleSubmitReply} disabled={!replyText.trim()}>
                  Responder
                </Button>
              </div>
            </div>
          )}

          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-4 space-y-4 pl-4 border-l-2 border-gray-100">
              {comment.replies.map((reply: any) => {
                const replyProfile = replyProfiles[reply.user];
                const replyDisplayName = replyProfile?.full_name || 'Anônimo';

                return (
                  <div key={reply.id} className="flex gap-4">
                    <Avatar className="h-8 w-8">
                      <img
                        src={replyProfile?.avatar_url || '/default-avatar.png'}
                        alt={replyDisplayName}
                      />
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{replyDisplayName}</span>
                        <span className="text-sm text-gray-500">
                          {formatTimestamp(reply.timestamp)}
                        </span>
                      </div>
                      <p className="text-gray-700 mb-2">{reply.text}</p>
                      <div className="flex items-center gap-4">
                        <button
                          onClick={() => handleReplyLike(reply.id, true)}
                          className={`flex items-center gap-1 text-sm ${
                            reply.likedBy?.includes(userId) ? 'text-blue-600' : 'text-gray-500'
                          }`}
                          disabled={!userId}
                        >
                          <ThumbsUp className="h-4 w-4" />
                          <span>{reply.likes || 0}</span>
                        </button>
                        <button
                          onClick={() => handleReplyLike(reply.id, false)}
                          className={`flex items-center gap-1 text-sm ${
                            reply.dislikedBy?.includes(userId) ? 'text-red-600' : 'text-gray-500'
                          }`}
                          disabled={!userId}
                        >
                          <ThumbsDown className="h-4 w-4" />
                          <span>{reply.dislikes || 0}</span>
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};