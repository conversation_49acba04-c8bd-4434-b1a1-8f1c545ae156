import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  X,
  BookOpen,
  TrendingUp,
  Filter,
  Eye,
  RotateCcw,
  Calendar,
  BarChart3,
  ArrowLeft,
  Loader2,
  Clock
} from 'lucide-react';
import Header from '@/components/Header';
import StudyNavBar from '@/components/study/StudyNavBar';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { Checkbox } from '@/components/ui/checkbox';
import { useErrorNotebook } from '@/hooks/useErrorNotebook';
import { useDarkMode } from '@/contexts/DarkModeContext';
import { toast } from '@/hooks/use-toast';
import { ErrorProgressChart } from '@/components/error-notebook/ErrorProgressChart';
import { ErrorFiltersComponent, ErrorFilters } from '@/components/error-notebook/ErrorFilters';
import { ErrorQuestionsList } from '@/components/error-notebook/ErrorQuestionsList';
import { QuestionViewMode } from '@/components/error-notebook/QuestionViewMode';
import { QuestionRetryMode } from '@/components/error-notebook/QuestionRetryMode';
import { QuestionSelector } from '@/components/error-notebook/QuestionSelector';
import { ReviewSessionDialog, ReviewSessionConfig } from '@/components/error-notebook/ReviewSessionDialog';
import { SessionSummaryDialog } from '@/components/error-notebook/SessionSummaryDialog';

const ErrorNotebook = () => {
  const navigate = useNavigate();
  const { isDarkMode } = useDarkMode();
  const {
    errorStats,
    errorQuestions,
    isLoading,
    fetchErrorQuestions,
    fetchReviewHistory,
    markAsReviewed,
    updateAnnotation,
    saveDiscursiveAnalysis
  } = useErrorNotebook(true); // Carregar automaticamente na página de erros
  const [selectedView, setSelectedView] = useState<'dashboard' | 'list' | 'view' | 'retry' | 'select-view' | 'select-retry'>('dashboard');
  const [filters, setFilters] = useState<ErrorFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [questionsPerPage] = useState(10);
  const [filteredQuestions, setFilteredQuestions] = useState(errorQuestions);
  const [isLoadingQuestions, setIsLoadingQuestions] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<any>(null);
  const [reviewHistory, setReviewHistory] = useState<any[]>([]);
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [currentSelectedIndex, setCurrentSelectedIndex] = useState(0);

  // Estado das respostas na sessão atual (para modo retry)
  const [sessionAnswerStatuses, setSessionAnswerStatuses] = useState<Record<number, boolean | null>>({});
  const [sessionAnswers, setSessionAnswers] = useState<Record<number, number | null>>({});
  const [sessionStartTime, setSessionStartTime] = useState<number>(Date.now());
  const [showSessionSummary, setShowSessionSummary] = useState(false);

  // Modal de configuração de revisão
  const [showReviewModal, setShowReviewModal] = useState(false);

  // Reset seleções ao mudar de view
  useEffect(() => {
    if (selectedView === 'dashboard') {
      setSelectedQuestions([]);
      setCurrentSelectedIndex(0);
    }
  }, [selectedView]);

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Aplicar filtros
  const applyFilters = async () => {
    setIsLoadingQuestions(true);
    setCurrentPage(1);

    try {
      const questions = await fetchErrorQuestions({
        specialty_id: filters.specialty_id,
        theme_id: filters.theme_id,
        focus_id: filters.focus_id,
        limit: 1000 // Buscar todas para filtrar localmente
      });

      let filtered = questions || [];

      // Filtrar por status
      if (filters.status === 'reviewed') {
        filtered = filtered.filter(q => q.revisado);
      } else if (filters.status === 'pending') {
        filtered = filtered.filter(q => !q.revisado);
      }

      // Filtrar por termo de busca
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        filtered = filtered.filter(q =>
          q.question_content.toLowerCase().includes(searchTerm) ||
          q.specialty_name.toLowerCase().includes(searchTerm) ||
          q.theme_name.toLowerCase().includes(searchTerm) ||
          q.focus_name.toLowerCase().includes(searchTerm)
        );
      }

      setFilteredQuestions(filtered);
    } catch (error) {
      console.error('Erro ao aplicar filtros:', error);
    } finally {
      setIsLoadingQuestions(false);
    }
  };

  // Calcular paginação
  const totalPages = Math.ceil(filteredQuestions.length / questionsPerPage);
  const startIndex = (currentPage - 1) * questionsPerPage;
  const endIndex = startIndex + questionsPerPage;
  const currentQuestions = filteredQuestions.slice(startIndex, endIndex);

  // Handlers
  const handleViewQuestion = async (question: any) => {
    // Reset da sessão para questão individual
    setSelectedQuestions([question.id]);
    setCurrentSelectedIndex(0);
    setSessionAnswerStatuses({});
    setSessionAnswers({});
    setSessionStartTime(Date.now());
    setShowSessionSummary(false);

    setSelectedQuestion(question);
    const history = await fetchReviewHistory(question.question_id, question.id);
    setReviewHistory(history);
    setSelectedView('view');
  };

  const handleRetryQuestion = async (question: any) => {
    // Reset da sessão para questão individual
    setSelectedQuestions([question.id]);
    setCurrentSelectedIndex(0);
    setSessionAnswerStatuses({});
    setSessionAnswers({});
    setSessionStartTime(Date.now());
    setShowSessionSummary(false);

    setSelectedQuestion(question);
    const history = await fetchReviewHistory(question.question_id, question.id);
    setReviewHistory(history);
    setSelectedView('retry');
  };

  const handleBackToList = () => {
    setSelectedQuestion(null);
    setReviewHistory([]);
    setSelectedView('list');
    applyFilters(); // Recarregar para atualizar status
  };

  const handleBackToDashboard = () => {
    setSelectedQuestion(null);
    setReviewHistory([]);
    setSelectedView('dashboard');
  };

  const handleSelectQuestionForView = async (question: any) => {
    setSelectedQuestion(question);
    const history = await fetchReviewHistory(question.question_id, question.id);
    setReviewHistory(history);
    setSelectedView('view');
  };

  const handleSelectQuestionForRetry = async (question: any) => {
    setSelectedQuestion(question);
    const history = await fetchReviewHistory(question.question_id, question.id);
    setReviewHistory(history);
    setSelectedView('retry');
  };

  const handleBackFromViewMode = () => {
    setSelectedQuestion(null);
    setReviewHistory([]);
    setSelectedView('dashboard');
    applyFilters(); // Recarregar para atualizar status
  };

  const handleBackFromRetryMode = () => {
    setSelectedQuestion(null);
    setReviewHistory([]);
    setSelectedView('dashboard');
    applyFilters(); // Recarregar para atualizar status
  };

  const handleMarkAsReviewed = async (
    questionId: string,
    userAnswerId: string,
    mode: 'visualizacao' | 'refazer',
    annotation?: string,
    correctOnReview?: boolean,
    timeSpent?: number,
    discursiveAnswer?: string
  ) => {
    const success = await markAsReviewed(userAnswerId, questionId, mode, annotation, correctOnReview, timeSpent, discursiveAnswer);
    if (success) {
      // Atualizar a questão atual
      if (selectedQuestion) {
        const updatedQuestion = {
          ...selectedQuestion,
          revisado: true,
          anotacao: annotation || selectedQuestion.anotacao
        };
        setSelectedQuestion(updatedQuestion);

        // Atualizar também nas listas para manter consistência
        setFilteredQuestions(prev =>
          prev.map(q => q.id === selectedQuestion.id ? updatedQuestion : q)
        );

        // Se há questões selecionadas, atualizar também essa lista
        if (selectedQuestions.length > 0) {
          const updatedSelectedQuestions = selectedQuestions.map(id => {
            const questionData = getSelectedQuestionsData().find(q => q.id === id);
            return questionData?.id === selectedQuestion.id ? updatedQuestion : questionData;
          });
          // Atualizar o cache das questões selecionadas
          setFilteredQuestions(prev =>
            prev.map(q => {
              const updated = updatedSelectedQuestions.find(uq => uq?.id === q.id);
              return updated || q;
            })
          );
        }
      }

      // Recarregar histórico da questão atual
      if (selectedQuestion) {
        const history = await fetchReviewHistory(selectedQuestion.question_id, selectedQuestion.id);
        setReviewHistory(history);
      }
    }
    return success;
  };

  const handleUpdateAnnotation = async (questionId: string, userAnswerId: string, annotation: string) => {
    const success = await updateAnnotation(userAnswerId, questionId, annotation);
    if (success) {
      // Atualizar estado local imediatamente para feedback visual instantâneo
      setFilteredQuestions(prev =>
        prev.map(q => q.id === userAnswerId ? { ...q, anotacao: annotation } : q)
      );

      // Atualizar também a questão selecionada se for a mesma
      if (selectedQuestion && selectedQuestion.id === userAnswerId) {
        setSelectedQuestion(prev => prev ? { ...prev, anotacao: annotation } : prev);
      }

      // Forçar re-render da lista
      setCurrentPage(prev => prev); // Trigger re-render
    }
    return success;
  };

  // Funções de seleção múltipla
  const handleToggleSelection = (questionId: string) => {
    setSelectedQuestions(prev =>
      prev.includes(questionId)
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  const handleSelectAll = () => {
    const currentPageQuestions = filteredQuestions
      .slice((currentPage - 1) * questionsPerPage, currentPage * questionsPerPage)
      .map(q => q.id);
    setSelectedQuestions(prev => [...new Set([...prev, ...currentPageQuestions])]);
  };

  const handleClearSelection = () => {
    setSelectedQuestions([]);
  };

  const handleReviewSelected = () => {
    if (selectedQuestions.length > 0) {
      // Pegar a primeira questão selecionada para iniciar a revisão
      const selectedQuestionsData = filteredQuestions.filter(q => selectedQuestions.includes(q.id));
      if (selectedQuestionsData.length > 0) {
        setCurrentSelectedIndex(0);
        setSelectedQuestion(selectedQuestionsData[0]);
        setSelectedView('view');
      }
    }
  };

  const handleRetrySelected = () => {
    if (selectedQuestions.length > 0) {
      // Pegar a primeira questão selecionada para iniciar o refazer
      const selectedQuestionsData = filteredQuestions.filter(q => selectedQuestions.includes(q.id));
      if (selectedQuestionsData.length > 0) {
        setCurrentSelectedIndex(0);
        setSelectedQuestion(selectedQuestionsData[0]);
        setSelectedView('retry');
      }
    }
  };

  const handleStartReviewSession = async (config: ReviewSessionConfig) => {
    try {
      // Limpar estados da sessão anterior
      clearSessionStates();

      // Buscar mais questões do que o necessário para permitir filtragem
      const searchLimit = Math.max(config.quantity * 3, 100);

      const questions = await fetchErrorQuestions({
        specialty_id: config.specialty_id,
        theme_id: config.theme_id,
        limit: searchLimit
      });

      if (questions.length === 0) {
        toast({
          title: "Nenhuma questão encontrada",
          description: "Não há questões disponíveis com os filtros selecionados.",
          variant: "destructive",
        });
        return;
      }

      // Filtrar apenas pendentes se solicitado
      let filteredQuestions = questions;
      if (config.onlyPending) {
        filteredQuestions = questions.filter(q => !q.revisado);
      }

      if (filteredQuestions.length === 0) {
        toast({
          title: "Nenhuma questão pendente",
          description: "Todas as questões com os filtros selecionados já foram revisadas.",
          variant: "destructive",
        });
        return;
      }

      // Ordenar questões conforme configuração
      let sortedQuestions = [...filteredQuestions];

      switch (config.order) {
        case 'oldest':
          sortedQuestions.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
          break;
        case 'random':
          sortedQuestions = sortedQuestions.sort(() => Math.random() - 0.5);
          break;
        case 'recent':
        default:
          sortedQuestions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
          break;
      }

      // Limitar quantidade
      const limitedQuestions = sortedQuestions.slice(0, config.quantity);

      // Configurar sessão
      setFilteredQuestions(limitedQuestions);
      setSelectedQuestions(limitedQuestions.map(q => q.id));
      setCurrentSelectedIndex(0);

      // Navegar para o primeiro item
      const firstQuestion = limitedQuestions[0];

      setSelectedQuestion(firstQuestion);
      const history = await fetchReviewHistory(firstQuestion.question_id, firstQuestion.id);
      setReviewHistory(history);
      setSelectedView(config.mode);

      toast({
        title: "Sessão iniciada!",
        description: `Revisando ${limitedQuestions.length} questão${limitedQuestions.length > 1 ? 'ões' : ''} no modo ${config.mode === 'view' ? 'visualização' : 'refazer'}.`,
      });

    } catch (error) {
      console.error('Erro ao iniciar sessão de revisão:', error);
      toast({
        title: "Erro",
        description: "Não foi possível iniciar a sessão de revisão.",
        variant: "destructive",
      });
    }
  };

  // Navegação entre questões selecionadas
  const getSelectedQuestionsData = () => {
    return filteredQuestions.filter(q => selectedQuestions.includes(q.id));
  };

  const goToNextQuestion = async () => {
    const questionsData = selectedQuestions.length > 0 ? getSelectedQuestionsData() : filteredQuestions;
    const currentIndex = selectedQuestions.length > 0 ? currentSelectedIndex :
      questionsData.findIndex(q => q.id === selectedQuestion?.id);

    if (currentIndex < questionsData.length - 1) {
      const nextIndex = currentIndex + 1;
      const nextQuestion = questionsData[nextIndex];

      if (selectedQuestions.length > 0) {
        setCurrentSelectedIndex(nextIndex);
      }
      setSelectedQuestion(nextQuestion);

      // Carregar histórico da nova questão
      const history = await fetchReviewHistory(nextQuestion.question_id, nextQuestion.id);
      setReviewHistory(history);
    }
  };

  const goToPreviousQuestion = async () => {
    const questionsData = selectedQuestions.length > 0 ? getSelectedQuestionsData() : filteredQuestions;
    const currentIndex = selectedQuestions.length > 0 ? currentSelectedIndex :
      questionsData.findIndex(q => q.id === selectedQuestion?.id);

    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      const prevQuestion = questionsData[prevIndex];

      if (selectedQuestions.length > 0) {
        setCurrentSelectedIndex(prevIndex);
      }
      setSelectedQuestion(prevQuestion);

      // Carregar histórico da nova questão
      const history = await fetchReviewHistory(prevQuestion.question_id, prevQuestion.id);
      setReviewHistory(history);
    }
  };

  const goToQuestion = async (targetIndex: number) => {
    const questionsData = selectedQuestions.length > 0 ? getSelectedQuestionsData() : filteredQuestions;

    if (targetIndex >= 0 && targetIndex < questionsData.length) {
      const targetQuestion = questionsData[targetIndex];

      if (selectedQuestions.length > 0) {
        setCurrentSelectedIndex(targetIndex);
      }
      setSelectedQuestion(targetQuestion);

      // Carregar histórico da nova questão
      const history = await fetchReviewHistory(targetQuestion.question_id, targetQuestion.id);
      setReviewHistory(history);
    }
  };

  // Função para decidir o que fazer após marcar como revisada
  const handleMarkAsReviewedComplete = async () => {
    const questionsData = selectedQuestions.length > 0 ? getSelectedQuestionsData() : filteredQuestions;
    const currentIndex = selectedQuestions.length > 0 ? currentSelectedIndex :
      questionsData.findIndex(q => q.id === selectedQuestion?.id);

    // Se há próxima questão, vai para ela
    if (currentIndex < questionsData.length - 1) {
      await goToNextQuestion();
    } else {
      // Se é a última questão, volta para o dashboard ou lista
      if (selectedQuestions.length > 0) {
        // Se estava em sessão, volta para dashboard
        handleBackToDashboard();
      } else {
        // Se era questão individual, volta para lista
        handleBackFromViewMode();
      }
    }
  };

  const getCurrentQuestionIndex = () => {
    const questionsData = selectedQuestions.length > 0 ? getSelectedQuestionsData() : filteredQuestions;
    return selectedQuestions.length > 0 ? currentSelectedIndex :
      questionsData.findIndex(q => q.id === selectedQuestion?.id);
  };

  const getTotalQuestionsCount = () => {
    return selectedQuestions.length > 0 ? selectedQuestions.length : filteredQuestions.length;
  };

  // Calcular quais questões já foram revisadas
  const getReviewedQuestions = (): Set<number> => {
    const questionsData = selectedQuestions.length > 0 ? getSelectedQuestionsData() : filteredQuestions;
    const reviewedSet = new Set<number>();

    questionsData.forEach((question, index) => {
      if (question.revisado) {
        reviewedSet.add(index);
      }
    });

    return reviewedSet;
  };

  // Função para atualizar status da resposta na sessão
  const handleAnswerStatusChange = (index: number, status: boolean | null) => {
    setSessionAnswerStatuses(prev => ({
      ...prev,
      [index]: status
    }));
  };

  // Função para atualizar resposta selecionada na sessão
  const handleAnswerChange = (index: number, answer: number | null) => {
    setSessionAnswers(prev => ({
      ...prev,
      [index]: answer
    }));
  };

  // Limpar estados da sessão ao iniciar nova sessão
  const clearSessionStates = () => {
    setSessionAnswerStatuses({});
    setSessionAnswers({});
    setSessionStartTime(Date.now());
  };

  // Calcular resumo da sessão
  const calculateSessionSummary = () => {
    const questionsData = selectedQuestions.length > 0 ? getSelectedQuestionsData() : filteredQuestions;
    const totalQuestions = questionsData.length;

    // Contar questões respondidas
    const answeredQuestions = Object.keys(sessionAnswerStatuses).length;

    // Contar acertos e erros
    const correctAnswers = Object.values(sessionAnswerStatuses).filter(status => status === true).length;
    const incorrectAnswers = Object.values(sessionAnswerStatuses).filter(status => status === false).length;

    // Contar questões marcadas como estudadas (revisadas)
    const markedAsStudied = questionsData.filter(q => q.revisado).length;

    // Calcular taxa de melhoria (questões que acertou desta vez)
    const improvementRate = answeredQuestions > 0 ? Math.round((correctAnswers / answeredQuestions) * 100) : 0;

    // Calcular duração da sessão
    const sessionDuration = formatSessionDuration(Date.now() - sessionStartTime);

    return {
      totalQuestions,
      answeredQuestions,
      correctAnswers,
      incorrectAnswers,
      markedAsStudied,
      improvementRate,
      sessionDuration
    };
  };

  // Formatar duração da sessão
  const formatSessionDuration = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Finalizar sessão de retry
  const handleFinishRetrySession = () => {
    setShowSessionSummary(true);
  };



  const goToNextSelectedQuestion = () => {
    const selectedQuestionsData = getSelectedQuestionsData();
    if (currentSelectedIndex < selectedQuestionsData.length - 1) {
      const nextIndex = currentSelectedIndex + 1;
      setCurrentSelectedIndex(nextIndex);
      setSelectedQuestion(selectedQuestionsData[nextIndex]);
    }
  };

  const goToPrevSelectedQuestion = () => {
    const selectedQuestionsData = getSelectedQuestionsData();
    if (currentSelectedIndex > 0) {
      const prevIndex = currentSelectedIndex - 1;
      setCurrentSelectedIndex(prevIndex);
      setSelectedQuestion(selectedQuestionsData[prevIndex]);
    }
  };

  // Carregar questões iniciais
  useEffect(() => {
    if (selectedView === 'list' || selectedView === 'select-view' || selectedView === 'select-retry') {
      applyFilters();
    }
  }, [selectedView]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
        <Header />
        <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
        
        <div className="container mx-auto px-4 py-8 flex items-center justify-center">
          <div className="flex items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin text-red-500" />
            <span className="text-lg font-medium">Carregando caderno de erros...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
      
      <motion.div
        className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 space-y-4 sm:space-y-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header Section - Só aparece quando não está em sessão de questões */}
        {selectedView !== 'view' && selectedView !== 'retry' ? (
          <motion.div variants={itemVariants} className="text-center mb-4 mt-6 pt-4 sm:pt-0">
            <div className="inline-block">
              <div className="bg-red-500 border-2 border-black px-4 py-1 text-white font-bold tracking-wide text-sm shadow-md">
                ❌ CADERNO DE ERROS
              </div>
            </div>
          </motion.div>
        ) : (
          // Espaçamento adequado quando em sessão (sem banner) - mais espaço no mobile
          <div className="pt-7 sm:pt-0"></div>
        )}

        {/* Dashboard Content */}
        {selectedView === 'dashboard' && errorStats.totalErrors > 0 && (
          <motion.div variants={itemVariants} className="max-w-6xl mx-auto">
            <div className="bg-white border-2 border-gray-200 rounded-2xl shadow-lg p-8 space-y-8">

              {/* Header */}
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">O que você gostaria de fazer?</h2>
                <p className="text-gray-600 mb-6">Escolha como revisar suas questões erradas</p>
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                <Button
                  onClick={() => setSelectedView('list')}
                  className="bg-gray-900 hover:bg-gray-800 text-white h-24 flex flex-col items-center justify-center gap-3 rounded-xl border-2 border-gray-800 shadow-lg hover:shadow-xl transition-all"
                >
                  <Filter className="h-7 w-7" />
                  <div className="text-center">
                    <div className="font-bold text-lg">Lista Completa</div>
                    <div className="text-sm opacity-90">Ver todas com filtros e controles</div>
                  </div>
                </Button>

                <Button
                  onClick={() => setShowReviewModal(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white h-24 flex flex-col items-center justify-center gap-3 rounded-xl border-2 border-blue-600 shadow-lg hover:shadow-xl transition-all"
                >
                  <BookOpen className="h-7 w-7" />
                  <div className="text-center">
                    <div className="font-bold text-lg">Revisar Questões</div>
                    <div className="text-sm opacity-90">
                      {errorStats.pendingErrors > 0
                        ? `${errorStats.pendingErrors} questões pendentes`
                        : 'Todas as questões revisadas!'
                      }
                    </div>
                  </div>
                </Button>
              </div>

              {/* Progress Chart */}
              <div className="max-w-2xl mx-auto">
                <ErrorProgressChart
                  reviewedErrors={errorStats.reviewedErrors}
                  totalErrors={errorStats.totalErrors}
                  topErrorThemes={errorStats.topErrorThemes}
                  topErrorSpecialties={errorStats.topErrorSpecialties}
                />
              </div>


            </div>
          </motion.div>
        )}





        {/* Lista de Questões com Filtros */}
        {selectedView === 'list' && (
          <motion.div variants={itemVariants} className="space-y-6">
            {/* Cabeçalho */}
            <div className="flex items-center gap-2 sm:gap-4">
              <Button
                variant="outline"
                onClick={() => setSelectedView('dashboard')}
                size="sm"
                className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
              >
                <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Voltar ao Dashboard</span>
                <span className="sm:hidden">Voltar</span>
              </Button>
              <h2 className="text-lg sm:text-2xl font-bold">Lista de Questões Erradas</h2>
            </div>

            {/* Filtros */}
            <ErrorFiltersComponent
              filters={filters}
              onFiltersChange={setFilters}
              onApplyFilters={applyFilters}
              totalResults={filteredQuestions.length}
              isLoading={isLoadingQuestions}
              errorStats={errorStats}
            />

            {/* Lista de Questões */}
            <ErrorQuestionsList
              questions={currentQuestions}
              currentPage={currentPage}
              totalPages={totalPages}
              questionsPerPage={questionsPerPage}
              isLoading={isLoadingQuestions}
              selectedQuestions={selectedQuestions}
              onPageChange={setCurrentPage}
              onViewQuestion={handleViewQuestion}
              onRetryQuestion={handleRetryQuestion}
              onMarkAsReviewed={handleMarkAsReviewed}
              onUpdateAnnotation={handleUpdateAnnotation}
              onToggleSelection={handleToggleSelection}
              onSelectAll={handleSelectAll}
              onClearSelection={handleClearSelection}
              onReviewSelected={handleReviewSelected}
              onRetrySelected={handleRetrySelected}
            />
          </motion.div>
        )}

        {/* Question View Mode */}
        {selectedView === 'view' && selectedQuestion && (
          <motion.div variants={itemVariants}>
            <QuestionViewMode
              question={selectedQuestion}
              onBack={handleBackFromViewMode}
              onMarkAsReviewed={(questionId, userAnswerId, annotation) =>
                handleMarkAsReviewed(questionId, userAnswerId, 'visualizacao', annotation, undefined, undefined)
              }
              onUpdateAnnotation={handleUpdateAnnotation}
              reviewHistory={reviewHistory}
              onNext={getTotalQuestionsCount() > 1 ? goToNextQuestion : undefined}
              onPrevious={getTotalQuestionsCount() > 1 ? goToPreviousQuestion : undefined}
              onGoToQuestion={getTotalQuestionsCount() > 1 ? goToQuestion : undefined}
              onMarkAsReviewedComplete={handleMarkAsReviewedComplete}
              currentIndex={getCurrentQuestionIndex()}
              totalQuestions={getTotalQuestionsCount()}
              reviewedQuestions={getReviewedQuestions()}
            />
          </motion.div>
        )}

        {/* Question Retry Mode */}
        {selectedView === 'retry' && selectedQuestion && (
          <motion.div variants={itemVariants}>
            <QuestionRetryMode
              question={selectedQuestion}
              onBack={handleBackFromRetryMode}
              onMarkAsReviewed={(questionId, userAnswerId, annotation, correctOnReview, timeSpent, discursiveAnswer) =>
                handleMarkAsReviewed(questionId, userAnswerId, 'refazer', annotation, correctOnReview, timeSpent, discursiveAnswer)
              }
              onUpdateAnnotation={handleUpdateAnnotation}
              onNext={getTotalQuestionsCount() > 1 ? goToNextQuestion : undefined}
              onPrevious={getTotalQuestionsCount() > 1 ? goToPreviousQuestion : undefined}
              onGoToQuestion={getTotalQuestionsCount() > 1 ? goToQuestion : undefined}
              currentIndex={getCurrentQuestionIndex()}
              totalQuestions={getTotalQuestionsCount()}
              reviewedQuestions={getReviewedQuestions()}
              answeredStatuses={sessionAnswerStatuses}
              onAnswerStatusChange={handleAnswerStatusChange}
              sessionAnswers={sessionAnswers}
              onAnswerChange={handleAnswerChange}
              onFinishSession={handleFinishRetrySession}
              saveDiscursiveAnalysis={saveDiscursiveAnalysis}
            />
          </motion.div>
        )}

        {/* Question Selector for View Mode */}
        {selectedView === 'select-view' && (
          <motion.div variants={itemVariants}>
            <QuestionSelector
              questions={filteredQuestions}
              mode="view"
              onBack={handleBackToDashboard}
              onSelectQuestion={handleSelectQuestionForView}
              isLoading={isLoadingQuestions}
            />
          </motion.div>
        )}

        {/* Question Selector for Retry Mode */}
        {selectedView === 'select-retry' && (
          <motion.div variants={itemVariants}>
            <QuestionSelector
              questions={filteredQuestions}
              mode="retry"
              onBack={handleBackToDashboard}
              onSelectQuestion={handleSelectQuestionForRetry}
              isLoading={isLoadingQuestions}
            />
          </motion.div>
        )}

        {/* Empty State */}
        {selectedView === 'dashboard' && errorStats.totalErrors === 0 && (
          <motion.div variants={itemVariants}>
            <Card className="p-12 bg-white border-2 border-black shadow-card-sm text-center">
              <div className="max-w-md mx-auto">
                <div className="p-4 bg-green-500/10 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                  <BookOpen className="h-10 w-10 text-green-500" />
                </div>
                <h3 className="text-xl font-bold mb-2">Parabéns! 🎉</h3>
                <p className="text-gray-600 mb-6">
                  Você ainda não tem questões erradas registradas. Continue praticando para manter esse excelente desempenho!
                </p>
                <Button
                  onClick={() => navigate('/questions')}
                  className="bg-green-500 hover:bg-green-600 text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
                >
                  <BookOpen className="h-4 w-4 mr-2" />
                  Praticar Questões
                </Button>
              </div>
            </Card>
          </motion.div>
        )}
      </motion.div>

      {/* Modal de Configuração de Revisão */}
      <ReviewSessionDialog
        open={showReviewModal}
        onOpenChange={setShowReviewModal}
        errorStats={errorStats}
        pendingQuestions={errorStats.pendingErrors}
        onStartSession={handleStartReviewSession}
      />

      {/* Session Summary Dialog */}
      <SessionSummaryDialog
        open={showSessionSummary}
        onOpenChange={setShowSessionSummary}
        summaryData={calculateSessionSummary()}
        onBackToDashboard={() => {
          setShowSessionSummary(false);
          handleBackToDashboard();
        }}
      />

    </div>
  );
};

export default ErrorNotebook;
