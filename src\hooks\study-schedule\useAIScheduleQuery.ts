import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@supabase/auth-helpers-react';
import { useToast } from '@/hooks/use-toast';
import { useAISchedule } from './useAISchedule';
import { useScheduleManagement } from './useScheduleManagement';
import type { AIScheduleOptions, GenerationStats } from '@/types/study-schedule';
import { useState } from 'react';

/**
 * Hook para geração de cronograma com IA usando React Query
 */
export const useAIScheduleQuery = () => {
  const user = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // ✅ Estado para capturar estatísticas da geração
  const [generationStats, setGenerationStats] = useState<GenerationStats | null>(null);
  const [isComplete, setIsComplete] = useState(false);

  const { addWeeks } = useScheduleManagement();

  // Wrapper para addWeeks que retorna Promise<boolean>
  const addWeeksWrapper = async (numberOfWeeks: number): Promise<boolean> => {
    const result = await addWeeks(numberOfWeeks);
    return result;
  };

  const { generateAISchedule } = useAISchedule(
    setGenerationStats, // ✅ Agora captura as estatísticas
    addWeeksWrapper
  );

  // ✅ Mutation para gerar cronograma com IA
  const generateAIScheduleMutation = useMutation({
    mutationFn: async (options: AIScheduleOptions) => {


      // Reset states
      setIsComplete(false);
      setGenerationStats(null);

      try {
        const result = await generateAISchedule(options);

        return result;
      } catch (error) {
        console.error('❌ [useAIScheduleQuery] Erro na geração:', error);
        throw error;
      }
    },
    onMutate: async (options: AIScheduleOptions) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['schedule', user?.id] });
    },
    onError: (err: any, options, context) => {
      setIsComplete(false);
      setGenerationStats(null);
    },
    onSuccess: (result) => {
      // ✅ Marcar como completo quando a IA terminar
      setIsComplete(true);

      // ✅ NOVO: Disparar aviso de refresh na página principal
      setTimeout(() => {
        if (typeof window !== 'undefined' && (window as any).showRefreshWarning) {
          (window as any).showRefreshWarning();
        }
      }, 1000); // Delay para dar tempo do dialog fechar

      // ✅ CORREÇÃO: Invalidar TODOS os caches relacionados ao cronograma
      setTimeout(async () => {


        // 1. Invalidar TODOS os caches relacionados a cronogramas
        await queryClient.invalidateQueries({
          predicate: (query) => {
            const key = query.queryKey[0];
            return key === 'consolidated-schedule-data' ||
                   key === 'processed-schedule-data' ||
                   key === 'schedule' ||
                   key === 'study_schedules' ||
                   key === 'study_schedule_items';
          },
          refetchType: 'all'
        });


        // 2. Forçar refetch imediato e sequencial
        setTimeout(async () => {
          // Refetch dados consolidados primeiro
          await queryClient.refetchQueries({
            queryKey: ['consolidated-schedule-data', user?.id]
          });


          // Aguardar e refetch query principal
          setTimeout(async () => {
            await queryClient.refetchQueries({
              queryKey: ['schedule', user?.id]
            });


            // Forçar refetch de queries de semanas individuais
            setTimeout(async () => {
              await queryClient.refetchQueries({
                predicate: (query) => {
                  const key = query.queryKey[0];
                  return key === 'study_schedules' || key === 'study_schedule_items';
                }
              });

            }, 100);
          }, 200);
        }, 100);
      }, 50);
    },
    onSettled: () => {
      // Não fazer nada aqui - deixar o dialog gerenciar o estado
    }
  });

  return {
    generateAISchedule: generateAIScheduleMutation.mutate,
    isGenerating: generateAIScheduleMutation.isPending,
    isComplete,
    generationStats,
    error: generateAIScheduleMutation.error,
    resetCompletion: () => {
      setIsComplete(false);
      setGenerationStats(null);
    }
  };
};
