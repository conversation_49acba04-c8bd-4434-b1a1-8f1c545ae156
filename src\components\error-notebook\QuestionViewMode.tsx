import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Eye,
  CheckCircle,
  X,
  ArrowLeft,
  MessageSquare,
  Save,
  Edit3,
  Calendar,
  Clock,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { ErrorQuestion } from '@/hooks/useErrorNotebook';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ReviewHistory } from './ReviewHistory';
import { ErrorQuestionAICommentary } from './ErrorQuestionAICommentary';
import { ErrorQuestionNavigation } from './ErrorQuestionNavigation';
import { QuestionImages } from '../question/QuestionImages';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface QuestionViewModeProps {
  question: ErrorQuestion;
  onBack: () => void;
  onMarkAsReviewed: (questionId: string, userAnswerId: string, annotation?: string, correctOnReview?: boolean, timeSpent?: number, discursiveAnswer?: string) => void;
  onUpdateAnnotation: (questionId: string, userAnswerId: string, annotation: string) => void;
  reviewHistory?: any[];
  onNext?: () => void;
  onPrevious?: () => void;
  onGoToQuestion?: (index: number) => void;
  currentIndex?: number;
  totalQuestions?: number;
  onMarkAsReviewedComplete?: () => void; // Nova prop para controlar o que fazer após marcar como revisada
  reviewedQuestions?: Set<number>; // Índices das questões já revisadas
  onFinishSession?: () => void;
}

export const QuestionViewMode: React.FC<QuestionViewModeProps> = ({
  question,
  onBack,
  onMarkAsReviewed,
  onUpdateAnnotation,
  reviewHistory = [],
  onNext,
  onPrevious,
  onGoToQuestion,
  currentIndex,
  totalQuestions,
  onMarkAsReviewedComplete,
  reviewedQuestions,
  onFinishSession
}) => {
  const [annotation, setAnnotation] = useState(question.anotacao || '');
  const [isEditingAnnotation, setIsEditingAnnotation] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const { isDarkMode } = useDarkMode();

  // Sincronizar anotação e estado quando a questão muda
  useEffect(() => {
    setAnnotation(question.anotacao || '');
    setIsEditingAnnotation(false); // Cancelar edição ao navegar
    setIsMarkedAsReviewed(false); // Reset estado de marcação
    setIsMarkingAsReviewed(false); // Reset estado de carregamento
  }, [question.id]);

  const handleSaveAnnotation = async () => {
    setIsSaving(true);
    try {
      await onUpdateAnnotation(question.question_id, question.id, annotation);
      setIsEditingAnnotation(false);
    } finally {
      setIsSaving(false);
    }
  };



  const [isMarkingAsReviewed, setIsMarkingAsReviewed] = useState(false);
  const [isMarkedAsReviewed, setIsMarkedAsReviewed] = useState(false);

  const handleMarkAsReviewed = async () => {
    setIsMarkingAsReviewed(true);
    try {
      await onMarkAsReviewed(question.question_id, question.id, annotation, undefined, undefined, undefined);

      // ✅ MELHORIA: Apenas atualizar o botão, sem navegação automática
      setIsMarkedAsReviewed(true);

      // Usuário navega manualmente usando os botões de navegação
    } catch (error) {
      console.error('Erro ao marcar como revisada:', error);
      setIsMarkingAsReviewed(false);
    }
  };

  const handleCommentaryGenerated = (commentary: any) => {
    // Callback quando a análise é gerada - pode ser usado para atualizar estado se necessário
  };

  const getAlternativeStyle = (index: number) => {
    const isUserAnswer = index === question.selected_answer;
    const isCorrectAnswer = index === parseInt(question.correct_choice);
    
    if (isUserAnswer && isCorrectAnswer) {
      return 'bg-green-100 border-green-500 text-green-800'; // Acertou
    } else if (isUserAnswer) {
      return 'bg-red-100 border-red-500 text-red-800'; // Resposta errada do usuário
    } else if (isCorrectAnswer) {
      return 'bg-green-100 border-green-500 text-green-800'; // Resposta correta
    }
    return 'bg-gray-50 border-gray-300 text-gray-700'; // Outras alternativas
  };

  const getAlternativeIcon = (index: number) => {
    const isUserAnswer = index === question.selected_answer;
    const isCorrectAnswer = index === parseInt(question.correct_choice);
    
    if (isUserAnswer && isCorrectAnswer) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    } else if (isUserAnswer) {
      return <X className="h-4 w-4 text-red-600" />;
    } else if (isCorrectAnswer) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    return null;
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ptBR 
      });
    } catch {
      return 'Data inválida';
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-0">
      {/* Navigation - Always show, even for single questions */}
      <ErrorQuestionNavigation
        currentIndex={currentIndex || 0}
        totalQuestions={totalQuestions || 1}
        onNext={onNext}
        onPrevious={onPrevious}
        onGoToQuestion={onGoToQuestion}
        onBack={onBack}
        mode="view"
        reviewedQuestions={reviewedQuestions}
      />

      {/* Status Badge */}
      <div className="flex justify-center">
        <Badge variant={question.revisado ? "default" : "destructive"} className="text-sm">
          {question.revisado ? 'Questão Revisada' : 'Pendente de Revisão'}
        </Badge>
      </div>

      {/* Question Card */}
      <Card className="p-3 sm:p-6 bg-white border-2 border-black shadow-card-sm">
        {/* Question Info */}
        <div className="mb-4 sm:mb-6">
          <div className="flex justify-between items-center mb-3 sm:mb-4">
            <div className="text-sm font-medium text-gray-600">
              Informações da questão
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className={`text-xs px-2 h-7 transition-colors duration-200 ${
                isDarkMode
                  ? 'text-gray-300 hover:text-gray-100 hover:bg-gray-700'
                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {showDetails ? (
                <span className="flex items-center gap-1">
                  Ocultar detalhes <ChevronUp size={14} />
                </span>
              ) : (
                <span className="flex items-center gap-1">
                  Mostrar detalhes <ChevronDown size={14} />
                </span>
              )}
            </Button>
          </div>

          {showDetails && (
            <div className="space-y-3">
              {/* Badges de metadados */}
              <div className="flex flex-wrap gap-2 text-sm">
                {/* Especialidade */}
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                  isDarkMode ? 'bg-blue-900/40 text-blue-300' : 'bg-blue-100 text-blue-800'
                }`}>
                  {question.specialty_name}
                </span>

                {/* Tema */}
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                  isDarkMode ? 'bg-green-900/40 text-green-300' : 'bg-green-100 text-green-800'
                }`}>
                  {question.theme_name}
                </span>

                {/* Foco (se disponível) */}
                {question.focus_name && question.focus_name !== question.theme_name && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    isDarkMode ? 'bg-purple-900/40 text-purple-300' : 'bg-purple-100 text-purple-800'
                  }`}>
                    {question.focus_name}
                  </span>
                )}

                {/* Origem/Local (se disponível) */}
                {question.exam_name && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    isDarkMode ? 'bg-yellow-900/40 text-yellow-300' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {question.exam_name}
                  </span>
                )}

                {/* Ano */}
                {question.exam_year && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {question.exam_year}
                  </span>
                )}
              </div>

              {/* Informações temporais */}
              <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Respondida {formatDate(question.created_at)}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {question.time_spent}s
                </div>
                {question.tentativas > 0 && (
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {question.tentativas} revisão{question.tentativas > 1 ? 'ões' : ''}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Question Content */}
        <div className="mb-4 sm:mb-6">
          <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Enunciado</h3>
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
            <p className="text-gray-800 leading-relaxed text-sm sm:text-base">
              {question.question_content}
            </p>
          </div>
        </div>

        {/* Question Images */}
        {question.media_attachments && question.media_attachments.length > 0 && (
          <QuestionImages images={question.media_attachments} />
        )}

        {/* Alternatives */}
        <div className="mb-4 sm:mb-6">
          <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Alternativas</h3>
          <div className="space-y-2 sm:space-y-3">
            {question.response_choices.map((choice, index) => (
              <div
                key={index}
                className={`p-3 sm:p-4 rounded-lg border-2 transition-all ${getAlternativeStyle(index)}`}
              >
                <div className="flex items-start gap-2 sm:gap-3">
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-sm sm:text-base">
                      {String.fromCharCode(65 + index)})
                    </span>
                    {getAlternativeIcon(index)}
                  </div>
                  <p className="flex-1">{choice}</p>
                </div>
                
                {/* Labels */}
                <div className="mt-2 flex gap-2">
                  {index === question.selected_answer && (
                    <Badge variant="destructive" className="text-xs">
                      Sua resposta
                    </Badge>
                  )}
                  {index === parseInt(question.correct_choice) && (
                    <Badge variant="default" className="text-xs bg-green-500">
                      Resposta correta
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* AI Commentary */}
        <ErrorQuestionAICommentary
          question={question}
          onCommentaryGenerated={handleCommentaryGenerated}
        />

        {/* Annotations */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              Suas Anotações
            </h3>
            {!isEditingAnnotation && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditingAnnotation(true)}
              >
                <Edit3 className="h-4 w-4 mr-2" />
                {annotation ? 'Editar' : 'Adicionar'}
              </Button>
            )}
          </div>
          
          {isEditingAnnotation ? (
            <div className="space-y-3">
              <Textarea
                value={annotation}
                onChange={(e) => setAnnotation(e.target.value)}
                placeholder="Adicione suas anotações sobre esta questão..."
                rows={4}
                className="border-2 border-gray-300"
              />
              <div className="flex gap-2">
                <Button
                  onClick={handleSaveAnnotation}
                  disabled={isSaving}
                  size="sm"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? 'Salvando...' : 'Salvar'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditingAnnotation(false);
                    setAnnotation(question.anotacao || '');
                  }}
                  size="sm"
                >
                  Cancelar
                </Button>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 p-4 rounded-lg min-h-[100px] border-2 border-gray-200">
              {annotation ? (
                <p className="text-gray-800 whitespace-pre-wrap">{annotation}</p>
              ) : (
                <p className="text-gray-500 italic">Nenhuma anotação adicionada.</p>
              )}
            </div>
          )}
        </div>

        {/* Review History */}
        {reviewHistory.length > 0 && (
          <div className="mb-6">
            <ReviewHistory
              attempts={reviewHistory.map(h => ({
                id: h.id,
                date: h.created_at,
                mode: h.modo_ultima_revisao || 'visualizacao',
                timeSpent: h.tempo_gasto_revisao,
                correctOnReview: h.acertou_na_revisao,
                annotation: h.anotacao
              }))}
              originalAttempt={{
                date: question.created_at,
                timeSpent: question.time_spent,
                wasCorrect: question.is_correct
              }}
            />

            {/* 🎯 BOTÕES DE NAVEGAÇÃO APÓS HISTÓRICO (quando marcado como estudado) */}
            {(isMarkedAsReviewed || question.revisado) && (
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center justify-between gap-1 sm:gap-4">
                  {/* Botão Anterior */}
                  <div className="flex-1">
                    {onPrevious && currentIndex !== undefined && currentIndex > 0 ? (
                      <Button
                        onClick={onPrevious}
                        variant="outline"
                        size="sm"
                        className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                      >
                        <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        <span className="hidden sm:inline">Anterior</span>
                        <span className="sm:hidden">Ant</span>
                      </Button>
                    ) : (
                      <div></div>
                    )}
                  </div>

                  {/* Botão Central - Marcado como Estudado */}
                  <div className="flex-shrink-0 px-1">
                    <Button
                      disabled={true}
                      size="sm"
                      className="border-2 border-black font-bold shadow-button text-xs sm:text-sm px-2 sm:px-4 bg-green-600 text-white cursor-default"
                    >
                      <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                      <span className="hidden sm:inline">Marcado como Estudado</span>
                      <span className="sm:hidden">Estudado</span>
                    </Button>
                  </div>

                  {/* Botão Próximo */}
                  <div className="flex-1 flex justify-end">
                    {onNext && currentIndex !== undefined && totalQuestions !== undefined && currentIndex < totalQuestions - 1 ? (
                      <Button
                        onClick={onNext}
                        variant="outline"
                        size="sm"
                        className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                      >
                        <span className="hidden sm:inline">Próximo</span>
                        <span className="sm:hidden">Prox</span>
                        <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                      </Button>
                    ) : currentIndex !== undefined && totalQuestions !== undefined && currentIndex >= totalQuestions - 1 ? (
                      <Button
                        onClick={onFinishSession || onBack}
                        variant="outline"
                        size="sm"
                        className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                      >
                        <span className="hidden sm:inline">Finalizar</span>
                        <span className="sm:hidden">Fim</span>
                      </Button>
                    ) : (
                      <div></div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="pt-4 border-t">
          {!question.revisado && (
            <div className="flex items-center justify-between gap-1 sm:gap-4">
              {/* Botão Anterior */}
              <div className="flex-1">
                {onPrevious && currentIndex !== undefined && currentIndex > 0 ? (
                  <Button
                    onClick={onPrevious}
                    variant="outline"
                    size="sm"
                    className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                  >
                    <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Anterior</span>
                    <span className="sm:hidden">Ant</span>
                  </Button>
                ) : null}
              </div>

              {/* Botão Central - Marcar como Revisada */}
              <div className="flex-shrink-0 px-1">
                <Button
                  onClick={handleMarkAsReviewed}
                  disabled={isMarkingAsReviewed || isMarkedAsReviewed}
                  size="sm"
                  className={`border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4 ${
                    isMarkedAsReviewed
                      ? 'bg-green-600 text-white cursor-default'
                      : isMarkingAsReviewed
                        ? 'bg-green-400 text-white cursor-wait'
                        : 'bg-green-500 hover:bg-green-600 text-white'
                  }`}
                >
                  <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  {isMarkedAsReviewed
                    ? <span className="hidden sm:inline">Marcada como Revisada ✓</span>
                    : isMarkingAsReviewed
                      ? <span className="hidden sm:inline">Marcando...</span>
                      : <span className="hidden sm:inline">Marcar como Revisada</span>
                  }
                  {isMarkedAsReviewed
                    ? <span className="sm:hidden">Revisada ✓</span>
                    : isMarkingAsReviewed
                      ? <span className="sm:hidden">Marcando...</span>
                      : <span className="sm:hidden">Marcar</span>
                  }
                </Button>
              </div>

              {/* Botão Próxima ou Finalizar */}
              <div className="flex-1 flex justify-end">
                {currentIndex !== undefined && totalQuestions !== undefined && currentIndex < totalQuestions - 1 ? (
                  onNext && (
                    <Button
                      onClick={onNext}
                      variant="outline"
                      size="sm"
                      className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                    >
                      <span className="hidden sm:inline">Próxima</span>
                      <span className="sm:hidden">Próx</span>
                      <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                    </Button>
                  )
                ) : (
                  onFinishSession && (
                    <Button
                      onClick={onFinishSession}
                      size="sm"
                      className="bg-blue-500 hover:bg-blue-600 text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                    >
                      Finalizar
                    </Button>
                  )
                )}
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};
