import { supabase } from '@/integrations/supabase/client';

/**
 * Utilitários para autenticação OAuth
 */

/**
 * Obter URL de redirecionamento baseada no ambiente
 */
export const getOAuthRedirectUrl = (): string => {
  const currentUrl = window.location.origin;
  
  // Para desenvolvimento (localhost com qualquer porta)
  if (currentUrl.includes('localhost')) {
    return `${currentUrl}/auth/callback`;
  }
  
  // Para produção (medevo.com.br)
  return 'https://medevo.com.br/auth/callback';
};

/**
 * Fazer login com Google OAuth
 */
export const signInWithGoogle = async () => {
  try {
    const redirectTo = getOAuthRedirectUrl();
    
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      throw error;
    }

    return { data, error: null };
  } catch (error) {
    console.error('Erro no login com Google:', error);
    return { data: null, error };
  }
};

/**
 * Processar callback OAuth e verificar perfil do usuário
 */
export const handleOAuthCallback = async () => {
  try {
    // Obter sessão atual
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      throw sessionError;
    }

    if (!session?.user) {
      throw new Error('Nenhuma sessão encontrada após OAuth');
    }

    // Verificar se o perfil existe
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, full_name, preparation_type, premium, premium_requested, created_at')
      .eq('id', session.user.id)
      .single();

    // Se não há perfil, criar um básico com dados do Google
    if (profileError && profileError.code === 'PGRST116') {
      const googleUserData = session.user.user_metadata;
      
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert({
          id: session.user.id,
          full_name: googleUserData.full_name || googleUserData.name || session.user.email?.split('@')[0] || 'Usuário',
          email: session.user.email,
          formation_area: 'Medicina', // Padrão para a plataforma
          graduation_year: new Date().getFullYear().toString(),
          is_professional: true,
          premium_requested: false,
          premium: false,
        })
        .select()
        .single();

      if (createError) {
        // ✅ SEGURANÇA: Log de erro sem expor dados sensíveis
        console.error('Erro ao criar perfil:', createError.message);
        throw createError;
      }

      return {
        user: session.user,
        profile: newProfile,
        isNewUser: true,
        redirectTo: '/onboarding'
      };
    }

    if (profileError) {
      throw profileError;
    }

    // Determinar redirecionamento baseado no perfil
    let redirectTo = '/plataformadeestudos';

    if (!profile.preparation_type) {
      redirectTo = '/onboarding';
    } else if (!profile.premium && !profile.premium_requested) {
      redirectTo = '/acesso-restrito';
    }

    return {
      user: session.user,
      profile,
      isNewUser: false,
      redirectTo
    };

  } catch (error) {
    console.error('Erro no callback OAuth:', error);
    return {
      user: null,
      profile: null,
      isNewUser: false,
      redirectTo: '/login',
      error
    };
  }
};

/**
 * Verificar se o usuário está autenticado via OAuth
 */
export const checkOAuthSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      throw error;
    }

    return {
      isAuthenticated: !!session?.user,
      user: session?.user || null,
      session,
      error: null
    };
  } catch (error) {
    return {
      isAuthenticated: false,
      user: null,
      session: null,
      error
    };
  }
};
