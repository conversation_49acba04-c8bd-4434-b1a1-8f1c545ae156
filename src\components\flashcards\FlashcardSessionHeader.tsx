
import React, { useState } from 'react';
import { Keyboard } from "lucide-react";
import { Button } from "@/components/ui/button";
import { LikeButtons } from "./LikeButtons";
import { KeyboardShortcutsToast } from "./KeyboardShortcutsToast";
import type { FlashcardWithHierarchy } from "@/components/collaborate/flashcards/types";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface FlashcardSessionHeaderProps {
  currentCard: FlashcardWithHierarchy;
  currentIndex: number;
  totalCards: number;
}

export const FlashcardSessionHeader = ({
  currentCard,
  currentIndex,
  totalCards
}: FlashcardSessionHeaderProps) => {
  const [showKeyboardDialog, setShowKeyboardDialog] = useState(false);

  const showKeyboardShortcuts = () => {
    setShowKeyboardDialog(true);
  };

  return (
    <>
      <div className="flex items-center justify-between gap-2 sm:gap-4 mb-6">
        <div className="flex items-center gap-2">
          <span
            className="inline-flex items-center gap-2
            bg-hackathon-yellow/10 text-black px-2 sm:px-3 py-1 rounded-full
            border-2 border-black shadow-sm hover:shadow-md transition-all"
          >
            <div className="w-2 h-2 bg-hackathon-yellow rounded-full animate-pulse" />
            <span className="text-xs sm:text-sm font-medium hidden sm:inline">Sessão em Progresso</span>
            <span className="text-xs font-medium sm:hidden">Ativa</span>
          </span>
        </div>

        <div className="flex items-center gap-1 sm:gap-2">
          {/* Botão de keyboard shortcuts - apenas desktop */}
          <Button
            variant="outline"
            size="icon"
            className="hidden sm:flex h-6 w-6 sm:h-8 sm:w-8 rounded-full border-2 border-black
            hover:bg-hackathon-yellow/10 transition-colors"
            onClick={showKeyboardShortcuts}
          >
            <Keyboard className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>

          {/* Botões de Like/Dislike/Report/Bury compactos */}
          <LikeButtons
            cardId={currentCard.id}
            initialLikes={currentCard.likes || 0}
            initialDislikes={currentCard.dislikes || 0}
            initialLikedBy={currentCard.liked_by || []}
            initialDislikedBy={currentCard.disliked_by || []}
            compact={true}
          />

          {/* Contador de progresso */}
          <div
            className="inline-flex items-center gap-2
            bg-hackathon-yellow/10 px-2 sm:px-3 py-1 rounded-full
            border-2 border-black shadow-sm hover:shadow-md transition-all"
          >
            <span className="text-xs sm:text-sm font-bold">
              {currentIndex + 1}/{totalCards}
            </span>
          </div>
        </div>
      </div>

      {/* Dialog de Atalhos do Teclado */}
      <Dialog open={showKeyboardDialog} onOpenChange={setShowKeyboardDialog}>
        <DialogContent className="sm:max-w-md max-w-[90vw] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Keyboard className="h-5 w-5" />
              Atalhos do Teclado
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <KeyboardShortcutsToast />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
