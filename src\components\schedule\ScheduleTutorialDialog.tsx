import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Sparkles, Target, CheckCircle, BookOpen, X, Calendar, Brain, Zap } from 'lucide-react';
import { motion } from 'framer-motion';

interface ScheduleTutorialDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: () => void;
  isLoading?: boolean;
}

export const ScheduleTutorialDialog: React.FC<ScheduleTutorialDialogProps> = ({
  open,
  onOpenChange,
  onComplete,
  isLoading = false
}) => {
  const [isCompleting, setIsCompleting] = React.useState(false);

  const handleComplete = async () => {
    if (isCompleting || isLoading) return; // ✅ CORREÇÃO: Evitar múltiplas chamadas

    setIsCompleting(true);
    try {
      await onComplete();
      console.log('✅ [ScheduleTutorialDialog] Tutorial completado, dando F5 na página');

      // ✅ SOLUÇÃO SIMPLES: Dar F5 na página para recarregar tudo
      window.location.reload();
    } catch (error) {
      console.error('❌ [ScheduleTutorialDialog] Erro ao completar tutorial:', error);
      setIsCompleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl w-[90vw] sm:w-full max-h-[85dvh] sm:max-h-[90dvh] overflow-hidden p-0 flex flex-col">
        {/* Header com gradiente - fixo */}
        <div className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white p-4 sm:p-6 rounded-t-lg flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="bg-white/20 p-2 sm:p-3 rounded-full">
                <Calendar className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
              <div>
                <DialogTitle className="text-lg sm:text-xl font-bold text-white">
                  🎯 Tutorial: Criando Seu Cronograma
                </DialogTitle>
                <p className="text-purple-100 text-xs sm:text-sm mt-1">
                  Aprenda a criar um cronograma personalizado em 3 passos simples!
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Conteúdo com scroll */}
        <div className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4 sm:space-y-6 pb-6">
          {/* Introdução */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-lg p-3 sm:p-4">
              <h3 className="font-bold text-purple-800 mb-2 flex items-center justify-center gap-2 text-sm sm:text-base">
                <Brain className="h-4 w-4 sm:h-5 sm:w-5" />
                Por que criar um cronograma?
              </h3>
              <p className="text-purple-700 text-xs sm:text-sm">
                Um cronograma personalizado organiza seus estudos baseado nas suas <strong>instituições alvo</strong>,
                cria <strong>revisões automáticas</strong> e te ajuda a estudar de forma <strong>eficiente e consistente</strong>!
              </p>
            </div>
          </div>

          {/* Passos */}
          <div className="space-y-3 sm:space-y-4">
            {/* Passo 1 */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="border-2 border-purple-200 bg-white">
                <CardContent className="p-3 sm:p-4">
                  <div className="flex items-start gap-3 sm:gap-4">
                    <div className="bg-purple-500 text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center font-bold text-sm sm:text-lg flex-shrink-0">
                      1
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-purple-800 mb-2 flex items-center gap-2 text-sm sm:text-base">
                        <Zap className="h-3 w-3 sm:h-4 sm:w-4" />
                        Clique em "Gerar com IA"
                      </h4>
                      <p className="text-purple-700 text-xs sm:text-sm mb-2 sm:mb-3">
                        O botão roxo <strong>"Gerar com IA"</strong> vai criar automaticamente um cronograma
                        baseado nas suas preferências já configuradas.
                      </p>
                      <div className="bg-purple-50 rounded-lg p-2 sm:p-3">
                        <div className="flex items-center gap-2 text-xs sm:text-sm text-purple-600">
                          <Target className="h-3 w-3 sm:h-4 sm:w-4" />
                          <span><strong>Suas instituições e horários já estão configurados!</strong></span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Passo 2 */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="border-2 border-purple-200 bg-white">
                <CardContent className="p-3 sm:p-4">
                  <div className="flex items-start gap-3 sm:gap-4">
                    <div className="bg-purple-500 text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center font-bold text-sm sm:text-lg flex-shrink-0">
                      2
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-purple-800 mb-2 flex items-center gap-2 text-sm sm:text-base">
                        <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                        Revise e Gere
                      </h4>
                      <p className="text-purple-700 text-xs sm:text-sm mb-2 sm:mb-3">
                        Revise suas configurações (já preenchidas automaticamente) e clique em
                        <strong> "Gerar Cronograma"</strong>. A IA organizará tudo por prioridade!
                      </p>
                      <div className="bg-purple-50 rounded-lg p-2 sm:p-3">
                        <div className="flex items-center gap-2 text-xs sm:text-sm text-purple-600">
                          <Brain className="h-3 w-3 sm:h-4 sm:w-4" />
                          <span><strong>Processo 100% automático e inteligente!</strong></span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Passo 3 */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card className="border-2 border-purple-200 bg-white">
                <CardContent className="p-3 sm:p-4">
                  <div className="flex items-start gap-3 sm:gap-4">
                    <div className="bg-purple-500 text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center font-bold text-sm sm:text-lg flex-shrink-0">
                      3
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-purple-800 mb-2 flex items-center gap-2 text-sm sm:text-base">
                        <BookOpen className="h-3 w-3 sm:h-4 sm:w-4" />
                        Use no Dia a Dia
                      </h4>
                      <p className="text-purple-700 text-xs sm:text-sm mb-2 sm:mb-3">
                        Depois de criado, os <strong>temas aparecerão na página inicial</strong> nos dias programados.
                        Você poderá praticar questões ou marcar como estudado com apenas um clique!
                      </p>
                      <div className="bg-purple-50 rounded-lg p-2 sm:p-3">
                        <div className="flex items-center gap-2 text-xs sm:text-sm text-purple-600">
                          <Sparkles className="h-3 w-3 sm:h-4 sm:w-4" />
                          <span><strong>Estudo organizado + Revisões automáticas!</strong></span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Call to Action Final */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg p-4 sm:p-6 text-white text-center"
          >
            <div className="flex items-center justify-center gap-2 mb-2 sm:mb-3">
              <Sparkles className="h-5 w-5 sm:h-6 sm:w-6" />
              <h3 className="text-base sm:text-lg font-bold">Pronto para começar?</h3>
            </div>
            <p className="text-purple-100 text-xs sm:text-sm mb-3 sm:mb-4">
              Agora clique no botão <strong>"Gerar com IA"</strong> na página para criar seu cronograma personalizado!
            </p>

            <Button
              onClick={handleComplete}
              disabled={isLoading || isCompleting}
              className="w-full bg-white text-purple-600 hover:bg-purple-50 font-bold py-3 sm:py-3 px-4 sm:px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base min-h-[44px]"
            >
              {(isLoading || isCompleting) ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-purple-600 mr-2"></div>
                  Salvando...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                  <span className="hidden sm:inline">🚀 Entendi! Vamos criar meu cronograma</span>
                  <span className="sm:hidden">🚀 Entendi! Criar cronograma</span>
                </>
              )}
            </Button>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
