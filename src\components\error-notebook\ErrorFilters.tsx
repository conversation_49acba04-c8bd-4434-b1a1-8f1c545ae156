import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Filter, 
  X, 
  Search, 
  Calendar,
  CheckCircle,
  Clock,
  RotateCcw
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@supabase/auth-helpers-react';

export interface ErrorFilters {
  specialty_id?: string;
  theme_id?: string;
  focus_id?: string;
  status?: 'all' | 'reviewed' | 'pending';
  dateFrom?: string;
  dateTo?: string;
  searchTerm?: string;
}

interface ErrorFiltersProps {
  filters: ErrorFilters;
  onFiltersChange: (filters: ErrorFilters) => void;
  onApplyFilters: () => void;
  totalResults: number;
  isLoading?: boolean;
  errorStats?: any; // 🚀 OTIMIZAÇÃO: Receber dados já carregados
}

interface FilterOption {
  id: string;
  name: string;
  total_count?: number;
  pending_count?: number;
}

export const ErrorFiltersComponent: React.FC<ErrorFiltersProps> = ({
  filters,
  onFiltersChange,
  onApplyFilters,
  totalResults,
  isLoading = false,
  errorStats
}) => {
  const user = useUser();
  const [specialties, setSpecialties] = useState<FilterOption[]>([]);
  const [themes, setThemes] = useState<FilterOption[]>([]);
  const [focuses, setFocuses] = useState<FilterOption[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(false);

  // Carregar opções de filtro
  useEffect(() => {
    if (user?.id) {
      loadFilterOptions();
    }
  }, [user?.id]);

  // Recarregar opções quando status mudar para atualizar contagens
  useEffect(() => {
    if (user?.id && (specialties.length > 0 || themes.length > 0 || focuses.length > 0)) {
      // Não recarregar se ainda não carregou as opções iniciais
      loadFilterOptions();
    }
  }, [filters.status]);

  const loadFilterOptions = async () => {
    if (!user?.id) return;

    try {
      setLoadingOptions(true);

      // 🎯 CONSULTA DINÂMICA: Calcular contagens reais baseadas no status pendente/revisado


      // Buscar todas as questões erradas do usuário com especialidades
      const { data: userAnswers, error: userAnswersError } = await supabase
        .from('user_answers')
        .select(`
          id,
          specialty_id,
          theme_id,
          focus_id,
          study_categories!user_answers_specialty_id_fkey(name),
          study_categories_theme:study_categories!user_answers_theme_id_fkey(name),
          study_categories_focus:study_categories!user_answers_focus_id_fkey(name)
        `)
        .eq('user_id', user.id)
        .eq('is_correct', false);

      if (userAnswersError) throw userAnswersError;

      // Buscar status de revisão
      const userAnswerIds = userAnswers?.map(ua => ua.id) || [];
      const { data: reviewedData, error: reviewedError } = await supabase
        .from('erros_meta')
        .select('user_answer_id, revisado')
        .eq('user_id', user.id)
        .in('user_answer_id', userAnswerIds);

      if (reviewedError) throw reviewedError;

      // Criar mapa de status de revisão
      const reviewedMap = new Map();
      reviewedData?.forEach(item => {
        reviewedMap.set(item.user_answer_id, item.revisado);
      });

      // Processar especialidades com contagens
      const specialtyMap = new Map<string, FilterOption>();
      userAnswers?.forEach(ua => {
        if (!ua.specialty_id || !ua.study_categories?.name) return;

        const isReviewed = reviewedMap.get(ua.id) || false;
        const key = ua.specialty_id;

        if (!specialtyMap.has(key)) {
          specialtyMap.set(key, {
            id: ua.specialty_id,
            name: ua.study_categories.name,
            total_count: 0,
            pending_count: 0
          });
        }

        const specialty = specialtyMap.get(key)!;
        specialty.total_count!++;
        if (!isReviewed) {
          specialty.pending_count!++;
        }
      });

      // Processar temas com contagens
      const themeMap = new Map<string, FilterOption>();
      userAnswers?.forEach(ua => {
        if (!ua.theme_id || !ua.study_categories_theme?.name) return;

        const isReviewed = reviewedMap.get(ua.id) || false;
        const key = ua.theme_id;

        if (!themeMap.has(key)) {
          themeMap.set(key, {
            id: ua.theme_id,
            name: ua.study_categories_theme.name,
            total_count: 0,
            pending_count: 0
          });
        }

        const theme = themeMap.get(key)!;
        theme.total_count!++;
        if (!isReviewed) {
          theme.pending_count!++;
        }
      });

      // Processar focos com contagens
      const focusMap = new Map<string, FilterOption>();
      userAnswers?.forEach(ua => {
        if (!ua.focus_id || !ua.study_categories_focus?.name) return;

        const isReviewed = reviewedMap.get(ua.id) || false;
        const key = ua.focus_id;

        if (!focusMap.has(key)) {
          focusMap.set(key, {
            id: ua.focus_id,
            name: ua.study_categories_focus.name,
            total_count: 0,
            pending_count: 0
          });
        }

        const focus = focusMap.get(key)!;
        focus.total_count!++;
        if (!isReviewed) {
          focus.pending_count!++;
        }
      });

      // 🎯 FILTRAR BASEADO NO STATUS SELECIONADO
      const filterByStatus = (items: FilterOption[]) => {
        return items.filter(item => {
          if (filters.status === 'pending') {
            return (item.pending_count || 0) > 0;
          } else if (filters.status === 'reviewed') {
            return (item.total_count || 0) - (item.pending_count || 0) > 0;
          }
          // 'all' ou undefined - mostrar todos que têm questões
          return (item.total_count || 0) > 0;
        });
      };

      const uniqueSpecialties = filterByStatus(Array.from(specialtyMap.values()))
        .sort((a, b) => {
          const countA = filters.status === 'pending' ? (a.pending_count || 0) : (a.total_count || 0);
          const countB = filters.status === 'pending' ? (b.pending_count || 0) : (b.total_count || 0);
          return countB - countA;
        });

      const uniqueThemes = filterByStatus(Array.from(themeMap.values()))
        .sort((a, b) => {
          const countA = filters.status === 'pending' ? (a.pending_count || 0) : (a.total_count || 0);
          const countB = filters.status === 'pending' ? (b.pending_count || 0) : (b.total_count || 0);
          return countB - countA;
        });

      const uniqueFocuses = filterByStatus(Array.from(focusMap.values()))
        .sort((a, b) => {
          const countA = filters.status === 'pending' ? (a.pending_count || 0) : (a.total_count || 0);
          const countB = filters.status === 'pending' ? (b.pending_count || 0) : (b.total_count || 0);
          return countB - countA;
        });

      setSpecialties(uniqueSpecialties);
      setThemes(uniqueThemes);
      setFocuses(uniqueFocuses);

    } catch (error) {
      console.error('Erro ao carregar opções de filtro:', error);
    } finally {
      setLoadingOptions(false);
    }
  };

  const handleFilterChange = (key: keyof ErrorFilters, value: string | undefined) => {
    // Converter "all" para undefined
    const processedValue = value === "all" ? undefined : value;
    const newFilters = { ...filters, [key]: processedValue };

    // Se mudou especialidade, limpar tema e foco
    if (key === 'specialty_id') {
      newFilters.theme_id = undefined;
      newFilters.focus_id = undefined;
    }

    // Se mudou tema, limpar foco
    if (key === 'theme_id') {
      newFilters.focus_id = undefined;
    }

    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  // 🎯 FILTRAR TEMAS BASEADO NA ESPECIALIDADE SELECIONADA
  const getFilteredThemes = () => {
    if (!filters.specialty_id) {
      return themes; // Se nenhuma especialidade selecionada, mostrar todos os temas
    }

    // Aqui precisaríamos de uma consulta adicional para buscar temas da especialidade
    // Por enquanto, retornar todos os temas (pode ser melhorado)
    return themes;
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value && value !== 'all').length;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'reviewed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-red-500" />;
      default:
        return <RotateCcw className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <Card className="p-6 bg-white border-2 border-black shadow-card-sm">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          <h3 className="text-lg font-bold">Filtros</h3>
          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary" className="text-xs">
              {getActiveFiltersCount()} ativo{getActiveFiltersCount() > 1 ? 's' : ''}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">
            {totalResults} resultado{totalResults !== 1 ? 's' : ''}
          </span>
          {getActiveFiltersCount() > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearFilters}
              className="text-xs"
            >
              <X className="h-3 w-3 mr-1" />
              Limpar
            </Button>
          )}
        </div>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        {/* Status */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Status</Label>
          <Select
            value={filters.status || 'all'}
            onValueChange={(value) => handleFilterChange('status', value)}
          >
            <SelectTrigger className="border-2 border-gray-300">
              <SelectValue placeholder="Todos os status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                <div className="flex items-center gap-2">
                  <RotateCcw className="h-4 w-4 text-blue-500" />
                  Todos
                </div>
              </SelectItem>
              <SelectItem value="pending">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-red-500" />
                  Pendentes
                </div>
              </SelectItem>
              <SelectItem value="reviewed">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Revisadas
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Especialidade */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Especialidade</Label>
          <Select
            value={filters.specialty_id || ''}
            onValueChange={(value) => handleFilterChange('specialty_id', value || undefined)}
            disabled={loadingOptions}
          >
            <SelectTrigger className="border-2 border-gray-300">
              <SelectValue placeholder="Todas as especialidades" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas as especialidades</SelectItem>
              {specialties
                .filter(specialty => {
                  if (filters.status === 'pending') {
                    return (specialty.pending_count || 0) > 0;
                  } else if (filters.status === 'reviewed') {
                    return (specialty.total_count || 0) - (specialty.pending_count || 0) > 0;
                  }
                  return (specialty.total_count || 0) > 0;
                })
                .map((specialty) => {
                  const count = filters.status === 'pending'
                    ? specialty.pending_count
                    : filters.status === 'reviewed'
                    ? (specialty.total_count || 0) - (specialty.pending_count || 0)
                    : specialty.total_count;

                  return (
                    <SelectItem key={specialty.id} value={specialty.id}>
                      {specialty.name} ({count})
                    </SelectItem>
                  );
                })}
            </SelectContent>
          </Select>
        </div>

        {/* Tema */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Tema</Label>
          <Select
            value={filters.theme_id || ''}
            onValueChange={(value) => handleFilterChange('theme_id', value || undefined)}
            disabled={loadingOptions}
          >
            <SelectTrigger className="border-2 border-gray-300">
              <SelectValue placeholder="Todos os temas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os temas</SelectItem>
              {themes
                .filter(theme => {
                  if (filters.status === 'pending') {
                    return (theme.pending_count || 0) > 0;
                  } else if (filters.status === 'reviewed') {
                    return (theme.total_count || 0) - (theme.pending_count || 0) > 0;
                  }
                  return (theme.total_count || 0) > 0;
                })
                .map((theme) => {
                  const count = filters.status === 'pending'
                    ? theme.pending_count
                    : filters.status === 'reviewed'
                    ? (theme.total_count || 0) - (theme.pending_count || 0)
                    : theme.total_count;

                  return (
                    <SelectItem key={theme.id} value={theme.id}>
                      {theme.name} ({count})
                    </SelectItem>
                  );
                })}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Busca por texto */}
      <div className="space-y-2 mb-4">
        <Label className="text-sm font-medium">Buscar no conteúdo</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Digite palavras-chave da questão..."
            value={filters.searchTerm || ''}
            onChange={(e) => handleFilterChange('searchTerm', e.target.value || undefined)}
            className="pl-10 border-2 border-gray-300"
          />
        </div>
      </div>

      {/* Botão Aplicar */}
      <Button
        onClick={onApplyFilters}
        disabled={isLoading}
        className="w-full bg-red-500 hover:bg-red-600 text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
      >
        {isLoading ? (
          <>
            <RotateCcw className="h-4 w-4 mr-2 animate-spin" />
            Aplicando...
          </>
        ) : (
          <>
            <Filter className="h-4 w-4 mr-2" />
            Aplicar Filtros
          </>
        )}
      </Button>
    </Card>
  );
};
