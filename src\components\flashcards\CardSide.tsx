import { FlashcardImage } from "./FlashcardImage";

interface CardSideProps {
  content: string;
  image?: string | null;
  isImageOpen: boolean;
  onImageOpenChange: (open: boolean) => void;
  actionText: string;
  isVisible: boolean;
  className?: string;
  flashcardType?: string;
}

export const CardSide = ({
  content,
  image,
  isImageOpen,
  onImageOpenChange,
  actionText,
  isVisible,
  className = "",
  flashcardType = "qa",
}: CardSideProps) => {
  // Remover "Categoria:" do conteúdo principal, se presente
  const cleanContent = content.replace(/\n*Categoria:.*(\n|$)/g, "").trim();

  // Get flashcard type display text
  const getFlashcardTypeText = (type: string) => {
    switch (type) {
      case 'vf': return 'V/F';
      case 'multipla': return 'Múltipla Escolha';
      case 'cloze': return 'Cloze';
      case 'qa': return 'Q&A';
      default: return 'Q&A';
    }
  };

  // Detect content characteristics for dynamic styling
  const isLongContent = cleanContent.length > 300;
  const hasMultipleChoice = /[A-E]\)/.test(cleanContent);
  const isVeryLong = cleanContent.length > 600;

  // Dynamic styling based on content length and type
  const getTextStyles = () => {
    let baseClasses = "px-2 sm:px-4 whitespace-pre-line leading-relaxed";

    if (isVeryLong) {
      // Very long content: smaller font, left align, tighter spacing
      return `${baseClasses} text-sm text-left leading-snug`;
    } else if (isLongContent || hasMultipleChoice) {
      // Long content or multiple choice: medium font, left align
      return `${baseClasses} text-base text-left leading-normal`;
    } else {
      // Normal content: large font, center align
      return `${baseClasses} text-lg text-center`;
    }
  };

  const formattedContent = cleanContent.split('\n').map((line, index) => (
    <span key={index}>
      {line}
      {index < cleanContent.split('\n').length - 1 && <br />}
    </span>
  ));
  
  return (
    <div 
      className={`
        absolute inset-0 w-full h-full
        backface-hidden 
        ${!isVisible ? 'invisible' : ''}
        ${className}
        bg-white rounded-lg
        transform-gpu
      `}
    >
      <div className="flex flex-col justify-between h-full">
        {/* Flashcard type badge */}
        <div className="absolute top-2 left-2 z-10">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 opacity-70">
            {getFlashcardTypeText(flashcardType)}
          </span>
        </div>

        <div className={`flex-1 flex flex-col justify-center ${isVeryLong ? 'space-y-2 py-2' : isLongContent ? 'space-y-4 py-3' : 'space-y-6 py-4'}`}>
          <p className={getTextStyles()}>{cleanContent}</p>
          {image && (
            <div className="flex justify-center items-center py-2">
              <FlashcardImage 
                src={image} 
                alt="Card image" 
                isOpen={isImageOpen}
                onOpenChange={onImageOpenChange}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
