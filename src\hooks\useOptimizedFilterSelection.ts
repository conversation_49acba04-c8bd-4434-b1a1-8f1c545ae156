import { useState, useCallback, useEffect, useRef } from 'react';
import { useQueryClient, useQuery } from '@tanstack/react-query';
import type { SelectedFilters } from '@/types/question';
import { useDomain } from '@/hooks/useDomain';
import { useQuestionCount } from '@/hooks/useQuestionCount';
import { supabase } from '@/integrations/supabase/client';

interface OptimizedFilterSelectionOptions {
  initialFilters: SelectedFilters;
  onFiltersChange?: (filters: SelectedFilters) => void;
  debounceMs?: number;
}

export const useOptimizedFilterSelection = ({
  initialFilters,
  onFiltersChange,
  debounceMs = 300
}: OptimizedFilterSelectionOptions) => {
  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>(initialFilters);
  const [isUpdating, setIsUpdating] = useState(false);
  const { domain, isReady } = useDomain();
  const queryClient = useQueryClient();
  const updateTimeoutRef = useRef<NodeJS.Timeout>();

  // Usar o hook otimizado de contagem
  const { count: questionCount, isFetching } = useQuestionCount({
    filters: selectedFilters,
    debounceMs,
    enabled: isReady && !!domain
  });

  // Monitorar execução da query de contagem (sem logs)
  useEffect(() => {
    // Query monitoring without console logs
  }, [isFetching, selectedFilters, domain]);



  // Função para atualizar filtros instantaneamente (sem delay visual)
  const handleFilterToggle = useCallback((id: string, type: 'specialty' | 'theme' | 'focus' | 'location' | 'year' | 'question_type' | 'question_format') => {
    const filterKeyMap = {
      specialty: 'specialties',
      theme: 'themes',
      focus: 'focuses',
      location: 'locations',
      year: 'years',
      question_type: 'question_types',
      question_format: 'question_formats'
    };

    const filterKey = filterKeyMap[type] as keyof SelectedFilters;

    setSelectedFilters(prev => {
      const currentFilters = [...(prev[filterKey] || [])];
      const isSelected = currentFilters.includes(id);

      const newFilters = {
        ...prev,
        [filterKey]: isSelected
          ? currentFilters.filter(item => item !== id)
          : [...currentFilters, id]
      };

      // Chamar callback imediatamente para atualizar UI
      if (onFiltersChange) {
        onFiltersChange(newFilters);
      }

      return newFilters;
    });

    // Indicar que está atualizando (mas sem bloquear UI)
    setIsUpdating(true);

    // Limpar timeout anterior se existir
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    // Definir que a atualização terminou após um tempo
    updateTimeoutRef.current = setTimeout(() => {
      setIsUpdating(false);
    }, debounceMs + 100);

  }, [onFiltersChange, debounceMs]);

  // Prefetch inteligente - invalidar cache quando filtros mudam significativamente
  useEffect(() => {
    const hasSignificantChange = Object.values(selectedFilters).some(f =>
      Array.isArray(f) && f.length > 0
    );

    if (hasSignificantChange && isReady && domain) {
      // Não precisamos invalidar queries aqui, pois o useQuestionCount já gerencia isso automaticamente
      // As queries são reativas aos filtros e se atualizam quando necessário
      // console.log('🔄 [useOptimizedFilterSelection] Filtros alterados, mas não invalidando queries desnecessariamente');
    }
  }, [selectedFilters, isReady, domain, queryClient]);

  // Sincronizar filtros quando initialFilters mudam (incluindo excludeAnswered)
  useEffect(() => {
    setSelectedFilters(initialFilters);
  }, [initialFilters]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  return {
    selectedFilters,
    setSelectedFilters,
    handleFilterToggle,
    questionCount,
    isUpdating: isUpdating || isFetching,
    isFetching // Para mostrar indicador sutil de carregamento
  };
};

// Hook auxiliar para contagens individuais de filtros (usado nos badges)
export const useFilterItemCounts = (
  selectedFilters: SelectedFilters,
  itemId: string,
  itemType: 'specialty' | 'theme' | 'focus' | 'location' | 'question_format'
) => {
  // Criar filtros temporários incluindo apenas este item
  const tempFilters = {
    specialties: itemType === 'specialty' ? [itemId] : [],
    themes: itemType === 'theme' ? [itemId] : [],
    focuses: itemType === 'focus' ? [itemId] : [],
    locations: itemType === 'location' ? [itemId] : [],
    years: [],
    question_types: [],
    question_formats: itemType === 'question_format' ? [itemId] : []
  };

  // Usar o hook otimizado de contagem
  const { count } = useQuestionCount({
    filters: tempFilters,
    debounceMs: 100, // Mais rápido para contagens individuais
    enabled: !!itemId
  });

  return {
    data: count,
    isLoading: false, // O hook interno já gerencia o loading
    error: null
  };
};

// Hook para contagens hierárquicas de filtros usando lógica frontend
export const useHierarchicalFilterCounts = (
  selectedFilters: SelectedFilters,
  targetLevel: 'locations' | 'years' | 'formats'
) => {
  const { domain } = useDomain();

  return useQuery({
    queryKey: [
      'hierarchical-filter-counts-v2',
      targetLevel,
      domain,
      // ✅ OTIMIZAÇÃO: Criar chave mais específica para evitar duplicações
      selectedFilters.specialties?.sort().join(',') || 'none',
      selectedFilters.themes?.sort().join(',') || 'none',
      selectedFilters.focuses?.sort().join(',') || 'none',
      selectedFilters.locations?.sort().join(',') || 'none',
      selectedFilters.years?.sort().join(',') || 'none'
    ],
    queryFn: async () => {
      console.log(`🔄 [useHierarchicalFilterCounts] Executando query para ${targetLevel}:`, {
        specialties: selectedFilters.specialties?.length || 0,
        themes: selectedFilters.themes?.length || 0,
        focuses: selectedFilters.focuses?.length || 0,
        locations: selectedFilters.locations?.length || 0,
        years: selectedFilters.years?.length || 0
      });

      try {
        // Verificar se há filtros de categoria ativos
        const hasCategoryFilters = (
          (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
          (selectedFilters.themes && selectedFilters.themes.length > 0) ||
          (selectedFilters.focuses && selectedFilters.focuses.length > 0)
        );

        // Se não há filtros de categoria, retornar vazio (usar contagens normais)
        if (!hasCategoryFilters && targetLevel === 'locations') {
          return {};
        }

        // Para anos, verificar se há filtros de categoria ou localização
        const hasLocationFilters = selectedFilters.locations && selectedFilters.locations.length > 0;
        if (!hasCategoryFilters && !hasLocationFilters && targetLevel === 'years') {
          return {};
        }

        // Para formatos, verificar se há filtros anteriores
        const hasYearFilters = selectedFilters.years && selectedFilters.years.length > 0;
        if (!hasCategoryFilters && !hasLocationFilters && !hasYearFilters && targetLevel === 'formats') {
          return {};
        }

        // ✅ REATIVADO: Calcular contagens hierárquicas para anos e formatos

        // Construir query base
        let query = supabase
          .from('questions')
          .select(targetLevel === 'locations' ? 'exam_location' :
                 targetLevel === 'years' ? 'exam_year' : 'question_format')
          .eq('knowledge_domain', domain || 'residencia');

        // Aplicar filtros de categoria (OR logic)
        if (hasCategoryFilters) {
          const categoryConditions = [];

          if (selectedFilters.specialties && selectedFilters.specialties.length > 0) {
            categoryConditions.push(`specialty_id.in.(${selectedFilters.specialties.join(',')})`);
          }

          if (selectedFilters.themes && selectedFilters.themes.length > 0) {
            categoryConditions.push(`theme_id.in.(${selectedFilters.themes.join(',')})`);
          }

          if (selectedFilters.focuses && selectedFilters.focuses.length > 0) {
            categoryConditions.push(`focus_id.in.(${selectedFilters.focuses.join(',')})`);
          }

          if (categoryConditions.length > 0) {
            query = query.or(categoryConditions.join(','));
          }
        }

        // Aplicar filtros de localização (para anos e formatos)
        if ((targetLevel === 'years' || targetLevel === 'formats') && hasLocationFilters) {
          // ✅ CORREÇÃO: Limitar locations para evitar URLs longas
          if (selectedFilters.locations.length > 30) {
            console.warn(`⚠️ [useOptimizedFilterSelection] Muitas locations (${selectedFilters.locations.length}), limitando para 30`);
            query = query.in('exam_location', selectedFilters.locations.slice(0, 30));
          } else {
            query = query.in('exam_location', selectedFilters.locations);
          }
        }

        // Aplicar filtros de ano (para formatos)
        if (targetLevel === 'formats' && hasYearFilters) {
          query = query.in('exam_year', selectedFilters.years.map(y => parseInt(y)));
        }

        const { data, error } = await query;

        if (error) {
          throw error;
        }

        // Contar por campo específico
        const counts: { [key: string]: number } = {};

        if (data) {
          data.forEach(row => {
            let key: string;

            if (targetLevel === 'locations') {
              key = row.exam_location;
            } else if (targetLevel === 'years') {
              key = row.exam_year?.toString();
            } else if (targetLevel === 'formats') {
              key = row.question_format;
            }

            if (key) {
              counts[key] = (counts[key] || 0) + 1;
            }
          });
        }

        return counts;
      } catch (err) {
        return {};
      }
    },
    enabled: !!domain,
    staleTime: 5 * 60 * 1000, // 5 minutos - cache mais longo
    gcTime: 30 * 60 * 1000, // 30 minutos - manter cache por muito tempo
    retry: 1,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    // ✅ OTIMIZAÇÃO: Evitar refetch desnecessários
    refetchInterval: false,
    refetchIntervalInBackground: false,
  });
};
