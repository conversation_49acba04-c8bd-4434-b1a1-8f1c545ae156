import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Settings, 
  User, 
  LogOut, 
  Shield, 
  ChevronRight,
  Calendar,
  Mail,
  GraduationCap
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useSharedProfile } from "@/hooks/useSharedProfile";

interface UserProfileCardProps {
  className?: string;
}

const UserProfileCard = ({ className = "" }: UserProfileCardProps) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { data: profile, isLoading } = useSharedProfile();
  const { toast } = useToast();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const isPremium = profile?.premium || false;
  const isPremiumRequested = profile?.premium_requested || false;
  const isAdmin = profile?.is_admin || false;

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await supabase.auth.signOut();

      toast({
        title: "Logout realizado com sucesso",
        description: "Até logo! Volte sempre.",
        className: "bg-hackathon-yellow/20 border-hackathon-yellow text-gray-800",
      });

      navigate("/");
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erro ao fazer logout",
        description: "Tente novamente.",
      });
    } finally {
      setIsLoggingOut(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (isLoading) {
    return (
      <div className={`bg-white border-2 border-black rounded-xl p-4 shadow-card ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border-2 border-black rounded-xl p-4 shadow-card ${className}`}>
      {/* Header do perfil */}
      <div className="flex items-center gap-3 mb-4">
        <Avatar className="h-12 w-12 border-2 border-black">
          <AvatarImage
            src={profile?.avatar_url}
            alt={profile?.full_name || user?.email || "Usuário"}
          />
          <AvatarFallback className="bg-hackathon-yellow text-black font-bold">
            {profile?.full_name ? getInitials(profile.full_name) :
             user?.email ? getInitials(user.email) : "U"}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <h3 className="font-bold text-gray-900 truncate">
            {profile?.full_name || user?.email?.split('@')[0] || "Usuário"}
          </h3>
          <p className="text-sm text-gray-600 truncate">
            {user?.email}
          </p>
        </div>
      </div>

      {/* Informações do usuário */}
      {profile && (
        <div className="space-y-2 mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
          {profile.formation_area && (
            <div className="flex items-center gap-2 text-sm">
              <GraduationCap className="h-4 w-4 text-gray-500" />
              <span className="text-gray-700">{profile.formation_area}</span>
            </div>
          )}

          {profile.graduation_year && (
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-gray-700">Formado em {profile.graduation_year}</span>
            </div>
          )}

          {user?.created_at && (
            <div className="flex items-center gap-2 text-sm">
              <Mail className="h-4 w-4 text-gray-500" />
              <span className="text-gray-700">Membro desde {formatDate(user.created_at)}</span>
            </div>
          )}
        </div>
      )}

      {/* Status do usuário */}
      <div className="mb-4">
        {isPremium ? (
          <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg">
            <Shield className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-700">Acesso Premium</span>
          </div>
        ) : isPremiumRequested ? (
          <div className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
            <Shield className="h-4 w-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-700">Acesso Solicitado</span>
          </div>
        ) : (
          <div className="flex items-center gap-2 p-2 bg-gray-50 border border-gray-200 rounded-lg">
            <Shield className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-600">Acesso Básico</span>
          </div>
        )}
      </div>

      {/* Ações do usuário */}
      <div className="space-y-2">
        <Button
          variant="outline"
          className="w-full justify-between border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all"
          onClick={() => navigate("/settings")}
        >
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span>Configurações</span>
          </div>
          <ChevronRight className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          className="w-full justify-between border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all"
          onClick={() => navigate("/progress")}
        >
          <div className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span>Meu Progresso</span>
          </div>
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Botão Admin (se aplicável) */}
        {isAdmin && (
          <Button
            variant="outline"
            className="w-full justify-between border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all"
            onClick={() => navigate("/admin")}
          >
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span>Painel Admin</span>
            </div>
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}

        <Button
          variant="outline"
          className="w-full justify-between border-2 border-red-300 hover:border-red-500 hover:bg-red-50 text-red-600 hover:text-red-700 transition-all"
          onClick={handleLogout}
          disabled={isLoggingOut}
        >
          <div className="flex items-center gap-2">
            <LogOut className="h-4 w-4" />
            <span>{isLoggingOut ? "Saindo..." : "Sair"}</span>
          </div>
        </Button>
      </div>
    </div>
  );
};

export default UserProfileCard;
