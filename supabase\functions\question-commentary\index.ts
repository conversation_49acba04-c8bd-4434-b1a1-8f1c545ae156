import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Function to get all available Gemini API keys
const getGeminiApiKeys = (): string[] => {
  const keys: string[] = [];

  // Primeira chave (sem número)
  const primaryKey = Deno.env.get('GEMINI_API_KEY');
  if (primaryKey) {
    keys.push(primaryKey);
  }

  // Chaves numeradas (GEMINI_API_KEY2, GEMINI_API_KEY3, etc.)
  for (let i = 2; i <= 10; i++) {
    const keyName = `GEMINI_API_KEY${i}`;
    const key = Deno.env.get(keyName);
    if (key) {
      keys.push(key);
    }
  }

  console.log(`🔑 [question-commentary] Encontradas ${keys.length} chaves API disponíveis`);
  return keys;
};

// Simple in-memory cache for rate limiting tracking
const keyUsageTracker = new Map<string, number>();

// Function to check if a key was used recently (within last 5 seconds)
const isKeyRecentlyUsed = (keyName: string): boolean => {
  const lastUsed = keyUsageTracker.get(keyName);
  if (!lastUsed) return false;

  const now = Date.now();
  const timeDiff = now - lastUsed;
  return timeDiff < 5000; // 5 seconds cooldown
};

// Function to mark a key as used
const markKeyAsUsed = (keyName: string): void => {
  keyUsageTracker.set(keyName, Date.now());
};

// Function to make Gemini API call with fallback system
const callGeminiWithFallback = async (requestBody: any): Promise<{ response: Response; keyUsed: string }> => {
  const apiKeys = getGeminiApiKeys();

  if (apiKeys.length === 0) {
    throw new Error('No Gemini API keys found');
  }

  let lastError: Error | null = null;

  for (let i = 0; i < apiKeys.length; i++) {
    const apiKey = apiKeys[i];
    const keyName = i === 0 ? 'GEMINI_API_KEY' : `GEMINI_API_KEY${i + 1}`;

    // Check if this key was used recently
    if (isKeyRecentlyUsed(keyName)) {
      continue;
    }

    console.log(`🔑 [question-commentary] Tentando ${keyName} (${i + 1}/${apiKeys.length})`);

    try {
      const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": apiKey
        },
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        console.log(`✅ [question-commentary] Sucesso com ${keyName}`);
        markKeyAsUsed(keyName);
        return { response, keyUsed: keyName };
      } else {
        const errorText = await response.text();
        console.warn(`⚠️ [question-commentary] ${keyName} falhou: ${response.status}`);
        lastError = new Error(`API key ${keyName} failed: ${response.status} ${response.statusText}`);
        markKeyAsUsed(keyName);

        // Se for rate limiting (429), aguarda um pouco antes da próxima tentativa
        if (response.status === 429) {
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    } catch (error) {
      console.warn(`⚠️ [question-commentary] Erro de rede com ${keyName}`);
      markKeyAsUsed(keyName); // Mark key as used even on network error
      lastError = error instanceof Error ? error : new Error(String(error));
    }
  }

  // Se chegou aqui, todas as chaves falharam
  console.error(`❌ [question-commentary] Todas as ${apiKeys.length} chaves Gemini falharam`);
  throw lastError || new Error('All Gemini API keys failed');
};
// Função para determinar o origin permitido baseado na requisição
const getAllowedOrigin = (request)=>{
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://medevo.com.br',
    'https://www.medevo.com.br',
    'http://localhost:5173',
    'http://localhost:800',
    'http://localhost:3000',
    'http://127.0.0.1:800',
    'http://127.0.0.1:5173',
    'http://pedb.com.br',
    'https://pedb.com.br',
    'https://www.pedb.com.br',
    'http://localhost:8080'
  ];
  if (origin && allowedOrigins.includes(origin)) {
    return origin;
  }
  // Em desenvolvimento, permitir qualquer localhost
  if (origin && (origin.includes('localhost') || origin.includes('127.0.0.1'))) {
    return origin;
  }
  return 'https://medevo.com.br'; // fallback
};
const getCorsHeaders = (request)=>({
    'Access-Control-Allow-Origin': getAllowedOrigin(request),
    'Access-Control-Allow-Headers': 'authorization, content-type, x-client-info, apikey',
    'Access-Control-Allow-Methods': 'POST, OPTIONS, GET',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400'
  });
serve(async (req)=>{
  const corsHeaders = getCorsHeaders(req);
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders,
      status: 200
    });
  }
  try {
    const { statement, alternatives, correctAnswer, specialty, mediaAttachments } = await req.json();
    // 🔍 DETECTAR TIPO DE QUESTÃO
    const isVerdadeiroFalsoQuestion = statement.includes('VERDADEIRO (V) ou FALSO (F)') || statement.includes('assinale VERDADEIRO') || statement.includes('V) ou FALSO (F)') || statement.includes('Está CORRETO:') || alternatives.some((alt)=>alt.match(/^[A-E]\s*[VF,\s]+$/));

    // Enhanced prompt for thinking mode
    const systemPrompt = `
Você é um professor experiente de residência médica, especializado na área da questão (como Clínica Médica, Pediatria, Cirurgia, Ginecologia, etc.).

**MODO THINKING ATIVADO:** Pense como um médico especialista. Indo ao ponto faça uma análise precisa. A ideia é entregar o essencial para um estudante que está se preparando para uma residência, então ele precisa estudar e não quer ficar lendo muita baboseira, então seja direto, você pensa para dar uma boa resposta, mas responde apenas o essencial, sempre que possível siga guidelines e protocolos brasileiros de forma estruturada antes de responder.

${isVerdadeiroFalsoQuestion ? 'Sua tarefa é analisar uma questão de VERDADEIRO ou FALSO onde cada afirmativa do enunciado deve ser classificada como V ou F, e as alternativas A, B, C, D representam diferentes sequências de V/F. IMPORTANTE: Analise cada afirmativa do enunciado individualmente e depois compare com as alternativas para identificar qual sequência está correta.' : 'Sua tarefa é analisar uma questão de múltipla escolha e fornecer comentários educativos detalhados para cada alternativa, além de um comentário final sobre o tema.'}

**INSTRUÇÕES IMPORTANTES:**
1. PRIMEIRO: Analise o contexto clínico e identifique os conceitos-chave
2. SE HOUVER IMAGENS: Analise cuidadosamente todas as imagens fornecidas e integre suas observações na análise relacionando com a questão/gabarito
3. ${isVerdadeiroFalsoQuestion ? 'QUESTÃO V/F: OBRIGATÓRIO - Para CADA alternativa (A, B, C, D), você DEVE explicar especificamente por que aquela sequência de V/F está correta ou incorreta. NÃO coloque toda a análise apenas na alternativa correta. Cada alternativa deve ter seu próprio comentário explicando por que sua sequência específica está certa ou errada.' : 'Para cada alternativa, explique POR QUE está correta ou incorreta com base em evidências'}
4. Use linguagem didática, como se estivesse ensinando um residente
5. Inclua dicas práticas e "pegadinhas" comuns *EXTREMAMENTE IMPORTANTE NÃO FICAR REPETITIVO, APENAS ONDE FOR ESSENCIAL, CASO CONTRÁRIO NÃO HÁ NECESSIDADE*
6. Use expressões como "Presta atenção nesse detalhe!", "Segura essa dica que vale ouro!", "Essa é uma típica casca de banana!", como no de cima, sem abusar.
7. Seja específico sobre conceitos médicos, guidelines atuais e protocolos quando necessário, para que não fique repetitivo.
8. No comentário final, faça uma síntese educativa do tema
9. Explique sua linha de raciocínio sem tomar muito espaço, apenas se for necessário ntes da resposta final
10. IMAGENS: Se houver imagens, descreva os achados relevantes e como eles se relacionam com a questão
11. Para o inicio de cada comentário de alternativa: Não precisa iniciar o comentário de cada alternativa com esta alternativa está correta/incorreta, pois isso só toma espaço desnecessário, já comece do seu comentário, pois já sei qual está correta.

**FORMATO DE RESPOSTA OBRIGATÓRIO:**
Você DEVE responder APENAS com um JSON válido no seguinte formato exato:

{
  "alternativas": [
    {
      "texto": "texto da alternativa",
      "comentario": "${isVerdadeiroFalsoQuestion ? 'Para questões V/F: Explique especificamente por que ESTA sequência de V/F está correta ou incorreta. Compare esta sequência com a análise das afirmativas do enunciado. NÃO repita a análise completa das afirmativas aqui.' : 'explicação detalhada com raciocínio clínico'}",
      "correta": true/false
    }
  ],
  "comentario_final": "${isVerdadeiroFalsoQuestion ? 'Para questões V/F: Faça aqui a análise detalhada de CADA afirmativa do enunciado (1ª afirmativa: V ou F e por quê, 2ª afirmativa: V ou F e por quê, etc.) e depois explique qual sequência está correta.' : 'síntese educativa do tema com raciocínio estruturado'}",
  "possivel_erro_no_gabarito": false,
  "justificativa_erro_gabarito": ""
}

**ATENÇÃO CRÍTICA:**
- Responda APENAS com o JSON válido
- NÃO inclua texto antes ou depois do JSON
- NÃO use markdown com três acentos graves
- NÃO inclua explicações fora do JSON
- O JSON deve ser válido e parseável
- Use asteriscos duplos para destacar palavras-chave importantes
- Use hífen ou números para listas, NÃO use asteriscos simples
- Estruture o texto de forma clara e organizada
`;
    // Preparar conteúdo da mensagem (texto + imagens se houver)
    const messageContent = [];
    // Adicionar texto principal
    const textContent = `
Especialidade: ${specialty}

Questão:
${statement}

Alternativas:
${alternatives.map((alt, i)=>`${i + 1}. ${alt}`).join('\n')}

Alternativa considerada correta (gabarito oficial): ${correctAnswer + 1}
(Índice ${correctAnswer} = Alternativa "${alternatives[correctAnswer]}")

Responda APENAS com o JSON válido conforme especificado no sistema.
`;
    messageContent.push({
      text: textContent
    });
    // Adicionar imagens se houver
    if (mediaAttachments && Array.isArray(mediaAttachments) && mediaAttachments.length > 0) {
      for (const imageUrl of mediaAttachments){
        try {
          // Fazer fetch da imagem para converter em base64
          const imageResponse = await fetch(imageUrl);
          if (imageResponse.ok) {
            const imageBuffer = await imageResponse.arrayBuffer();
            const base64Image = btoa(String.fromCharCode(...new Uint8Array(imageBuffer)));
            // Detectar tipo MIME da imagem
            const mimeType = imageResponse.headers.get('content-type') || 'image/jpeg';
            messageContent.push({
              inline_data: {
                mime_type: mimeType,
                data: base64Image
              }
            });
          }
        } catch (imageError) {}
      }
    }
    let completionText = "";
    let apiUsed = "";
    // Get API keys from environment
    const openAiKey = Deno.env.get("OPENAI_API_KEY");

    // ✅ NOVO: Sistema de fallback com múltiplas chaves Gemini
    const geminiApiKeys = getGeminiApiKeys();

    // FORCE Gemini 2.5 Flash as primary with thinking mode - try all available keys
    if (geminiApiKeys.length > 0) {
      try {
        const requestBody = {
          contents: [
            {
              role: "user",
              parts: [
                {
                  text: systemPrompt
                },
                ...messageContent
              ]
            }
          ],
          generationConfig: {
            temperature: 0.5,
            maxOutputTokens: 8192,
            topP: 0.9
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_ONLY_HIGH"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_ONLY_HIGH"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_ONLY_HIGH"
            },
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_ONLY_HIGH"
            }
          ]
        };

        // ✅ NOVO: Usar sistema de fallback com múltiplas chaves
        const { response: geminiResponse, keyUsed } = await callGeminiWithFallback(requestBody);

        const geminiData = await geminiResponse.json();
        completionText = geminiData.candidates[0].content.parts[0].text;
        apiUsed = `Gemini 2.5 Flash (thinking mode) - ${keyUsed}`;

      } catch (geminiError) {
        console.error('❌ [question-commentary] Todas as chaves Gemini falharam');
        // ✅ MELHORADO: Se todas as chaves Gemini falharam, tentar OpenAI como fallback
        if (openAiKey) {
          console.log('🔄 [question-commentary] Tentando OpenAI como fallback');
          try {
            const response = await fetch("https://api.openai.com/v1/chat/completions", {
              method: "POST",
              headers: {
                "Authorization": `Bearer ${openAiKey}`,
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                model: "gpt-4o-mini",
                messages: [
                  {
                    role: "system",
                    content: systemPrompt
                  },
                  {
                    role: "user",
                    content: textContent
                  }
                ],
                temperature: 0.2,
                max_tokens: 2500
              })
            });
            if (!response.ok) {
              throw new Error("Serviço de análise temporariamente indisponível");
            }
            const data = await response.json();
            completionText = data.choices[0].message.content;
            apiUsed = "OpenAI (fallback após falha Gemini)";
            console.log('✅ [question-commentary] OpenAI fallback bem-sucedido');
          } catch (openAiError) {
            console.error('❌ [question-commentary] OpenAI fallback também falhou');
            throw new Error("Serviço de análise temporariamente indisponível");
          }
        } else {
          throw new Error("Serviço de análise temporariamente indisponível");
        }
      }
    } else if (openAiKey) {
      // ✅ MELHORADO: Use OpenAI directly ONLY if no Gemini keys configured
      console.log('🔄 [question-commentary] Usando OpenAI diretamente');
      try {
        const response = await fetch("https://api.openai.com/v1/chat/completions", {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${openAiKey}`,
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            model: "gpt-4o-mini",
            messages: [
              {
                role: "system",
                content: systemPrompt
              },
              {
                role: "user",
                content: textContent
              }
            ],
            temperature: 0.2,
            max_tokens: 2500
          })
        });
        if (!response.ok) {
          throw new Error("Serviço de análise temporariamente indisponível");
        }
        const data = await response.json();
        completionText = data.choices[0].message.content;
        apiUsed = "OpenAI (direto)";
        console.log('✅ [question-commentary] OpenAI direto bem-sucedido');
      } catch (openAiError) {
        console.error('❌ [question-commentary] OpenAI direto falhou:', openAiError);
        throw new Error("Serviço de análise temporariamente indisponível");
      }
    } else {
      console.error('❌ [question-commentary] Nenhuma API key disponível (nem Gemini nem OpenAI)');
      throw new Error("Serviço de análise temporariamente indisponível");
    }
    // Ensure we have a response
    if (!completionText) {
      console.error('❌ [question-commentary] Nenhuma resposta obtida de nenhuma API');
      throw new Error("Serviço de análise temporariamente indisponível");
    }

    console.log(`✅ [question-commentary] Resposta obtida com sucesso usando: ${apiUsed}`);
    // Clean up markdown from JSON if present and improve JSON extraction
    // More robust JSON extraction
    let cleanedText = completionText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
    // 🔧 CORREÇÃO CRÍTICA: Função robusta para extrair JSON válido
    const extractValidJSON = (text)=>{
      // Primeiro, limpar caracteres problemáticos
      let cleaned = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove caracteres de controle
      .trim();
      // Tentar encontrar JSON completo
      const jsonStart = cleaned.indexOf('{');
      if (jsonStart === -1) return cleaned;
      // Encontrar o último } válido
      let braceCount = 0;
      let jsonEnd = -1;
      for(let i = jsonStart; i < cleaned.length; i++){
        if (cleaned[i] === '{') braceCount++;
        if (cleaned[i] === '}') {
          braceCount--;
          if (braceCount === 0) {
            jsonEnd = i;
            break;
          }
        }
      }
      if (jsonEnd !== -1) {
        return cleaned.substring(jsonStart, jsonEnd + 1);
      }
      return cleaned;
    };
    cleanedText = extractValidJSON(cleanedText);
    // If response doesn't start with {, try to find JSON in the text
    if (!cleanedText.startsWith('{')) {
      // Look for JSON patterns in the text
      const jsonMatches = [
        cleanedText.match(/\{[\s\S]*?"alternativas"[\s\S]*?"comentario_final"[\s\S]*?\}/),
        cleanedText.match(/\{[\s\S]*\}/)
      ];
      for (const match of jsonMatches){
        if (match) {
          try {
            // Test if this JSON is valid
            JSON.parse(match[0]);
            cleanedText = match[0];
            break;
          } catch (e) {
            continue;
          }
        }
      }
    }
    // 🛡️ BULLETPROOF JSON VALIDATION
    let jsonResponse;
    try {
      jsonResponse = JSON.parse(cleanedText);
      // 🔧 FORCE VALID STRUCTURE: Ensure all required fields exist with fallbacks
      jsonResponse = validateAndFixStructure(jsonResponse, alternatives, correctAnswer);
      // Convert asterisks to HTML bold tags and handle line breaks
      jsonResponse.alternativas.forEach((alt)=>{
        if (alt.comentario) {
          alt.comentario = convertTextToHtml(alt.comentario);
        }
      });
      if (jsonResponse.comentario_final) {
        jsonResponse.comentario_final = convertTextToHtml(jsonResponse.comentario_final);
      }
      if (jsonResponse.justificativa_erro_gabarito) {
        jsonResponse.justificativa_erro_gabarito = convertTextToHtml(jsonResponse.justificativa_erro_gabarito);
      }
      // Special validation for true/false questions
      if (alternatives.length === 2) {
        const hasEmptyComments = jsonResponse.alternativas.some((alt)=>!alt.comentario || alt.comentario.trim() === '');
        if (hasEmptyComments) {
          jsonResponse.alternativas.forEach((alt)=>{
            if (!alt.comentario || alt.comentario.trim() === '') {
              if (alt.correta) {
                alt.comentario = "Esta alternativa está correta conforme o gabarito oficial.";
              } else {
                alt.comentario = "Esta alternativa está incorreta conforme o gabarito oficial.";
              }
            }
          });
        }
      }
      return new Response(JSON.stringify(jsonResponse), {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        },
        status: 200
      });
    } catch (parseError) {
      // 🔧 ANTES DO FALLBACK: Tentar extrair JSON de forma mais agressiva
      try {
        // Procurar por padrões de JSON válidos na resposta original
        const jsonPattern = /\{[\s\S]*?"alternativas"[\s\S]*?"comentario_final"[\s\S]*?\}/;
        const match = completionText.match(jsonPattern);
        if (match) {
          let extractedJSON = match[0];
          // Limpar caracteres problemáticos do JSON extraído
          extractedJSON = extractedJSON.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').replace(/\n/g, ' ').replace(/\r/g, ' ').replace(/\t/g, ' ').replace(/\s+/g, ' ').trim();
          const directParsed = JSON.parse(extractedJSON);
          const directValidated = validateAndFixStructure(directParsed, alternatives, correctAnswer);
          // Converter para HTML
          directValidated.alternativas.forEach((alt)=>{
            if (alt.comentario) {
              alt.comentario = convertTextToHtml(alt.comentario);
            }
          });
          if (directValidated.comentario_final) {
            directValidated.comentario_final = convertTextToHtml(directValidated.comentario_final);
          }
          return new Response(JSON.stringify(directValidated), {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 200
          });
        }
      } catch (extractError) {
      // Se extração direta falhar, continuar para sanitização
      }
      // Try sanitization as last resort
      try {
        const sanitizedText = sanitizeAIResponse(cleanedText, alternatives, correctAnswer);
        const sanitizedResponse = JSON.parse(sanitizedText);
        const validatedResponse = validateAndFixStructure(sanitizedResponse, alternatives, correctAnswer);
        // Convert to HTML
        validatedResponse.alternativas.forEach((alt)=>{
          if (alt.comentario) {
            alt.comentario = convertTextToHtml(alt.comentario);
          }
        });
        if (validatedResponse.comentario_final) {
          validatedResponse.comentario_final = convertTextToHtml(validatedResponse.comentario_final);
        }
        return new Response(JSON.stringify(validatedResponse), {
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json"
          },
          status: 200
        });
      } catch (sanitizeError) {
        // Final fallback
        const processedResponse = processAIResponse(completionText, alternatives, correctAnswer);
        return new Response(JSON.stringify(processedResponse), {
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json"
          },
          status: 200
        });
      }
    }
  } catch (error) {
    return new Response(JSON.stringify({
      error: "AI service temporarily unavailable",
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      },
      status: 500
    });
  }
});
function convertTextToHtml(text) {
  if (!text || typeof text !== 'string') {
    return text;
  }
  // Handle different types of line breaks first
  text = text.replace(/\r\n/g, '\n'); // Windows line breaks
  text = text.replace(/\r/g, '\n'); // Mac line breaks
  // Convert bullet points with asterisks to proper HTML lists
  // Match lines that start with asterisk (bullet points)
  text = text.replace(/^\s*\*\s+(.+)$/gm, '<li>$1</li>');
  // Wrap consecutive <li> elements in <ul> tags
  text = text.replace(/(<li>.*<\/li>)(\s*<li>.*<\/li>)*/gs, '<ul>$&</ul>');
  // Convert double asterisks to bold (must come before single asterisks)
  text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  // Convert single asterisks to bold, but avoid those at end of sentences
  // This pattern looks for asterisks that are NOT at the end of a sentence
  text = text.replace(/\*([^*\n.!?]+)\*/g, '<strong>$1</strong>');
  // Clean up asterisks at the end of sentences (like "precisa*.")
  text = text.replace(/\*([.!?])/g, '$1');
  // Remove standalone asterisks that might be left
  text = text.replace(/\s\*\s/g, ' ');
  text = text.replace(/\*$/gm, '');
  // Convert double line breaks to paragraph breaks
  text = text.replace(/\n\s*\n/g, '</p><p>');
  // Convert single line breaks to <br> tags (but not inside lists)
  text = text.replace(/\n(?![<\/])/g, '<br>');
  // Wrap in paragraph tags if not already wrapped
  if (!text.startsWith('<p>') && !text.startsWith('<ul>')) {
    text = '<p>' + text + '</p>';
  }
  // Clean up empty paragraphs
  text = text.replace(/<p>\s*<\/p>/g, '');
  text = text.replace(/<p><br><\/p>/g, '');
  // Fix list formatting - ensure lists are not inside paragraphs
  text = text.replace(/<p>(<ul>.*?<\/ul>)<\/p>/gs, '$1');
  return text;
}
// Keep the old function for backward compatibility
function convertAsterisksToHtml(text) {
  return convertTextToHtml(text);
}
function processAIResponse(responseText, alternatives, correctAnswer) {
  // Try to extract useful information from the raw response
  const alternativasProcessadas = alternatives.map((texto, index)=>{
    const isCorrect = index === parseInt(correctAnswer.toString()); // ✅ CORREÇÃO: Usar indexação 0-based
    // Try to find specific commentary for this alternative in the raw text
    let comentario = "Análise detalhada não disponível no momento.";
    // Look for patterns that might indicate commentary for this alternative
    const altNumber = index + 1;
    const patterns = [
      new RegExp(`${altNumber}[.):]\\s*([^\\n]{50,200})`, 'i'),
      new RegExp(`alternativa\\s+${altNumber}[^\\n]*([^\\n]{50,200})`, 'i'),
      new RegExp(`opção\\s+${altNumber}[^\\n]*([^\\n]{50,200})`, 'i')
    ];
    for (const pattern of patterns){
      const match = responseText.match(pattern);
      if (match && match[1]) {
        comentario = match[1].trim();
        break;
      }
    }
    return {
      texto,
      comentario: convertTextToHtml(comentario),
      correta: isCorrect
    };
  });
  const result = {
    alternativas: alternativasProcessadas,
    comentario_final: convertTextToHtml(responseText.substring(0, 1000) + (responseText.length > 1000 ? '...' : '')),
    possivel_erro_no_gabarito: false,
    justificativa_erro_gabarito: ""
  };
  return result;
}
// 🛡️ ULTRA-ROBUST FUNCTION: Sanitize ANY malformed AI response
function sanitizeAIResponse(rawText, alternatives, correctAnswer) {
  let cleanedText = rawText;
  // Step 1: First try to parse as-is (most common case)
  try {
    const directParse = JSON.parse(cleanedText);
    if (directParse.alternativas && Array.isArray(directParse.alternativas) && directParse.comentario_final && typeof directParse.comentario_final === 'string') {
      return cleanedText; // Return as-is if it's already valid
    }
  } catch (e) {}
  // Step 2: Handle nested JSON structures (AI returning JSON as text content)
  if (cleanedText.includes('"alternativas"') && cleanedText.includes('"comentario_final"')) {
    try {
      const parsedStructure = JSON.parse(cleanedText);
      // Check if this is a valid structure that we can use directly
      if (parsedStructure.alternativas && Array.isArray(parsedStructure.alternativas) && parsedStructure.comentario_final && typeof parsedStructure.comentario_final === 'string') {
        return cleanedText; // Return the original valid JSON
      }
    } catch (e) {}
  }
  // Step 2: Try to extract JSON from mixed content
  const jsonPattern = /\{[\s\S]*?"alternativas"[\s\S]*?"comentario_final"[\s\S]*?\}/;
  const jsonMatch = cleanedText.match(jsonPattern);
  if (jsonMatch) {
    try {
      const extractedJson = JSON.parse(jsonMatch[0]);
      if (extractedJson.alternativas && extractedJson.comentario_final) {
        return jsonMatch[0];
      }
    } catch (e) {}
  }
  // Step 3: If no JSON found, create one from the text content
  if (!cleanedText.includes('"alternativas"') || !cleanedText.includes('"comentario_final"')) {
    const fallbackResponse = {
      alternativas: alternatives.map((alt, index)=>({
          texto: alt,
          comentario: `Análise baseada no texto da IA: ${cleanedText.substring(0, 200)}...`,
          correta: index === correctAnswer // ✅ CORREÇÃO: Usar indexação 0-based
        })),
      comentario_final: cleanedText.length > 50 ? cleanedText.substring(0, 1000) : "Análise detalhada não disponível no momento.",
      possivel_erro_no_gabarito: false,
      justificativa_erro_gabarito: ""
    };
    return JSON.stringify(fallbackResponse);
  }
  // Step 3: If all else fails, create a valid structure from scratch
  const fallbackStructure = {
    alternativas: alternatives.map((texto, index)=>({
        texto: texto,
        comentario: "Análise detalhada será gerada em breve.",
        correta: index === correctAnswer // ✅ CORREÇÃO: Usar indexação 0-based
      })),
    comentario_final: "Análise completa da questão será disponibilizada em breve.",
    possivel_erro_no_gabarito: false,
    justificativa_erro_gabarito: ""
  };
  return JSON.stringify(fallbackStructure);
}
// 🛡️ BULLETPROOF STRUCTURE VALIDATOR: Ensures response always has valid structure
function validateAndFixStructure(jsonResponse, alternatives, correctAnswer) {
  // Ensure base structure exists
  if (!jsonResponse || typeof jsonResponse !== 'object') {
    jsonResponse = {};
  }
  // Fix alternativas array
  if (!jsonResponse.alternativas || !Array.isArray(jsonResponse.alternativas)) {
    jsonResponse.alternativas = [];
  }
  // Ensure we have the right number of alternatives
  while(jsonResponse.alternativas.length < alternatives.length){
    const index = jsonResponse.alternativas.length;
    jsonResponse.alternativas.push({
      texto: alternatives[index],
      comentario: "Análise detalhada não disponível no momento.",
      correta: index === correctAnswer // ✅ CORREÇÃO: Usar indexação 0-based
    });
  }
  // Fix each alternative structure
  jsonResponse.alternativas = jsonResponse.alternativas.map((alt, index)=>({
      texto: alt.texto || alternatives[index] || `Alternativa ${index + 1}`,
      comentario: alt.comentario || "Análise detalhada não disponível no momento.",
      correta: alt.correta !== undefined ? Boolean(alt.correta) : index === correctAnswer // ✅ CORREÇÃO: Usar indexação 0-based
    }));
  // Fix comentario_final
  if (!jsonResponse.comentario_final || typeof jsonResponse.comentario_final !== 'string') {
    jsonResponse.comentario_final = "Análise completa da questão não disponível no momento.";
  }
  // Fix possivel_erro_no_gabarito
  if (jsonResponse.possivel_erro_no_gabarito === undefined) {
    jsonResponse.possivel_erro_no_gabarito = false;
  } else {
    jsonResponse.possivel_erro_no_gabarito = Boolean(jsonResponse.possivel_erro_no_gabarito);
  }
  // Fix justificativa_erro_gabarito
  if (!jsonResponse.justificativa_erro_gabarito || typeof jsonResponse.justificativa_erro_gabarito !== 'string') {
    jsonResponse.justificativa_erro_gabarito = "";
  }

  return jsonResponse;
}
