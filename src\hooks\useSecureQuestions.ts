import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

/**
 * Hook para buscar questões de forma segura (sem respostas corretas)
 * e validar respostas no backend
 */
export const useSecureQuestions = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const { toast } = useToast();

  /**
   * Busca questões por IDs sem expor dados sensíveis
   */
  const getQuestionsByIds = async (
    questionIds: string[],
    domain?: string
  ) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('get_questions_by_ids_secure', {
        question_ids: questionIds,
        domain_filter: domain
      });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error: any) {
      console.error('❌ [useSecureQuestions] Error fetching questions:', error);
      toast({
        title: "Erro ao carregar questões",
        description: error.message || "Ocorreu um erro ao carregar as questões",
        variant: "destructive",
      });
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Busca questões filtradas sem expor dados sensíveis
   */
  const getFilteredQuestions = async (params: {
    specialty_ids?: string[];
    theme_ids?: string[];
    focus_ids?: string[];
    location_ids?: string[];
    years?: number[];
    question_types?: string[];
    question_formats?: string[];
    page_number?: number;
    items_per_page?: number;
    domain_filter?: string;
  }) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('get_filtered_questions_secure', {
        specialty_ids: params.specialty_ids || [],
        theme_ids: params.theme_ids || [],
        focus_ids: params.focus_ids || [],
        location_ids: params.location_ids || [],
        years: params.years || [],
        question_types: params.question_types || [],
        question_formats: params.question_formats || [],
        page_number: params.page_number || 1,
        items_per_page: params.items_per_page || 20,
        domain_filter: params.domain_filter
      });

      if (error) {
        throw error;
      }

      return data || { questions: [], total_count: 0 };
    } catch (error: any) {
      console.error('❌ [useSecureQuestions] Error fetching filtered questions:', error);
      toast({
        title: "Erro ao carregar questões",
        description: error.message || "Ocorreu um erro ao carregar as questões",
        variant: "destructive",
      });
      return { questions: [], total_count: 0 };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Valida uma resposta no backend de forma segura
   */
  const validateAnswer = async (
    questionId: string,
    selectedAnswer: string,
    userId?: string
  ) => {
    setIsValidating(true);

    try {
      const { data, error } = await supabase.rpc('validate_answer', {
        p_question_id: questionId,
        p_selected_answer: selectedAnswer,
        p_user_id: userId
      });

      if (error) {
        throw error;
      }

      return {
        isCorrect: data?.is_correct || false,
        questionId: data?.question_id,
        aiCommentary: data?.ai_commentary
      };
    } catch (error: any) {
      console.error('❌ [useSecureQuestions] Error validating answer:', error);
      toast({
        title: "Erro ao validar resposta",
        description: error.message || "Ocorreu um erro ao validar a resposta",
        variant: "destructive",
      });
      return {
        isCorrect: false,
        questionId,
        aiCommentary: null
      };
    } finally {
      setIsValidating(false);
    }
  };

  /**
   * 🎨 Busca informações de feedback APÓS validação (apenas para exibição)
   * Esta função só é chamada DEPOIS que o usuário já respondeu
   */
  const getFeedbackInfo = async (questionId: string) => {
    try {
      const { data, error } = await supabase.rpc('get_question_feedback_info', {
        question_id: questionId
      });

      if (error) {
        throw error;
      }

      return {
        correctChoice: data?.correct_choice,
        aiCommentary: data?.ai_commentary,
        alternativeComments: data?.alternative_comments
      };
    } catch (error: any) {
      console.error('❌ [useSecureQuestions] Error getting feedback info:', error);
      return {
        correctChoice: null,
        aiCommentary: null,
        alternativeComments: null
      };
    }
  };

  return {
    getQuestionsByIds,
    getFilteredQuestions,
    validateAnswer,
    getFeedbackInfo,
    isLoading,
    isValidating
  };
};
