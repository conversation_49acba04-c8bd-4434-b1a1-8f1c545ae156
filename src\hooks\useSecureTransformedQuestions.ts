import { useMemo } from 'react';

/**
 * Função otimizada para processamento de imagens
 * Memoizada para evitar re-processamento
 */
const processQuestionImages = (images: any): string[] => {
  if (!images) return [];

  if (typeof images === 'string') {
    return [images];
  }

  if (Array.isArray(images)) {
    return images.map(img => typeof img === 'string' ? img : '').filter(Boolean);
  }

  // Se for um objeto JSON, tentar extrair URLs
  if (typeof images === 'object' && images !== null) {
    const values = Object.values(images);
    return values.filter(val => typeof val === 'string' && val.length > 0) as string[];
  }

  return [];
};

/**
 * Hook otimizado para transformação SEGURA de questões
 * Remove dados sensíveis e evita re-computação desnecessária
 */
export const useSecureTransformedQuestions = (questionsData: any[] | null) => {
  return useMemo(() => {
    if (!questionsData?.length) return [];

    const transformedQuestions = questionsData.map((q) => {
      const imagesProcessed = processQuestionImages(q.media_attachments || q.images);

      return {
        id: q.id,
        statement: q.question_content || q.statement,
        question_content: q.question_content || q.statement,
        alternatives: Array.isArray(q.alternatives) && q.alternatives.length > 0
          ? q.alternatives.map((alt) => String(alt))
          : typeof q.alternatives === "object" && q.alternatives !== null
          ? Object.values(q.alternatives).map(String)
          : undefined,
        response_choices: Array.isArray(q.response_choices) && q.response_choices.length > 0
          ? q.response_choices.map((alt) => String(alt))
          : Array.isArray(q.alternatives) && q.alternatives.length > 0
          ? q.alternatives.map((alt) => String(alt))
          : [],
        
        // ❌ REMOVIDO: correct_answer, correct_choice (SEGURANÇA)
        // ❌ REMOVIDO: ai_commentary (pode conter respostas)
        // ❌ REMOVIDO: alternative_comments (pode conter respostas)
        // ❌ REMOVIDO: statistics (dados sensíveis)
        // ❌ REMOVIDO: owner (informação interna)

        specialty_id: q.specialty_id,
        theme_id: q.theme_id,
        focus_id: q.focus_id,
        exam_location: q.exam_location,
        exam_year: q.exam_year,
        specialty: q.specialty_name 
          ? { id: q.specialty_id, name: q.specialty_name }
          : q.specialty || null,
        theme: q.theme_name 
          ? { id: q.theme_id, name: q.theme_name }
          : q.theme || null,
        focus: q.focus_name 
          ? { id: q.focus_id, name: q.focus_name }
          : q.focus || null,
        location: q.location_name 
          ? { id: q.exam_location, name: q.location_name }
          : q.location || null,
        question_format: q.question_format,
        content_tags: q.content_tags || {},
        comments: [], // Sempre vazio por segurança
        likes: q.likes || 0,
        dislikes: q.dislikes || 0,
        liked_by: [], // Sempre vazio por segurança
        disliked_by: [], // Sempre vazio por segurança
        created_at: q.created_at,
        domain: q.knowledge_domain || q.domain,
        images: imagesProcessed,
        media_attachments: imagesProcessed,
        question_type: q.assessment_type || q.question_type,
        assessment_type: q.assessment_type || q.question_type,
        question_number: q.question_number
      };
    });

    return transformedQuestions;
  }, [questionsData]);
};
