import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Eye,
  RotateCcw,
  BookOpen,
  Filter,
  Users,
  Calendar,
  Target,
  Shuffle,
  ChevronDown,
  ChevronUp,
  Settings
} from 'lucide-react';
import { useDarkMode } from '@/contexts/DarkModeContext';
import { ErrorStats } from '@/hooks/useErrorNotebook';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface ReviewSessionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  errorStats: ErrorStats;
  onStartSession: (config: ReviewSessionConfig) => void;
  pendingQuestions: number;
}

export interface ReviewSessionConfig {
  mode: 'view' | 'retry';
  quantity: number;
  specialty_id?: string;
  theme_id?: string;
  order: 'recent' | 'oldest' | 'random';
  onlyPending: boolean;
}

interface SpecialtyCounts {
  specialty_id: string;
  specialty_name: string;
  total_count: number;
  pending_count: number;
}

interface ThemeCounts {
  theme_id: string;
  theme_name: string;
  total_count: number;
  pending_count: number;
  specialty_id?: string;
}

export const ReviewSessionDialog: React.FC<ReviewSessionDialogProps> = ({
  open,
  onOpenChange,
  errorStats,
  onStartSession,
  pendingQuestions
}) => {
  const { isDarkMode } = useDarkMode();
  const { user } = useAuth();
  const [config, setConfig] = useState<ReviewSessionConfig>({
    mode: 'retry',
    quantity: Math.min(10, pendingQuestions),
    order: 'recent',
    onlyPending: true
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('all');
  const [selectedTheme, setSelectedTheme] = useState<string>('all');
  const [specialtyCounts, setSpecialtyCounts] = useState<SpecialtyCounts[]>([]);
  const [themeCounts, setThemeCounts] = useState<ThemeCounts[]>([]);
  const [isLoadingCounts, setIsLoadingCounts] = useState(false);

  // 🚀 OTIMIZAÇÃO: Usar dados já carregados do errorStats em vez de fazer novas consultas
  const fetchSpecialtyCounts = async () => {
    if (!user?.id) return;

    setIsLoadingCounts(true);

    try {
      // 🔄 CONSULTA REAL: Sempre buscar dados atualizados para filtros precisos

      // Buscar todas as questões erradas do usuário com especialidades
      const { data: userAnswers, error } = await supabase
        .from('user_answers')
        .select(`
          id,
          question_id,
          specialty_id,
          study_categories!user_answers_specialty_id_fkey(name)
        `)
        .eq('user_id', user.id)
        .eq('is_correct', false)
        .not('specialty_id', 'is', null);

      if (error) throw error;

      // Buscar status de revisão
      const userAnswerIds = userAnswers?.map(ua => ua.id) || [];
      const { data: reviewedData, error: reviewedError } = await supabase
        .from('erros_meta')
        .select('user_answer_id, revisado')
        .eq('user_id', user.id)
        .in('user_answer_id', userAnswerIds);

      if (reviewedError) throw reviewedError;

      // Criar mapa de status de revisão
      const reviewedMap = new Map();
      reviewedData?.forEach(item => {
        reviewedMap.set(item.user_answer_id, item.revisado);
      });

      // Agrupar por especialidade
      const specialtyMap = new Map<string, SpecialtyCounts>();

      userAnswers?.forEach(ua => {
        if (!ua.specialty_id || !ua.study_categories?.name) return;

        const isReviewed = reviewedMap.get(ua.id) || false;
        const key = ua.specialty_id;

        if (!specialtyMap.has(key)) {
          specialtyMap.set(key, {
            specialty_id: ua.specialty_id,
            specialty_name: ua.study_categories.name,
            total_count: 0,
            pending_count: 0
          });
        }

        const counts = specialtyMap.get(key)!;
        counts.total_count++;
        if (!isReviewed) {
          counts.pending_count++;
        }
      });

      const specialtyCountsArray = Array.from(specialtyMap.values())
        .sort((a, b) => b.total_count - a.total_count);


      setSpecialtyCounts(specialtyCountsArray);

    } catch (error) {
      console.error('❌ [ReviewSessionDialog] Erro ao buscar contagens de especialidades:', error);
    } finally {
      setIsLoadingCounts(false);
    }
  };

  // 🔄 CONSULTA REAL: Buscar dados atualizados para filtros precisos
  const fetchThemeCounts = async (specialtyId?: string) => {
    if (!user?.id) return;

    try {
      // 🔄 CONSULTA REAL: Sempre buscar dados atualizados para filtros precisos

      // Buscar questões erradas do usuário com temas
      let query = supabase
        .from('user_answers')
        .select(`
          id,
          question_id,
          theme_id,
          specialty_id,
          study_categories!user_answers_theme_id_fkey(name)
        `)
        .eq('user_id', user.id)
        .eq('is_correct', false)
        .not('theme_id', 'is', null);

      // Filtrar por especialidade se selecionada
      if (specialtyId && specialtyId !== 'all') {
        query = query.eq('specialty_id', specialtyId);
      }

      const { data: userAnswers, error } = await query;
      if (error) throw error;



      // Buscar status de revisão
      const userAnswerIds = userAnswers?.map(ua => ua.id) || [];
      const { data: reviewedData, error: reviewedError } = await supabase
        .from('erros_meta')
        .select('user_answer_id, revisado')
        .eq('user_id', user.id)
        .in('user_answer_id', userAnswerIds);

      if (reviewedError) throw reviewedError;

      // Criar mapa de status de revisão
      const reviewedMap = new Map();
      reviewedData?.forEach(item => {
        reviewedMap.set(item.user_answer_id, item.revisado);
      });

      // Agrupar por tema
      const themeMap = new Map<string, ThemeCounts>();

      userAnswers?.forEach(ua => {
        if (!ua.theme_id || !ua.study_categories?.name) return;

        const isReviewed = reviewedMap.get(ua.id) || false;
        const key = ua.theme_id;

        if (!themeMap.has(key)) {
          themeMap.set(key, {
            theme_id: ua.theme_id,
            theme_name: ua.study_categories.name,
            total_count: 0,
            pending_count: 0,
            specialty_id: ua.specialty_id
          });
        }

        const counts = themeMap.get(key)!;
        counts.total_count++;
        if (!isReviewed) {
          counts.pending_count++;
        }
      });

      const themeCountsArray = Array.from(themeMap.values())
        .sort((a, b) => b.total_count - a.total_count);


      setThemeCounts(themeCountsArray);

    } catch (error) {
      console.error('❌ [ReviewSessionDialog] Erro ao buscar contagens de temas:', error);
    }
  };

  // Carregar contagens quando o dialog abre
  useEffect(() => {
    if (open && user?.id) {
      fetchSpecialtyCounts();
      fetchThemeCounts();
    }
  }, [open, user?.id]);

  // Recarregar temas quando especialidade muda e resetar tema selecionado
  useEffect(() => {
    if (open && user?.id) {
      // Reset tema para 'all' quando especialidade muda
      setSelectedTheme('all');
      fetchThemeCounts(selectedSpecialty !== 'all' ? selectedSpecialty : undefined);

    }
  }, [selectedSpecialty, open, user?.id]);

  // Calcular questões disponíveis baseado nos filtros REAIS
  const availableQuestions = useMemo(() => {


    let total = config.onlyPending ? pendingQuestions : errorStats.totalErrors;

    // Se temos especialidade selecionada, usar dados reais
    if (selectedSpecialty !== 'all') {
      const specialty = specialtyCounts.find(s => s.specialty_id === selectedSpecialty);
      if (specialty) {
        total = config.onlyPending ? specialty.pending_count : specialty.total_count;

      } else {
        total = 0;

      }
    }

    // Se temos tema selecionado, usar dados reais
    if (selectedTheme !== 'all') {
      const theme = themeCounts.find(t => t.theme_id === selectedTheme);
      if (theme) {
        total = config.onlyPending ? theme.pending_count : theme.total_count;

      } else {
        total = 0;

      }
    }

    const result = Math.max(0, total);

    return result;
  }, [config.onlyPending, selectedSpecialty, selectedTheme, specialtyCounts, themeCounts, pendingQuestions, errorStats.totalErrors]);

  // Atualizar quantidade automaticamente quando filtros mudam
  useEffect(() => {
    if (availableQuestions > 0) {
      // Sempre ajustar para o máximo disponível, limitado a 100
      const newQuantity = Math.min(availableQuestions, 100);
      if (config.quantity !== newQuantity) {
        setConfig(prev => ({ ...prev, quantity: newQuantity }));

      }
    } else if (config.quantity > availableQuestions) {
      // Se não há questões disponíveis, ajustar para 1 (mínimo)
      setConfig(prev => ({ ...prev, quantity: Math.max(1, availableQuestions) }));
    }
  }, [availableQuestions]);

  const handleStartSession = () => {
    const finalConfig: ReviewSessionConfig = {
      ...config,
      specialty_id: selectedSpecialty !== 'all' ? selectedSpecialty : undefined,
      theme_id: selectedTheme !== 'all' ? selectedTheme : undefined,
    };
    onStartSession(finalConfig);
    onOpenChange(false);
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={`w-[95vw] sm:w-[85vw] max-w-2xl max-h-[90vh] overflow-y-auto transition-colors duration-200 ${
        isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
      }`}>
        <DialogHeader>
          <DialogTitle className={`flex items-center gap-2 text-xl transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-900'
          }`}>
            <BookOpen className="h-5 w-5" />
            Iniciar Sessão de Revisão
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Estatísticas Rápidas */}
          <Card className={`transition-colors duration-200 ${
            isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
          }`}>
            <CardContent className="p-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className={`text-2xl font-bold transition-colors duration-200 ${
                    isDarkMode ? 'text-blue-400' : 'text-blue-600'
                  }`}>
                    {errorStats.totalErrors}
                  </div>
                  <div className={`text-sm transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Total de Erros
                  </div>
                </div>
                <div>
                  <div className={`text-2xl font-bold transition-colors duration-200 ${
                    isDarkMode ? 'text-orange-400' : 'text-orange-600'
                  }`}>
                    {pendingQuestions}
                  </div>
                  <div className={`text-sm transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Pendentes
                  </div>
                </div>
                <div>
                  <div className={`text-2xl font-bold transition-colors duration-200 ${
                    isDarkMode ? 'text-green-400' : 'text-green-600'
                  }`}>
                    {errorStats.reviewedErrors}
                  </div>
                  <div className={`text-sm transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Revisadas
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Modo de Revisão */}
          <div className="space-y-3">
            <Label className={`text-base font-semibold transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-900'
            }`}>
              Como você quer revisar?
            </Label>
            <RadioGroup
              value={config.mode}
              onValueChange={(value: 'view' | 'retry') => setConfig(prev => ({ ...prev, mode: value }))}
              className="grid grid-cols-1 gap-3"
            >
              {/* Refazer Questões - PRIMEIRO */}
              <div className={`flex items-center space-x-3 border rounded-lg p-4 transition-all duration-200 cursor-pointer ${
                config.mode === 'retry'
                  ? isDarkMode
                    ? 'border-green-500 bg-green-900/20'
                    : 'border-green-500 bg-green-50'
                  : isDarkMode
                    ? 'border-gray-600 hover:bg-gray-700'
                    : 'border-gray-200 hover:bg-gray-50'
              }`} onClick={() => setConfig(prev => ({ ...prev, mode: 'retry' }))}>
                <RadioGroupItem value="retry" id="retry" />
                <div className="flex items-center gap-3 flex-1">
                  <RotateCcw className="h-5 w-5 text-green-500" />
                  <div>
                    <Label htmlFor="retry" className={`font-medium cursor-pointer transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>
                      Refazer Questões
                    </Label>
                    <p className={`text-sm transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Tentar responder novamente e ver se acertou
                    </p>
                  </div>
                </div>
              </div>

              {/* Apenas Visualizar - SEGUNDO */}
              <div className={`flex items-center space-x-3 border rounded-lg p-4 transition-all duration-200 cursor-pointer ${
                config.mode === 'view'
                  ? isDarkMode
                    ? 'border-blue-500 bg-blue-900/20'
                    : 'border-blue-500 bg-blue-50'
                  : isDarkMode
                    ? 'border-gray-600 hover:bg-gray-700'
                    : 'border-gray-200 hover:bg-gray-50'
              }`} onClick={() => setConfig(prev => ({ ...prev, mode: 'view' }))}>
                <RadioGroupItem value="view" id="view" />
                <div className="flex items-center gap-3 flex-1">
                  <Eye className="h-5 w-5 text-blue-500" />
                  <div>
                    <Label htmlFor="view" className={`font-medium cursor-pointer transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-900'
                    }`}>
                      Apenas Visualizar
                    </Label>
                    <p className={`text-sm transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Ver questão, resposta correta e análise da IA
                    </p>
                  </div>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Quantidade de Questões */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className={`text-base font-semibold transition-colors duration-200 ${
                isDarkMode ? 'text-gray-200' : 'text-gray-900'
              }`}>
                Quantas questões revisar?
              </Label>
              <Badge variant="outline" className={`transition-colors duration-200 ${
                isDarkMode ? 'border-gray-600 text-gray-300' : 'border-gray-300 text-gray-700'
              }`}>
                {config.quantity} de {Math.min(availableQuestions, 100)} {availableQuestions > 100 ? '(máx 100)' : 'disponíveis'}
              </Badge>
            </div>
            
            <div className="space-y-4">
              <Slider
                value={[config.quantity]}
                onValueChange={(value) => setConfig(prev => ({ ...prev, quantity: value[0] }))}
                max={Math.min(availableQuestions, 100)}
                min={1}
                step={1}
                className="w-full"
              />
              
              <div className="flex gap-2 flex-wrap">
                {[5, 10, 20, 50].filter(num => num <= Math.min(availableQuestions, 100)).map(num => (
                  <Button
                    key={num}
                    variant={config.quantity === num ? "default" : "outline"}
                    size="sm"
                    onClick={() => setConfig(prev => ({ ...prev, quantity: num }))}
                    className="text-xs"
                  >
                    {num}
                  </Button>
                ))}
                {availableQuestions > 50 && (
                  <Button
                    variant={config.quantity === Math.min(availableQuestions, 100) ? "default" : "outline"}
                    size="sm"
                    onClick={() => setConfig(prev => ({ ...prev, quantity: Math.min(availableQuestions, 100) }))}
                    className="text-xs"
                  >
                    {availableQuestions > 100 ? 'Máx (100)' : `Todas (${availableQuestions})`}
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Filtros Avançados */}
          <div className="space-y-3">
            <Button
              variant="ghost"
              onClick={() => setShowFilters(!showFilters)}
              className={`w-full justify-between transition-colors duration-200 ${
                isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <span>Filtros Avançados</span>
                {(selectedSpecialty !== 'all' || selectedTheme !== 'all') && (
                  <Badge variant="secondary" className="ml-2">
                    {[selectedSpecialty !== 'all' ? '1' : '', selectedTheme !== 'all' ? '1' : ''].filter(Boolean).length} ativo(s)
                  </Badge>
                )}
              </div>
              {showFilters ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>

            {showFilters && (
              <div className="space-y-4 pt-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className={`text-sm font-medium transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Especialidade
                    </Label>
                    <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
                      <SelectTrigger className={`transition-colors duration-200 ${
                        isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-white'
                      }`}>
                        <SelectValue placeholder="Todas as especialidades" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as especialidades</SelectItem>
                        {isLoadingCounts ? (
                          <SelectItem value="loading" disabled>
                            Carregando contagens...
                          </SelectItem>
                        ) : (
                          specialtyCounts
                            .filter(specialty => {
                              const count = config.onlyPending ? specialty.pending_count : specialty.total_count;
                              return count > 0; // Só mostrar se tiver questões
                            })
                            .map(specialty => {
                              const count = config.onlyPending ? specialty.pending_count : specialty.total_count;
                              return (
                                <SelectItem key={specialty.specialty_id} value={specialty.specialty_id}>
                                  {specialty.specialty_name} ({count})
                                </SelectItem>
                              );
                            })
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className={`text-sm font-medium transition-colors duration-200 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Tema
                    </Label>
                    <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                      <SelectTrigger className={`transition-colors duration-200 ${
                        isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-white'
                      }`}>
                        <SelectValue placeholder="Todos os temas" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos os temas</SelectItem>
                        {isLoadingCounts ? (
                          <SelectItem value="loading" disabled>
                            Carregando contagens...
                          </SelectItem>
                        ) : (
                          themeCounts
                            .filter(theme => {
                              const count = config.onlyPending ? theme.pending_count : theme.total_count;
                              return count > 0; // Só mostrar se tiver questões
                            })
                            .map(theme => {
                              const count = config.onlyPending ? theme.pending_count : theme.total_count;
                              return (
                                <SelectItem key={theme.theme_id} value={theme.theme_id}>
                                  {theme.theme_name} ({count})
                                </SelectItem>
                              );
                            })
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className={`text-sm font-medium transition-colors duration-200 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Ordem das Questões
                  </Label>
                  <Select 
                    value={config.order} 
                    onValueChange={(value: 'recent' | 'oldest' | 'random') => 
                      setConfig(prev => ({ ...prev, order: value }))
                    }
                  >
                    <SelectTrigger className={`transition-colors duration-200 ${
                      isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-white'
                    }`}>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="recent">Mais recentes primeiro</SelectItem>
                      <SelectItem value="oldest">Mais antigas primeiro</SelectItem>
                      <SelectItem value="random">Ordem aleatória</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>

          {/* Opções Adicionais */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="onlyPending"
                checked={config.onlyPending}
                onChange={(e) => setConfig(prev => ({ ...prev, onlyPending: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <Label htmlFor="onlyPending" className={`text-sm transition-colors duration-200 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Apenas questões pendentes
                <span className={`ml-1 font-semibold transition-colors duration-200 ${
                  isDarkMode ? 'text-orange-400' : 'text-orange-600'
                }`}>
                  ({pendingQuestions} disponíveis)
                </span>
              </Label>
            </div>
            {!config.onlyPending && (
              <p className={`text-xs transition-colors duration-200 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Incluindo questões já revisadas
                <span className={`font-semibold transition-colors duration-200 ${
                  isDarkMode ? 'text-blue-400' : 'text-blue-600'
                }`}>
                  ({errorStats.totalErrors} total)
                </span>
              </p>
            )}
          </div>

          <Separator className={`transition-colors duration-200 ${
            isDarkMode ? 'bg-gray-600' : 'bg-gray-200'
          }`} />

          {/* Botões de Ação */}
          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleStartSession}
              disabled={availableQuestions === 0 || (config.onlyPending && pendingQuestions === 0)}
              className="flex-1 bg-blue-500 hover:bg-blue-600 text-white disabled:opacity-50"
            >
              <BookOpen className="h-4 w-4 mr-2" />
              {availableQuestions === 0
                ? "Nenhuma questão disponível"
                : config.onlyPending && pendingQuestions === 0
                ? "Nenhuma questão pendente"
                : "Iniciar Revisão"
              }
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
