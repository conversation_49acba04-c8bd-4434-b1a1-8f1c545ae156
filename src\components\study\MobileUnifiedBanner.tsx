import React, { useState, useEffect, lazy, Suspense } from 'react';
import { motion } from 'framer-motion';
import {
  Spark<PERSON>,
  Calendar,
  Clock,
  CheckCircle,
  Flame,
  Star,
  Trophy,
  Brain,
  RefreshCw,
  Info,
  BookOpen,
  Play
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useStreakSystem, isDayActive } from '@/hooks/useOptimizedStreakStats';
import { useOptimizedTodayStats } from '@/hooks/useConsolidatedDashboardStats';
import { useInsights } from '@/hooks/useInsights';
import { useInsightStudy } from '@/hooks/useInsightStudy';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import { useNextStudy, getTopicTitle, getTemperatureColor } from '@/hooks/useNextStudy';
import { useStudySchedule } from '@/hooks/useStudySchedule';
import InsightCard from '@/components/insights/InsightCard';
import { InsightInfoDialog } from '@/components/insights/InsightInfoDialog';
import { StudyOptionsDialog } from '@/components/study/StudyOptionsDialog';
import { getStudyTimeStatus, getTimeBasedMotivation } from '@/utils/studyTimeStatus';
import { useToast } from '@/hooks/use-toast';
import { useDomain } from '@/hooks/useDomain';
import { useUser } from '@supabase/auth-helpers-react';
import { useNavigate } from 'react-router-dom';
import { useStudySession } from '@/hooks/useStudySession';
import { useStudyTopics } from '@/hooks/study-schedule/useStudyTopics';
import { useQueryClient } from '@tanstack/react-query';
import { AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useFeedbackDialog } from "@/components/ui/feedback-dialog";
import { format, startOfWeek, addDays, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Lazy imports para os mesmos dialogs do TodayStudies
const ConfirmStudyDialog = lazy(() => import('@/components/progress/ConfirmStudyDialog').then(module => ({ default: module.ConfirmStudyDialog })));

// ✅ CACHE AGRESSIVO: Cache global para evitar requests repetidas (mesmo do TodayStudies)
const TOPIC_CACHE = new Map<string, { count: number; data: any[]; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

const getCachedTopicData = (cacheKey: string) => {
  const cached = TOPIC_CACHE.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached;
  }
  return null;
};

const setCachedTopicData = (cacheKey: string, data: { count: number; data: any[] }) => {
  TOPIC_CACHE.set(cacheKey, { ...data, timestamp: Date.now() });
};

// ✅ EXATAMENTE IGUAL ao TodayStudies
const formatRevisionNumber = (num: number) => {
  switch (num) {
    case 1:
      return "primeira";
    case 2:
      return "segunda";
    case 3:
      return "terceira";
    default:
      return `${num}ª`;
  }
};

const MobileUnifiedBanner: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [excludedInsightIds, setExcludedInsightIds] = useState<string[]>([]);
  const [showInsightInfo, setShowInsightInfo] = useState(false);

  // Hooks para próximo estudo
  const { topic: nextTopic, isLoading: nextStudyLoading, completedCount, totalCount } = useNextStudy();

  // ✅ NOVO: Status inteligente de horário baseado no tópico
  const timeStatus = getStudyTimeStatus(nextTopic?.start_time || nextTopic?.startTime);
  const { markTopicAsStudied, loadCurrentSchedule } = useStudySchedule();
  const { createWeeksAndMarkStudied } = useStudyTopics(); // ✅ Hook adicional para criação de semanas

  // Estados para funcionalidade dos botões (EXATAMENTE iguais ao TodayStudies)
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(null);
  const [selectedTopicName, setSelectedTopicName] = useState<string>("");
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [studySuccessMessage, setStudySuccessMessage] = useState("");
  const [isLastRevision, setIsLastRevision] = useState(false);
  const [nextRevisionInfo, setNextRevisionInfo] = useState<any>(null);
  const [createWeeksDialogOpen, setCreateWeeksDialogOpen] = useState(false);
  const [weeksToCreate, setWeeksToCreate] = useState(0);
  const [revisionDateFormatted, setRevisionDateFormatted] = useState("");
  const [pendingTopicId, setPendingTopicId] = useState<string | null>(null);
  const [pendingRevisionNumber, setPendingRevisionNumber] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [allTopicsData, setAllTopicsData] = useState<any[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [nextStudyMaxQuestions, setNextStudyMaxQuestions] = useState(0);

  // Hooks adicionais
  const { toast } = useToast();
  const { domain } = useDomain();
  const { showFeedback } = useFeedbackDialog();
  const navigate = useNavigate();
  const { createSession } = useStudySession();
  const [loadingState, setLoadingState] = useState(false);
  const queryClient = useQueryClient();




  
  const {
    currentStreak,
    maxStreak,
    weekActivities,
    isLoading: streakLoading
  } = useStreakSystem();

  const {
    questionsAnswered,
    timeStudied,
    flashcardsStudied,
    isLoading: statsLoading
  } = useOptimizedTodayStats();

  // ✅ NOVO: Hooks para Insights
  const { insights, isLoading: insightsLoading, refetch: refetchInsights } = useInsights(excludedInsightIds);
  const { preferences } = useStudyPreferences();
  const {
    openStudyDialog,
    closeStudyDialog,
    isDialogOpen,
    selectedInsight,
    maxQuestions,
    availableYears,
    availableInstitutions,
    startStudy
  } = useInsightStudy();

  // ✅ NOVO: Função para trocar insight do dia
  const handleRefreshInsight = () => {
    const currentInsight = insights?.suggested_focus;
    if (currentInsight) {
      // Adicionar o insight atual à lista de exclusões
      setExcludedInsightIds(prev => [...prev, currentInsight.focus_id]);

      // Limpar cache de insights para forçar nova busca
      const cacheKeys = Object.keys(localStorage).filter(key =>
        key.includes('insights_cache') || key.includes('focus_insights')
      );
      cacheKeys.forEach(key => localStorage.removeItem(key));

      // Buscar novos insights
      refetchInsights();
    }
  };

  // ✅ REUTILIZAR: Mesmas funções do TodayStudies
  const handleMarkAsStudied = async (topicId: string, topicName?: string) => {
    setSelectedTopicId(topicId);
    setSelectedTopicName(topicName || "este tópico");
    setConfirmDialogOpen(true);
  };

  const handleConfirmStudied = async () => {
    if (selectedTopicId) {
      setConfirmDialogOpen(false);
      const result = await markTopicAsStudied(selectedTopicId);

      // ✅ NOVO: Verificar se precisa criar semanas
      if (result && result.needsWeeks) {
        setWeeksToCreate(result.weeksNeeded || 1);
        setRevisionDateFormatted(result.revisionDate || "");
        setPendingTopicId(result.topicId || selectedTopicId);
        setPendingRevisionNumber(result.revisionNumber || 0);
        setCreateWeeksDialogOpen(true);
        return;
      }

      if (result && result.success) {
        // 🔄 ATUALIZAÇÃO IMEDIATA: Invalidar queries E recarregar dados locais
        queryClient.invalidateQueries({ queryKey: ['study-schedule', user?.id] });
        await loadCurrentSchedule(); // ✅ NOVO: Recarregar dados locais para sincronizar TodayStudies
        setRefreshTrigger(prev => prev + 1);

        if (result.isLastRevision) {
          setIsLastRevision(true);
          setStudySuccessMessage(result.message);
        } else if (result.nextRevision) {
          setIsLastRevision(false);
          setNextRevisionInfo(result.nextRevision);
          setStudySuccessMessage(`O tópico "${selectedTopicName}" foi marcado como estudado com sucesso!`);
        } else {
          setIsLastRevision(false);
          setNextRevisionInfo(null);
          setStudySuccessMessage(result.message);
        }

        setSuccessDialogOpen(true);
      } else {
        // ✅ NOVO: Tratar erro igual ao TodayStudies
        showFeedback({
          title: "Erro",
          description: result?.message || "Ocorreu um erro ao marcar o tópico como estudado.",
          type: "error"
        });
      }
    }
  };

  const handleCancelCreateWeeks = () => {
    setCreateWeeksDialogOpen(false);
    setPendingTopicId(null);
    setWeeksToCreate(0);
    setRevisionDateFormatted("");
    setPendingRevisionNumber(0);
  };

  const handleConfirmCreateWeeks = async () => {
    if (pendingTopicId) {
      setCreateWeeksDialogOpen(false);

      try {
        const result = await createWeeksAndMarkStudied(
          pendingTopicId,
          weeksToCreate,
          pendingRevisionNumber
        );

        if (result && result.success) {
          // 🔄 ATUALIZAÇÃO IMEDIATA: Invalidar queries E recarregar dados locais
          queryClient.invalidateQueries({ queryKey: ['study-schedule', user?.id] });
          await loadCurrentSchedule(); // ✅ NOVO: Recarregar dados locais para sincronizar TodayStudies
          setRefreshTrigger(prev => prev + 1);

          if (result.isLastRevision) {
            setIsLastRevision(true);
            setStudySuccessMessage(result.message);
          } else if (result.nextRevision) {
            setIsLastRevision(false);
            setNextRevisionInfo(result.nextRevision);
            setStudySuccessMessage(`O tópico "${selectedTopicName}" foi marcado como estudado com sucesso!`);
          } else {
            setIsLastRevision(false);
            setNextRevisionInfo(null);
            setStudySuccessMessage(result.message);
          }

          setSuccessDialogOpen(true);
        } else {
          // ✅ NOVO: Tratar erro igual ao TodayStudies
          showFeedback({
            title: "Erro",
            description: result?.message || "Erro ao criar semanas de estudo. Tente novamente.",
            type: "error"
          });
        }
      } catch (error) {
        console.error('Erro ao criar semanas:', error);
        showFeedback({
          title: "Erro",
          description: "Erro ao criar semanas de estudo. Tente novamente.",
          type: "error"
        });
      }
    }

    setPendingTopicId(null);
    setWeeksToCreate(0);
    setRevisionDateFormatted("");
    setPendingRevisionNumber(0);
  };

  // ✅ EXATAMENTE IGUAL ao TodayStudies
  const handlePracticeQuestions = async (topic: any) => {
    try {
      setLoadingQuestions(true);

      const cacheKey = `${topic.specialtyId || topic.specialty_id}-${topic.themeId || topic.theme_id}-${topic.focusId || topic.focus_id}-${domain}-${user?.id}-initial`;
      const cachedData = getCachedTopicData(cacheKey);

      let result;
      if (cachedData) {
        result = cachedData;
      } else {
        const { getQuestionsOptimized } = await import('@/utils/questionUtils');
        result = await getQuestionsOptimized(
          topic.specialtyId || topic.specialty_id,
          topic.themeId || topic.theme_id,
          topic.focusId || topic.focus_id,
          1000, // Limite alto para contar todas
          undefined,
          undefined, // Sem filtro de instituições aqui
          false,
          domain,
          false, // hideAnswered será aplicado depois no dialog
          user?.id,
          true // ✅ NOVO: Retornar apenas IDs
        );
        setCachedTopicData(cacheKey, result);
      }

      setNextStudyMaxQuestions(result.count);
      setAllTopicsData([{
        topic,
        questionCount: result.count,
        questions: result.data // Apenas IDs agora
      }]);
      setDialogOpen(true);
    } catch (error) {
      showFeedback({
        title: "Erro",
        description: "Erro ao carregar questões. Tente novamente.",
        type: "error"
      });
    } finally {
      setLoadingQuestions(false);
    }
  };

  // ✅ REUTILIZAR: Mesma função do TodayStudies
  const handleStartStudy = async (quantity: number, hideAnswered: boolean = false, institutionIds?: string[]) => {
    if (!user?.id || allTopicsData.length === 0) {
      return;
    }

    try {
      setLoadingState(true);
      if (allTopicsData.length === 1) {
        const { topic, questions } = allTopicsData[0];
        if (!questions.length) {
          toast({
            title: "Nenhuma questão encontrada",
            description: "Não encontramos questões com os critérios selecionados.",
            variant: "destructive"
          });
          return;
        }

        const { getQuestionsOptimized } = await import('@/utils/questionUtils');
        const result = await getQuestionsOptimized(
          topic.specialtyId || topic.specialty_id,
          topic.themeId || topic.theme_id,
          topic.focusId || topic.focus_id,
          quantity,
          undefined,
          institutionIds,
          true,
          domain,
          hideAnswered,
          user.id,
          true
        );

        if (!result.data || result.data.length === 0) {
          throw new Error('Nenhuma questão encontrada com os filtros aplicados');
        }

        const title = getTopicTitle(topic);
        const questionIds = result.data.map((q: any) => typeof q === 'string' ? q : q.id);

        const session = await createSession(user.id, questionIds, title);
        if (session) {
          navigate(`/questions/${session.id}`);
        } else {
          throw new Error('Erro ao criar sessão de estudo');
        }
        return;
      }
    } catch (error) {
      console.error('Erro ao iniciar estudo:', error);
      toast({
        title: "❌ Erro",
        description: "Erro ao iniciar estudo. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setLoadingState(false);
    }
  };

  // Atualizar horário a cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(interval);
  }, []);

  // Saudação baseada no horário
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Bom dia';
    if (hour < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  // Nome do usuário
  const userName = user?.user_metadata?.name || 
    user?.user_metadata?.full_name || 
    user?.email?.split('@')[0] || 
    'Estudante';

  // Função para formatar tempo
  const formatTime = (minutes: number) => {
    if (minutes < 1) return '0min';
    return `${minutes}min`;
  };

  // Gerar dias da semana (domingo a sábado)
  const today = new Date();
  const startDate = startOfWeek(today, { weekStartsOn: 0 }); // Domingo
  
  const weekDays = Array.from({ length: 7 }, (_, index) => {
    const date = addDays(startDate, index);
    const shortName = format(date, 'EEEEE', { locale: ptBR }).toUpperCase(); // D, S, T, Q, Q, S, S
    const isActive = isDayActive(date, weekActivities);
    const isCurrentDay = isToday(date);
    
    return {
      key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`,
      shortName,
      isActive,
      isCurrentDay
    };
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-xl p-4 border border-gray-200/50 shadow-lg backdrop-blur-sm"
    >


      {/* ✅ NOVO: Layout Criativo - Stats + Insights Unificados */}
      <div className="space-y-3 mb-4">
        {/* Stats do Dia - Linha Compacta */}
        <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-center gap-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div className="text-center">
                <div className="text-sm font-bold text-green-700">
                  {statsLoading ? '...' : questionsAnswered}
                </div>
                <div className="text-xs text-green-600">questões hoje</div>
              </div>
            </div>

            <div className="w-px h-8 bg-gray-300"></div>

            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <div className="text-center">
                <div className="text-sm font-bold text-blue-700">
                  {statsLoading ? '...' : formatTime(timeStudied)}
                </div>
                <div className="text-xs text-blue-600">estudado hoje</div>
              </div>
            </div>

            <div className="w-px h-8 bg-gray-300"></div>

            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-purple-600" />
              <div className="text-center">
                <div className="text-sm font-bold text-purple-700">
                  {statsLoading ? '...' : flashcardsStudied}
                </div>
                <div className="text-xs text-purple-600">cards hoje</div>
              </div>
            </div>
          </div>
        </div>

        {/* Seção Compacta Mobile: Insight + Próximo Estudo */}
        <div className="space-y-2">

          {/* Insight Diário - Ultra Compacto Mobile */}
          {(insights?.suggested_focus || insightsLoading) && (
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-2 border border-purple-200">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-1">
                  <Brain className="h-3 w-3 text-purple-600" />
                  <span className="text-xs font-medium text-purple-700">
                    Insight Diário
                  </span>
                </div>

                {/* Botões de ação no cabeçalho mobile */}
                <div className="flex items-center gap-1">
                  {/* Botão Trocar Insight */}
                  {insights?.suggested_focus && (
                    <button
                      onClick={handleRefreshInsight}
                      className="w-4 h-4 rounded bg-white/60 hover:bg-white border border-purple-200 hover:border-purple-300 flex items-center justify-center transition-all duration-200 group/refresh"
                      title="Trocar insight do dia"
                    >
                      <RefreshCw className="h-2 w-2 text-purple-600 group-hover/refresh:text-purple-700" />
                    </button>
                  )}

                  {/* Botão Info */}
                  {insights?.suggested_focus && (
                    <button
                      onClick={() => setShowInsightInfo(true)}
                      className="w-4 h-4 rounded bg-white/60 hover:bg-white border border-purple-200 hover:border-purple-300 flex items-center justify-center transition-all duration-200 group/info"
                      title="Ver detalhes do insight"
                    >
                      <Info className="h-2 w-2 text-purple-600 group-hover/info:text-purple-700" />
                    </button>
                  )}
                </div>
              </div>

              {/* Conteúdo do Insight - Ultra Compacto Mobile */}
              {insights?.suggested_focus ? (
                <div className="space-y-1">
                  <div className="text-xs font-medium text-gray-800 leading-tight line-clamp-1">
                    {insights.suggested_focus.focus_name}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`px-1 py-0.5 rounded text-xs font-medium border ${getTemperatureColor(insights.suggested_focus.temperature)}`}>
                      {insights.suggested_focus.temperature || 'Frio'}
                    </span>
                    <button
                      onClick={() => openStudyDialog(insights.suggested_focus)}
                      className="h-4 px-1.5 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-medium rounded flex items-center gap-0.5"
                    >
                      <Play className="h-1.5 w-1.5" />
                      Estudar
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-xs text-gray-500">Carregando...</div>
              )}
            </div>
          )}

          {/* Próximo Estudo - Mobile com Status de Horário */}
          <div className="bg-gradient-to-br from-[#58CC02]/10 to-[#46a302]/10 rounded-lg border-2 border-[#58CC02]/30 p-2 shadow-sm">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-1.5">
                <div className="w-4 h-4 bg-gradient-to-br from-[#58CC02] to-[#46a302] rounded flex items-center justify-center shadow-sm border border-black/10">
                  <BookOpen className="h-2 w-2 text-white" />
                </div>
                <span className="text-xs font-bold text-[#58CC02]">
                  Próximo Estudo
                </span>
                {totalCount > 0 && (
                  <span className="text-xs text-[#46a302] font-semibold bg-[#58CC02]/20 px-1 py-0.5 rounded-full">
                    {completedCount}/{totalCount}
                  </span>
                )}
              </div>
              {/* Status de Horário Mobile */}
              <div className={`px-1 py-0.5 rounded text-xs font-semibold border ${timeStatus.bgColor} ${timeStatus.color}`}>
                {timeStatus.icon}
              </div>
            </div>



            {/* Conteúdo mobile redesenhado com foco no título */}
            {nextStudyLoading ? (
              <div className="text-xs text-gray-500 flex items-center gap-1">
                <div className="w-2.5 h-2.5 border border-[#58CC02]/30 border-t-[#58CC02] rounded-full animate-spin"></div>
                Carregando...
              </div>
            ) : nextTopic ? (
              <div className="space-y-1">
                {/* TÍTULO PRINCIPAL - Maior destaque mobile */}
                <div className="text-sm font-bold text-gray-900 leading-tight line-clamp-2">
                  {getTopicTitle(nextTopic)}
                </div>
                <div className="flex items-center justify-between">
                  {/* Status de horário com emoji mobile */}
                  <div className="flex items-center gap-1">
                    <span className={`text-xs font-medium ${timeStatus.color} flex items-center gap-1`}>
                      {timeStatus.icon} {timeStatus.message}
                    </span>
                    {nextTopic.is_manual && (
                      <span className="px-1 py-0.5 bg-gray-100 text-gray-600 rounded text-xs">
                        📚
                      </span>
                    )}
                  </div>
                  {/* Botões de ação mobile */}
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleMarkAsStudied(nextTopic.id, getTopicTitle(nextTopic))}
                      className="h-4 px-1.5 bg-[#58CC02] hover:bg-[#46a302] text-white border border-black/10 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-bold rounded flex items-center gap-0.5"
                    >
                      <CheckCircle className="h-1.5 w-1.5" />
                      OK
                    </button>
                    {!nextTopic.is_manual && (
                      <button
                        onClick={() => handlePracticeQuestions(nextTopic)}
                        disabled={loadingQuestions}
                        className="h-4 px-1.5 bg-gradient-to-r from-[#7E69AB] to-[#9B87C4] hover:from-[#6B5B95] hover:to-[#8A7BB8] text-white border border-black/10 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-bold rounded flex items-center gap-0.5 disabled:opacity-50"
                      >
                        <Play className="h-1.5 w-1.5" />
                        {loadingQuestions ? '...' : 'Estudar'}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ) : totalCount > 0 ? (
              <div className="text-center py-1 space-y-1">
                <div className="text-sm">🎉</div>
                <div className="text-xs font-bold text-[#58CC02]">
                  Todos concluídos!
                </div>
                <button
                  onClick={() => navigate('/schedule')}
                  className="w-full h-4 bg-gradient-to-r from-[#7E69AB] to-[#9B87C4] hover:from-[#6B5B95] hover:to-[#8A7BB8] text-white border border-black/10 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-bold rounded flex items-center justify-center gap-1"
                >
                  <Calendar className="h-2 w-2" />
                  Ir ao Cronograma
                </button>
              </div>
            ) : (
              <div className="text-center py-1.5 space-y-1.5">
                <div className="space-y-0.5">
                  <div className="text-xs font-semibold text-gray-700">
                    Nenhum estudo programado
                  </div>
                </div>
                <button
                  onClick={() => navigate('/schedule')}
                  className="w-full h-4 bg-gradient-to-r from-[#7E69AB] to-[#9B87C4] hover:from-[#6B5B95] hover:to-[#8A7BB8] text-white border border-black/10 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-bold rounded flex items-center justify-center gap-1"
                >
                  <Calendar className="h-2 w-2" />
                  Criar Cronograma
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Ofensiva Compacta */}
      <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-3 border border-orange-200">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="text-xs font-semibold text-orange-700">Ofensiva Semanal</span>
            {maxStreak > 0 && (
              <div className="flex items-center gap-1 bg-amber-50 px-1.5 py-0.5 rounded-full border border-amber-200">
                <Trophy className="h-2.5 w-2.5 text-amber-600" />
                <span className="text-xs font-bold text-amber-700">{maxStreak}</span>
              </div>
            )}
          </div>
        </div>

        {/* Mini Calendário */}
        <div className="flex items-center justify-between gap-1">
          {weekDays.map((day, index) => (
            <motion.div
              key={day.key}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.03 * index }}
              className="flex flex-col items-center"
            >
              <span className={`text-xs font-bold mb-0.5 ${
                day.isCurrentDay 
                  ? 'text-blue-600' 
                  : day.isActive 
                    ? 'text-orange-500' 
                    : 'text-gray-500'
              }`}>
                {day.shortName}
              </span>
              
              <div className={`
                w-4 h-4 rounded-full flex items-center justify-center transition-all duration-300
                ${day.isActive
                  ? 'bg-orange-500 text-white'
                  : day.isCurrentDay
                    ? 'bg-blue-200 text-blue-600'
                    : 'bg-gray-200 text-gray-400'
                }
              `}>
                {day.isActive ? (
                  <CheckCircle className="h-2 w-2" />
                ) : day.isCurrentDay ? (
                  <Star className="h-1.5 w-1.5" />
                ) : null}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* ✅ NOVO: Dialog de Estudos do Insight */}
      <StudyOptionsDialog
        open={isDialogOpen}
        onOpenChange={closeStudyDialog}
        maxQuestions={maxQuestions}
        minQuestions={1}
        availableYears={availableYears}
        availableInstitutions={availableInstitutions}
        onStartStudy={startStudy}
        totalTopics={1}
        specialtyId={undefined}
        themeId={undefined}
        focusId={selectedInsight?.focus_id}
      />

      {/* Dialog de Informações sobre Insights */}
      <InsightInfoDialog
        open={showInsightInfo}
        onOpenChange={setShowInsightInfo}
      />

      {/* ✅ REUTILIZAR: Mesmos dialogs do TodayStudies */}
      <Suspense fallback={null}>
        <ConfirmStudyDialog
          open={confirmDialogOpen}
          onOpenChange={setConfirmDialogOpen}
          onConfirm={handleConfirmStudied}
          topicName={selectedTopicName}
        />
      </Suspense>

      <AlertDialog open={successDialogOpen} onOpenChange={setSuccessDialogOpen}>
        <AlertDialogContent className="w-[80dvw] border-2 border-black rounded-xl p-0 overflow-hidden max-w-md">
          <AlertDialogHeader className="p-4 sm:p-6 border-b-2 border-black bg-green-100">
            <div className="flex items-center gap-3">
              <div className="bg-white p-2 rounded-full border-2 border-black">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <AlertDialogTitle className="text-xl font-bold text-black">
                {isLastRevision ? "Revisões Concluídas" : "Tópico estudado"}
              </AlertDialogTitle>
            </div>
          </AlertDialogHeader>

          <div className="p-4 sm:p-6">
            <AlertDialogDescription className="text-base text-gray-700">
              {studySuccessMessage}
            </AlertDialogDescription>

            {nextRevisionInfo && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">
                  Próxima Revisão Agendada
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                    <span>
                      <strong>Data:</strong> {nextRevisionInfo.date} ({nextRevisionInfo.dayOfWeek})
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-blue-600" />
                    <span>
                      <strong>Em:</strong> {nextRevisionInfo.daysUntil} dias
                    </span>
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2 text-blue-600" />
                    <span>
                      <strong>Revisão:</strong> {formatRevisionNumber(nextRevisionInfo.revisionNumber)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <AlertDialogFooter className="flex justify-end mt-6">
              <AlertDialogAction className="bg-black hover:bg-black/90 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all">
                OK
              </AlertDialogAction>
            </AlertDialogFooter>
          </div>
        </AlertDialogContent>
      </AlertDialog>

      {/* ✅ NOVO: Diálogo de criação de semanas (IGUAL TodayStudies) */}
      <AlertDialog open={createWeeksDialogOpen} onOpenChange={setCreateWeeksDialogOpen}>
        <AlertDialogContent className="w-[80dvw] border-2 border-black rounded-xl p-0 overflow-hidden max-w-md">
          <AlertDialogHeader className="p-4 sm:p-6 border-b-2 border-black bg-orange-100">
            <div className="flex items-center gap-3">
              <div className="bg-white p-2 rounded-full border-2 border-black">
                <Calendar className="h-5 w-5 text-orange-600" />
              </div>
              <AlertDialogTitle className="text-xl font-bold text-black">
                Criar Semanas de Estudo
              </AlertDialogTitle>
            </div>
          </AlertDialogHeader>

          <div className="p-4 sm:p-6">
            <AlertDialogDescription className="text-base text-gray-700 mb-4">
              Você não tem semanas de estudo suficientes para agendar a revisão deste tópico.
            </AlertDialogDescription>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
              <h4 className="font-semibold text-blue-800 mb-2">Detalhes da Revisão</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                  <span><strong>Data prevista:</strong> {revisionDateFormatted}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-blue-600" />
                  <span><strong>Semanas necessárias:</strong> {weeksToCreate}</span>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-6">
              <p className="text-sm text-yellow-800">
                <strong>Deseja criar {weeksToCreate} {weeksToCreate === 1 ? 'semana' : 'semanas'} de estudo para revisar este tema no dia {revisionDateFormatted}?</strong>
              </p>
            </div>

            <AlertDialogFooter className="flex gap-2 justify-end">
              <AlertDialogAction
                onClick={handleCancelCreateWeeks}
                className="bg-gray-500 hover:bg-gray-600 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              >
                Não, cancelar
              </AlertDialogAction>
              <AlertDialogAction
                onClick={handleConfirmCreateWeeks}
                className="bg-green-600 hover:bg-green-700 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              >
                Sim, criar semanas
              </AlertDialogAction>
            </AlertDialogFooter>
          </div>
        </AlertDialogContent>
      </AlertDialog>

      <Suspense fallback={null}>
        <StudyOptionsDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          maxQuestions={nextStudyMaxQuestions}
          minQuestions={allTopicsData.length > 0 ? allTopicsData.length : 1}
          availableYears={availableYears}
          availableInstitutions={availableInstitutions}
          onStartStudy={handleStartStudy}
          totalTopics={allTopicsData.length}
          specialtyId={allTopicsData.length === 1 ? allTopicsData[0]?.topic?.specialtyId || allTopicsData[0]?.topic?.specialty_id : undefined}
          themeId={allTopicsData.length === 1 ? allTopicsData[0]?.topic?.themeId || allTopicsData[0]?.topic?.theme_id : undefined}
          focusId={allTopicsData.length === 1 ? allTopicsData[0]?.topic?.focusId || allTopicsData[0]?.topic?.focus_id : undefined}
          allTopicsData={allTopicsData}
        />
      </Suspense>
    </motion.div>
  );
};

export default MobileUnifiedBanner;
