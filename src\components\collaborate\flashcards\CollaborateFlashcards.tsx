import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { FlashcardForm } from "@/components/collaborate/flashcards/FlashcardForm";
import { FlashcardList } from "@/components/collaborate/flashcards/FlashcardList";
import { SpecialtyTab } from "@/components/collaborate/flashcards/hierarchy/SpecialtyTab";
import { ThemeTab } from "@/components/collaborate/flashcards/hierarchy/ThemeTab";
import { FocusTab } from "@/components/collaborate/flashcards/hierarchy/FocusTab";
import { ExtrafocusTab } from "@/components/collaborate/flashcards/hierarchy/ExtrafocusTab";
import DashboardLayout from "@/components/DashboardLayout";
import { Card } from "@/components/ui/card";

const CollaborateFlashcards = () => {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <Card className="p-6">
          <h2 className="text-2xl font-semibold mb-6">Gerenciar Categorias</h2>
          <Tabs defaultValue="specialty" className="w-full">
            <TabsList className="w-full">
              <TabsTrigger value="specialty" className="flex-1">Especialidades</TabsTrigger>
              <TabsTrigger value="theme" className="flex-1">Temas</TabsTrigger>
              <TabsTrigger value="focus" className="flex-1">Focos</TabsTrigger>
            </TabsList>
            
            <TabsContent value="specialty">
              <SpecialtyTab />
            </TabsContent>
            
            <TabsContent value="theme">
              <ThemeTab />
            </TabsContent>
            
            <TabsContent value="focus">
              <FocusTab />
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default CollaborateFlashcards;