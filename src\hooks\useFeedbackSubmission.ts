import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface FeedbackData {
  feedbackType: string;
  rating: number;
  comment: string;
  email?: string | null;
  whatsapp?: string | null;
}

export const useFeedbackSubmission = () => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const submitFeedback = async (data: FeedbackData) => {
    if (!user) {
      toast.error('Você precisa estar logado para enviar feedback');
      return;
    }

    setIsSubmitting(true);
    setIsSuccess(false);

    try {
      // Preparar dados para inserção na tabela site_visitor_feedbacks
      const feedbackData = {
        visitor_id: user.id,
        rating: data.rating,
        comment: `[${data.feedbackType.toUpperCase()}] ${data.comment}`,
        email: data.email || user.email || null,
        whatsapp: data.whatsapp || null,
        interested_in_beta: null, // Pode ser usado futuramente
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('site_visitor_feedbacks')
        .insert(feedbackData);

      if (error) {
        throw error;
      }

      setIsSuccess(true);
      toast.success('Feedback enviado com sucesso! Obrigado pela sua contribuição.');

    } catch (error) {
      toast.error('Erro ao enviar feedback. Tente novamente mais tarde.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setIsSuccess(false);
  };

  return {
    submitFeedback,
    isSubmitting,
    isSuccess,
    resetForm
  };
};
