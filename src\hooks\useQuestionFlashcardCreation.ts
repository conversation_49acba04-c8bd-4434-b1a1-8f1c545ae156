import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useCurrentQuestion } from '@/contexts/CurrentQuestionContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// 🎯 Armazenar IDs dos flashcards importados durante a sessão atual
let currentSessionImportedIds: string[] = [];

// 🎯 Função para obter flashcards importados na sessão atual
export const getCurrentSessionImportedIds = () => currentSessionImportedIds;

// 🎯 Função para limpar flashcards da sessão (quando iniciar nova sessão)
export const clearCurrentSessionImportedIds = () => {
  currentSessionImportedIds = [];
};

export interface FlashcardGenerationRequest {
  types: string[];
  quantity?: number;
}

export interface GeneratedFlashcard {
  id: string;
  front: string;
  back: string;
  type: string;
  front_image?: string | null;
  back_image?: string | null;
}

export interface FlashcardCreationData {
  front: string;
  back: string;
  front_image?: string | null;
  back_image?: string | null;
  specialty_id?: string;
  theme_id?: string;
  focus_id?: string;
}

// 🎯 Função para buscar ID de categoria na tabela study_categories
const findCategoryIdByName = async (categoryName: string | undefined, categoryType: 'specialty' | 'theme' | 'focus'): Promise<string | null> => {
  if (!categoryName) return null;

  try {
    const { data, error } = await supabase
      .from('study_categories')
      .select('id')
      .eq('name', categoryName)
      .eq('type', categoryType)
      .single();

    if (error) {
      console.warn(`⚠️ [findCategoryIdByName] Categoria não encontrada: ${categoryName} (${categoryType})`, error);
      return null;
    }

    console.log(`✅ [findCategoryIdByName] Categoria encontrada: ${categoryName} -> ${data.id}`);
    return data.id;
  } catch (error) {
    console.error(`❌ [findCategoryIdByName] Erro ao buscar categoria: ${categoryName}`, error);
    return null;
  }
};

export const useQuestionFlashcardCreation = () => {
  const { user } = useAuth();
  const { currentQuestion } = useCurrentQuestion();
  const { toast } = useToast();
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [generatedCards, setGeneratedCards] = useState<GeneratedFlashcard[]>([]);

  // 🎯 Função para gerar flashcards com IA
  const generateFlashcards = async (request: FlashcardGenerationRequest): Promise<GeneratedFlashcard[]> => {
    if (!currentQuestion || !user) {
      console.error('❌ [useQuestionFlashcardCreation] Questão atual ou usuário não disponível:', {
        hasCurrentQuestion: !!currentQuestion,
        hasUser: !!user,
        currentQuestion: currentQuestion
      });
      throw new Error('Questão atual ou usuário não disponível');
    }

    setIsGenerating(true);

    try {

      // Preparar dados da questão para a IA
      const questionData = {
        id: currentQuestion.id,
        question_content: currentQuestion.question_content || currentQuestion.statement || '',
        response_choices: currentQuestion.response_choices || [],
        correct_choice: currentQuestion.correct_choice || 0,
        specialty: currentQuestion.specialty?.name || '',
        theme: currentQuestion.theme?.name || '',
        focus: currentQuestion.focus?.name || '',
        explanation: currentQuestion.explanation || '',
        specialty_id: currentQuestion.specialty_id,
        theme_id: currentQuestion.theme_id,
        focus_id: currentQuestion.focus_id,
        ai_commentary: currentQuestion.ai_commentary || null // ✅ Incluir comentário da IA
      };

      // Verificar se tem comentário da IA (sem log detalhado)

      const results: GeneratedFlashcard[] = [];

      // Gerar flashcards para cada tipo selecionado
      for (const type of request.types) {
        const requestBody = {
          fcType: type,
          quantity: request.quantity || 1,
          directQuestions: [questionData], // Edge function espera array de questões
          userId: user.id
        };

        const { data, error } = await supabase.functions.invoke('flashcards-ai-generate', {
          body: requestBody
        });

        if (error) {
          console.error(`❌ [useQuestionFlashcardCreation] Erro ao gerar flashcard tipo ${type}:`, error);
          throw error;
        }

        if (data?.generated && Array.isArray(data.generated)) {
          const typedCards = data.generated.map((card: any, index: number) => ({
            id: `generated-${type}-${index}-${Date.now()}`,
            front: card.front || '',
            back: card.back || '',
            type: type,
            front_image: card.front_image || null,
            back_image: card.back_image || null
          }));

          results.push(...typedCards);
        }
      }

      setGeneratedCards(results);

      toast({
        title: "Flashcards gerados!",
        description: `${results.length} flashcard(s) gerado(s) com sucesso.`
      });

      return results;

    } catch (error) {
      console.error('❌ [useQuestionFlashcardCreation] Erro na geração:', error);
      
      toast({
        title: "Erro na geração",
        description: "Não foi possível gerar os flashcards. Tente novamente.",
        variant: "destructive"
      });

      throw error;
    } finally {
      setIsGenerating(false);
    }
  };

  // 🎯 Função para criar flashcard manual
  const createManualFlashcard = async (data: FlashcardCreationData): Promise<void> => {
    if (!user || !currentQuestion) {
      throw new Error('Usuário ou questão atual não disponível');
    }

    setIsImporting(true);

    try {
      console.log('🎯 [useQuestionFlashcardCreation] Criando flashcard manual:', data);

      // 🔍 Log das categorias da questão atual
      console.log('🔍 [useQuestionFlashcardCreation] Categorias da questão atual:', {
        specialty: currentQuestion.specialty,
        theme: currentQuestion.theme,
        focus: currentQuestion.focus
      });

      // 🎯 Buscar IDs das categorias na tabela study_categories
      let validSpecialtyId = data.specialty_id || currentQuestion.specialty_id;
      let validThemeId = data.theme_id || currentQuestion.theme_id;
      let validFocusId = data.focus_id || currentQuestion.focus_id;

      // Se não temos IDs diretos, buscar pelos nomes das categorias
      if (!validSpecialtyId && currentQuestion.specialty?.name) {
        validSpecialtyId = await findCategoryIdByName(currentQuestion.specialty.name, 'specialty');

      }

      if (!validThemeId && currentQuestion.theme?.name) {
        validThemeId = await findCategoryIdByName(currentQuestion.theme.name, 'theme');

      }

      if (!validFocusId && currentQuestion.focus?.name) {
        validFocusId = await findCategoryIdByName(currentQuestion.focus.name, 'focus');

      }


      const flashcardData = {
        user_id: user.id,
        front: data.front.trim(),
        back: data.back.trim(),
        front_image: data.front_image || null,
        back_image: data.back_image || null,
        specialty_id: validSpecialtyId || null,
        theme_id: validThemeId || null,
        focus_id: validFocusId || null,
        current_state: 'available',
        is_shared: false, // Flashcards criados durante estudo são privados por padrão
        origin_id: null
      };



      const { data: insertData, error } = await supabase
        .from('flashcards_cards')
        .insert(flashcardData)
        .select('id')
        .single();

      if (error) {
        console.error('❌ [useQuestionFlashcardCreation] Erro ao criar flashcard:', error);
        throw error;
      }

      // Atualizar origin_id para referenciar a si mesmo
      if (insertData?.id) {
        await supabase
          .from('flashcards_cards')
          .update({ origin_id: insertData.id })
          .eq('id', insertData.id);
      }

      // Adicionar à lista de flashcards importados na sessão atual
      if (insertData.id) {
        currentSessionImportedIds.push(insertData.id);
      }

      toast({
        title: "Flashcard importado!",
        description: "Flashcard adicionado diretamente à sua coleção.",
        duration: 3000
      });

    } catch (error) {
      console.error('❌ [useQuestionFlashcardCreation] Erro na criação:', error);
      
      toast({
        title: "Erro na criação",
        description: "Não foi possível criar o flashcard. Tente novamente.",
        variant: "destructive"
      });

      throw error;
    } finally {
      setIsImporting(false);
    }
  };

  // 🎯 Função para importar flashcard gerado
  const importGeneratedFlashcard = async (card: GeneratedFlashcard): Promise<void> => {
    if (!user || !currentQuestion) {
      throw new Error('Usuário ou questão atual não disponível');
    }

    setIsImporting(true);

    try {
      console.log('🎯 [useQuestionFlashcardCreation] Importando flashcard gerado:', card);

      // 🎯 Buscar IDs das categorias na tabela study_categories (mesmo processo)
      let validSpecialtyId = currentQuestion.specialty_id;
      let validThemeId = currentQuestion.theme_id;
      let validFocusId = currentQuestion.focus_id;

      // Se não temos IDs diretos, buscar pelos nomes das categorias
      if (!validSpecialtyId && currentQuestion.specialty?.name) {
        validSpecialtyId = await findCategoryIdByName(currentQuestion.specialty.name, 'specialty');
      }

      if (!validThemeId && currentQuestion.theme?.name) {
        validThemeId = await findCategoryIdByName(currentQuestion.theme.name, 'theme');
      }

      if (!validFocusId && currentQuestion.focus?.name) {
        validFocusId = await findCategoryIdByName(currentQuestion.focus.name, 'focus');
      }

      const flashcardData = {
        user_id: user.id,
        front: card.front.trim(),
        back: card.back.trim(),
        front_image: card.front_image || null,
        back_image: card.back_image || null,
        specialty_id: validSpecialtyId || null,
        theme_id: validThemeId || null,
        focus_id: validFocusId || null,
        current_state: 'available',
        is_shared: false, // Flashcards criados durante estudo são privados por padrão
        origin_id: null,
        flashcard_type: card.type
      };

      const { data: insertData, error } = await supabase
        .from('flashcards_cards')
        .insert(flashcardData)
        .select('id')
        .single();

      if (error) {
        console.error('❌ [useQuestionFlashcardCreation] Erro ao importar flashcard:', error);
        throw error;
      }

      // Atualizar origin_id para referenciar a si mesmo
      if (insertData?.id) {
        await supabase
          .from('flashcards_cards')
          .update({ origin_id: insertData.id })
          .eq('id', insertData.id);
      }

      console.log('✅ [useQuestionFlashcardCreation] Flashcard importado diretamente:', insertData);

      // Adicionar à lista de flashcards importados na sessão atual
      if (insertData?.id) {
        currentSessionImportedIds.push(insertData.id);
        console.log('📝 [useQuestionFlashcardCreation] Flashcard gerado adicionado à sessão atual:', {
          id: insertData.id,
          totalNaSessao: currentSessionImportedIds.length,
          idsNaSessao: currentSessionImportedIds
        });
      }

      toast({
        title: "Flashcard importado!",
        description: "Flashcard adicionado à sua coleção e à sessão atual."
      });

    } catch (error) {
      console.error('❌ [useQuestionFlashcardCreation] Erro na importação:', error);
      
      toast({
        title: "Erro na importação",
        description: "Não foi possível importar o flashcard. Tente novamente.",
        variant: "destructive"
      });

      throw error;
    } finally {
      setIsImporting(false);
    }
  };

  // 🎯 Função para importar flashcard individual
  const importSingleFlashcard = async (flashcard: {front: string, back: string, type?: string}): Promise<void> => {
    try {
      await createManualFlashcard({
        front: flashcard.front,
        back: flashcard.back
      });
    } catch (error) {
      // Erro já tratado no createManualFlashcard
      throw error;
    }
  };

  // 🎯 Função para limpar flashcards gerados
  const clearGeneratedCards = () => {
    setGeneratedCards([]);
  };

  return {
    // Estados
    isGenerating,
    isImporting,
    generatedCards,

    // Funções
    generateFlashcards,
    createManualFlashcard,
    importGeneratedFlashcard,
    importSingleFlashcard,
    clearGeneratedCards,

    // Dados da questão atual
    currentQuestion,
    hasCurrentQuestion: !!currentQuestion
  };
};
