import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Sparkles, Calendar, Clock, Target, CheckCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const PersonalizedBanner: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Atualizar horário a cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(interval);
  }, []);

  // Saudação baseada no horário
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Bom dia';
    if (hour < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  // Nome do usuário
  const userName = user?.user_metadata?.name || 
    user?.user_metadata?.full_name || 
    user?.email?.split('@')[0] || 
    'Estudante';

  // Data formatada
  const formattedDate = currentTime.toLocaleDateString('pt-BR', { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long' 
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-r from-blue-50 via-white to-purple-50 rounded-xl p-4 border-2 border-blue-200 shadow-md hover:shadow-lg transition-all duration-300"
    >
      <div className="flex items-center justify-between">
        {/* Left: Greeting & Info */}
        <div className="flex items-center gap-4">
          <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-2 rounded-full border-2 border-blue-300">
            <Sparkles className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-800">
              {getGreeting()}, {userName}! 👋
            </h3>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Calendar className="h-3 w-3" />
              <span>{formattedDate}</span>
            </div>
          </div>
        </div>

        {/* Center: Quick Stats */}
        <div className="hidden lg:flex items-center gap-4">
          <div className="flex items-center gap-2 bg-green-50 px-3 py-2 rounded-lg border border-green-200">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <div className="text-center">
              <div className="text-sm font-bold text-green-700">0</div>
              <div className="text-xs text-green-600">questões hoje</div>
            </div>
          </div>
          <div className="flex items-center gap-2 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
            <Clock className="h-4 w-4 text-blue-600" />
            <div className="text-center">
              <div className="text-sm font-bold text-blue-700">0min</div>
              <div className="text-xs text-blue-600">estudado</div>
            </div>
          </div>
          <div className="flex items-center gap-2 bg-purple-50 px-3 py-2 rounded-lg border border-purple-200">
            <Target className="h-4 w-4 text-purple-600" />
            <div className="text-center">
              <div className="text-sm font-bold text-purple-700">14%</div>
              <div className="text-xs text-purple-600">meta semanal</div>
            </div>
          </div>
        </div>

        {/* Right: Motivational Message */}
        <div className="hidden sm:block lg:hidden bg-gradient-to-r from-green-100 to-blue-100 px-4 py-2 rounded-full border border-green-200">
          <span className="text-sm font-medium text-green-700">
            Pronto para estudar? 🚀
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default PersonalizedBanner;
