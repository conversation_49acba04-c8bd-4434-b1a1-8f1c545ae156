import React, { useState, useRef } from 'react';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface FormattedQuestionContentProps {
  content: string;
  className?: string;
  questionId?: string; // Para salvar grifos por questão
}

export const FormattedQuestionContent: React.FC<FormattedQuestionContentProps> = ({
  content,
  className = "",
  questionId = "default"
}) => {
  const [highlights, setHighlights] = useState<string[]>([]);
  const contentRef = useRef<HTMLDivElement>(null);
  const { isDarkMode } = useDarkMode();

  // Detectar se é dispositivo móvel
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  const handleTextSelection = () => {
    // No mobile, não usar seleção de texto (muito complicado)
    if (isMobile) return;

    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const selectedText = selection.toString().trim();
    if (selectedText.length < 3) return; // Mínimo 3 caracteres

    // Verificar se o texto já está grifado (busca exata ou parcial)
    const existingHighlight = highlights.find(h =>
      h === selectedText ||
      h.includes(selectedText) ||
      selectedText.includes(h)
    );

    if (existingHighlight) {
      // Remover grifo existente
      setHighlights(prev => prev.filter(h => h !== existingHighlight));
    } else {
      // Adicionar novo grifo
      setHighlights(prev => [...prev, selectedText]);
    }

    // Limpar seleção
    selection.removeAllRanges();
  };

  const applyHighlights = (text: string): string => {
    let highlightedText = text;

    highlights.forEach((highlight, index) => {
      const regex = new RegExp(`(${highlight.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
      const highlightClass = isDarkMode
        ? 'bg-yellow-600 hover:bg-red-600 text-gray-900'
        : 'bg-yellow-200 hover:bg-red-200 text-gray-900';

      highlightedText = highlightedText.replace(regex,
        `<mark class="${highlightClass} px-1 rounded cursor-pointer transition-colors"
         data-highlight="${index}"
         title="Clique para remover o grifo">$1</mark>`
      );
    });

    return highlightedText;
  };

  const handleHighlightClick = (event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    if (target.tagName === 'MARK') {
      const highlightIndex = parseInt(target.getAttribute('data-highlight') || '-1');
      if (highlightIndex >= 0 && highlightIndex < highlights.length) {
        setHighlights(prev => prev.filter((_, index) => index !== highlightIndex));
      }
    }
  };

  const formatContent = (text: string): string => {
    let formattedText = text
      // 1. Corrigir entidades HTML (ambas as formas)
      .replace(/&#39;/g, "'")           // &#39; → '
      .replace(/&amp;#39;/g, "'")      // &amp;#39; → '
      .replace(/&#quot;/g, '"')        // &#quot; → "
      .replace(/&amp;quot;/g, '"')     // &amp;quot; → "
      .replace(/&#lt;/g, '<')          // &#lt; → <
      .replace(/&amp;lt;/g, '<')       // &amp;lt; → <
      .replace(/&#gt;/g, '>')          // &#gt; → >
      .replace(/&amp;gt;/g, '>')       // &amp;gt; → >
      .replace(/&#amp;/g, '&')         // &#amp; → &
      .replace(/&amp;amp;/g, '&')      // &amp;amp; → &
      .replace(/&nbsp;/g, ' ')          // &nbsp; → espaço

      // 2. Corrigir caracteres Unicode comuns
      .replace(/\\u00e9/g, 'é')
      .replace(/\\u00e1/g, 'á')
      .replace(/\\u00f3/g, 'ó')
      .replace(/\\u00e7/g, 'ç')
      .replace(/\\u00ea/g, 'ê')
      .replace(/\\u00e0/g, 'à')
      .replace(/\\u00f4/g, 'ô')
      .replace(/\\u00e2/g, 'â')
      .replace(/\\u00ed/g, 'í')
      .replace(/\\u00fa/g, 'ú')
      .replace(/\\u00f5/g, 'õ')
      .replace(/\\u00e3/g, 'ã')
      .replace(/\\u00fc/g, 'ü')
      .replace(/\\u00c1/g, 'Á')
      .replace(/\\u00c9/g, 'É')
      .replace(/\\u00cd/g, 'Í')
      .replace(/\\u00d3/g, 'Ó')
      .replace(/\\u00da/g, 'Ú')
      .replace(/\\u00c7/g, 'Ç')

      // 3. Corrigir aspas e caracteres especiais
      .replace(/\\u201c|\\u201d/g, '"')
      .replace(/\\u2018|\\u2019/g, "'")
      .replace(/\\u2013/g, '–')
      .replace(/\\u2014/g, '—')
      .replace(/\\u00b0/g, '°')
      .replace(/\\u00b2/g, '²')
      .replace(/\\u00b3/g, '³')
      .replace(/\\u00a0/g, ' ')

      // 4. Limpar espaços extras
      .replace(/\s+/g, ' ')
      .trim();

    // 5. Detectar pergunta apenas no final do texto (mais conservador)
    // Procurar por padrões que claramente indicam uma pergunta no final
    const questionMatch = formattedText.match(/^(.*?)\s+((?:Assinale|Qual|Marque|Indique)\s+[^.]*[.?:])\s*$/i);

    if (questionMatch) {
      const mainContent = questionMatch[1].trim();
      const question = questionMatch[2].trim();

      // Adicionar quebras de linha básicas apenas
      const formattedMainContent = mainContent
        .replace(/\.\s+([A-Z][a-z]{3,}[^.]{50,})/g, '.\n\n$1');

      return `${formattedMainContent}\n\n|||QUESTION|||${question}`;
    }

    // Se não encontrou pergunta clara, apenas adicionar quebras básicas
    return formattedText
      .replace(/\.\s+([A-Z][a-z]{3,}[^.]{50,})/g, '.\n\n$1');
  };

  const renderFormattedContent = (formattedText: string) => {
    // Separar o texto principal da pergunta
    const parts = formattedText.split('|||QUESTION|||');

    if (parts.length === 1) {
      // Não há pergunta separada, renderizar tudo junto
      return (
        <div
          ref={contentRef}
          className={`leading-relaxed whitespace-pre-line transition-colors duration-200 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          } ${isMobile ? 'cursor-default' : 'cursor-text select-text'}`}
          dangerouslySetInnerHTML={{ __html: applyHighlights(parts[0]) }}
          onMouseUp={handleTextSelection}
          onClick={handleHighlightClick}
        />
      );
    }

    // Há pergunta separada
    const mainContent = parts[0].trim();
    const question = parts[1].trim();

    return (
      <>
        {mainContent && (
          <div
            className={`leading-relaxed mb-4 whitespace-pre-line transition-colors duration-200 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-700'
            } ${isMobile ? 'cursor-default' : 'cursor-text select-text'}`}
            dangerouslySetInnerHTML={{ __html: applyHighlights(mainContent) }}
            onMouseUp={handleTextSelection}
            onClick={handleHighlightClick}
          />
        )}
        {question && (
          <div
            className={`font-medium mt-4 mb-2 p-3 rounded-lg border-l-4 transition-colors duration-200 ${
              isDarkMode
                ? 'text-gray-100 bg-blue-900 border-blue-400'
                : 'text-gray-900 bg-blue-50 border-blue-400'
            } ${isMobile ? 'cursor-default' : 'cursor-text select-text'}`}
            dangerouslySetInnerHTML={{ __html: applyHighlights(question) }}
            onMouseUp={handleTextSelection}
            onClick={handleHighlightClick}
          />
        )}
      </>
    );
  };

  const formattedContent = formatContent(content);

  return (
    <div className={`formatted-question-content ${className}`}>
      {renderFormattedContent(formattedContent)}
    </div>
  );
};

export default FormattedQuestionContent;
