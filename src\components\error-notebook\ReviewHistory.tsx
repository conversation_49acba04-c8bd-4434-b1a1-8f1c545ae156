import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  Clock, 
  Eye, 
  RotateCcw, 
  CheckCircle, 
  X,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ReviewAttempt {
  id: string;
  date: string;
  mode: 'visualizacao' | 'refazer';
  timeSpent?: number;
  correctOnReview?: boolean;
  annotation?: string;
}

interface ReviewHistoryProps {
  attempts: ReviewAttempt[];
  originalAttempt: {
    date: string;
    timeSpent: number;
    wasCorrect: boolean;
  };
}

export const ReviewHistory: React.FC<ReviewHistoryProps> = ({
  attempts,
  originalAttempt
}) => {
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ptBR 
      });
    } catch {
      return 'Data inválida';
    }
  };

  const getModeIcon = (mode: 'visualizacao' | 'refazer') => {
    return mode === 'visualizacao' 
      ? <Eye className="h-4 w-4 text-blue-500" />
      : <RotateCcw className="h-4 w-4 text-purple-500" />;
  };

  const getModeLabel = (mode: 'visualizacao' | 'refazer') => {
    return mode === 'visualizacao' ? 'Visualização' : 'Refazer';
  };

  const getResultIcon = (correct?: boolean) => {
    if (correct === undefined) return null;
    return correct 
      ? <CheckCircle className="h-4 w-4 text-green-500" />
      : <X className="h-4 w-4 text-red-500" />;
  };

  const getPerformanceTrend = () => {
    const retryAttempts = attempts.filter(a => a.mode === 'refazer' && a.correctOnReview !== undefined);
    if (retryAttempts.length === 0) return null;

    const correctCount = retryAttempts.filter(a => a.correctOnReview).length;
    const totalRetries = retryAttempts.length;
    const successRate = (correctCount / totalRetries) * 100;

    return {
      successRate,
      trend: successRate > 50 ? 'improving' : 'needs_work',
      correctCount,
      totalRetries
    };
  };

  const performance = getPerformanceTrend();

  return (
    <Card className="p-6 bg-white border-2 border-black shadow-card-sm">
      <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
        <Calendar className="h-5 w-5 text-gray-600" />
        Histórico de Revisões
      </h3>

      {/* Performance Summary */}
      {performance && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium">Desempenho nas Revisões</span>
            <div className="flex items-center gap-2">
              {performance.trend === 'improving' ? (
                <TrendingUp className="h-4 w-4 text-green-500" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500" />
              )}
              <Badge variant={performance.trend === 'improving' ? "default" : "destructive"}>
                {performance.successRate.toFixed(0)}% de acerto
              </Badge>
            </div>
          </div>
          <p className="text-sm text-gray-600">
            {performance.correctCount} acertos em {performance.totalRetries} tentativa{performance.totalRetries > 1 ? 's' : ''} de refazer
          </p>
        </div>
      )}

      {/* Timeline */}
      <div className="space-y-4">
        {/* Original Attempt */}
        <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border-l-4 border-gray-400">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <div className="p-2 bg-gray-200 rounded-full">
              <Clock className="h-4 w-4 text-gray-600" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm">Primeira tentativa</span>
                <Badge variant="outline" className="text-xs">
                  Original
                </Badge>
                {getResultIcon(!originalAttempt.wasCorrect)}
              </div>
              <div className="text-xs text-gray-600">
                {formatDate(originalAttempt.date)} • {originalAttempt.timeSpent}s
              </div>
            </div>
          </div>
        </div>

        {/* Review Attempts */}
        {attempts.map((attempt, index) => (
          <div 
            key={attempt.id} 
            className="flex items-start gap-4 p-4 bg-white rounded-lg border-l-4 border-blue-400"
          >
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <div className="p-2 bg-blue-100 rounded-full">
                {getModeIcon(attempt.mode)}
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-sm">
                    {getModeLabel(attempt.mode)} #{index + 1}
                  </span>
                  <Badge 
                    variant={attempt.mode === 'visualizacao' ? "secondary" : "outline"}
                    className="text-xs"
                  >
                    {getModeLabel(attempt.mode)}
                  </Badge>
                  {getResultIcon(attempt.correctOnReview)}
                </div>
                <div className="text-xs text-gray-600 flex items-center gap-2">
                  <span>{formatDate(attempt.date)}</span>
                  {attempt.timeSpent && (
                    <>
                      <span>•</span>
                      <span>{attempt.timeSpent}s</span>
                    </>
                  )}
                  {attempt.correctOnReview !== undefined && attempt.correctOnReview !== null && (
                    <>
                      <span>•</span>
                      <span className={attempt.correctOnReview ? 'text-green-600' : 'text-red-600'}>
                        {attempt.correctOnReview ? 'Acertou' : 'Errou'}
                      </span>
                    </>
                  )}
                </div>
                {attempt.annotation && (
                  <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                    <span className="font-medium">Anotação:</span> {attempt.annotation}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Empty State */}
        {attempts.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Nenhuma revisão realizada ainda</p>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-4 border-t grid grid-cols-3 gap-4 text-center">
        <div>
          <div className="text-lg font-bold text-blue-600">{attempts.length}</div>
          <div className="text-xs text-gray-600">Revisões</div>
        </div>
        <div>
          <div className="text-lg font-bold text-purple-600">
            {attempts.filter(a => a.mode === 'refazer').length}
          </div>
          <div className="text-xs text-gray-600">Refez</div>
        </div>
        <div>
          <div className="text-lg font-bold text-green-600">
            {attempts.filter(a => a.mode === 'refazer' && a.correctOnReview).length}
          </div>
          <div className="text-xs text-gray-600">Acertos</div>
        </div>
      </div>
    </Card>
  );
};
