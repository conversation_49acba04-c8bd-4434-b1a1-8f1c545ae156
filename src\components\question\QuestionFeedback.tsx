import { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { QuestionAICommentary } from "./QuestionAICommentary";
import { ChevronRight, ChevronLeft } from "lucide-react";
import type { Question, AICommentaryResponse } from "@/types/question";
import { supabase } from "@/integrations/supabase/client";
import { useNavigationLock } from "@/contexts/NavigationLockContext";

interface QuestionFeedbackProps {
  question: Question;
  selectedAnswer: string | null;
  onNext?: () => void;
  onPrevious?: () => void;
  isLastQuestion: boolean;
  isFirstQuestion?: boolean;
  discursiveAnswer?: string;
  sessionId: string;
  allQuestionsAnswered?: boolean;
  onPauseTimer?: () => void;
}

export const QuestionFeedback = ({
  question,
  selectedAnswer,
  onNext,
  onPrevious,
  isLastQuestion,
  isFirstQuestion = false,
  discursiveAnswer,
  sessionId,
  allQuestionsAnswered = false,
  onPauseTimer
}: QuestionFeedbackProps) => {
  const [aiCommentary, setAiCommentary] = useState<AICommentaryResponse | null>(null);
  const currentQuestionRef = useRef<string>(question.id);
  const { isNavigationLocked } = useNavigationLock();

  useEffect(() => {
    if (currentQuestionRef.current !== question.id) {
      currentQuestionRef.current = question.id;
      // 🧹 LIMPAR aiCommentary ao mudar questão para evitar vazamento
      setAiCommentary(null);
    }
  }, [question.id]);

  useEffect(() => {
    const loadSavedCommentary = async () => {
      if (sessionId && question.id === currentQuestionRef.current) {
        try {
          // Verificar se o usuário está autenticado
          const { data: userData } = await supabase.auth.getUser();
          if (!userData.user?.id) {
            return;
          }

          const { data, error } = await supabase
            .from('user_answers')
            .select('ai_commentary')
            .match({
              question_id: question.id,
              session_id: sessionId,
              user_id: userData.user.id
            })
            .maybeSingle();

          if (error) throw error;

          if (data?.ai_commentary) {
            const typedCommentary = data.ai_commentary as unknown as AICommentaryResponse;
            setAiCommentary(typedCommentary);
          }
        } catch (err) {
          // Erro silencioso
        }
      }
    };

    // Carregar comentário salvo apenas se não temos nenhum comentário para esta questão
    if (!aiCommentary && !question.ai_commentary && question.id === currentQuestionRef.current) {

      loadSavedCommentary();
    }
  }, [question.id, sessionId, aiCommentary, question.ai_commentary]);

  const handleCommentaryGenerated = useCallback((commentary: AICommentaryResponse) => {
    if (question.id === currentQuestionRef.current) {
      // 🛡️ EVITAR LOOP: só atualizar se realmente mudou
      if (!aiCommentary || JSON.stringify(aiCommentary) !== JSON.stringify(commentary)) {
        setAiCommentary(commentary);
      }
    }
  }, [question.id, aiCommentary]);

  return (
    <div className="space-y-6">
      <QuestionAICommentary
        question={question}
        onCommentaryGenerated={handleCommentaryGenerated}
        existingCommentary={aiCommentary}
        sessionId={sessionId}
        onPauseTimer={onPauseTimer}
      />

      {(onNext || onPrevious) && (
        <div className="flex justify-between mt-6">
          {!isFirstQuestion && onPrevious && (
            <Button
              onClick={onPrevious}
              variant="outline"
              disabled={isNavigationLocked}
              className="flex items-center gap-2 rounded-full px-6"
            >
              <ChevronLeft className="h-4 w-4" />
              Anterior
            </Button>
          )}

          <div className={!isFirstQuestion ? "ml-auto" : ""}>
            {onNext && (
              <>
                {!(isLastQuestion && !allQuestionsAnswered) && (
                  <Button
                    onClick={onNext}
                    disabled={isNavigationLocked}
                    className="flex items-center gap-2 rounded-full px-6 bg-blue-500 hover:bg-blue-600"
                    style={{ color: 'white' }}
                  >
                    {isLastQuestion ? 'Finalizar' : 'Próxima'}
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
