
import { useState } from "react";
import { useHierarchyData } from "../hooks/useHierarchyData";

export const useFlashcardWizardState = () => {
  const [specialty, setSpecialty] = useState("");
  const [theme, setTheme] = useState("");
  const [focus, setFocus] = useState("");
  const [extrafocus, setExtrafocus] = useState("");
  const [quantity, setQuantity] = useState(5);
  const [fcType, setFcType] = useState<"cloze"|"vf"|"multipla"|"qa">("cloze");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingFront, setEditingFront] = useState("");
  const [editingBack, setEditingBack] = useState("");
  const [importingIndex, setImportingIndex] = useState<number | null>(null);
  const [importedIndexes, setImportedIndexes] = useState<number[]>([]);
  const [importingAll, setImportingAll] = useState(false);

  const {
    specialties,
    themes,
    focuses,
    extraFocuses,
  } = useHierarchyData(
    specialty || undefined,
    theme || undefined,
    focus || undefined
  );

  const handleSpecialty = (value: string) => {
    setSpecialty(value);
    setTheme("");
    setFocus("");
    setExtrafocus("");
  };

  const handleTheme = (value: string) => {
    setTheme(value);
    setFocus("");
    setExtrafocus("");
  };

  const handleFocus = (value: string) => {
    setFocus(value);
    setExtrafocus("");
  };

  return {
    specialty,
    theme,
    focus,
    extrafocus,
    quantity,
    fcType,
    editingIndex,
    editingFront,
    editingBack,
    importingIndex,
    importedIndexes,
    importingAll,
    specialties,
    themes,
    focuses,
    extraFocuses,
    setSpecialty: handleSpecialty,
    setTheme: handleTheme,
    setFocus: handleFocus,
    setExtrafocus,
    setQuantity,
    setFcType,
    setEditingIndex,
    setEditingFront,
    setEditingBack,
    setImportingIndex,
    setImportedIndexes,
    setImportingAll,
  };
};
