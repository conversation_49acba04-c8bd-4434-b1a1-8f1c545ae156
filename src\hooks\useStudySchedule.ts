
import { useState, useCallback, useEffect, useRef } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@supabase/auth-helpers-react';
import { useDomain } from '@/hooks/useDomain';
import type { WeeklySchedule, StudyTopic, AIScheduleOptions, GenerationStats } from './study-schedule/types';
import { useScheduleManagement } from './study-schedule/useScheduleManagement';
import { useStudyTopics } from './study-schedule/useStudyTopics';
import { useAISchedule } from './study-schedule/useAISchedule';

export const useStudySchedule = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [weeklySchedule, setWeeklySchedule] = useState<WeeklySchedule | null>(null);
  const [generationStats, setGenerationStats] = useState<GenerationStats | null>(null);

  const user = useUser();
  const { domain, isReady } = useDomain();
  const queryClient = useQueryClient();
  const loadingStateChanges = useRef(0);

  const {
    loadCurrentSchedule: fetchSchedule,
    addWeeks: addWeeksOriginal,
    deleteWeek: scheduleDeleteWeek,
    deleteAllWeeks: scheduleDeleteAllWeeks,
    updateTopic: scheduleUpdateTopic,
    isLoading: isScheduleLoading
  } = useScheduleManagement();

  const {
    markTopicAsStudied,
    deleteTopic,
    isLoading: isTopicLoading
  } = useStudyTopics();

  // Função addWeeks que funciona com IA
  const addWeeks = useCallback(async (numberOfWeeks: number) => {
    try {
      const result = await addWeeksOriginal(numberOfWeeks);

      if (result) {
        // Recarregar dados após adicionar semanas
        const schedule = await fetchSchedule();
        if (schedule) {
          setWeeklySchedule(schedule);
        }
      }

      return result;
    } catch (error: any) {
      return false;
    }
  }, [addWeeksOriginal, fetchSchedule]);

  const {
    generateAISchedule,
    isLoading: isAILoading
  } = useAISchedule(setGenerationStats, addWeeks);

  // Log loading state changes to debug dialog visibility issues
  useEffect(() => {
    const isCurrentlyLoading = isLoading || isScheduleLoading || isTopicLoading || isAILoading;
    loadingStateChanges.current += 1;
  }, [isLoading, isScheduleLoading, isTopicLoading, isAILoading]);

  const loadCurrentSchedule = useCallback(async () => {
    try {
      setIsLoading(true);
      const schedule = await fetchSchedule();

      if (schedule) {
        setWeeklySchedule(schedule);
      }
    } catch (error: any) {
      console.error('❌ [useStudySchedule] Erro ao carregar cronograma:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchSchedule]);

  const deleteWeek = useCallback(async (weekNumber: number) => {
    try {
      setIsLoading(true);

      if (!scheduleDeleteWeek || typeof scheduleDeleteWeek !== 'function') {
        throw new Error('Delete function not available');
      }

      const result = await scheduleDeleteWeek(weekNumber);

      // RECARREGAR OS DADOS COMO FUNCIONAVA ANTES
      const schedule = await fetchSchedule();
      if (schedule) {
        setWeeklySchedule(schedule);
      }
    } catch (error: any) {
      // Error handled silently
    } finally {
      setIsLoading(false);
    }
  }, [scheduleDeleteWeek, fetchSchedule]);

  const deleteAllWeeks = useCallback(async () => {
    try {
      setIsLoading(true);

      if (!scheduleDeleteAllWeeks || typeof scheduleDeleteAllWeeks !== 'function') {
        throw new Error('Delete all function not available');
      }

      const result = await scheduleDeleteAllWeeks();

      // Recarregar dados localmente
      const schedule = await fetchSchedule();
      if (schedule) {
        setWeeklySchedule(schedule);
      } else {
        setWeeklySchedule(null);
      }
    } catch (error: any) {
      // Error handled silently
    } finally {
      setIsLoading(false);
    }
  }, [scheduleDeleteAllWeeks, fetchSchedule]);

  const handleAddTopic = useCallback(async (dayOfWeek: string, weekNumber: number, scheduleId: string, source: 'platform' | 'manual') => {
    try {
      if (!weeklySchedule) {
        return;
      }


      // Create empty topic with necessary data
      const newTopic: StudyTopic = {
        specialty: '',
        theme: '',
        focus: '',
        difficulty: 'Médio',
        activity: '',
        startTime: '08:00',
        duration: '1:00',
        day: dayOfWeek,
        weekNumber: weekNumber,
        scheduleId: scheduleId,
        is_manual: source === 'manual'
      };



      // We'll handle the actual saving in topicEditDialog by calling updateTopic
      return newTopic;
    } catch (error: any) {
      return null;
    }
  }, [weeklySchedule]);

  // ✅ Função updateTopic com atualização incremental
  const updateTopic = useCallback(async (topicData: StudyTopic) => {
    try {
      // Atualização otimista: atualizar UI primeiro
      setWeeklySchedule(prev => {
        if (!prev) return prev;

        const newState = {
          ...prev,
          recommendations: prev.recommendations.map(day => ({
            ...day,
            topics: day.topics.map(topic =>
              topic.id === topicData.id ? { ...topic, ...topicData } : topic
            )
          }))
        };

        return newState;
      });

      // Sincronizar com servidor
      const result = await scheduleUpdateTopic(topicData);
      return result;
    } catch (error) {
      console.error('❌ [useStudySchedule.updateTopic] ERRO:', error);

      // Se falhar, recarregar para reverter
      const schedule = await fetchSchedule();
      if (schedule) {
        setWeeklySchedule(schedule);
      }
      return false;
    }
  }, [scheduleUpdateTopic, fetchSchedule]);

  // Combine loading states for more reliable tracking
  const combinedLoadingState = isLoading || isScheduleLoading || isTopicLoading || isAILoading;

  return {
    weeklySchedule,
    isLoading: combinedLoadingState,
    generationStats,
    loadCurrentSchedule,
    updateTopic,
    handleAddTopic,
    addWeeks,
    deleteWeek,
    deleteAllWeeks,
    generateAISchedule,
    markTopicAsStudied,
    deleteTopic,
  };
};
