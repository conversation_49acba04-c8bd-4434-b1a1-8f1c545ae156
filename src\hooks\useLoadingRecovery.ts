import { useState, useEffect, useCallback, useRef } from 'react';

interface UseLoadingRecoveryOptions {
  timeoutMs?: number;
  maxRetries?: number;
  retryDelayMs?: number;
  onTimeout?: () => void;
  onMaxRetriesReached?: () => void;
}

interface LoadingRecoveryState {
  isLoading: boolean;
  hasTimedOut: boolean;
  retryCount: number;
  error: string | null;
  canRetry: boolean;
  isEmergencyMode: boolean;
  hasReachedMaxRetries: boolean;
}

export const useLoadingRecovery = (options: UseLoadingRecoveryOptions = {}) => {
  const {
    timeoutMs = 15000, // 15 segundos padrão
    maxRetries = 3,
    retryDelayMs = 2000,
    onTimeout,
    onMaxRetriesReached
  } = options;

  const [state, setState] = useState<LoadingRecoveryState>({
    isLoading: false,
    hasTimedOut: false,
    retryCount: 0,
    error: null,
    canRetry: true,
    isEmergencyMode: false,
    hasReachedMaxRetries: false
  });

  const timeoutRef = useRef<NodeJS.Timeout>();
  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const loadingStartTimeRef = useRef<number>();

  // Iniciar loading com timeout
  const startLoading = useCallback(() => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      hasTimedOut: false,
      error: null
    }));

    loadingStartTimeRef.current = Date.now();

    // Configurar timeout
    timeoutRef.current = setTimeout(() => {
      setState(prev => ({
        ...prev,
        isLoading: false,
        hasTimedOut: true,
        error: 'Timeout: Operação demorou mais que o esperado'
      }));
      
      onTimeout?.();
    }, timeoutMs);
  }, [timeoutMs, onTimeout]);

  // Finalizar loading com sucesso
  const finishLoading = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    setState(prev => ({
      ...prev,
      isLoading: false,
      hasTimedOut: false,
      error: null,
      retryCount: 0 // Reset retry count on success
    }));
  }, []);

  // Finalizar loading com erro
  const finishWithError = useCallback((error: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    setState(prev => ({
      ...prev,
      isLoading: false,
      error,
      canRetry: prev.retryCount < maxRetries
    }));
  }, [maxRetries]);

  // Tentar novamente
  const retry = useCallback(() => {
    setState(prev => {
      const newRetryCount = prev.retryCount + 1;
      const canRetryAgain = newRetryCount < maxRetries;
      const hasReachedMax = newRetryCount >= maxRetries;

      if (hasReachedMax) {
        onMaxRetriesReached?.();
        return {
          ...prev,
          retryCount: newRetryCount,
          canRetry: false,
          hasTimedOut: false,
          error: 'Todas as tentativas foram esgotadas',
          isEmergencyMode: true,
          hasReachedMaxRetries: true
        };
      }

      return {
        ...prev,
        retryCount: newRetryCount,
        canRetry: canRetryAgain,
        hasTimedOut: false,
        error: null,
        isEmergencyMode: false,
        hasReachedMaxRetries: false
      };
    });

    // Delay antes de tentar novamente (apenas se ainda pode tentar)
    if (state.retryCount + 1 < maxRetries) {
      retryTimeoutRef.current = setTimeout(() => {
        startLoading();
      }, retryDelayMs);
    }
  }, [maxRetries, retryDelayMs, startLoading, onMaxRetriesReached, state.retryCount]);

  // Reset completo
  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    
    setState({
      isLoading: false,
      hasTimedOut: false,
      retryCount: 0,
      error: null,
      canRetry: true,
      isEmergencyMode: false,
      hasReachedMaxRetries: false
    });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Calcular tempo decorrido
  const getElapsedTime = useCallback(() => {
    if (!loadingStartTimeRef.current) return 0;
    return Date.now() - loadingStartTimeRef.current;
  }, []);

  return {
    ...state,
    startLoading,
    finishLoading,
    finishWithError,
    retry,
    reset,
    getElapsedTime,
    maxRetries,
    hasRetriesLeft: state.retryCount < maxRetries
  };
};

export default useLoadingRecovery;
