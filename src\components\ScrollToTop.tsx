import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Componente que força scroll para o topo sempre que a rota muda
 * Resolve o problema de manter posição do scroll ao navegar entre páginas
 */
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Scroll para o topo sempre que a rota mudar
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }, [pathname]);

  return null;
};

export default ScrollToTop;
