import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { User } from '@supabase/supabase-js';
import { useLoadingRecovery } from '@/hooks/useLoadingRecovery';
import LoadingErrorRecovery from '@/components/common/LoadingErrorRecovery';
import EmergencyFallback from '@/components/common/EmergencyFallback';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  hasError: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const initialized = useRef(false);

  // Sistema de recovery para loading
  const recovery = useLoadingRecovery({
    timeoutMs: 10000, // 10 segundos
    maxRetries: 3,
    onTimeout: () => {
      setLoading(false);
    },
    onMaxRetriesReached: () => {
      setLoading(false);
    }
  });

  useEffect(() => {
    if (initialized.current) {
      return;
    }
    initialized.current = true;

    // Obter sessão inicial com sistema de recovery
    const getInitialSession = async () => {
      recovery.startLoading();

      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          recovery.finishWithError(`Erro de autenticação: ${error.message}`);
          setUser(null);
        } else {
          recovery.finishLoading();
          setUser(session?.user ?? null);
          setLoading(false);
        }
      } catch (error: any) {
        recovery.finishWithError(`Erro de conexão: ${error.message}`);
        setUser(null);
      }
    };

    getInitialSession();

    // Configurar listener de mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        const newUserId = session?.user?.id || null;
        const currentUserId = window.__currentUserId || null;

        // Evitar múltiplas atualizações para o mesmo usuário
        if (newUserId === currentUserId && event !== 'SIGNED_OUT') {
          return;
        }

        // Atualizar referência global
        window.__currentUserId = newUserId;

        // Limpar dados quando faz logout
        if (event === 'SIGNED_OUT') {
          setUser(null);

          // Limpar todas as queries do React Query se disponível
          if (window.__queryClient) {
            window.__queryClient.clear();
          }

          // Limpar referência global
          window.__currentUserId = null;

          // Limpar localStorage relacionado ao usuário
          try {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && (key.includes('referral_notification_shown_') || key.includes('pendingReferralCode'))) {
                keysToRemove.push(key);
              }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
          } catch (error) {
            // Silent error handling
          }

          // Forçar redirecionamento para página inicial após logout
          setTimeout(() => {
            if (window.location.pathname !== '/') {
              window.location.href = '/';
            }
          }, 100);
        } else {
          setUser(session?.user ?? null);
        }

        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    try {
      // Limpar queries antes do logout se disponível
      if (window.__queryClient) {
        window.__queryClient.clear();
      }

      // Limpar referência global
      window.__currentUserId = null;

      // Limpar estado local imediatamente
      setUser(null);

      // Fazer logout
      await supabase.auth.signOut();

      // Forçar redirecionamento imediato para página inicial
      window.location.href = '/';

    } catch (error) {
      // Mesmo com erro, redirecionar para página inicial
      window.location.href = '/';
    }
  };

  // Mostrar tela de emergência se esgotou todas as tentativas
  if (recovery.isEmergencyMode || recovery.hasReachedMaxRetries) {
    return (
      <EmergencyFallback
        title="Sistema de Autenticação Indisponível"
        message="Não foi possível estabelecer conexão com o sistema de autenticação após várias tentativas. Isso pode ser um problema temporário de conectividade."
        onForceReload={() => {
          // Limpar tudo e recarregar
          localStorage.clear();
          sessionStorage.clear();
          window.location.reload();
        }}
        onGoHome={() => {
          // Tentar ir para home sem autenticação
          recovery.reset();
          setLoading(false);
          setUser(null);
          window.location.href = '/';
        }}
        showSupportOption={true}
      />
    );
  }

  // Mostrar tela de recovery normal se necessário
  if (recovery.hasTimedOut || recovery.error) {
    return (
      <LoadingErrorRecovery
        title="Problema na Autenticação"
        message={recovery.error || "A verificação de autenticação está demorando mais que o esperado."}
        onRetry={recovery.canRetry ? () => {
          recovery.retry();
          if (recovery.canRetry) {
            getInitialSession();
          }
        } : undefined}
        onGoHome={() => {
          recovery.reset();
          setLoading(false);
          setUser(null);
        }}
        onContinueOffline={() => {
          recovery.reset();
          setLoading(false);
          setUser(null);
        }}
        showOfflineOption={true}
        retryCount={recovery.retryCount}
        maxRetries={recovery.maxRetries}
      />
    );
  }

  const value = {
    user,
    loading: loading || recovery.isLoading,
    hasError: !!recovery.error,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
