
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { Users, BookOpen, Share2 } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DailyReviewButton } from "./DailyReviewButton";
import { FSRSInfoDialog } from "./FSRSInfoDialog";

export const FlashcardsHome = () => {
  const navigate = useNavigate();
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
      
      {/* Espaçamento para StudyNavBar flutuante */}
      <div className="h-16 sm:h-20"></div>

      <div className="container mx-auto px-4 py-8 space-y-8 animate-fade-in relative z-10">
        {/* Header Section */}
        <div className="relative">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-5xl font-black leading-none mb-2">
                <span className="inline-block bg-black text-white px-4 py-2 transform -rotate-1">
                  Aprenda com Flashcards
                </span>
              </h1>
              <p className="text-xl text-gray-700 max-w-lg">
                Escolha uma das opções abaixo para começar a estudar
              </p>
            </div>
            <FSRSInfoDialog />
          </div>
        </div>

        {/* Daily Review Button — AGORA NO TOPO */}
        <DailyReviewButton />

        {/* Cards Grid */}
        <div className="grid md:grid-cols-3 gap-6 w-full overflow-hidden">
          {/* Study Card */}
          <Card
            className="p-6 bg-white border-2 border-black shadow-card-sm hover:shadow-lg hover:-translate-y-1 transition-all cursor-pointer group flex flex-col h-full"
            onClick={() => navigate("/flashcards/study")}
          >
            <div className="flex items-start gap-4 mb-4">
              <div className="p-3 bg-hackathon-green/10 rounded-lg group-hover:bg-hackathon-green/20 transition-colors flex-shrink-0">
                <BookOpen className="h-8 w-8 text-hackathon-green" />
              </div>
              <div className="flex-1 min-w-0">
                <h2 className="text-xl sm:text-2xl font-bold leading-tight">Estudar</h2>
              </div>
            </div>
            <p className="text-gray-600 mb-4 flex-grow">
              Revise seus flashcards personalizados utilizando um sistema de repetição espaçada
            </p>
            <Button
              className="w-full bg-hackathon-green hover:bg-hackathon-green/90 text-black border-2 border-black font-bold shadow-button group-hover:shadow-button-hover mt-auto"
            >
              Acessar
            </Button>
          </Card>

          {/* Collaborate Card */}
          <Card
            className="p-6 bg-white border-2 border-black shadow-card-sm hover:shadow-lg hover:-translate-y-1 transition-all cursor-pointer group flex flex-col h-full"
            onClick={() => navigate("/collaborate/flashcards")}
          >
            <div className="flex items-start gap-4 mb-4">
              <div className="p-3 bg-hackathon-red/10 rounded-lg group-hover:bg-hackathon-red/20 transition-colors flex-shrink-0">
                <Users className="h-8 w-8 text-hackathon-red" />
              </div>
              <div className="flex-1 min-w-0">
                <h2 className="text-xl sm:text-2xl font-bold leading-tight">Gerenciar Flashcards</h2>
              </div>
            </div>
            <p className="text-gray-600 mb-4 flex-grow">
              Crie, edite e compartilhe flashcards com a galera
            </p>
            <Button
              variant="default"
              className="w-full bg-hackathon-red hover:bg-hackathon-red/90 text-white border-2 border-black font-bold shadow-button group-hover:shadow-button-hover mt-auto"
            >
              Acessar
            </Button>
          </Card>

          {/* Shared Cards */}
          <Card
            className="p-6 bg-white border-2 border-black shadow-card-sm hover:shadow-lg hover:-translate-y-1 transition-all cursor-pointer group flex flex-col h-full"
            onClick={() => navigate("/collaborative/flashcards")}
          >
            <div className="flex items-start gap-4 mb-4">
              <div className="p-3 bg-hackathon-yellow/10 rounded-lg group-hover:bg-hackathon-yellow/20 transition-colors flex-shrink-0">
                <Share2 className="h-8 w-8 text-hackathon-yellow" />
              </div>
              <div className="flex-1 min-w-0">
                <h2 className="text-xl sm:text-2xl font-bold leading-tight">Cards Compartilhados</h2>
              </div>
            </div>
            <p className="text-gray-600 mb-4 flex-grow">
              Explore e estude flashcards criados por outros estudantes
            </p>
            <Button
              variant="default"
              className="w-full bg-hackathon-yellow hover:bg-hackathon-yellow/90 text-black border-2 border-black font-bold shadow-button group-hover:shadow-button-hover mt-auto"
            >
              Acessar
            </Button>
          </Card>
        </div>

        {/* Espaçamento extra para mobile (botão flutuante) */}
        <div className="h-20 md:h-0"></div>
      </div>
    </div>
  );
};
