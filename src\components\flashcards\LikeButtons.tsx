
import { useState, useEffect } from "react";
import { ThumbsUp, ThumbsDown, Flag, XCircle } from "lucide-react";
import { useUser } from "@supabase/auth-helpers-react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";


interface LikeButtonsProps {
  cardId: string;
  initialLikes: number;
  initialDislikes: number;
  initialLikedBy: string[];
  initialDislikedBy: string[];
  onLikeImported?: () => void;
  compact?: boolean;
}

export const LikeButtons = ({
  cardId,
  initialLikes,
  initialDislikes,
  initialLikedBy,
  initialDislikedBy,
  onLikeImported,
  compact = false
}: LikeButtonsProps) => {
  const [likes, setLikes] = useState(initialLikes);
  const [dislikes, setDislikes] = useState(initialDislikes);
  const [likedBy, setLikedBy] = useState<string[]>(initialLikedBy || []);
  const [dislikedBy, setDislikedBy] = useState<string[]>(initialDislikedBy || []);
  const [isLoading, setIsLoading] = useState(false);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportReason, setReportReason] = useState("");
  const [isReporting, setIsReporting] = useState(false);
  const [reportSuccess, setReportSuccess] = useState(false);

  const user = useUser();
  const [userId, setUserId] = useState<string | null>(null);

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUserId(user.id);
      }
    };
    getUser();
  }, []);

  // Collaborative hook is not available in this environment
  // Removing the problematic require() call that causes ReferenceError
  const likeDislikeCardHook: null = null;

  const handleVote = async (isLike: boolean) => {
    try {
      setIsLoading(true);


      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {

        return;
      }

      // Direct database update approach (collaborative hook not available)

      const currentUserId = user.id;
      const hasLiked = likedBy.includes(currentUserId);
      const hasDisliked = dislikedBy.includes(currentUserId);

      let newLikedBy = [...likedBy];
      let newDislikedBy = [...dislikedBy];
      let newLikes = likes;
      let newDislikes = dislikes;

      if (hasLiked) {
        newLikedBy = newLikedBy.filter(id => id !== currentUserId);
        newLikes--;
      }
      if (hasDisliked) {
        newDislikedBy = newDislikedBy.filter(id => id !== currentUserId);
        newDislikes--;
      }

      if (isLike && !hasLiked) {
        newLikedBy.push(currentUserId);
        newLikes++;
      } else if (!isLike && !hasDisliked) {
        newDislikedBy.push(currentUserId);
        newDislikes++;
      }

      // Get the card details first to check if it's an imported card
      const { data: cardData, error: cardError } = await supabase
        .from('flashcards_cards')
        .select('origin_id')
        .eq('id', cardId)
        .single();

      if (cardError) {

        throw cardError;
      }

      // Update the current card
      const { error: updateError } = await supabase
        .from('flashcards_cards')
        .update({
          likes: newLikes,
          dislikes: newDislikes,
          liked_by: newLikedBy,
          disliked_by: newDislikedBy
        })
        .eq('id', cardId);

      if (updateError) throw updateError;

      // If this is an imported card (has origin_id), also update the original card
      if (cardData?.origin_id) {


        // First get the current state of the original card
        const { data: originalCard, error: originalCardError } = await supabase
          .from('flashcards_cards')
          .select('liked_by, disliked_by, likes, dislikes')
          .eq('id', cardData.origin_id)
          .single();

        if (originalCardError) {

        } else {
          // Update liked_by and disliked_by arrays on the original card
          let originalLikedBy = originalCard.liked_by || [];
          let originalDislikedBy = originalCard.disliked_by || [];

          if (isLike) {
            // Add user to liked_by if not already there
            if (!originalLikedBy.includes(currentUserId)) {
              originalLikedBy.push(currentUserId);
            }
            // Remove from disliked_by if present
            originalDislikedBy = originalDislikedBy.filter(id => id !== currentUserId);
          } else {
            // Add user to disliked_by if not already there
            if (!originalDislikedBy.includes(currentUserId)) {
              originalDislikedBy.push(currentUserId);
            }
            // Remove from liked_by if present
            originalLikedBy = originalLikedBy.filter(id => id !== currentUserId);
          }

          // Update the original card
          const { error: originalUpdateError } = await supabase
            .from('flashcards_cards')
            .update({
              likes: originalLikedBy.length,
              dislikes: originalDislikedBy.length,
              liked_by: originalLikedBy,
              disliked_by: originalDislikedBy
            })
            .eq('id', cardData.origin_id);

          if (originalUpdateError) {
          }
        }
      }

      setLikes(newLikes);
      setDislikes(newDislikes);
      setLikedBy(newLikedBy);
      setDislikedBy(newDislikedBy);


    } catch (error) {

    } finally {
      setIsLoading(false);
    }
  };

  const handleReport = () => {
    setShowReportDialog(true);
  };

  const submitReport = async () => {
    if (!reportReason.trim()) {
      toast.error("Por favor, descreva o motivo do report");
      return;
    }

    setIsReporting(true);
    try {
      const { error } = await supabase
        .from('flashcard_reports')
        .insert({
          card_id: cardId,
          user_id: user?.id,
          message: reportReason.trim()
        });

      if (error) throw error;

      // Mostrar sucesso no dialog
      setReportSuccess(true);

      // Fechar dialog após 1.5 segundos
      setTimeout(() => {
        setShowReportDialog(false);
        setReportReason("");
        setReportSuccess(false);
      }, 1500);

    } catch (error) {
      console.error('Erro ao enviar report:', error);
      toast.error("Erro ao enviar report. Tente novamente.");
    } finally {
      setIsReporting(false);
    }
  };

  const handleBury = () => {
    // Bury functionality placeholder
    toast.info("Funcionalidade em desenvolvimento");
  };

  const renderButtons = () => {
    if (compact) {
      return (
        <div className="flex items-center gap-0.5 sm:gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              handleVote(true);
            }}
            disabled={isLoading}
            className={`h-6 w-6 sm:h-8 sm:w-8 rounded-full border border-gray-300 ${userId && likedBy.includes(userId) ? 'text-green-500 bg-green-50' : 'text-gray-600 bg-white'} hover:bg-green-50 transition-colors`}
          >
            <ThumbsUp className={`h-3 w-3 sm:h-4 sm:w-4 ${userId && likedBy.includes(userId) ? 'text-green-500 fill-green-500' : ''}`} />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              handleVote(false);
            }}
            disabled={isLoading}
            className={`h-6 w-6 sm:h-8 sm:w-8 rounded-full border border-gray-300 ${userId && dislikedBy.includes(userId) ? 'text-red-500 bg-red-50' : 'text-gray-600 bg-white'} hover:bg-red-50 transition-colors`}
          >
            <ThumbsDown className={`h-3 w-3 sm:h-4 sm:w-4 ${userId && dislikedBy.includes(userId) ? 'text-red-500 fill-red-500' : ''}`} />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              handleReport();
            }}
            className="h-6 w-6 sm:h-8 sm:w-8 rounded-full border border-gray-300 text-yellow-600 bg-white hover:bg-yellow-50 transition-colors"
          >
            <Flag className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              handleBury();
            }}
            className="h-6 w-6 sm:h-8 sm:w-8 rounded-full border border-gray-300 text-gray-500 bg-white hover:bg-gray-50 transition-colors"
          >
            <XCircle className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleVote(true)}
          disabled={isLoading}
          className={`flex items-center gap-2 ${userId && likedBy.includes(userId) ? 'text-green-500' : ''}`}
        >
          <ThumbsUp className={`h-4 w-4 ${userId && likedBy.includes(userId) ? 'text-green-500 fill-green-500' : ''}`} />
          <span>{likes}</span>
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleVote(false)}
          disabled={isLoading}
          className={`flex items-center gap-2 ${userId && dislikedBy.includes(userId) ? 'text-red-500' : ''}`}
        >
          <ThumbsDown className={`h-4 w-4 ${userId && dislikedBy.includes(userId) ? 'text-red-500 fill-red-500' : ''}`} />
          <span>{dislikes}</span>
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleReport}
          className="flex items-center gap-2 text-yellow-500"
        >
          <Flag className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleBury}
          className="flex items-center gap-2 text-gray-500"
        >
          <XCircle className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  return (
    <>
      {renderButtons()}

      {/* Dialog de Report */}
      <Dialog open={showReportDialog} onOpenChange={setShowReportDialog}>
        <DialogContent className="sm:max-w-md max-w-[90vw] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {reportSuccess ? (
                <>
                  <div className="h-5 w-5 rounded-full bg-green-500 flex items-center justify-center">
                    <svg className="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  Report Enviado!
                </>
              ) : (
                <>
                  <Flag className="h-5 w-5 text-yellow-600" />
                  Reportar Flashcard
                </>
              )}
            </DialogTitle>
            {!reportSuccess && (
              <DialogDescription>
                Descreva o motivo pelo qual você está reportando este flashcard. Nossa equipe irá revisar.
              </DialogDescription>
            )}
          </DialogHeader>

          {reportSuccess ? (
            <div className="text-center py-6">
              <div className="text-green-600 font-medium mb-2">
                ✅ Seu report foi enviado com sucesso!
              </div>
              <div className="text-sm text-gray-500">
                Nossa equipe irá revisar em breve.
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Textarea
                placeholder="Descreva o problema (conteúdo inadequado, erro, spam, etc.)"
                value={reportReason}
                onChange={(e) => setReportReason(e.target.value)}
                className="min-h-[100px] resize-none"
                maxLength={500}
              />
              <div className="text-sm text-gray-500 text-right">
                {reportReason.length}/500 caracteres
              </div>
              <div className="flex gap-2 justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowReportDialog(false);
                    setReportReason("");
                    setReportSuccess(false);
                  }}
                  disabled={isReporting}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={submitReport}
                  disabled={isReporting || !reportReason.trim()}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  {isReporting ? "Enviando..." : "Enviar Report"}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};
