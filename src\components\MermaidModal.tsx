import React, { useEffect, useRef } from 'react';
import { X, Download, ZoomIn, ZoomOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { useDarkMode } from '@/contexts/DarkModeContext';

interface MermaidModalProps {
  isOpen: boolean;
  onClose: () => void;
  mermaidCode: string;
  title?: string;
}

// 🎯 FUNÇÃO PARA DETECTAR TIPO DE DIAGRAMA NO MODAL
const detectDiagramTypeFromCode = (code: string): { icon: string; title: string } => {
  const lowerCode = code.toLowerCase();

  if (lowerCode.includes('fluxograma') || lowerCode.includes('flowchart')) {
    return { icon: '📊', title: 'Fluxograma Interativo' };
  }

  if (lowerCode.includes('organograma')) {
    return { icon: '🏢', title: 'Organograma Interativo' };
  }

  if (lowerCode.includes('diagrama')) {
    return { icon: '📈', title: 'Diagrama Interativo' };
  }

  if (lowerCode.includes('esquema')) {
    return { icon: '🗂️', title: 'Esquema Interativo' };
  }

  // Default: mapa mental
  return { icon: '🧠', title: 'Mapa Mental Interativo' };
};

export const MermaidModal: React.FC<MermaidModalProps> = ({
  isOpen,
  onClose,
  mermaidCode,
  title
}) => {
  const { isDarkMode } = useDarkMode();
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = React.useState(1);
  const [isRendering, setIsRendering] = React.useState(true);

  // Estados para pan/drag
  const [isPanning, setIsPanning] = React.useState(false);
  const [panStart, setPanStart] = React.useState({ x: 0, y: 0 });
  const [panOffset, setPanOffset] = React.useState({ x: 0, y: 0 });

  // Detectar tipo de diagrama
  const diagramInfo = detectDiagramTypeFromCode(mermaidCode);
  const modalTitle = title || diagramInfo.title;

  useEffect(() => {
    if (isOpen && mermaidCode && containerRef.current) {
      renderMermaid();
    }
  }, [isOpen, mermaidCode]);

  const renderMermaid = async () => {
    if (!containerRef.current) return;

    setIsRendering(true);

    try {
      // Aguardar Mermaid estar disponível
      let attempts = 0;
      while (typeof (window as any).mermaid === 'undefined' && attempts < 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        attempts++;
      }

      if (typeof (window as any).mermaid === 'undefined') {
        throw new Error('Mermaid.js não disponível após 10 tentativas');
      }

      const mermaid = (window as any).mermaid;

      // Usar código já limpo pelo messageFormatter
      let cleanCode = mermaidCode;

      // Configurar Mermaid
      mermaid.initialize({
        startOnLoad: false,
        theme: isDarkMode ? 'dark' : 'default',
        themeVariables: isDarkMode ? {
          primaryColor: '#8b5cf6',
          primaryTextColor: '#e5e7eb',
          primaryBorderColor: '#6366f1',
          lineColor: '#9ca3af',
          background: '#1f2937',
          mainBkg: '#374151',
          secondBkg: '#4b5563',
          tertiaryColor: '#6b7280',
          cScale0: '#1f2937',
          cScale1: '#374151',
          cScale2: '#4b5563'
        } : {
          primaryColor: '#8b5cf6',
          primaryTextColor: '#1f2937',
          primaryBorderColor: '#6366f1',
          lineColor: '#6b7280',
          background: '#ffffff'
        },
        mindmap: {
          padding: 60, // Muito mais espaçamento
          maxNodeSizeX: 350, // Nós ainda maiores
          maxNodeSizeY: 180,
          useMaxWidth: false,
          htmlLabels: true,
          // Configurações específicas para melhor espaçamento
          nodeSpacing: 150, // Espaçamento entre nós
          levelSpacing: 200, // Espaçamento entre níveis
          curve: 'basis' // Curvas mais suaves
        },
        logLevel: 'error'
      });

      // Gerar ID único
      const svgId = `mermaid-modal-${Date.now()}`;

      // Renderizar
      const result = await mermaid.render(svgId, cleanCode);

      if (containerRef.current) {
        containerRef.current.innerHTML = result.svg;

        // Ajustar SVG para evitar cortes
        const svg = containerRef.current.querySelector('svg');
        if (svg) {
          // Remover restrições de tamanho
          svg.style.maxWidth = 'none';
          svg.style.maxHeight = 'none';
          svg.style.width = 'auto';
          svg.style.height = 'auto';
          svg.style.display = 'block';
          svg.style.margin = '0 auto';
          svg.style.transform = `scale(${zoom}) translate(${panOffset.x}px, ${panOffset.y}px)`;
          svg.style.transformOrigin = 'center';

          // Adicionar margem extra no viewBox para evitar cortes
          const viewBox = svg.getAttribute('viewBox');
          if (viewBox) {
            const [x, y, width, height] = viewBox.split(' ').map(Number);
            const margin = 30; // Margem extra
            svg.setAttribute('viewBox',
              `${x - margin} ${y - margin} ${width + margin * 2} ${height + margin * 2}`
            );
          }

          // Adaptar SVG para modo noturno
          if (isDarkMode) {
            // Corrigir fundo branco
            const whiteBackgrounds = svg.querySelectorAll('rect[fill="white"]');
            whiteBackgrounds.forEach(rect => {
              rect.setAttribute('fill', '#1f2937');
            });

            // Se não há fundo, criar um escuro
            if (whiteBackgrounds.length === 0) {
              const backgroundRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
              backgroundRect.setAttribute('width', '100%');
              backgroundRect.setAttribute('height', '100%');
              backgroundRect.setAttribute('fill', '#1f2937');
              backgroundRect.setAttribute('x', '0');
              backgroundRect.setAttribute('y', '0');
              svg.insertBefore(backgroundRect, svg.firstChild);
            }

            // Corrigir textos escuros
            const darkTexts = svg.querySelectorAll('text[fill="#333"], text[fill="black"], text[fill="#000"]');
            darkTexts.forEach(text => {
              text.setAttribute('fill', '#e5e7eb');
            });
          }
        }
      }

    } catch (error) {
      console.error('🧠 [MermaidModal] Erro ao renderizar:', error);

      // Tentar versão ultra-simplificada como fallback
      try {

        const simplifiedCode = `mindmap
  root((${title || 'Mapa Mental'}))
    Informação 1
    Informação 2
    Informação 3`;

        const mermaid = (window as any).mermaid;
        const svgId = `mermaid-fallback-${Date.now()}`;
        const result = await mermaid.render(svgId, simplifiedCode);

        if (containerRef.current) {
          containerRef.current.innerHTML = result.svg;

          const svg = containerRef.current.querySelector('svg');
          if (svg) {
            svg.style.maxWidth = '100%';
            svg.style.height = 'auto';
            svg.style.display = 'block';
            svg.style.margin = '0 auto';
            svg.style.transform = `scale(${zoom})`;
            svg.style.transformOrigin = 'center';
          }
        }



      } catch (fallbackError) {
        console.error('🧠 [MermaidModal] Erro no fallback:', fallbackError);

        if (containerRef.current) {
          containerRef.current.innerHTML = `
            <div class="text-center text-red-500 p-8">
              <p class="mb-4">Não foi possível renderizar o mapa mental</p>
              <p class="text-sm text-gray-600 mb-4">O conteúdo é muito complexo para visualização</p>
              <details class="text-left">
                <summary class="cursor-pointer text-blue-600 hover:text-blue-800">Ver código original</summary>
                <pre class="text-xs text-gray-600 bg-gray-100 p-4 rounded overflow-auto max-h-40 mt-2">${mermaidCode}</pre>
              </details>
            </div>
          `;
        }
      }
    } finally {
      setIsRendering(false);
    }
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom + 0.2, 3);
    setZoom(newZoom);
    updateZoom(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom - 0.2, 0.5);
    setZoom(newZoom);
    updateZoom(newZoom);
  };

  const updateZoom = (newZoom: number) => {
    if (containerRef.current) {
      const svg = containerRef.current.querySelector('svg');
      if (svg) {
        svg.style.transform = `scale(${newZoom}) translate(${panOffset.x}px, ${panOffset.y}px)`;
      }
    }
  };

  // Funções para pan/drag
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsPanning(true);
    setPanStart({ x: e.clientX - panOffset.x, y: e.clientY - panOffset.y });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isPanning) return;

    const newOffset = {
      x: e.clientX - panStart.x,
      y: e.clientY - panStart.y
    };

    setPanOffset(newOffset);

    if (containerRef.current) {
      const svg = containerRef.current.querySelector('svg');
      if (svg) {
        svg.style.transform = `scale(${zoom}) translate(${newOffset.x}px, ${newOffset.y}px)`;
      }
    }
  };

  const handleMouseUp = () => {
    setIsPanning(false);
  };

  const handleMouseLeave = () => {
    setIsPanning(false);
  };

  // Reset pan quando modal abre
  React.useEffect(() => {
    if (isOpen) {
      setPanOffset({ x: 0, y: 0 });
      setZoom(1);
    }
  }, [isOpen]);



  const handleDownload = () => {
    if (!containerRef.current) return;

    const svg = containerRef.current.querySelector('svg');
    if (!svg) return;

    try {
      // Clonar o SVG
      const svgClone = svg.cloneNode(true) as SVGElement;

      // Remover transformações
      svgClone.style.transform = 'none';
      svgClone.style.maxWidth = 'none';
      svgClone.style.maxHeight = 'none';

      // Adicionar fundo apropriado para download
      const backgroundRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      backgroundRect.setAttribute('width', '100%');
      backgroundRect.setAttribute('height', '100%');
      backgroundRect.setAttribute('fill', isDarkMode ? '#1f2937' : 'white');
      svgClone.insertBefore(backgroundRect, svgClone.firstChild);

      // Serializar SVG
      const svgData = new XMLSerializer().serializeToString(svgClone);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // Criar link de download
      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = `mapa-mental-${Date.now()}.svg`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(svgUrl);

      toast({
        title: "Download concluído!",
        description: "Mapa mental salvo como SVG."
      });
    } catch (err) {
      toast({
        title: "Erro no download",
        description: "Não foi possível baixar o mapa mental.",
        variant: "destructive"
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
      <div className={`rounded-lg max-w-6xl max-h-[90vh] w-full flex flex-col transition-colors duration-200 ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        {/* Header */}
        <div className={`border-b transition-colors duration-200 ${
          isDarkMode ? 'border-gray-600' : 'border-gray-200'
        }`}>
          {/* Linha 1: Título e botão fechar */}
          <div className="flex items-center justify-between p-3 sm:p-4">
            <div className="flex-1 min-w-0">
              <h3 className={`text-base sm:text-lg font-semibold truncate transition-colors duration-200 ${
                isDarkMode ? 'text-gray-200' : 'text-gray-900'
              }`}>
                {diagramInfo.icon} {modalTitle}
              </h3>
              <p className={`text-xs mt-1 hidden sm:block transition-colors duration-200 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                🖱️ Arraste para navegar • 🔍 Use os botões para zoom
              </p>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={onClose}
              className="ml-2 flex-shrink-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Linha 2: Controles de zoom e download */}
          <div className="flex items-center justify-between px-3 pb-3 sm:px-4 sm:pb-4">
            <p className={`text-xs sm:hidden transition-colors duration-200 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-500'
            }`}>
              🖱️ Arraste • 🔍 Zoom
            </p>
            <div className="flex items-center gap-1 sm:gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleZoomOut}
                disabled={zoom <= 0.5}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className={`text-xs sm:text-sm min-w-[2.5rem] sm:min-w-[3rem] text-center transition-colors duration-200 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {Math.round(zoom * 100)}%
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={handleZoomIn}
                disabled={zoom >= 5}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={handleDownload}
                className="flex items-center gap-1 sm:gap-2 ml-1 sm:ml-2"
                title="Baixar mapa mental como SVG"
              >
                <Download className="h-4 w-4" />
                <span className="hidden sm:inline">SVG</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className={`flex-1 overflow-auto p-6 transition-colors duration-200 ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        }`}>
          {isRendering && (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              <span className={`ml-2 transition-colors duration-200 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>Renderizando mapa mental...</span>
            </div>
          )}
          
          <div
            ref={containerRef}
            className="text-center min-h-[400px] flex items-center justify-center overflow-hidden relative"
            style={{
              cursor: isPanning ? 'grabbing' : 'grab',
              userSelect: 'none',
              touchAction: 'none' // Previne scroll nativo no mobile
            }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            // Touch events para mobile
            onTouchStart={(e) => {
              const touch = e.touches[0];
              handleMouseDown({
                clientX: touch.clientX,
                clientY: touch.clientY,
                preventDefault: () => e.preventDefault()
              } as any);
            }}
            onTouchMove={(e) => {
              const touch = e.touches[0];
              handleMouseMove({
                clientX: touch.clientX,
                clientY: touch.clientY,
                preventDefault: () => e.preventDefault()
              } as any);
            }}
            onTouchEnd={(e) => {
              handleMouseUp(e as any);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default MermaidModal;
