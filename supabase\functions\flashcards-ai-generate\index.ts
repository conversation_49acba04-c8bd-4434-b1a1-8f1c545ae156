import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
/// <reference types="https://deno.land/x/types/index.d.ts" />
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.0";

// Get environment variables
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

// Function to get all available Gemini API keys
const getGeminiApiKeys = (): string[] => {
  const keys: string[] = [];

  // Primeira chave (sem número)
  const primaryKey = Deno.env.get('GEMINI_API_KEY');
  if (primaryKey) {
    keys.push(primaryKey);
  }

  // Chaves numeradas (GEMINI_API_KEY2, GEMINI_API_KEY3, etc.)
  for (let i = 2; i <= 10; i++) {
    const keyName = `GEMINI_API_KEY${i}`;
    const key = Deno.env.get(keyName);
    if (key) {
      keys.push(key);
    }
  }

  console.log(`🔑 [flashcards-ai-generate] Encontradas ${keys.length} chaves API disponíveis`);
  return keys;
};

// Simple in-memory cache for rate limiting tracking
const keyUsageTracker = new Map<string, number>();

// Function to check if a key was used recently (within last 5 seconds)
const isKeyRecentlyUsed = (keyName: string): boolean => {
  const lastUsed = keyUsageTracker.get(keyName);
  if (!lastUsed) return false;

  const now = Date.now();
  const timeDiff = now - lastUsed;
  return timeDiff < 5000; // 5 seconds cooldown
};

// Function to mark a key as used
const markKeyAsUsed = (keyName: string): void => {
  keyUsageTracker.set(keyName, Date.now());
};

// Internal prompts for each flashcard type
const getFlashcardPrompt = (fcType: string, quantity: number): string => {
  const baseInstructions = `
INSTRUÇÕES GERAIS:
- Use apenas o conteúdo das questões fornecidas como base
- Crie conteúdo original (não copie literalmente)
- Use linguagem técnica voltada para médicos
- Evite conteúdo genérico ou superficial
- Priorize relevância clínica prática
- Gere EXATAMENTE ${quantity} flashcards
`;

  switch (fcType) {
    case "cloze":
      return `
✅ Prompt para Criação de Flashcards CLOZE DELETION

🎯 Objetivo
Gerar flashcards com lacuna em estilo cloze deletion, voltados para médicos e estudantes de medicina em fase de residência. O conteúdo deve ser técnico, direto e baseado em evidências.

📌 Estrutura do Flashcard
- front: uma frase clínica com uma única lacuna entre colchetes: [ ... ], representando um dado técnico importante (conduta, achado, valor laboratorial, dose, diagnóstico etc).
- back: a resposta correta que preenche a lacuna de forma precisa.

📘 Regras
- Varie a posição e o conteúdo da lacuna
- Não inclua explicações, fontes, listas ou formatações extras
- Foque em dados clínicos objetivos

📦 Exemplo
{
  "front": "A dose inicial de adrenalina intramuscular para anafilaxia em adultos é de [ ... ] mg.",
  "back": "0,3"
}

${baseInstructions}`;

    case "vf":
      return `
✅ Prompt para Criação de Flashcards VERDADEIRO OU FALSO

🎯 Objetivo
Gerar flashcards com afirmações clínicas baseadas em evidências que o usuário deve julgar como Verdadeiro ou Falso.

📌 Estrutura do Flashcard
- front: uma afirmação clínica direta
- back: "Verdadeiro" ou "Falso", seguido de uma justificativa técnica com até 2 frases

📘 Regras
- Afirmações devem ser clínicas, relevantes e objetivas
- Não use afirmações triviais, vagas ou sem impacto clínico
- Justifique com base fisiopatológica, conduta ou evidência

📦 Exemplo
{
  "front": "Todo paciente com meningite bacteriana deve receber corticóide antes do antibiótico.",
  "back": "Falso. O corticóide é indicado apenas em casos suspeitos de meningite por Haemophilus ou pneumococo, e deve preceder o antibiótico por até 30 minutos."
}

${baseInstructions}`;

    case "multipla":
      return `
✅ Prompt para Criação de Flashcards de MÚLTIPLA ESCOLHA

🎯 Objetivo
Gerar flashcards no formato de múltipla escolha com 4 alternativas, baseados em questões reais.

📌 Estrutura do Flashcard
- front: pergunta clara e direta, com alternativas A), B), C), D)
- back: letra da alternativa correta, seguida de uma explicação breve (1-2 frases)

📘 Regras
- Apenas uma alternativa deve estar correta
- Alternativas devem ser clinicamente plausíveis (evite absurdas)
- Não usar "Todas estão corretas" ou "Nenhuma"

📦 Exemplo
{
  "front": "Qual dos achados abaixo é mais específico para meningite bacteriana?\\nA) Cefaleia\\nB) Febre\\nC) Rigidez de nuca\\nD) Vômitos",
  "back": "C. A rigidez de nuca é um sinal meníngeo típico e mais específico que os demais sintomas gerais."
}

${baseInstructions}`;

    case "qa":
      return `
✅ Prompt para Criação de Flashcards de PERGUNTA E RESPOSTA

🎯 Objetivo
Gerar flashcards diretos em estilo pergunta e resposta, com foco clínico prático e linguagem técnica para médicos.

📌 Estrutura do Flashcard
- front: uma pergunta objetiva e técnica (ex: conduta, definição, indicação, valor de corte)
- back: a resposta direta, precisa e sem explicação adicional

📘 Regras
- Perguntas devem ser relevantes para prática clínica
- Não inclua listas, enumerações ou comentários
- Evite perguntas muito óbvias ou vagas

📦 Exemplo
{
  "front": "Qual é a principal bactéria causadora de faringoamigdalite em crianças entre 5 e 15 anos?",
  "back": "Streptococcus pyogenes"
}

${baseInstructions}`;

    default:
      return getFlashcardPrompt("cloze", quantity);
  }
};

// Function to make Gemini API call with fallback system
const callGeminiWithFallback = async (requestBody: any): Promise<Response> => {
  const apiKeys = getGeminiApiKeys();

  if (apiKeys.length === 0) {
    throw new Error('No Gemini API keys found');
  }

  let lastError: Error | null = null;

  for (let i = 0; i < apiKeys.length; i++) {
    const apiKey = apiKeys[i];
    const keyName = i === 0 ? 'GEMINI_API_KEY' : `GEMINI_API_KEY_${i + 1}`;

    // Check if this key was used recently
    if (isKeyRecentlyUsed(keyName)) {
      continue;
    }

    console.log(`🔑 [flashcards-ai-generate] Tentando ${keyName} (${i + 1}/${apiKeys.length})`);

    try {
      const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": apiKey
        },
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        console.log(`✅ [flashcards-ai-generate] Sucesso com ${keyName}`);
        markKeyAsUsed(keyName);
        return response;
      } else {
        const errorText = await response.text();
        console.warn(`⚠️ [flashcards-ai-generate] ${keyName} falhou: ${response.status}`);
        lastError = new Error(`API key ${keyName} failed: ${response.status} ${response.statusText}`);
        markKeyAsUsed(keyName);

        // Se for rate limiting (429), aguarda um pouco antes da próxima tentativa
        if (response.status === 429) {
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    } catch (error) {
      console.warn(`⚠️ [flashcards-ai-generate] Erro de rede com ${keyName}`);
      markKeyAsUsed(keyName); // Mark key as used even on network error
      lastError = error instanceof Error ? error : new Error(String(error));
    }
  }

  // Se chegou aqui, todas as chaves falharam
  console.error(`❌ [flashcards-ai-generate] Todas as ${apiKeys.length} chaves API falharam`);
  throw lastError || new Error('All API keys failed');
};

// Initialize Supabase client with the service role key for admin access
const supabase = createClient(supabaseUrl || '', supabaseServiceKey || '');

// Função para determinar o origin permitido baseado na requisição
const getAllowedOrigin = (request: Request): string => {
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://medevo.com.br',
    'https://www.medevo.com.br',
    'http://localhost:5173',
    'http://localhost:800'
  ];

  if (origin && allowedOrigins.includes(origin)) {
    return origin;
  }

  return 'https://medevo.com.br'; // fallback
};

const getCorsHeaders = (request: Request) => ({
  'Access-Control-Allow-Origin': getAllowedOrigin(request),
  'Access-Control-Allow-Headers': 'authorization, content-type, apikey, x-client-info',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true',
});

// Improved function to extract JSON from various text formats
const extractJSONFromText = (text) => {
  // First clean the text - remove markdown formatting if present
  let cleanText = text.replace(/```(json)?\s*/g, '').replace(/\s*```$/g, '');

  // Try direct JSON parse first (most common case)
  try {
    const parsed = JSON.parse(cleanText);

    // If we parsed an array successfully, return it
    if (Array.isArray(parsed)) {
      return parsed;
    }

    // If we parsed an object with a data/items/cards/flashcards field, return that
    if (parsed.flashcards) return parsed.flashcards;
    if (parsed.cards) return parsed.cards;
    if (parsed.items) return parsed.items;
    if (parsed.data) return parsed.data;
    if (parsed.generated) return parsed.generated;

    // Return the parsed object wrapped in an array as fallback
    return [parsed];
  } catch (directError) {
    console.warn(`⚠️ [flashcards-ai-generate] Direct parse failed, trying alternatives`);
  }

  // Try finding JSON arrays in the text
  const arrayMatch = cleanText.match(/\[\s*\{[\s\S]*\}\s*\]/);
  if (arrayMatch) {
    try {
      const parsed = JSON.parse(arrayMatch[0]);
      console.log(`✅ [flashcards-ai-generate] Extracted and parsed array JSON: ${parsed.length} items`);
      return parsed;
    } catch (arrayError) {
      console.warn(`⚠️ [flashcards-ai-generate] Array extraction failed: ${arrayError.message}`);
    }
  }

  // Try to extract individual JSON objects and combine them
  const objects = [];
  const objectMatches = cleanText.matchAll(/\{[^{}]*\}/g);
  for (const match of objectMatches) {
    try {
      const obj = JSON.parse(match[0]);
      if (obj.front && obj.back) {
        objects.push(obj);
      }
    } catch {
      // Ignore parsing errors for individual objects
    }
  }

  if (objects.length > 0) {
    console.log(`✅ [flashcards-ai-generate] Extracted ${objects.length} individual JSON objects`);
    return objects;
  }

  console.error(`❌ [flashcards-ai-generate] Failed to extract any valid JSON from the response`);
  // Last resort: try to extract front/back pairs manually
  try {
    const frontBackPairs = cleanText.match(/["']front["']\s*:\s*["']([^"']+)["']\s*,\s*["']back["']\s*:\s*["']([^"']+)["']/g);
    if (frontBackPairs && frontBackPairs.length > 0) {
      console.log(`⚠️ [flashcards-ai-generate] Attempting manual extraction of ${frontBackPairs.length} front/back pairs`);
      return frontBackPairs.map(pair => {
        const front = pair.match(/["']front["']\s*:\s*["']([^"']+)["']/)[1];
        const back = pair.match(/["']back["']\s*:\s*["']([^"']+)["']/)[1];
        return { front, back };
      });
    }
  } catch (e) {
    console.error(`❌ [flashcards-ai-generate] Manual extraction failed: ${e.message}`);
  }

  return null;
};

serve(async (req) => {
  const corsHeaders = getCorsHeaders(req);

  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {

    const availableKeys = getGeminiApiKeys();
    if (availableKeys.length === 0) {
      console.error("❌ [flashcards-ai-generate] Nenhuma chave Gemini API configurada");
      return new Response(
        JSON.stringify({
          error: "Serviço de geração de flashcards temporariamente indisponível. Tente novamente em alguns minutos."
        }),
        {
          status: 503,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("❌ [flashcards-ai-generate] Credenciais do Supabase não configuradas");
      return new Response(
        JSON.stringify({
          error: "Credenciais do Supabase não configuradas no servidor."
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    let requestData;
    try {
      requestData = await req.json();
    } catch (error) {
      console.error("❌ [flashcards-ai-generate] Erro ao parsear JSON:", error);
      return new Response(
        JSON.stringify({ error: "Dados da requisição inválidos" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    const {
      specialty,
      theme,
      focus,
      extrafocus,
      quantity = 5,
      fcType = "cloze",
      questionIds,
      directQuestions
    } = requestData;

    // Generate internal prompt based on fcType
    const prompt = getFlashcardPrompt(fcType, quantity);

    console.log(`🎯 [flashcards-ai-generate] Gerando ${quantity} flashcards (${fcType})`);

    let questionsData;

    if (directQuestions && directQuestions.length > 0) {
      // ✅ SEGURANÇA: Log removido - não expor dados de questões

      // Normalizar a estrutura dos dados vindos do frontend
      questionsData = directQuestions.map(q => {
        // ✅ SEGURANÇA: Log removido - não expor estrutura de questões

        return {
          id: q.id,
          question_content: q.question_content || q.statement, // Fallback para compatibilidade
          response_choices: q.response_choices || q.alternatives,
          correct_choice: q.correct_choice || q.correct_answer,
          exam_year: q.exam_year || q.year,
          specialty_id: q.specialty_id || q.specialty?.id,
          theme_id: q.theme_id || q.theme?.id,
          focus_id: q.focus_id || q.focus?.id,
          source_id: q.id
        };
      });

      // ✅ SEGURANÇA: Log removido - não expor dados de questões
    }
    else if (questionIds && questionIds.length > 0) {
      // ✅ SEGURANÇA: Log removido - não expor IDs de questões
      // ✅ SEGURANÇA: Log removido - não expor IDs de questões



      const { data, error } = await supabase
        .from('questions')
        .select(`
          id,
          question_content,
          response_choices,
          correct_choice,
          exam_year,
          specialty_id,
          theme_id,
          focus_id,
          ai_commentary
        `)
        .in('id', questionIds);

      console.log(`🔍 [flashcards-ai-generate] Resultado da query principal:`, { data: data?.length, error });

      if (error) {
        console.error('❌ [flashcards-ai-generate] Erro ao buscar questões pelos IDs:', error);
        return new Response(
          JSON.stringify({ error: `Erro ao buscar questões: ${error.message}` }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      if (!data || data.length === 0) {
        console.warn("⚠️ [flashcards-ai-generate] Nenhuma questão encontrada com os IDs fornecidos");
        return new Response(
          JSON.stringify({
            error: "Nenhuma questão encontrada com os IDs fornecidos."
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      questionsData = data;
      console.log(`✅ [flashcards-ai-generate] Encontradas ${questionsData.length} questões pelos IDs`);
    }
    else {
      console.log(`🔍 [flashcards-ai-generate] Usando filtros de categoria`);

      // First, count total questions with filters to calculate random offset
      let countQuery = supabase
        .from('questions')
        .select('id', { count: 'exact', head: true });

      // Apply same filters to count query
      if (specialty) {
        countQuery = countQuery.eq('specialty_id', specialty);
      }
      if (theme) {
        countQuery = countQuery.eq('theme_id', theme);
      }
      if (focus) {
        console.log(`🔍 [flashcards-ai-generate] Aplicando filtro focus_id: ${focus}`);
        countQuery = countQuery.eq('focus_id', focus);
      }

      const { count: totalQuestions, error: countError } = await countQuery;

      if (countError) {
        console.error('❌ [flashcards-ai-generate] Erro ao contar questões:', countError);
        throw countError;
      }

      console.log(`🔍 [flashcards-ai-generate] Total de questões disponíveis: ${totalQuestions}`);

      if (!totalQuestions || totalQuestions === 0) {
        console.warn("⚠️ [flashcards-ai-generate] Nenhuma questão encontrada com os filtros fornecidos");
        return new Response(
          JSON.stringify({
            error: "Nenhuma questão encontrada com os filtros selecionados.",
            appliedFilters: { specialty, theme, focus }
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      // Calculate random offset to get truly random questions from the entire dataset
      const maxOffset = Math.max(0, totalQuestions - quantity);
      const randomOffset = Math.floor(Math.random() * (maxOffset + 1));

      console.log(`🎲 [flashcards-ai-generate] Usando offset aleatório: ${randomOffset} de ${maxOffset} possíveis`);

      // Build the main query with random offset
      let query = supabase
        .from('questions')
        .select(`
          id,
          question_content,
          response_choices,
          correct_choice,
          exam_year,
          specialty_id,
          theme_id,
          focus_id,
          knowledge_domain,
          ai_commentary
        `)
        .range(randomOffset, randomOffset + quantity - 1); // Use range for offset + limit

      // Apply same filters to main query
      if (specialty) {
        query = query.eq('specialty_id', specialty);
      }
      if (theme) {
        query = query.eq('theme_id', theme);
      }
      if (focus) {
        query = query.eq('focus_id', focus);
      }

      const { data, error } = await query;

      console.log(`🔍 [flashcards-ai-generate] Query result:`, { 
        data: data?.length, 
        error,
        firstQuestion: data?.[0] ? {
          id: data[0].id,
          domain: data[0].knowledge_domain,
          specialty_id: data[0].specialty_id,
          theme_id: data[0].theme_id,
          focus_id: data[0].focus_id
        } : null
      });

      if (error) {
        console.error('❌ [flashcards-ai-generate] Erro ao buscar questões pelos filtros:', error);
        return new Response(
          JSON.stringify({ error: `Erro ao buscar questões: ${error.message}` }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      if (!data || data.length === 0) {
        console.warn("⚠️ [flashcards-ai-generate] Nenhuma questão encontrada com os filtros fornecidos");
        
        // Try a simpler query without any filters to see if we have any questions at all
        const { data: anyQuestions, error: anyError } = await supabase
          .from('questions')
          .select('id, knowledge_domain, specialty_id, theme_id, focus_id')
          .limit(5);

        console.log(`🔍 [flashcards-ai-generate] Any questions available:`, { 
          anyQuestions: anyQuestions?.length, 
          anyError,
          samples: anyQuestions 
        });

        return new Response(
          JSON.stringify({
            error: "Nenhuma questão encontrada com esses filtros. Tente outros filtros ou reduza a especificidade.",
            debug: {
              totalQuestionsAvailable: anyQuestions?.length || 0,
              appliedFilters: { specialty, theme, focus }
            }
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      questionsData = data;
      console.log(`✅ [flashcards-ai-generate] Selecionadas ${questionsData.length} questões`);
    }

    const questionsWithIds = questionsData.map((q, i) => {
      const categoryInfo =
        `[Q${i+1} ID: ${q.id}] ` +
        `[Especialidade ID: ${q.specialty_id || 'N/A'}] ` +
        `[Tema ID: ${q.theme_id || 'N/A'}] ` +
        `[Foco ID: ${q.focus_id || 'N/A'}]`;

      return categoryInfo;
    }).join('\n');

    const questionIdentifierInstruction = `
MUITO IMPORTANTE: Para cada flashcard gerado, você DEVE incluir o ID da questão que o inspirou no formato #QID#ID_DA_QUESTÃO# no início da propriedade "front".
Este ID será usado para rastrear a origem de cada flashcard e garantir que ele seja associado à categoria correta.

Exemplo de JSON correto:
{
  "front": "#QID#12345# A principal causa de [...] é [...]",
  "back": "resposta"
}

SEM ESTE ID ESPECÍFICO, NÃO SERÁ POSSÍVEL RASTREAR CORRETAMENTE A ORIGEM DO FLASHCARD.
    `;

    // Log resumido das questões
    console.log(`📝 [flashcards-ai-generate] Processando ${questionsData.length} questões`);

    const questionsText = questionsData.map((q, i) => {
      const categoryInfo = `Especialidade ID: ${q.specialty_id || 'N/A'}, Tema ID: ${q.theme_id || 'N/A'}, Foco ID: ${q.focus_id || 'N/A'}`;

      const alternatives = Array.isArray(q.response_choices)
        ? q.response_choices.map((alt, j) => `${String.fromCharCode(65 + j)}) ${alt}`).join('\n')
        : Object.values(q.response_choices || {}).map((alt, j) => `${String.fromCharCode(65 + j)}) ${alt}`).join('\n');

      const questionContent = q.question_content || 'Conteúdo da questão não disponível';

      // Incluir comentário da IA se disponível
      const aiCommentary = q.ai_commentary ? `\n[Comentário da IA]: ${q.ai_commentary}` : '';

      // Log resumido sobre comentários da IA
      if (i === 0) { // Log apenas na primeira questão para não poluir
        const questionsWithAI = questionsData.filter(q => q.ai_commentary).length;
        console.log(`🤖 [flashcards-ai-generate] ${questionsWithAI}/${questionsData.length} questões têm comentário da IA`);
      }

      return `Q${i + 1} [ID:${q.id}]: ${questionContent}\n${alternatives}` +
        (q.exam_year ? ` [Ano: ${q.exam_year}]` : "") +
        `\n[Categorias: ${categoryInfo}]${aiCommentary}`;
    }).join('\n\n');

    const enhancedPrompt = `
${prompt}

${questionIdentifierInstruction}

INSTRUÇÕES ESPECÍFICAS PARA CATEGORIZAÇÃO:
1. Cada flashcard deve ser baseado EXCLUSIVAMENTE em UMA questão específica.
2. O ID da questão (#QID#) deve corresponder exatamente ao ID de uma das questões abaixo.
3. Não misture conteúdos de diferentes questões em um único flashcard.
4. Não invente categorias - use apenas as informações fornecidas nas questões.
5. IMPORTANTE: Crie EXATAMENTE ${quantity} flashcards no total.
6. Formate a resposta como um ARRAY JSON de ${quantity} objetos, cada um com 'front' e 'back'.
7. UTILIZE O COMENTÁRIO DA IA: Quando uma questão tiver [Comentário da IA], use essas informações para:
   - Enriquecer o conteúdo dos flashcards com explicações mais detalhadas
   - Adicionar contexto clínico relevante
   - Incluir dicas de memorização ou associações importantes
   - Fornecer explicações mais didáticas e contextualizadas

FORMATO DE RESPOSTA:
Por favor, retorne EXATAMENTE ${quantity} flashcards em formato JSON puro, SEM marcações markdown. Apenas o array JSON com os flashcards.

Exemplo correto de formatação (formate EXATAMENTE assim):
[
  {
    "front": "#QID#id-da-questao-1# Texto da frente com [...] lacuna",
    "back": "resposta 1"
  },
  {
    "front": "#QID#id-da-questao-2# Outro texto com [...] lacuna",
    "back": "resposta 2"
  }
]

Utilize as questões abaixo como base de inspiração para formular os flashcards:

${questionsText}
    `.trim();

    // Prepare Gemini request body
    const geminiRequestBody = {
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `Você é um assistente especialista em criar bons flashcards para estudantes de medicina se preparando para residência médica. Você DEVE sempre retornar um array JSON com a quantidade exata de flashcards solicitada.

${enhancedPrompt}`
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.30,
        maxOutputTokens: 8000, // Aumentado de 2000 para 8000
        topP: 0.9,
        topK: 40
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_ONLY_HIGH"
        },
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_ONLY_HIGH"
        }
      ]
    };

    console.log("📤 [flashcards-ai-generate] Enviando solicitação para Gemini");

    let response: Response;
    try {
      response = await callGeminiWithFallback(geminiRequestBody);
    } catch (error) {
      console.error(`❌ [flashcards-ai-generate] Erro na API Gemini:`, error);
      return new Response(
        JSON.stringify({
          error: "Serviço temporariamente indisponível. Tente novamente em alguns minutos."
        }),
        {
          status: 503,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    const responseStatus = `${response.status} ${response.statusText}`;
    console.log(`🔍 [flashcards-ai-generate] Resposta recebida do Gemini, status: ${responseStatus}`);

    // Process simple Gemini response (non-streaming)
    const geminiData = await response.json();
    console.log(`✅ [flashcards-ai-generate] Resposta JSON recebida do Gemini`);

    // Extract content from Gemini response
    let generatedContent = "";

    if (geminiData.candidates && Array.isArray(geminiData.candidates) && geminiData.candidates.length > 0) {
      const candidate = geminiData.candidates[0];

      // Check for token limit issue
      if (candidate.finishReason === "MAX_TOKENS") {
        console.warn(`⚠️ [flashcards-ai-generate] ATENÇÃO: Limite de tokens atingido! Resposta pode estar incompleta.`);
        console.warn(`⚠️ [flashcards-ai-generate] Configuração atual: maxOutputTokens = 8000`);
      }

      if (candidate.content && candidate.content.parts && Array.isArray(candidate.content.parts)) {
        for (const part of candidate.content.parts) {
          if (part.text && typeof part.text === 'string' && part.text.length > 0) {
            generatedContent += part.text;
          }
        }
      }
    }

    console.log(`📝 [flashcards-ai-generate] Conteúdo extraído (${generatedContent.length} caracteres)`);

    // Create data object similar to OpenAI response format
    const data = {
      choices: [
        {
          message: {
            content: generatedContent
          }
        }
      ]
    };

    if (!data.choices || data.choices.length === 0) {
      console.error("❌ [flashcards-ai-generate] Resposta inválida da OpenAI:", data);
      return new Response(
        JSON.stringify({ error: "Resposta inválida da OpenAI" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    if (!generatedContent) {
      console.error("❌ [flashcards-ai-generate] Conteúdo ausente na resposta do Gemini");
      return new Response(
        JSON.stringify({ error: "Conteúdo ausente na resposta do Gemini" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }



    let generatedFlashcards;

    try {
      generatedFlashcards = extractJSONFromText(generatedContent);

      if (!generatedFlashcards || generatedFlashcards.length === 0) {
        throw new Error("Não foi possível extrair JSON válido da resposta ou o array está vazio");
      }



      const questionMetadataMap = questionsData.reduce((acc, q) => {
        acc[q.id] = {
          specialty_id: q.specialty_id,
          theme_id: q.theme_id,
          focus_id: q.focus_id,
          hierarchy: {
            specialty: { name: 'N/A' },
            theme: { name: 'N/A' },
            focus: { name: 'N/A' }
          }
        };
        return acc;
      }, {});

      generatedFlashcards = generatedFlashcards.map(card => {
        let sourceId = null;

        if (card.front && typeof card.front === 'string') {
          const idMatch = card.front.match(/#QID#([^#]+)#/);
          if (idMatch && idMatch[1]) {
            sourceId = idMatch[1];
            card.front = card.front.replace(/#QID#[^#]+#\s*/, '');

            const metadata = questionMetadataMap[sourceId];
            if (metadata) {
              card.specialty_id = metadata.specialty_id;
              card.theme_id = metadata.theme_id;
              card.focus_id = metadata.focus_id;
              card.hierarchy = metadata.hierarchy;
              card.source_question_id = sourceId;
            }
          } else {
            console.warn(`⚠️ [flashcards-ai-generate] Não foi possível encontrar QID em:`, card.front.substring(0, 50));

            const firstQuestionId = questionsData[0].id;
            const metadata = questionMetadataMap[firstQuestionId];
            card.specialty_id = metadata.specialty_id;
            card.theme_id = metadata.theme_id;
            card.focus_id = metadata.focus_id;
            card.hierarchy = metadata.hierarchy;
            card.source_question_id = firstQuestionId;


          }
        }

        return card;
      });

      console.log(`✅ [flashcards-ai-generate] Gerados ${generatedFlashcards.length} flashcards`);

      return new Response(
        JSON.stringify({
          generated: generatedFlashcards,
          generatedText: generatedContent,
          fcType,
          source: directQuestions ? 'direct_questions' : (questionIds ? 'question_ids' : 'filters')
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    } catch (e) {
      console.error(`❌ [flashcards-ai-generate] Erro ao processar resposta: ${e.message}`, e.stack);
      return new Response(
        JSON.stringify({ error: e.message }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
  } catch (e) {
    console.error(`❌ [flashcards-ai-generate] Erro: ${e.message}`, e.stack);
    return new Response(
      JSON.stringify({ error: e.message }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});
