import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// Função para determinar o origin permitido baseado na requisição
const getAllowedOrigin = (request)=>{
  const origin = request.headers.get('origin');
  const allowedOrigins = [
    'https://medevo.com.br',
    'https://www.medevo.com.br',
    'http://localhost:5173',
    'http://localhost:800',
    'http://localhost:3000',
    'http://127.0.0.1:800',
    'http://127.0.0.1:5173',
    'http://pedb.com.br',
    'https://pedb.com.br',
    'https://www.pedb.com.br',
    'http://localhost:8080'
  ];
  if (origin && allowedOrigins.includes(origin)) {
    return origin;
  }
  // Em desenvolvimento, permitir qualquer localhost
  if (origin && (origin.includes('localhost') || origin.includes('127.0.0.1'))) {
    return origin;
  }
  return 'https://medevo.com.br'; // fallback
};
const getCorsHeaders = (request)=>({
    'Access-Control-Allow-Origin': getAllowedOrigin(request),
    'Access-Control-Allow-Headers': 'authorization, content-type, x-client-info, apikey',
    'Access-Control-Allow-Methods': 'POST, OPTIONS, GET',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400'
  });
serve(async (req)=>{
  const corsHeaders = getCorsHeaders(req);
  const startTime = Date.now();
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders,
      status: 200
    });
  }
  try {
    const { statement, alternatives, correctAnswer, specialty, mediaAttachments } = await req.json();


    // Enhanced prompt for thinking mode
    const systemPrompt = `
Você é um professor experiente de residência médica, especializado na área da questão (como Clínica Médica, Pediatria, Cirurgia, Ginecologia, etc.).

**MODO THINKING ATIVADO:** Pense como um médico especialista. Elabore raciocínio clínico passo a passo para chegar à análise mais precisa. Considere sinais, sintomas, guidelines e protocolos de forma estruturada antes de responder.

Sua tarefa é analisar uma questão de múltipla escolha e fornecer comentários educativos detalhados para cada alternativa, além de um comentário final sobre o tema.

**INSTRUÇÕES IMPORTANTES:**
1. PRIMEIRO: Analise o contexto clínico e identifique os conceitos-chave
2. SE HOUVER IMAGENS: Analise cuidadosamente todas as imagens fornecidas e integre suas observações na análise relacionando com a questão/gabarito
3. Para cada alternativa, explique POR QUE está correta ou incorreta com base em evidências
4. Use linguagem didática, como se estivesse ensinando um residente
5. Inclua dicas práticas e "pegadinhas" comuns
6. Use expressões como "Presta atenção nesse detalhe!", "Segura essa dica que vale ouro!", "Essa é uma típica casca de banana!"
7. Seja específico sobre conceitos médicos, guidelines atuais e protocolos
8. No comentário final, faça uma síntese educativa do tema
9. Explique sua linha de raciocínio antes da resposta final
10. IMAGENS: Se houver imagens, descreva os achados relevantes e como eles se relacionam com a questão

**FORMATO DE RESPOSTA OBRIGATÓRIO:**
Você DEVE responder APENAS com um JSON válido no seguinte formato exato:

{
  "alternativas": [
    {
      "texto": "texto da alternativa",
      "comentario": "explicação detalhada com raciocínio clínico",
      "correta": true/false
    }
  ],
  "comentario_final": "síntese educativa do tema com raciocínio estruturado",
  "possivel_erro_no_gabarito": false,
  "justificativa_erro_gabarito": ""
}

**ATENÇÃO CRÍTICA:**
- Responda APENAS com o JSON válido
- NÃO inclua texto antes ou depois do JSON
- NÃO use markdown com três acentos graves
- NÃO inclua explicações fora do JSON
- O JSON deve ser válido e parseável
- Use asteriscos duplos para destacar palavras-chave importantes
- Use hífen ou números para listas, NÃO use asteriscos simples
- Estruture o texto de forma clara e organizada
`;
    // Preparar conteúdo da mensagem (texto + imagens se houver)
    const messageContent = [];

    // Adicionar texto principal
    const textContent = `
Especialidade: ${specialty}

Questão:
${statement}

Alternativas:
${alternatives.map((alt, i)=>`${i + 1}. ${alt}`).join('\n')}

Alternativa considerada correta (gabarito oficial): ${correctAnswer + 1}
(Índice ${correctAnswer} = Alternativa "${alternatives[correctAnswer]}")

Responda APENAS com o JSON válido conforme especificado no sistema.
`;

    messageContent.push({ text: textContent });

    // Adicionar imagens se houver
    if (mediaAttachments && Array.isArray(mediaAttachments) && mediaAttachments.length > 0) {

      for (const imageUrl of mediaAttachments) {
        try {
          // Fazer fetch da imagem para converter em base64
          const imageResponse = await fetch(imageUrl);
          if (imageResponse.ok) {
            const imageBuffer = await imageResponse.arrayBuffer();
            const base64Image = btoa(String.fromCharCode(...new Uint8Array(imageBuffer)));

            // Detectar tipo MIME da imagem
            const mimeType = imageResponse.headers.get('content-type') || 'image/jpeg';

            messageContent.push({
              inline_data: {
                mime_type: mimeType,
                data: base64Image
              }
            });

          }
        } catch (imageError) {
        }
      }
    }
    let completionText = "";
    let apiUsed = "";
    // Get API keys from environment
    const openAiKey = Deno.env.get("OPENAI_API_KEY");
    const geminiKey = Deno.env.get("GEMINI_API_KEY");
    // FORCE Gemini 2.5 Flash as primary with thinking mode - only use OpenAI if Gemini completely fails
    // Try Gemini 2.5 Flash first with thinking mode
    if (geminiKey) {
      try {
        const geminiResponse = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-goog-api-key": geminiKey
          },
          body: JSON.stringify({
            contents: [
              {
                role: "user",
                parts: [
                  {
                    text: systemPrompt
                  },
                  ...messageContent
                ]
              }
            ],
            generationConfig: {
              temperature: 0.5,
              maxOutputTokens: 8192,
              topP: 0.9
            },
            safetySettings: [
              {
                category: "HARM_CATEGORY_HATE_SPEECH",
                threshold: "BLOCK_ONLY_HIGH"
              },
              {
                category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                threshold: "BLOCK_ONLY_HIGH"
              },
              {
                category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                threshold: "BLOCK_ONLY_HIGH"
              },
              {
                category: "HARM_CATEGORY_HARASSMENT",
                threshold: "BLOCK_ONLY_HIGH"
              }
            ]
          })
        });
        if (!geminiResponse.ok) {
          throw new Error("Serviço de análise temporariamente indisponível");
        }
        const geminiData = await geminiResponse.json();
        completionText = geminiData.candidates[0].content.parts[0].text;
        apiUsed = "Gemini 2.5 Flash (thinking mode)";


      } catch (geminiError) {
        console.error('❌ [question-commentary] ERRO NO GEMINI:', {
          error: geminiError,
          message: geminiError.message,
          stack: geminiError.stack
        });

        // If Gemini 2.5 Flash fails, try OpenAI as fallback
        if (openAiKey) {
          try {
            const response = await fetch("https://api.openai.com/v1/chat/completions", {
              method: "POST",
              headers: {
                "Authorization": `Bearer ${openAiKey}`,
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                model: "gpt-4o-mini",
                messages: [
                  {
                    role: "system",
                    content: systemPrompt
                  },
                  {
                    role: "user",
                    content: textContent
                  }
                ],
                temperature: 0.2,
                max_tokens: 2500
              })
            });
            if (!response.ok) {
              throw new Error("Serviço de análise temporariamente indisponível");
            }
            const data = await response.json();
            completionText = data.choices[0].message.content;
            apiUsed = "OpenAI (fallback)";
          } catch (openAiError) {
            throw new Error("Serviço de análise temporariamente indisponível");
          }
        } else {
          throw new Error("Serviço de análise temporariamente indisponível");
        }
      }
    } else if (openAiKey) {
      // Use OpenAI directly ONLY if Gemini not configured
      try {
        const response = await fetch("https://api.openai.com/v1/chat/completions", {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${openAiKey}`,
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            model: "gpt-4o-mini",
            messages: [
              {
                role: "system",
                content: systemPrompt
              },
              {
                role: "user",
                content: textContent
              }
            ],
            temperature: 0.2,
            max_tokens: 2500
          })
        });
        if (!response.ok) {
          throw new Error("Serviço de análise temporariamente indisponível");
        }
        const data = await response.json();
        completionText = data.choices[0].message.content;
        apiUsed = "OpenAI";
      } catch (openAiError) {
        throw new Error("Serviço de análise temporariamente indisponível");
      }
    } else {
      throw new Error("Serviço de análise temporariamente indisponível");
    }
    // Ensure we have a response
    if (!completionText) {
      throw new Error("Serviço de análise temporariamente indisponível");
    }
    // Clean up markdown from JSON if present and improve JSON extraction
    // More robust JSON extraction
    let cleanedText = completionText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();

    // 🔧 CORREÇÃO CRÍTICA: Função robusta para extrair JSON válido
    const extractValidJSON = (text) => {
      // Primeiro, limpar caracteres problemáticos
      let cleaned = text
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove caracteres de controle
        .trim();

      // Tentar encontrar JSON completo
      const jsonStart = cleaned.indexOf('{');
      if (jsonStart === -1) return cleaned;

      // Encontrar o último } válido
      let braceCount = 0;
      let jsonEnd = -1;

      for (let i = jsonStart; i < cleaned.length; i++) {
        if (cleaned[i] === '{') braceCount++;
        if (cleaned[i] === '}') {
          braceCount--;
          if (braceCount === 0) {
            jsonEnd = i;
            break;
          }
        }
      }

      if (jsonEnd !== -1) {
        return cleaned.substring(jsonStart, jsonEnd + 1);
      }

      return cleaned;
    };

    cleanedText = extractValidJSON(cleanedText);
    // If response doesn't start with {, try to find JSON in the text
    if (!cleanedText.startsWith('{')) {
      // Look for JSON patterns in the text
      const jsonMatches = [
        cleanedText.match(/\{[\s\S]*?"alternativas"[\s\S]*?"comentario_final"[\s\S]*?\}/),
        cleanedText.match(/\{[\s\S]*\}/)
      ];
      for (const match of jsonMatches){
        if (match) {
          try {
            // Test if this JSON is valid
            JSON.parse(match[0]);
            cleanedText = match[0];
            break;
          } catch (e) {
            continue;
          }
        }
      }
    }
    // 🛡️ BULLETPROOF JSON VALIDATION
    let jsonResponse;
    try {
      jsonResponse = JSON.parse(cleanedText);
      // 🔧 FORCE VALID STRUCTURE: Ensure all required fields exist with fallbacks
      jsonResponse = validateAndFixStructure(jsonResponse, alternatives, correctAnswer);
      // Convert asterisks to HTML bold tags and handle line breaks
      jsonResponse.alternativas.forEach((alt)=>{
        if (alt.comentario) {
          alt.comentario = convertTextToHtml(alt.comentario);
        }
      });
      if (jsonResponse.comentario_final) {
        jsonResponse.comentario_final = convertTextToHtml(jsonResponse.comentario_final);
      }
      if (jsonResponse.justificativa_erro_gabarito) {
        jsonResponse.justificativa_erro_gabarito = convertTextToHtml(jsonResponse.justificativa_erro_gabarito);
      }
      // Special validation for true/false questions
      if (alternatives.length === 2) {
        const hasEmptyComments = jsonResponse.alternativas.some((alt)=>!alt.comentario || alt.comentario.trim() === '');
        if (hasEmptyComments) {
          jsonResponse.alternativas.forEach((alt)=>{
            if (!alt.comentario || alt.comentario.trim() === '') {
              if (alt.correta) {
                alt.comentario = "Esta alternativa está correta conforme o gabarito oficial.";
              } else {
                alt.comentario = "Esta alternativa está incorreta conforme o gabarito oficial.";
              }
            }
          });
        }
      }
      return new Response(JSON.stringify(jsonResponse), {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        },
        status: 200
      });
    } catch (parseError) {
      // 🔧 ANTES DO FALLBACK: Tentar extrair JSON de forma mais agressiva
      try {
        // Procurar por padrões de JSON válidos na resposta original
        const jsonPattern = /\{[\s\S]*?"alternativas"[\s\S]*?"comentario_final"[\s\S]*?\}/;
        const match = completionText.match(jsonPattern);

        if (match) {
          let extractedJSON = match[0];
          // Limpar caracteres problemáticos do JSON extraído
          extractedJSON = extractedJSON
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
            .replace(/\n/g, ' ')
            .replace(/\r/g, ' ')
            .replace(/\t/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();

          const directParsed = JSON.parse(extractedJSON);
          const directValidated = validateAndFixStructure(directParsed, alternatives, correctAnswer);

          // Converter para HTML
          directValidated.alternativas.forEach((alt) => {
            if (alt.comentario) {
              alt.comentario = convertTextToHtml(alt.comentario);
            }
          });
          if (directValidated.comentario_final) {
            directValidated.comentario_final = convertTextToHtml(directValidated.comentario_final);
          }

          return new Response(JSON.stringify(directValidated), {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 200
          });
        }
      } catch (extractError) {
        // Se extração direta falhar, continuar para sanitização
      }

      // Try sanitization as last resort
      try {
        const sanitizedText = sanitizeAIResponse(cleanedText, alternatives, correctAnswer);
        const sanitizedResponse = JSON.parse(sanitizedText);
        const validatedResponse = validateAndFixStructure(sanitizedResponse, alternatives, correctAnswer);
        // Convert to HTML
        validatedResponse.alternativas.forEach((alt)=>{
          if (alt.comentario) {
            alt.comentario = convertTextToHtml(alt.comentario);
          }
        });
        if (validatedResponse.comentario_final) {
          validatedResponse.comentario_final = convertTextToHtml(validatedResponse.comentario_final);
        }
        return new Response(JSON.stringify(validatedResponse), {
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json"
          },
          status: 200
        });
      } catch (sanitizeError) {
        // Final fallback
        const processedResponse = processAIResponse(completionText, alternatives, correctAnswer);
        return new Response(JSON.stringify(processedResponse), {
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json"
          },
          status: 200
        });
      }
    }
  } catch (error) {
    return new Response(JSON.stringify({
      error: "AI service temporarily unavailable",
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      },
      status: 500
    });
  }
});
function convertTextToHtml(text) {
  if (!text || typeof text !== 'string') {
    return text;
  }
  // Handle different types of line breaks first
  text = text.replace(/\r\n/g, '\n'); // Windows line breaks
  text = text.replace(/\r/g, '\n'); // Mac line breaks
  // Convert bullet points with asterisks to proper HTML lists
  // Match lines that start with asterisk (bullet points)
  text = text.replace(/^\s*\*\s+(.+)$/gm, '<li>$1</li>');
  // Wrap consecutive <li> elements in <ul> tags
  text = text.replace(/(<li>.*<\/li>)(\s*<li>.*<\/li>)*/gs, '<ul>$&</ul>');
  // Convert double asterisks to bold (must come before single asterisks)
  text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  // Convert single asterisks to bold, but avoid those at end of sentences
  // This pattern looks for asterisks that are NOT at the end of a sentence
  text = text.replace(/\*([^*\n.!?]+)\*/g, '<strong>$1</strong>');
  // Clean up asterisks at the end of sentences (like "precisa*.")
  text = text.replace(/\*([.!?])/g, '$1');
  // Remove standalone asterisks that might be left
  text = text.replace(/\s\*\s/g, ' ');
  text = text.replace(/\*$/gm, '');
  // Convert double line breaks to paragraph breaks
  text = text.replace(/\n\s*\n/g, '</p><p>');
  // Convert single line breaks to <br> tags (but not inside lists)
  text = text.replace(/\n(?![<\/])/g, '<br>');
  // Wrap in paragraph tags if not already wrapped
  if (!text.startsWith('<p>') && !text.startsWith('<ul>')) {
    text = '<p>' + text + '</p>';
  }
  // Clean up empty paragraphs
  text = text.replace(/<p>\s*<\/p>/g, '');
  text = text.replace(/<p><br><\/p>/g, '');
  // Fix list formatting - ensure lists are not inside paragraphs
  text = text.replace(/<p>(<ul>.*?<\/ul>)<\/p>/gs, '$1');
  return text;
}
// Keep the old function for backward compatibility
function convertAsterisksToHtml(text) {
  return convertTextToHtml(text);
}
function processAIResponse(responseText, alternatives, correctAnswer) {
  // Try to extract useful information from the raw response
  const alternativasProcessadas = alternatives.map((texto, index)=>{
    const isCorrect = index === parseInt(correctAnswer.toString()); // ✅ CORREÇÃO: Usar indexação 0-based
    // Try to find specific commentary for this alternative in the raw text
    let comentario = "Análise detalhada não disponível no momento.";
    // Look for patterns that might indicate commentary for this alternative
    const altNumber = index + 1;
    const patterns = [
      new RegExp(`${altNumber}[.):]\\s*([^\\n]{50,200})`, 'i'),
      new RegExp(`alternativa\\s+${altNumber}[^\\n]*([^\\n]{50,200})`, 'i'),
      new RegExp(`opção\\s+${altNumber}[^\\n]*([^\\n]{50,200})`, 'i')
    ];
    for (const pattern of patterns){
      const match = responseText.match(pattern);
      if (match && match[1]) {
        comentario = match[1].trim();
        break;
      }
    }
    return {
      texto,
      comentario: convertTextToHtml(comentario),
      correta: isCorrect
    };
  });
  const result = {
    alternativas: alternativasProcessadas,
    comentario_final: convertTextToHtml(responseText.substring(0, 1000) + (responseText.length > 1000 ? '...' : '')),
    possivel_erro_no_gabarito: false,
    justificativa_erro_gabarito: ""
  };
  return result;
}
// 🛡️ ULTRA-ROBUST FUNCTION: Sanitize ANY malformed AI response
function sanitizeAIResponse(rawText, alternatives, correctAnswer) {
  let cleanedText = rawText;
  // Step 1: First try to parse as-is (most common case)
  try {
    const directParse = JSON.parse(cleanedText);
    if (directParse.alternativas && Array.isArray(directParse.alternativas) && directParse.comentario_final && typeof directParse.comentario_final === 'string') {
      return cleanedText; // Return as-is if it's already valid
    }
  } catch (e) {
  }
  // Step 2: Handle nested JSON structures (AI returning JSON as text content)
  if (cleanedText.includes('"alternativas"') && cleanedText.includes('"comentario_final"')) {
    try {
      const parsedStructure = JSON.parse(cleanedText);
      // Check if this is a valid structure that we can use directly
      if (parsedStructure.alternativas && Array.isArray(parsedStructure.alternativas) && parsedStructure.comentario_final && typeof parsedStructure.comentario_final === 'string') {
        return cleanedText; // Return the original valid JSON
      }
    } catch (e) {
    }
  }
  // Step 2: Try to extract JSON from mixed content
  const jsonPattern = /\{[\s\S]*?"alternativas"[\s\S]*?"comentario_final"[\s\S]*?\}/;
  const jsonMatch = cleanedText.match(jsonPattern);
  if (jsonMatch) {
    try {
      const extractedJson = JSON.parse(jsonMatch[0]);
      if (extractedJson.alternativas && extractedJson.comentario_final) {
        return jsonMatch[0];
      }
    } catch (e) {
    }
  }
  // Step 3: If no JSON found, create one from the text content
  if (!cleanedText.includes('"alternativas"') || !cleanedText.includes('"comentario_final"')) {
    const fallbackResponse = {
      alternativas: alternatives.map((alt, index)=>({
          texto: alt,
          comentario: `Análise baseada no texto da IA: ${cleanedText.substring(0, 200)}...`,
          correta: index === correctAnswer // ✅ CORREÇÃO: Usar indexação 0-based
        })),
      comentario_final: cleanedText.length > 50 ? cleanedText.substring(0, 1000) : "Análise detalhada não disponível no momento.",
      possivel_erro_no_gabarito: false,
      justificativa_erro_gabarito: ""
    };
    return JSON.stringify(fallbackResponse);
  }
  // Step 3: If all else fails, create a valid structure from scratch
  const fallbackStructure = {
    alternativas: alternatives.map((texto, index)=>({
        texto: texto,
        comentario: "Análise detalhada será gerada em breve.",
        correta: index === correctAnswer // ✅ CORREÇÃO: Usar indexação 0-based
      })),
    comentario_final: "Análise completa da questão será disponibilizada em breve.",
    possivel_erro_no_gabarito: false,
    justificativa_erro_gabarito: ""
  };
  return JSON.stringify(fallbackStructure);
}
// 🛡️ BULLETPROOF STRUCTURE VALIDATOR: Ensures response always has valid structure
function validateAndFixStructure(jsonResponse, alternatives, correctAnswer) {
  // Ensure base structure exists
  if (!jsonResponse || typeof jsonResponse !== 'object') {
    jsonResponse = {};
  }
  // Fix alternativas array
  if (!jsonResponse.alternativas || !Array.isArray(jsonResponse.alternativas)) {
    jsonResponse.alternativas = [];
  }
  // Ensure we have the right number of alternatives
  while(jsonResponse.alternativas.length < alternatives.length){
    const index = jsonResponse.alternativas.length;
    jsonResponse.alternativas.push({
      texto: alternatives[index],
      comentario: "Análise detalhada não disponível no momento.",
      correta: index === correctAnswer // ✅ CORREÇÃO: Usar indexação 0-based
    });
  }
  // Fix each alternative structure
  jsonResponse.alternativas = jsonResponse.alternativas.map((alt, index)=>({
      texto: alt.texto || alternatives[index] || `Alternativa ${index + 1}`,
      comentario: alt.comentario || "Análise detalhada não disponível no momento.",
      correta: alt.correta !== undefined ? Boolean(alt.correta) : index === correctAnswer // ✅ CORREÇÃO: Usar indexação 0-based
    }));
  // Fix comentario_final
  if (!jsonResponse.comentario_final || typeof jsonResponse.comentario_final !== 'string') {
    jsonResponse.comentario_final = "Análise completa da questão não disponível no momento.";
  }
  // Fix possivel_erro_no_gabarito
  if (jsonResponse.possivel_erro_no_gabarito === undefined) {
    jsonResponse.possivel_erro_no_gabarito = false;
  } else {
    jsonResponse.possivel_erro_no_gabarito = Boolean(jsonResponse.possivel_erro_no_gabarito);
  }
  // Fix justificativa_erro_gabarito
  if (!jsonResponse.justificativa_erro_gabarito || typeof jsonResponse.justificativa_erro_gabarito !== 'string') {
    jsonResponse.justificativa_erro_gabarito = "";
  }
  console.log('✅ [validateAndFixStructure] Structure validated and fixed');
  return jsonResponse;
}
