import { ThumbsUp, ThumbsDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useState, useEffect } from "react";
import { useDarkMode } from "@/contexts/DarkModeContext";

interface QuestionLikeButtonsProps {
  questionId: string;
  userId: string;
  initialLikes: number;
  initialDislikes: number;
  likedBy: string[];
  dislikedBy: string[];
}

export const QuestionLikeButtons = ({
  questionId,
  userId,
  initialLikes,
  initialDislikes,
  likedBy = [],
  dislikedBy = []
}: QuestionLikeButtonsProps) => {
  const { toast } = useToast();
  const { isDarkMode } = useDarkMode();
  const [likes, setLikes] = useState(initialLikes);
  const [dislikes, setDislikes] = useState(initialDislikes);
  const [localLikedBy, setLocalLikedBy] = useState<string[]>(likedBy);
  const [localDislikedBy, setLocalDislikedBy] = useState<string[]>(dislikedBy);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchCurrentData = async () => {
      const { data: currentQuestion, error } = await supabase
        .from('questions')
        .select('likes, dislikes, liked_by, disliked_by')
        .eq('id', questionId)
        .single();

      if (error) return;

      if (currentQuestion) {
        setLikes(currentQuestion.likes || 0);
        setDislikes(currentQuestion.dislikes || 0);
        setLocalLikedBy(currentQuestion.liked_by || []);
        setLocalDislikedBy(currentQuestion.disliked_by || []);
      }
    };

    fetchCurrentData();
  }, [questionId]);

  const hasLiked = localLikedBy.includes(userId);
  const hasDisliked = localDislikedBy.includes(userId);

  const handleLike = async (isLike: boolean) => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      const { data: currentQuestion, error: fetchError } = await supabase
        .from('questions')
        .select('likes, dislikes, liked_by, disliked_by')
        .eq('id', questionId)
        .single();

      if (fetchError) throw fetchError;

      let newLikedBy = [...(currentQuestion.liked_by || [])];
      let newDislikedBy = [...(currentQuestion.disliked_by || [])];
      let newLikes = currentQuestion.likes || 0;
      let newDislikes = currentQuestion.dislikes || 0;

      if (newLikedBy.includes(userId)) {
        newLikedBy = newLikedBy.filter(id => id !== userId);
        newLikes--;
      }
      if (newDislikedBy.includes(userId)) {
        newDislikedBy = newDislikedBy.filter(id => id !== userId);
        newDislikes--;
      }

      if (isLike && !newLikedBy.includes(userId)) {
        newLikedBy.push(userId);
        newLikes++;
      } else if (!isLike && !newDislikedBy.includes(userId)) {
        newDislikedBy.push(userId);
        newDislikes++;
      }

      const { error: updateError } = await supabase
        .from('questions')
        .update({
          likes: newLikes,
          dislikes: newDislikes,
          liked_by: newLikedBy,
          disliked_by: newDislikedBy
        })
        .eq('id', questionId);

      if (updateError) throw updateError;

      setLikes(newLikes);
      setDislikes(newDislikes);
      setLocalLikedBy(newLikedBy);
      setLocalDislikedBy(newDislikedBy);



    } catch (error: any) {
      toast({
        title: "Erro ao registrar avaliação",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleLike(true)}
        disabled={isSubmitting}
        className={`flex items-center gap-2 transition-colors duration-200 ${
          hasLiked
            ? isDarkMode
              ? 'bg-green-900/40 hover:bg-green-900/60 border-green-600 text-green-300'
              : 'bg-green-100 hover:bg-green-200 border-green-300 text-green-700'
            : isDarkMode
              ? 'bg-gray-700 hover:bg-gray-600 border-gray-600 text-gray-300'
              : 'bg-white hover:bg-gray-50 border-gray-300 text-gray-700'
        }`}
      >
        <ThumbsUp className={`h-4 w-4 transition-colors duration-200 ${
          hasLiked
            ? isDarkMode ? 'text-green-400' : 'text-green-600'
            : isDarkMode ? 'text-gray-400' : 'text-gray-500'
        }`} />
        <span>{likes}</span>
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={() => handleLike(false)}
        disabled={isSubmitting}
        className={`flex items-center gap-2 transition-colors duration-200 ${
          hasDisliked
            ? isDarkMode
              ? 'bg-red-900/40 hover:bg-red-900/60 border-red-600 text-red-300'
              : 'bg-red-100 hover:bg-red-200 border-red-300 text-red-700'
            : isDarkMode
              ? 'bg-gray-700 hover:bg-gray-600 border-gray-600 text-gray-300'
              : 'bg-white hover:bg-gray-50 border-gray-300 text-gray-700'
        }`}
      >
        <ThumbsDown className={`h-4 w-4 transition-colors duration-200 ${
          hasDisliked
            ? isDarkMode ? 'text-red-400' : 'text-red-600'
            : isDarkMode ? 'text-gray-400' : 'text-gray-500'
        }`} />
        <span>{dislikes}</span>
      </Button>
    </div>
  );
};
