
import { shuffle } from "@/lib/utils";



// Function to format multiple choice options with proper line breaks
const formatMultipleChoiceOptions = (text: string): string => {
  // Check if text contains multiple choice pattern
  const multipleChoicePattern = /([A-E]\)\s*[^A-E)]+)/g;
  const matches = text.match(multipleChoicePattern);

  if (!matches || matches.length < 2) {
    return text; // Not a multiple choice question or too few options
  }

  // Find where the first option starts
  const firstOptionIndex = text.search(/[A-E]\)/);
  if (firstOptionIndex === -1) {
    return text;
  }

  // Split into question and options
  const question = text.substring(0, firstOptionIndex).trim();
  const optionsText = text.substring(firstOptionIndex);

  // Split options and clean them up
  const options = optionsText
    .split(/(?=[A-E]\))/) // Split before each option letter
    .filter(opt => opt.trim()) // Remove empty parts
    .map(opt => opt.trim()); // Clean whitespace

  // Reconstruct with proper formatting
  if (question && options.length >= 2) {
    return question + '\n\n' + options.join('\n');
  }

  return text; // Fallback to original
};

export const processCard = (card: any, question: any) => {


  // Extract the correct hierarchy data from the source question
  const baseCard = {
    specialty_id: question.specialty_id,
    theme_id: question.theme_id,
    focus_id: question.focus_id,
    hierarchy: {
      specialty: { name: question.specialty?.name || "Não especificada" },
      theme: { name: question.theme?.name || "Não especificado" },
      focus: { name: question.focus?.name || "Não especificada" }
    },
    source_question_id: question.id // Track which question this card was derived from
  };

  const hierarchyPath =
    `${baseCard.hierarchy.specialty.name} > ${baseCard.hierarchy.theme?.name || "Geral"} > ${baseCard.hierarchy.focus?.name || "Geral"}`;

  // Remove QID prefix from front content if it exists
  let frontContent = card.front;
  if (frontContent) {
    // Remove the #QID#ID# prefix pattern
    frontContent = frontContent.replace(/^#QID#[a-f0-9-]+#\s*/i, '');

    // Remove "Categoria:" line if present
    frontContent = frontContent.replace(/\n\nCategoria:.*$/m, '').trim();

    // Format multiple choice options if detected
    frontContent = formatMultipleChoiceOptions(frontContent);
  }

  if (card.statement && (card.answer === "V" || card.answer === "F")) {
    return {
      ...baseCard,
      front: card.statement,
      back: card.answer === "V" ? "Verdadeiro" : "Falso",
    };
  }

  if (card.question && card.options && card.answer) {
    // Remove QID prefix from question if it exists
    let questionContent = card.question;
    if (questionContent) {
      questionContent = questionContent.replace(/^#QID#[a-f0-9-]+#\s*/i, '');
    }

    const optionsFormatted = card.options
      .map((opt: string, idx: number) => `${String.fromCharCode(65 + idx)}) ${opt}`)
      .join("\n");

    return {
      ...baseCard,
      front: `${questionContent}\n\n${optionsFormatted}`,
      back: card.answer,
    };
  }

  if (card.question && card.answer) {
    return {
      ...baseCard,
      front: card.question,
      back: card.answer,
    };
  }

  if (card.front && card.back) {
    // Check if this is a multiple choice card that came in wrong format
    // Look for QID pattern which indicates it's from a multiple choice question
    const hasQID = card.front.includes('#QID#');

    if (hasQID && question && question.response_choices) {
      // This is a multiple choice card - convert to proper format


      // Parse the original question's choices
      let choices = [];
      try {
        choices = typeof question.response_choices === 'string'
          ? JSON.parse(question.response_choices)
          : question.response_choices;
      } catch (e) {
        console.error('❌ [processCard] Error parsing response_choices:', e);
        choices = [];
      }

      if (choices && choices.length > 0) {


        // Extract just the question part (remove existing options if any)
        let questionOnly = frontContent;

        // More aggressive cleaning - remove everything after the first A) pattern
        const firstOptionIndex = questionOnly.search(/\s*A\)\s*/);
        if (firstOptionIndex !== -1) {
          questionOnly = questionOnly.substring(0, firstOptionIndex).trim();
        }

        // Also try to remove patterns like "A) ... B) ... C) ... D) ..."
        questionOnly = questionOnly.replace(/\s*[A-Z]\)\s*[^.]*\.\s*/g, '').trim();

        const optionsFormatted = choices
          .map((choice: any, idx: number) => `${String.fromCharCode(65 + idx)}) ${choice.text || choice}`)
          .join("\n");

        return {
          ...baseCard,
          front: `${questionOnly}\n\n${optionsFormatted}`,
          back: card.back,
        };
      }
    }

    return {
      ...baseCard,
      front: frontContent,
      back: card.back,
    };
  }

  return {
    ...baseCard,
    front: "Formato não suportado",
    back: "Formato não suportado",
  };
};

// Função auxiliar para garantir que temos um array válido
const ensureValidArray = (data: any): any[] => {
  if (!data) return [];

  if (Array.isArray(data)) return data;

  // Se for objeto, verifica se tem propriedades que poderiam conter arrays
  if (typeof data === 'object') {
    if (data.generated && Array.isArray(data.generated)) return data.generated;
    if (data.flashcards && Array.isArray(data.flashcards)) return data.flashcards;
    if (data.cards && Array.isArray(data.cards)) return data.cards;
    if (data.items && Array.isArray(data.items)) return data.items;

    // Se tiver apenas front e back, retorna como único item de array
    if (data.front && data.back) return [data];

    // Tenta extrair valores de objeto que parecem ser arrays
    const possibleArrays = Object.values(data).filter(v => Array.isArray(v));
    if (possibleArrays.length > 0) {
      return possibleArrays[0];
    }
  }

  // Se todas as tentativas falharem, retorna array vazio
  console.warn("⚠️ [parseGeneratedContent] Não foi possível extrair um array válido dos dados");
  return [];
};

export const parseGeneratedContent = async (
  data: any,
  questionMap?: Record<string, any> | null,
  shuffledQuestions?: any[] | null,
  customProcessor?: (card: any) => any
): Promise<any[]> => {
  let iaFlashcards = [];



  // Check if data.generated is an array and has items
  if (data.generated && Array.isArray(data.generated) && data.generated.length > 0) {

    iaFlashcards = [...data.generated]; // Use all items from the array

    if (customProcessor) {
      iaFlashcards = iaFlashcards.map(customProcessor);
    } else if (questionMap && shuffledQuestions) {
      iaFlashcards = iaFlashcards.map((card: any, index: number) => {
        // Check if card already has correct metadata from the edge function
        if (card.source_question_id && card.specialty_id && card.hierarchy) {
          return card;
        }

        // If card has source_question_id but missing other metadata
        if (card.source_question_id && questionMap[card.source_question_id]) {
          return processCard(card, questionMap[card.source_question_id]);
        }

        // Extract source_question_id from QID pattern in front property if available
        if (card.front && typeof card.front === 'string') {
          const qidMatch = card.front.match(/#QID#([a-f0-9-]+)#/i);
          if (qidMatch && qidMatch[1] && questionMap[qidMatch[1]]) {
            const extractedQuestionId = qidMatch[1];
            card.source_question_id = extractedQuestionId;
            return processCard(card, questionMap[extractedQuestionId]);
          }
        }

        // Fallback to cycling through questions if no source_question_id
        const questionIndex = index % shuffledQuestions.length;
        const sourceQuestion = shuffledQuestions[questionIndex];

        return processCard(card, sourceQuestion);
      });
    }
  }
  // Handle string response - this is the key part that's failing
  else if (typeof data.generated === "string" || typeof data.generatedText === "string") {
    const jsonText = typeof data.generated === "string" ? data.generated : data.generatedText;

    try {
      // Remove markdown code block indicators
      const cleanJson = jsonText.replace(/```(json)?\s*/g, '').replace(/\s*```$/g, '');

      // Try to parse the JSON
      let parsed;
      try {
        parsed = JSON.parse(cleanJson);
      } catch (e) {
        // Try to extract JSON array pattern
        const arrayMatch = cleanJson.match(/\[\s*\{[\s\S]*\}\s*\]/);
        if (arrayMatch) {
          parsed = JSON.parse(arrayMatch[0]);
        } else {
          throw new Error(`Erro ao parsear JSON: ${e.message}`);
        }
      }

      // Ensure we have an array
      if (!Array.isArray(parsed)) {
        parsed = ensureValidArray(parsed);
      }

      // Process the parsed flashcards
      iaFlashcards = parsed;

      if (customProcessor) {
        iaFlashcards = iaFlashcards.map(customProcessor);
      } else if (questionMap && shuffledQuestions) {
        iaFlashcards = iaFlashcards.map((card: any, index: number) => {
          // Process QID from front text
          if (card.front && typeof card.front === 'string') {
            const qidMatch = card.front.match(/#QID#([a-f0-9-]+)#/i);
            if (qidMatch && qidMatch[1] && questionMap[qidMatch[1]]) {
              const extractedQuestionId = qidMatch[1];
              card.source_question_id = extractedQuestionId;
              return processCard(card, questionMap[extractedQuestionId]);
            }
          }

          // Process card based on source_question_id
          if (card.source_question_id && questionMap[card.source_question_id]) {
            return processCard(card, questionMap[card.source_question_id]);
          }

          // Fallback to a question from the collection
          const questionIndex = index % shuffledQuestions.length;
          const sourceQuestion = shuffledQuestions[questionIndex];
          return processCard(card, sourceQuestion);
        });
      }
    } catch (e) {
      console.error(`❌ [useFlashcardGeneration] Erro ao processar string JSON:`, e);
      throw new Error(`Falha ao processar resposta da IA: ${e.message}`);
    }
  }
  // Check other possible locations for flashcard data
  else if (data.generatedText) {
    // Similar handling as above for data.generatedText
    const result = await parseGeneratedContent(
      { generated: data.generatedText },
      questionMap,
      shuffledQuestions,
      customProcessor
    );
    return result;
  }
  else if (data.data && Array.isArray(data.data)) {
    iaFlashcards = data.data;
  }
  else if (data.flashcards && Array.isArray(data.flashcards)) {
    iaFlashcards = data.flashcards;
  }

  // Última verificação para garantir que temos um array válido
  if (!Array.isArray(iaFlashcards) || iaFlashcards.length === 0) {
    // Try to find any array inside data
    const foundArray = ensureValidArray(data);
    if (foundArray.length > 0) {
      iaFlashcards = foundArray;
    } else {
      console.warn(`⚠️ [useFlashcardGeneration] Não foi possível encontrar flashcards nos dados`);
      iaFlashcards = [];
    }
  }



  return iaFlashcards;
};
