import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  RotateCcw,
  CheckCircle,
  X,
  ArrowLeft,
  MessageSquare,
  Save,
  Clock,
  Calendar,
  TrendingUp,
  TrendingDown,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Edit3,
  Brain
} from 'lucide-react';
import { ErrorQuestion } from '@/hooks/useErrorNotebook';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ErrorQuestionAICommentary } from './ErrorQuestionAICommentary';
import { ErrorQuestionNavigation } from './ErrorQuestionNavigation';
import { QuestionImages } from '../question/QuestionImages';
import { DiscursiveAnswer } from '../question/DiscursiveAnswer';
import { DiscursiveAIAnalysis } from '../question/DiscursiveAIAnalysis';
import { useDarkMode } from '@/contexts/DarkModeContext';
import { useToast } from '@/hooks/use-toast';

interface QuestionRetryModeProps {
  question: ErrorQuestion;
  onBack: () => void;
  onMarkAsReviewed: (questionId: string, userAnswerId: string, annotation?: string, correctOnReview?: boolean, timeSpent?: number, discursiveAnswer?: string) => void;
  onUpdateAnnotation: (questionId: string, userAnswerId: string, annotation: string) => void;
  saveDiscursiveAnalysis?: (userAnswerId: string, questionId: string, analysis: any) => Promise<boolean>;
  onNext?: () => void;
  onPrevious?: () => void;
  onGoToQuestion?: (index: number) => void;
  currentIndex?: number;
  totalQuestions?: number;
  reviewedQuestions?: Set<number>;
  answeredStatuses?: Record<number, boolean | null>;
  onAnswerStatusChange?: (index: number, status: boolean | null) => void;
  sessionAnswers?: Record<number, number | null>; // Respostas selecionadas por questão
  onAnswerChange?: (index: number, answer: number | null) => void;
  onFinishSession?: () => void;
}

export const QuestionRetryMode: React.FC<QuestionRetryModeProps> = ({
  question,
  onBack,
  onMarkAsReviewed,
  onUpdateAnnotation,
  onNext,
  onPrevious,
  onGoToQuestion,
  currentIndex,
  totalQuestions,
  reviewedQuestions,
  answeredStatuses = {},
  onAnswerStatusChange,
  sessionAnswers = {},
  onAnswerChange,
  onFinishSession,
  saveDiscursiveAnalysis
}) => {
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [annotation, setAnnotation] = useState(question.anotacao || '');
  const [isEditingAnnotation, setIsEditingAnnotation] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [startTime, setStartTime] = useState<number>(Date.now());
  const [timeSpent, setTimeSpent] = useState<number>(0);
  const [showDetails, setShowDetails] = useState(false);
  const [isMarkingAsReviewed, setIsMarkingAsReviewed] = useState(false);
  const [isMarkedAsReviewed, setIsMarkedAsReviewed] = useState(false);

  // Estados para questões discursivas
  const [discursiveAnswer, setDiscursiveAnswer] = useState('');
  const [hasAnsweredDiscursive, setHasAnsweredDiscursive] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState<any>(null);

  const { isDarkMode } = useDarkMode();
  const { toast } = useToast();

  // Verificar se é questão discursiva
  const isDiscursive = (question.question_format === 'DISSERTATIVA');

  useEffect(() => {
    setStartTime(Date.now());
    // Resetar estados quando questão mudar
    setIsMarkingAsReviewed(false);
    setIsMarkedAsReviewed(false);

    // Carregar resposta discursiva e análise existentes
    if (isDiscursive) {
      setDiscursiveAnswer(question.resposta_discursiva || '');
      setHasAnsweredDiscursive(!!question.resposta_discursiva);
      setAiAnalysis(question.analise_ia_discursiva || null);
    }
  }, [question.id, isDiscursive]);

  // Sincronizar anotação quando a questão muda
  useEffect(() => {
    setAnnotation(question.anotacao || '');
    setIsEditingAnnotation(false); // Cancelar edição ao navegar

    // Verificar se já existe resposta para esta questão na sessão atual
    const currentQuestionIndex = currentIndex || 0;
    const existingAnswer = answeredStatuses[currentQuestionIndex];
    const savedAnswer = sessionAnswers[currentQuestionIndex];

    if (existingAnswer !== undefined) {
      // Já foi respondida nesta sessão, mostrar resultado
      setHasAnswered(true);
      setShowResult(true);
      // Carregar a resposta salva
      setSelectedAnswer(savedAnswer !== undefined ? savedAnswer : null);
    } else {
      // Nova questão ou não respondida ainda
      setSelectedAnswer(savedAnswer !== undefined ? savedAnswer : null);
      setHasAnswered(false);
      setShowResult(false);
    }
  }, [question.id, currentIndex, answeredStatuses, sessionAnswers]);

  const handleSaveAnnotation = async () => {
    setIsSaving(true);
    try {
      await onUpdateAnnotation(question.question_id, question.id, annotation);
      setIsEditingAnnotation(false);
    } finally {
      setIsSaving(false);
    }
  };



  const handleAnswerSelect = (answerIndex: number) => {
    if (!hasAnswered) {
      setSelectedAnswer(answerIndex);

      // Salvar a resposta na sessão
      const currentQuestionIndex = currentIndex || 0;
      if (onAnswerChange) {
        onAnswerChange(currentQuestionIndex, answerIndex);
      }
    }
  };

  const handleSubmitAnswer = () => {
    if (selectedAnswer === null) return;

    const endTime = Date.now();
    const totalTime = Math.round((endTime - startTime) / 1000);
    setTimeSpent(totalTime);
    setHasAnswered(true);
    setShowResult(true);

    // Salvar status da resposta para esta questão
    const isCorrect = selectedAnswer === parseInt(question.correct_choice);
    const currentQuestionIndex = currentIndex || 0;

    if (onAnswerStatusChange) {
      onAnswerStatusChange(currentQuestionIndex, isCorrect);
    }
  };

  // Funções para questões discursivas
  const handleDiscursiveSubmit = async () => {
    if (!discursiveAnswer.trim()) return;

    try {
      // Salvar a resposta discursiva automaticamente
      const result = await onMarkAsReviewed(question.question_id, question.id, annotation, true, timeSpent, discursiveAnswer);

      setHasAnsweredDiscursive(true);
      setShowResult(true);

      toast({
        title: "Resposta salva",
        description: "Sua resposta foi salva com sucesso!",
      });
    } catch (error) {
      console.error('Erro ao salvar resposta discursiva:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar sua resposta. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  const handleDiscursiveAnalyze = async () => {
    if (!discursiveAnswer.trim()) {
      toast({
        title: "Resposta vazia",
        description: "Por favor, escreva sua resposta antes de solicitar análise",
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);

    try {
      const requestData = {
        statement: question.question_content,
        userAnswer: discursiveAnswer,
      };

      const res = await fetch(
        `https://bxedpdmgvgatjdfxgxij.functions.supabase.co/discursive-ai-analysis`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestData),
        }
      );

      if (!res.ok) {
        throw new Error(`Erro na API: ${res.status}`);
      }

      const data = await res.json();

      // Estrutura da resposta da IA igual ao sistema de questões
      const analysis = {
        aiAnswer: data.ai_answer || "",
        feedback: data.feedback || "",
        isCorrect: data.is_correct || false,
        scorePercentage: data.score_percentage || 0,
        mainPointsCovered: data.main_points_covered || [],
        missingPoints: data.missing_points || []
      };

      setAiAnalysis(analysis);

      // Salvar a análise no banco se a função estiver disponível
      if (saveDiscursiveAnalysis) {
        await saveDiscursiveAnalysis(question.id, question.question_id, analysis);
      }

      toast({
        title: "Análise gerada",
        description: "A análise da sua resposta foi gerada com sucesso!",
      });

    } catch (error) {
      console.error('Erro ao gerar análise:', error);
      toast({
        title: "Erro",
        description: "Não foi possível gerar a análise. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleMarkAsReviewed = async () => {
    setIsMarkingAsReviewed(true);
    try {
      let isCorrect = false;

      if (isDiscursive) {
        // Para questões discursivas, não há resposta "correta" objetiva
        isCorrect = hasAnsweredDiscursive;
      } else {
        isCorrect = selectedAnswer === parseInt(question.correct_choice);
      }

      // Incluir resposta discursiva se for questão discursiva
      const discursiveResponse = isDiscursive ? discursiveAnswer : undefined;

      await onMarkAsReviewed(question.question_id, question.id, annotation, isCorrect, timeSpent, discursiveResponse);

      // ✅ MELHORIA: Apenas atualizar o botão, sem navegação automática
      setIsMarkedAsReviewed(true);

      // Usuário navega manualmente usando os botões de navegação
    } catch (error) {
      console.error('Erro ao marcar como revisada:', error);
      setIsMarkingAsReviewed(false);
    }
  };

  const handleCommentaryGenerated = (commentary: any) => {
    // Callback quando a análise é gerada - pode ser usado para atualizar estado se necessário
  };

  const isCorrectAnswer = selectedAnswer === parseInt(question.correct_choice);
  const originallyWrong = question.selected_answer !== parseInt(question.correct_choice);

  const getAlternativeStyle = (index: number) => {
    if (!showResult) {
      return selectedAnswer === index 
        ? 'bg-blue-100 border-blue-500 text-blue-800 cursor-pointer' 
        : 'bg-gray-50 border-gray-300 text-gray-700 cursor-pointer hover:bg-gray-100';
    }

    const isSelected = selectedAnswer === index;
    const isCorrect = index === parseInt(question.correct_choice);
    const wasOriginalAnswer = index === question.selected_answer;

    if (isSelected && isCorrect) {
      return 'bg-green-100 border-green-500 text-green-800'; // Acertou agora
    } else if (isSelected && !isCorrect) {
      return 'bg-red-100 border-red-500 text-red-800'; // Errou novamente
    } else if (isCorrect) {
      return 'bg-green-100 border-green-500 text-green-800'; // Resposta correta
    } else if (wasOriginalAnswer) {
      return 'bg-orange-100 border-orange-500 text-orange-800'; // Resposta original errada
    }
    return 'bg-gray-50 border-gray-300 text-gray-700';
  };

  const getAlternativeIcon = (index: number) => {
    if (!showResult) return null;

    const isSelected = selectedAnswer === index;
    const isCorrect = index === parseInt(question.correct_choice);
    const wasOriginalAnswer = index === question.selected_answer;

    if (isSelected && isCorrect) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    } else if (isSelected && !isCorrect) {
      return <X className="h-4 w-4 text-red-600" />;
    } else if (isCorrect) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    } else if (wasOriginalAnswer) {
      return <X className="h-4 w-4 text-orange-600" />;
    }
    return null;
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ptBR 
      });
    } catch {
      return 'Data inválida';
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-0">
      {/* Navigation - Always show, even for single questions */}
      <ErrorQuestionNavigation
        currentIndex={currentIndex || 0}
        totalQuestions={totalQuestions || 1}
        onNext={onNext}
        onPrevious={onPrevious}
        onGoToQuestion={onGoToQuestion}
        onBack={onBack}
        mode="retry"
        reviewedQuestions={reviewedQuestions}
        answeredStatuses={answeredStatuses}
        onFinishSession={onFinishSession}
      />

      {/* Status Badge */}
      {showResult && (
        <div className="flex justify-center">
          <Badge variant={isCorrectAnswer ? "default" : "destructive"} className="text-sm">
            {isCorrectAnswer ? 'Acertou na Revisão!' : 'Errou Novamente'}
          </Badge>
        </div>
      )}

      {/* Question Card */}
      <Card className="p-3 sm:p-6 bg-white border-2 border-black shadow-card-sm">
        {/* Question Info */}
        <div className="mb-4 sm:mb-6">
          <div className="flex justify-between items-center mb-3 sm:mb-4">
            <div className="text-sm font-medium text-gray-600">
              Informações da questão
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className={`text-xs px-2 h-7 transition-colors duration-200 ${
                isDarkMode
                  ? 'text-gray-300 hover:text-gray-100 hover:bg-gray-700'
                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {showDetails ? (
                <span className="flex items-center gap-1">
                  Ocultar detalhes <ChevronUp size={14} />
                </span>
              ) : (
                <span className="flex items-center gap-1">
                  Mostrar detalhes <ChevronDown size={14} />
                </span>
              )}
            </Button>
          </div>

          {showDetails && (
            <div className="space-y-3">
              {/* Badges de metadados */}
              <div className="flex flex-wrap gap-2 text-sm">
                {/* Especialidade */}
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                  isDarkMode ? 'bg-blue-900/40 text-blue-300' : 'bg-blue-100 text-blue-800'
                }`}>
                  {question.specialty_name}
                </span>

                {/* Tema */}
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                  isDarkMode ? 'bg-green-900/40 text-green-300' : 'bg-green-100 text-green-800'
                }`}>
                  {question.theme_name}
                </span>

                {/* Foco (se disponível) */}
                {question.focus_name && question.focus_name !== question.theme_name && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    isDarkMode ? 'bg-purple-900/40 text-purple-300' : 'bg-purple-100 text-purple-800'
                  }`}>
                    {question.focus_name}
                  </span>
                )}

                {/* Origem/Local (se disponível) */}
                {question.exam_name && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    isDarkMode ? 'bg-yellow-900/40 text-yellow-300' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {question.exam_name}
                  </span>
                )}

                {/* Ano */}
                {question.exam_year && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {question.exam_year}
                  </span>
                )}
              </div>

              {/* Informações temporais */}
              <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Primeira tentativa {formatDate(question.created_at)}
                </div>
                {showResult && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    Tempo: {timeSpent}s
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Question Content */}
        <div className="mb-4 sm:mb-6">
          <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Enunciado</h3>
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
            <p className="text-gray-800 leading-relaxed text-sm sm:text-base">
              {question.question_content}
            </p>
          </div>
        </div>

        {/* Question Images */}
        {question.media_attachments && question.media_attachments.length > 0 && (
          <QuestionImages images={question.media_attachments} />
        )}

        {/* Alternatives or Discursive Answer */}
        {isDiscursive ? (
          <div className="mb-4 sm:mb-6">
            <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Sua Resposta</h3>
            <DiscursiveAnswer
              value={discursiveAnswer}
              onChange={setDiscursiveAnswer}
              onSubmit={handleDiscursiveSubmit}
              onAnalyze={handleDiscursiveAnalyze}
              hasAnswered={hasAnsweredDiscursive}
              isAnalyzing={isAnalyzing}
              hasBeenEvaluated={!!aiAnalysis}
              readOnly={false}
            />

            {/* Análise da IA para questões discursivas */}
            {aiAnalysis && (
              <div className="mt-4">
                <div className={`rounded-lg border-2 shadow-lg transition-colors duration-200 ${
                  isDarkMode ? 'border-gray-600' : 'border-gray-300'
                }`}>
                  <div className={`border-b-2 py-4 px-6 flex items-center justify-between transition-colors duration-200 ${
                    isDarkMode
                      ? 'border-gray-600 bg-red-900/20'
                      : 'border-gray-300 bg-red-50'
                  }`}>
                    <div className="flex items-center gap-2">
                      <Brain className={`h-5 w-5 transition-colors duration-200 ${
                        isDarkMode ? 'text-red-400' : 'text-red-600'
                      }`} />
                      <span className={`font-bold transition-colors duration-200 ${
                        isDarkMode ? 'text-red-300' : 'text-red-700'
                      }`}>Análise da IA</span>
                    </div>
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-bold transition-colors duration-200 ${
                      aiAnalysis.isCorrect
                        ? isDarkMode
                          ? 'bg-green-900/40 text-green-300'
                          : 'bg-green-100 text-green-700'
                        : isDarkMode
                          ? 'bg-red-900/40 text-red-300'
                          : 'bg-red-100 text-red-700'
                    }`}>
                      {aiAnalysis.isCorrect ? '✅ Correta' : '❌ Incorreta'}
                      {aiAnalysis.scorePercentage && (
                        <span className="ml-1">({aiAnalysis.scorePercentage}%)</span>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4 p-4">
                    {aiAnalysis.aiAnswer && (
                      <div className={`rounded-lg p-4 border-2 transition-colors duration-200 ${
                        isDarkMode
                          ? 'border-green-600 bg-green-900/20 hover:bg-green-900/30'
                          : 'border-green-200 bg-green-50/50 hover:bg-green-50'
                      }`}>
                        <h4 className={`font-bold mb-2 transition-colors duration-200 ${
                          isDarkMode ? 'text-green-300' : 'text-green-800'
                        }`}>Resposta Esperada</h4>
                        <div className={`leading-relaxed whitespace-pre-line transition-colors duration-200 ${
                          isDarkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          {aiAnalysis.aiAnswer}
                        </div>
                      </div>
                    )}

                    {aiAnalysis.feedback && (
                      <div className={`rounded-lg p-4 border-2 transition-colors duration-200 ${
                        aiAnalysis.isCorrect
                          ? isDarkMode
                            ? 'border-blue-600 bg-blue-900/20 hover:bg-blue-900/30'
                            : 'border-blue-200 bg-blue-50/50 hover:bg-blue-50'
                          : isDarkMode
                            ? 'border-orange-600 bg-orange-900/20 hover:bg-orange-900/30'
                            : 'border-orange-200 bg-orange-50/50 hover:bg-orange-50'
                      }`}>
                        <h4 className={`font-bold mb-2 flex items-center gap-2 transition-colors duration-200 ${
                          aiAnalysis.isCorrect
                            ? isDarkMode ? 'text-blue-300' : 'text-blue-800'
                            : isDarkMode ? 'text-orange-300' : 'text-orange-800'
                        }`}>
                          <Brain className="h-4 w-4" />
                          Feedback sobre sua resposta
                        </h4>
                        <div className={`leading-relaxed whitespace-pre-line transition-colors duration-200 ${
                          isDarkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          {aiAnalysis.feedback}
                        </div>
                      </div>
                    )}

                    {aiAnalysis.missingPoints && aiAnalysis.missingPoints.length > 0 && (
                      <div className={`rounded-lg p-4 border-2 transition-colors duration-200 ${
                        isDarkMode
                          ? 'border-orange-600 bg-orange-900/20'
                          : 'border-orange-200 bg-orange-50'
                      }`}>
                        <h4 className={`font-bold mb-2 transition-colors duration-200 ${
                          isDarkMode ? 'text-orange-300' : 'text-orange-800'
                        }`}>⚠️ Pontos Não Abordados</h4>
                        <ul className={`list-disc list-inside space-y-1 transition-colors duration-200 ${
                          isDarkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          {aiAnalysis.missingPoints.map((point: string, index: number) => (
                            <li key={index}>{point}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="mb-4 sm:mb-6">
            <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Alternativas</h3>
            <div className="space-y-2 sm:space-y-3">
            {question.response_choices.map((choice, index) => (
              <div
                key={index}
                className={`p-3 sm:p-4 rounded-lg border-2 transition-all ${getAlternativeStyle(index)}`}
                onClick={() => handleAnswerSelect(index)}
              >
                <div className="flex items-start gap-2 sm:gap-3">
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-sm sm:text-base">
                      {String.fromCharCode(65 + index)})
                    </span>
                    {getAlternativeIcon(index)}
                  </div>
                  <p className="flex-1 text-sm sm:text-base">{choice}</p>
                </div>
                
                {/* Labels */}
                {showResult && (
                  <div className="mt-2 flex gap-2">
                    {selectedAnswer === index && (
                      <Badge variant={isCorrectAnswer ? "default" : "destructive"} className="text-xs">
                        Sua nova resposta
                      </Badge>
                    )}
                    {index === parseInt(question.correct_choice) && (
                      <Badge variant="default" className="text-xs bg-green-500">
                        Resposta correta
                      </Badge>
                    )}
                    {index === question.selected_answer && (
                      <Badge variant="outline" className="text-xs border-orange-500 text-orange-700">
                        Resposta original
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            ))}
            </div>
          </div>
        )}

        {/* Submit Button - apenas para questões de múltipla escolha */}
        {!isDiscursive && !hasAnswered && (
          <div className="mb-6">
            <Button
              onClick={handleSubmitAnswer}
              disabled={selectedAnswer === null}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
            >
              Confirmar Resposta
            </Button>
          </div>
        )}

        {/* Result Analysis - apenas para questões de múltipla escolha */}
        {!isDiscursive && showResult && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">Análise do Resultado</h3>
            <div className={`p-4 rounded-lg border-2 ${isCorrectAnswer ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
              <div className="flex items-center gap-2 mb-3">
                {isCorrectAnswer ? (
                  <>
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-green-800">Parabéns! Você acertou desta vez!</span>
                  </>
                ) : (
                  <>
                    <TrendingDown className="h-5 w-5 text-red-600" />
                    <span className="font-semibold text-red-800">Você errou novamente.</span>
                  </>
                )}
              </div>
              
              <div className="text-sm space-y-2">
                <p>
                  <strong>Resposta original:</strong> {String.fromCharCode(65 + question.selected_answer)}) {question.response_choices[question.selected_answer]}
                </p>
                <p>
                  <strong>Sua nova resposta:</strong> {String.fromCharCode(65 + selectedAnswer!)}) {question.response_choices[selectedAnswer!]}
                </p>
                <p>
                  <strong>Resposta correta:</strong> {String.fromCharCode(65 + parseInt(question.correct_choice))}) {question.response_choices[parseInt(question.correct_choice)]}
                </p>
                <p>
                  <strong>Tempo gasto:</strong> {timeSpent} segundos (original: {question.time_spent}s)
                </p>
              </div>
            </div>
          </div>
        )}

        {/* AI Commentary */}
        {showResult && (
          <ErrorQuestionAICommentary
            question={question}
            onCommentaryGenerated={handleCommentaryGenerated}
          />
        )}

        {/* Annotations */}
        {showResult && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-blue-500" />
                Suas Anotações
              </h3>
              {!isEditingAnnotation && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditingAnnotation(true)}
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  {annotation ? 'Editar' : 'Adicionar'}
                </Button>
              )}
            </div>

            {isEditingAnnotation ? (
              <div className="space-y-3">
                <Textarea
                  value={annotation}
                  onChange={(e) => setAnnotation(e.target.value)}
                  placeholder="Adicione suas reflexões sobre esta questão..."
                  rows={4}
                  className="border-2 border-gray-300"
                />
                <div className="flex gap-2">
                  <Button
                    onClick={handleSaveAnnotation}
                    disabled={isSaving}
                    size="sm"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? 'Salvando...' : 'Salvar'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsEditingAnnotation(false);
                      setAnnotation(question.anotacao || '');
                    }}
                    size="sm"
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 p-4 rounded-lg min-h-[100px] border-2 border-gray-200">
                {annotation ? (
                  <p className="text-gray-800 whitespace-pre-wrap">{annotation}</p>
                ) : (
                  <p className="text-gray-500 italic">Nenhuma anotação adicionada.</p>
                )}
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="pt-4 border-t">
          {showResult && (
            <div className="flex items-center justify-between gap-1 sm:gap-4">
              {/* Botão Anterior */}
              <div className="flex-1">
                {onPrevious && currentIndex !== undefined && currentIndex > 0 ? (
                  <Button
                    onClick={onPrevious}
                    variant="outline"
                    size="sm"
                    className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                  >
                    <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Anterior</span>
                    <span className="sm:hidden">Ant</span>
                  </Button>
                ) : null}
              </div>

              {/* Botão Central - Marcar como Revisada */}
              <div className="flex-shrink-0 px-1">
                <Button
                  onClick={handleMarkAsReviewed}
                  disabled={
                    isMarkingAsReviewed ||
                    isMarkedAsReviewed ||
                    question.revisado ||
                    (!isDiscursive && !hasAnswered) ||
                    (isDiscursive && !hasAnsweredDiscursive)
                  }
                  size="sm"
                  className={`border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4 ${
                    isMarkedAsReviewed || question.revisado
                      ? 'bg-green-600 text-white cursor-default'
                      : isMarkingAsReviewed
                        ? 'bg-green-400 text-white cursor-wait'
                        : 'bg-green-500 hover:bg-green-600 text-white'
                  }`}
                >
                  <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  {isMarkedAsReviewed || question.revisado
                    ? <span className="hidden sm:inline">Marcada como Revisada ✓</span>
                    : isMarkingAsReviewed
                      ? <span className="hidden sm:inline">Marcando...</span>
                      : <span className="hidden sm:inline">Marcar como Revisada</span>
                  }
                  {isMarkedAsReviewed || question.revisado
                    ? <span className="sm:hidden">Revisada ✓</span>
                    : isMarkingAsReviewed
                      ? <span className="sm:hidden">Marcando...</span>
                      : <span className="sm:hidden">Marcar Revisado</span>
                  }
                </Button>
              </div>

              {/* Botão Próxima ou Finalizar */}
              <div className="flex-1 flex justify-end">
                {currentIndex !== undefined && totalQuestions !== undefined && currentIndex < totalQuestions - 1 ? (
                  onNext && (
                    <Button
                      onClick={onNext}
                      variant="outline"
                      size="sm"
                      className="border-2 border-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                    >
                      <span className="hidden sm:inline">Próxima</span>
                      <span className="sm:hidden">Próx</span>
                      <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                    </Button>
                  )
                ) : (
                  onFinishSession && (
                    <Button
                      onClick={onFinishSession}
                      size="sm"
                      className="bg-blue-500 hover:bg-blue-600 text-white border-2 border-black font-bold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all text-xs sm:text-sm px-2 sm:px-4"
                    >
                      Finalizar
                    </Button>
                  )
                )}
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};
